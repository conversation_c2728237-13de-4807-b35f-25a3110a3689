-- Stripe-related tables for Rowanenergy

-- Table to store Stripe customers
CREATE TABLE IF NOT EXISTS `rowanenergy_stripe_customers` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `stripe_customer_id` varchar(255) NOT NULL,
  `email` varchar(255) NOT NULL,
  `name` varchar(255) DEFAULT NULL,
  `phone` varchar(50) DEFAULT NULL,
  `metadata` text DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_user_id` (`user_id`),
  UNIQUE KEY `unique_stripe_customer_id` (`stripe_customer_id`),
  INDEX `idx_user_id` (`user_id`),
  INDEX `idx_stripe_customer_id` (`stripe_customer_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table to store Stripe subscriptions
CREATE TABLE IF NOT EXISTS `rowanenergy_stripe_subscriptions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `stripe_subscription_id` varchar(255) NOT NULL,
  `stripe_customer_id` varchar(255) NOT NULL,
  `status` varchar(50) NOT NULL DEFAULT 'active',
  `current_period_start` datetime DEFAULT NULL,
  `current_period_end` datetime DEFAULT NULL,
  `cancel_at_period_end` tinyint(1) DEFAULT 0,
  `canceled_at` datetime DEFAULT NULL,
  `ended_at` datetime DEFAULT NULL,
  `trial_start` datetime DEFAULT NULL,
  `trial_end` datetime DEFAULT NULL,
  `metadata` text DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_stripe_subscription_id` (`stripe_subscription_id`),
  INDEX `idx_user_id` (`user_id`),
  INDEX `idx_stripe_customer_id` (`stripe_customer_id`),
  INDEX `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table to store Stripe subscription items (pricing details)
CREATE TABLE IF NOT EXISTS `rowanenergy_stripe_subscription_items` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `subscription_id` int(11) NOT NULL,
  `stripe_subscription_item_id` varchar(255) NOT NULL,
  `stripe_price_id` varchar(255) NOT NULL,
  `stripe_product_id` varchar(255) NOT NULL,
  `quantity` int(11) DEFAULT 1,
  `unit_amount` int(11) DEFAULT NULL,
  `currency` varchar(10) DEFAULT 'gbp',
  `interval` varchar(20) DEFAULT 'month',
  `interval_count` int(11) DEFAULT 1,
  `nickname` varchar(255) DEFAULT NULL,
  `product_name` varchar(255) DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_stripe_subscription_item_id` (`stripe_subscription_item_id`),
  INDEX `idx_subscription_id` (`subscription_id`),
  INDEX `idx_stripe_price_id` (`stripe_price_id`),
  FOREIGN KEY (`subscription_id`) REFERENCES `rowanenergy_stripe_subscriptions` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table to store Stripe payment methods
CREATE TABLE IF NOT EXISTS `rowanenergy_stripe_payment_methods` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `stripe_payment_method_id` varchar(255) NOT NULL,
  `stripe_customer_id` varchar(255) NOT NULL,
  `type` varchar(50) DEFAULT 'card',
  `card_brand` varchar(50) DEFAULT NULL,
  `card_last4` varchar(4) DEFAULT NULL,
  `card_exp_month` int(2) DEFAULT NULL,
  `card_exp_year` int(4) DEFAULT NULL,
  `card_fingerprint` varchar(255) DEFAULT NULL,
  `is_default` tinyint(1) DEFAULT 0,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_stripe_payment_method_id` (`stripe_payment_method_id`),
  INDEX `idx_user_id` (`user_id`),
  INDEX `idx_stripe_customer_id` (`stripe_customer_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table to store Stripe invoices
CREATE TABLE IF NOT EXISTS `rowanenergy_stripe_invoices` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `stripe_invoice_id` varchar(255) NOT NULL,
  `stripe_customer_id` varchar(255) NOT NULL,
  `stripe_subscription_id` varchar(255) DEFAULT NULL,
  `amount_due` int(11) DEFAULT NULL,
  `amount_paid` int(11) DEFAULT NULL,
  `currency` varchar(10) DEFAULT 'gbp',
  `status` varchar(50) DEFAULT NULL,
  `invoice_pdf` text DEFAULT NULL,
  `hosted_invoice_url` text DEFAULT NULL,
  `period_start` datetime DEFAULT NULL,
  `period_end` datetime DEFAULT NULL,
  `due_date` datetime DEFAULT NULL,
  `paid_at` datetime DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_stripe_invoice_id` (`stripe_invoice_id`),
  INDEX `idx_user_id` (`user_id`),
  INDEX `idx_stripe_customer_id` (`stripe_customer_id`),
  INDEX `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table to store available Stripe products/plans
CREATE TABLE IF NOT EXISTS `rowanenergy_stripe_products` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `stripe_product_id` varchar(255) NOT NULL,
  `name` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `active` tinyint(1) DEFAULT 1,
  `features` text DEFAULT NULL, -- JSON array of features
  `metadata` text DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_stripe_product_id` (`stripe_product_id`),
  INDEX `idx_active` (`active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table to store Stripe prices
CREATE TABLE IF NOT EXISTS `rowanenergy_stripe_prices` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `stripe_price_id` varchar(255) NOT NULL,
  `stripe_product_id` varchar(255) NOT NULL,
  `nickname` varchar(255) DEFAULT NULL,
  `unit_amount` int(11) NOT NULL,
  `currency` varchar(10) DEFAULT 'gbp',
  `recurring_interval` varchar(20) DEFAULT 'month',
  `recurring_interval_count` int(11) DEFAULT 1,
  `active` tinyint(1) DEFAULT 1,
  `metadata` text DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_stripe_price_id` (`stripe_price_id`),
  INDEX `idx_stripe_product_id` (`stripe_product_id`),
  INDEX `idx_active` (`active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert sample products and prices for Rowanenergy
INSERT INTO `rowanenergy_stripe_products` 
(`stripe_product_id`, `name`, `description`, `active`, `features`, `metadata`) 
VALUES 
('prod_premium_plan', 'Premium Plan', 'Perfect for growing businesses', 1, 
 '["Unlimited survey bookings", "Priority engineer assignment", "Advanced reporting & analytics", "Priority support", "Custom branding on reports"]',
 '{"platform": "rowanenergy", "tier": "premium"}'),
('prod_enterprise_plan', 'Enterprise Plan', 'For large organizations', 1,
 '["Everything in Premium", "Dedicated account manager", "Custom SLA agreements", "API access", "White-label solution", "Multi-location management"]',
 '{"platform": "rowanenergy", "tier": "enterprise"}');

INSERT INTO `rowanenergy_stripe_prices` 
(`stripe_price_id`, `stripe_product_id`, `nickname`, `unit_amount`, `currency`, `recurring_interval`, `recurring_interval_count`, `active`, `metadata`) 
VALUES 
('price_premium_monthly', 'prod_premium_plan', 'Premium Monthly', 4900, 'gbp', 'month', 1, 1, '{"platform": "rowanenergy"}'),
('price_premium_yearly', 'prod_premium_plan', 'Premium Yearly', 49000, 'gbp', 'year', 1, 1, '{"platform": "rowanenergy"}'),
('price_enterprise_monthly', 'prod_enterprise_plan', 'Enterprise Monthly', 9900, 'gbp', 'month', 1, 1, '{"platform": "rowanenergy"}'),
('price_enterprise_yearly', 'prod_enterprise_plan', 'Enterprise Yearly', 99000, 'gbp', 'year', 1, 1, '{"platform": "rowanenergy"}');
