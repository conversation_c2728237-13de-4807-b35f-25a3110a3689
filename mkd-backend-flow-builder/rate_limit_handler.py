import time
import logging
from functools import wraps
import random
import re
import json
from google.api_core.exceptions import ResourceExhausted, ServiceUnavailable, TooManyRequests

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("gemini_api_errors.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("gemini_rate_limit")

def extract_retry_delay(error):
    """
    Extract retry delay from error message if present.
    Returns delay in seconds or None if not found.
    """
    try:
        # Convert error to string to handle different error types
        error_str = str(error)
        
        # Try to extract JSON part from the error
        json_match = re.search(r'\{.*\}', error_str)
        if json_match:
            error_json = json.loads(json_match.group(0))
            
            # Check for retryDelay in RetryInfo
            details = error_json.get('error', {}).get('details', [])
            for detail in details:
                if detail.get('@type') == 'type.googleapis.com/google.rpc.RetryInfo':
                    retry_delay = detail.get('retryDelay')
                    if retry_delay:
                        # Convert from string format (e.g. "23s") to seconds
                        seconds_match = re.search(r'(\d+)s', retry_delay)
                        if seconds_match:
                            return int(seconds_match.group(1))
        
        # Try to extract seconds directly with regex as fallback
        seconds_match = re.search(r'retryDelay[\'"]?\s*[:=]\s*[\'"]?(\d+)s', error_str)
        if seconds_match:
            return int(seconds_match.group(1))
            
    except Exception as e:
        logger.warning(f"Failed to extract retry delay: {e}")
    
    return None

def handle_rate_limits(max_retries=5, initial_backoff=30, backoff_multiplier=2, jitter=0.2):
    """
    Decorator for handling rate limiting and other transient API errors.
    
    Args:
        max_retries (int): Maximum number of retry attempts
        initial_backoff (int): Initial backoff in seconds
        backoff_multiplier (float): Multiplier for exponential backoff
        jitter (float): Random jitter factor to add to backoff times
    
    Returns:
        Function decorator that adds retry logic
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            retries = 0
            last_exception = None
            backoff = initial_backoff
            
            while retries <= max_retries:
                try:
                    return func(*args, **kwargs)
                except (ResourceExhausted, TooManyRequests) as e:
                    # These are rate limit errors
                    last_exception = e
                    retries += 1
                    if retries > max_retries:
                        logger.error(f"Rate limit exceeded. Max retries ({max_retries}) reached. Giving up.")
                        break
                    
                    # Try to extract retry delay from error
                    api_retry_delay = extract_retry_delay(e)
                    
                    if api_retry_delay:
                        # Use the API-suggested retry delay plus a small buffer
                        sleep_time = api_retry_delay + random.uniform(1, 5)
                        logger.warning(f"Rate limit hit. API suggested retry after {api_retry_delay}s. Waiting {sleep_time:.2f}s. (Attempt {retries}/{max_retries})")
                    else:
                        # Calculate backoff with jitter as fallback
                        jitter_amount = random.uniform(0, jitter * backoff)
                        sleep_time = backoff + jitter_amount
                        logger.warning(f"Rate limit hit. Retrying in {sleep_time:.2f} seconds. (Attempt {retries}/{max_retries})")
                    
                    # Log the error details
                    logger.info(f"Rate limit error details: {str(e)}")
                    
                    time.sleep(sleep_time)
                    
                    # Increase backoff for next attempt
                    backoff *= backoff_multiplier
                    
                except ServiceUnavailable as e:
                    # Service unavailable - usually temporary
                    last_exception = e
                    retries += 1
                    if retries > max_retries:
                        logger.error(f"Service unavailable. Max retries ({max_retries}) reached. Giving up.")
                        break
                    
                    # Try to extract retry delay first
                    api_retry_delay = extract_retry_delay(e)
                    
                    if api_retry_delay:
                        sleep_time = api_retry_delay + random.uniform(1, 5)
                    else:
                        # Use a more aggressive backoff for service unavailability
                        jitter_amount = random.uniform(0, jitter * backoff)
                        sleep_time = backoff * 1.5 + jitter_amount
                    
                    logger.warning(f"Service unavailable. Retrying in {sleep_time:.2f} seconds. (Attempt {retries}/{max_retries})")
                    time.sleep(sleep_time)
                    
                    # Increase backoff for next attempt
                    backoff *= backoff_multiplier
                    
                except Exception as e:
                    # For other exceptions, we don't retry
                    logger.error(f"Unexpected error: {e}")
                    raise
            
            # If we've exhausted retries, raise the last exception
            if last_exception:
                logger.error(f"All retries failed. Last error: {last_exception}")
                raise last_exception
            
        return wrapper
    return decorator

# Example usage:
# @handle_rate_limits(max_retries=3, initial_backoff=60)
# def call_gemini_api(prompt, model):
#     # API call implementation
#     pass 