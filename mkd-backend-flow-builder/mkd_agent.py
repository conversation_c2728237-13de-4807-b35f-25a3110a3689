import os
import json
import requests
import time
import datetime
import re
from google import genai
from google.genai import types
from rate_limit_handler import handle_rate_limits
from google.api_core.exceptions import ResourceExhausted

# Configuration: API key and model version.
API_KEY = os.environ.get("GOOGLE_API_KEY")  # Ensure your API key is set in the environment.
# MODEL = "models/gemini-2.0-flash-exp"
# MODEL = "models/gemini-1.5-flash-001"
# MODEL = "models/gemini-2.0-flash-001"
MODEL = "models/gemini-2.5-flash-001"  # Updated to use Gemini 2.5
# CACHE_TTL = "3600s"  # Cache time-to-live (e.g. 1 hour).

# Create a Gemini client using the Python SDK.
client = genai.Client(api_key="AIzaSyCz6mAU4mhZUqdtn1CWJXNvs4fdqcKx-jI")

def build_full_context(project_root):
    """
    Build a full project prompt that includes:
      - The full project structure and code (with truncation rules)
      - Admin samples
      - Detailed conversion instructions.
    """
    project_root_abs = os.path.abspath(project_root)
    # --- Build project prompt ---
    prompt_lines = ["Project structure and code:"]
    non_truncate_paths = [
        os.path.join(project_root_abs, "package.json"),
        os.path.join(project_root_abs, "src", "utils", "metadata.json"),
        os.path.join(project_root_abs, "routes.tsx"),
        os.path.join(project_root_abs, "lazyload.tsx")
    ]
    non_truncate_dirs = [
        os.path.join(project_root_abs, "src", "components"),
        os.path.join(project_root_abs, "context")
    ]
    ignore_files = [
        os.path.join(project_root_abs, "src", "utils", "TreeSDK.tsx"),
        os.path.join(project_root_abs, "src", "utils", "MkdSDK.tsx"),
        os.path.join(project_root_abs, "src", "utils", "EcomSDK.tsx")
    ]
    max_chars = 1500

    for dirpath, _, filenames in os.walk(project_root_abs):
        rel_dir = os.path.relpath(dirpath, project_root_abs)
        prompt_lines.append(f"\nDirectory: {rel_dir}")
        for filename in filenames:
            file_path = os.path.join(dirpath, filename)
            file_abs = os.path.abspath(file_path)
            if file_abs in ignore_files:
                continue
            rel_file_path = os.path.relpath(file_path, project_root_abs)
            prompt_lines.append(f"\nFile: {rel_file_path}")
            try:
                with open(file_path, "r", encoding="utf-8") as f:
                    content = f.read()
                    non_truncate = (file_abs in non_truncate_paths) or any(file_abs.startswith(nd) for nd in non_truncate_dirs)
                    if not non_truncate and len(content) > max_chars:
                        content = content[:max_chars] + "\n...[truncated]..."
                    prompt_lines.append("Content:")
                    prompt_lines.append(content)
            except Exception as e:
                prompt_lines.append(f"Error reading file: {e}")
    project_prompt = "\n".join(prompt_lines)

    # --- Build admin sample section ---
    admin_samples = {}
    admin_dirs = {
        "Admin/Add": os.path.join(project_root_abs, "pages", "Admin", "Add"),
        "Admin/Edit": os.path.join(project_root_abs, "pages", "Admin", "Edit"),
        "Admin/List": os.path.join(project_root_abs, "pages", "Admin", "List"),
        "Admin/View": os.path.join(project_root_abs, "pages", "Admin", "View")
    }
    admin_sample_lines = ["\nAdmin Sample Files:"]
    for category, dir_path in admin_dirs.items():
        if os.path.isdir(dir_path):
            found = False
            for fname in os.listdir(dir_path):
                file_full_path = os.path.join(dir_path, fname)
                if os.path.isfile(file_full_path) and fname.endswith(".tsx"):
                    try:
                        with open(file_full_path, "r", encoding="utf-8") as f:
                            admin_samples[category] = {
                                "filename": os.path.relpath(file_full_path, project_root_abs),
                                "content": f.read()
                            }
                            found = True
                            break
                    except Exception as e:
                        admin_samples[category] = {
                            "filename": os.path.relpath(file_full_path, project_root_abs),
                            "content": f"Error reading file: {e}"
                        }
                        found = True
                        break
            if not found:
                admin_samples[category] = {"filename": f"No TSX file found in {dir_path}", "content": ""}
        else:
            admin_samples[category] = {"filename": f"Directory {dir_path} not found", "content": ""}
    for category, sample in admin_samples.items():
        admin_sample_lines.append(f"\nCategory: {category}")
        admin_sample_lines.append(f"File: {sample.get('filename', 'N/A')}")
        admin_sample_lines.append("Content:")
        admin_sample_lines.append(sample.get("content", ""))
    admin_section = "\n".join(admin_sample_lines)

    # --- Detailed conversion instructions ---
    conversion_instructions = (
        "You are a senior software engineer tasked with converting React code snippets to match our project's structure and component usage. "
        "Rules:\n"
        "1. IMPORT HANDLING IS CRITICAL - Analyze and include all necessary imports at the top of the file.\n"
        "2. Order imports logically: React first, then libraries, then relative imports.\n"
        "3. CRITICAL: ONLY import components that actually exist in the project. DO NOT create imports for components that don't exist in the project structure.\n"
        "4. Make sure ALL imports end with semicolons.\n"
        "5. CRITICAL: YOU MUST REPLACE ALL IMAGE URLs with 'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcQeJQeJyzgAzTEVqXiGe90RGBFhfp_4RcJJMQ&s' - NO EXCEPTIONS!\n"
        """5.5. CRITICAL: YOU MUST REPLACE ALL SVGS with '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M12 12M12 12L12 12" stroke="black" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/><path d="M12 12M12 12L12 12" stroke="black" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/></svg>' - NO EXCEPTIONS!\n"""
        "6. Update imports to include necessary modules (Context/Global, lazyload components, skeleton loaders).\n"
        "6.5. CRITICAL: VERIFY that ALL imported components actually exist within the project files. DO NOT add imports for components that don't exist.\n" 
        "7. Ensure the exported component name matches the file name and follows naming conventions.\n"
        "8. Format the code properly with correct indentation and line breaks.\n"
        "9. Preserve JSX structure and component hierarchy.\n"
        "10. Return a JSON object with keys: updatedCode, repoPath, updatedFiles, notes.\n\n"
        "Below is the full project context for reference:\n\n"
    )
    full_context = conversion_instructions + project_prompt + "\n" + admin_section
    return full_context

# Caching functions commented out since Gemini 2.5 doesn't support caching
"""
def cache_context(full_context):
    # Create a cache with the given full_context. Returns the cache name.
    cache_config = types.CreateCachedContentConfig(
        display_name="project_context_cache",
        system_instruction=full_context,
        ttl=CACHE_TTL,
    )
    cache = client.caches.create(model=MODEL, config=cache_config)
    print("Context cached with name:", cache.name)
    return cache.name
"""

@handle_rate_limits(max_retries=3, initial_backoff=60)
def call_conversion_with_context(file_content, full_context):
    """
    Call the Gemini API for conversion using the full context directly (no caching).
    Uses the rate limit handler decorator to manage API rate limiting.
    Returns the response.
    """
    prompt = full_context + "\n\n" + (
        "Convert the following React code snippet to match our project style.\n\n"
        "CRITICAL REQUIREMENTS:\n"
        "1. Maintain and properly format ALL necessary imports\n"
        "2. ONLY import components that actually exist in the project\n"
        "3. Component name must match the filename\n"
        "4. Provide clean, well-formatted code without escaped characters\n"
        "5. REPLACE ALL image URLs with 'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcQeJQeJyzgAzTEVqXiGe90RGBFhfp_4RcJJMQ&s'\n"
        "6. REPLACE ALL SVGs with the placeholder SVG in the instructions\n"
        "7. Preserve the core functionality while adapting to our project style\n\n"
        + file_content
    )
    
    response = client.models.generate_content(
        model=MODEL,
        contents=prompt
    )
    return response

# Removing cache-related parameters and modifying to use full context directly
def process_file(file_path, full_context, input_dir, output_base):
    """
    Process a single file using the full context. Saves conversion as .tsx if parsed,
    or full response as .json if parsing fails.
    """
    relative_path = os.path.relpath(file_path, input_dir)
    print(f"Processing file: {relative_path}")
    try:
        with open(file_path, "r", encoding="utf-8") as f:
            file_content = f.read()
    except Exception as e:
        print(f"Error reading {file_path}: {e}")
        return

    # Extract file name for better prompting
    base_name, _ = os.path.splitext(os.path.basename(file_path))

    # Enhanced prompt with explicit instructions for proper imports and formatting
    enhanced_prompt = (
        f"Convert the following React code snippet into a properly formatted TSX component named '{base_name}'.\n\n"
        "CRITICAL REQUIREMENTS:\n"
        "1. Include ALL necessary imports at the top of the file\n"
        "2. Ensure React is properly imported\n"
        "3. ONLY import components that actually exist in the project\n"
        "4. Make sure all imports end with semicolons\n"
        "5. Order imports logically: React first, then libraries, then relative imports\n"
        "6. REPLACE ALL image URLs with 'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcQeJQeJyzgAzTEVqXiGe90RGBFhfp_4RcJJMQ&s'\n"
        "7. Format the code properly with correct indentation\n\n"
        "Original code:\n\n"
        f"{file_content}"
    )

    # Try with full context first
    try_reduced_context = False
    
    try:
        try:
            response = call_conversion_with_context(enhanced_prompt, full_context)
            if response is None:
                print("No response from Gemini API.")
                return
        except ResourceExhausted as e:
            # If we hit resource exhausted, it might be due to context size
            if "input_token_count" in str(e):
                print("Input tokens exceeded. Trying with reduced context...")
                try_reduced_context = True
                raise e
            else:
                raise
    except Exception as e:
        if try_reduced_context:
            # Reduce context by keeping only essentials
            reduced_context = (
                "You are a senior software engineer tasked with converting React code snippets to match a project's structure.\n"
                "REQUIREMENTS:\n"
                "1. CRITICAL: ONLY import components that actually exist in the project\n"
                "2. REPLACE ALL image URLs with 'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcQeJQeJyzgAzTEVqXiGe90RGBFhfp_4RcJJMQ&s'\n"
                "3. REPLACE ALL SVGs with a placeholder SVG\n"
                "4. Make component name match the filename\n"
                "5. Return properly formatted TSX code\n"
            )
            try:
                print("Attempting with reduced context...")
                response = call_conversion_with_context(enhanced_prompt, reduced_context)
                if response is None:
                    print("No response from Gemini API with reduced context.")
                    return
            except Exception as e2:
                print(f"Failed to get response from Gemini API after retries: {e2}")
                return
        else:
            print(f"Failed to get response from Gemini API after retries: {e}")
            return

    # Use the response.text attribute provided by the Gemini SDK.
    candidate_output = response.text
    output_file_dir = os.path.join(output_base, os.path.dirname(relative_path))
    os.makedirs(output_file_dir, exist_ok=True)

    try:
        # First try parsing as a structured response
        conversion_result = parse_candidate_output(candidate_output)
        updated_code = conversion_result.get("updatedCode", "")

        # Check if parsing failed but we have a TSX-like response - try direct extraction
        if not updated_code and '<' in candidate_output and ('import ' in candidate_output or 'export ' in candidate_output):
            print(f"Initial parsing failed but the response looks like TSX code. Trying direct extraction...")
            # Attempt direct code extraction
            code_lines = []
            in_code_block = False

            for line in candidate_output.splitlines():
                if line.strip().startswith('```') and 'tsx' in line.lower():
                    in_code_block = True
                    continue
                elif line.strip() == '```' and in_code_block:
                    in_code_block = False
                    continue
                elif in_code_block:
                    code_lines.append(line)
                # Also look for apparent code outside blocks
                elif line.strip().startswith('import ') or line.strip().startswith('export '):
                    code_lines.append(line)

            if code_lines:
                updated_code = '\n'.join(code_lines)
                print("Successfully extracted code directly from the response.")

        # Apply the enhanced formatting fix before saving
        if updated_code:
            updated_code = _fix_code_formatting(updated_code)

            # Perform a second pass analysis to ensure the content has expected imports
            has_react_import = 'import React' in updated_code
            has_jsx_import = "import { " in updated_code and "} from 'react'" in updated_code

            if not has_react_import and not has_jsx_import:
                print(f"Warning: No React imports found in processed code for {base_name}. Adding default import.")
                updated_code = "import React from 'react';\n" + updated_code

            # Verify component naming matches file name
            component_name_match = re.search(r'export\s+(?:const|function|default|class)\s+(\w+)', updated_code)
            if component_name_match and component_name_match.group(1) != base_name:
                print(f"Warning: Component name {component_name_match.group(1)} doesn't match file name {base_name}. Renaming component.")
                updated_code = re.sub(
                    r'export\s+(const|function|default|class)\s+\w+',
                    f'export \\1 {base_name}',
                    updated_code
                )

            out_path = os.path.join(output_file_dir, base_name + ".tsx")
            with open(out_path, "w", encoding="utf-8") as out_file:
                out_file.write(updated_code)
            print(f"Saved converted code to {out_path}")
        else:
            # Try one last approach - just extract any code-like blocks
            import_lines = []
            code_blocks = []

            for line in candidate_output.splitlines():
                if line.strip().startswith('import '):
                    import_lines.append(line)
                elif line.strip().startswith('export ') or line.strip().startswith('function ') or line.strip().startswith('const '):
                    code_blocks.append(line)

            if import_lines or code_blocks:
                combined_code = '\n'.join(import_lines + [''] + code_blocks)
                out_path = os.path.join(output_file_dir, base_name + ".tsx")
                with open(out_path, "w", encoding="utf-8") as out_file:
                    out_file.write(_fix_code_formatting(combined_code))
                print(f"Saved partial code extraction to {out_path}")
            else:
                raise ValueError("updatedCode key missing or empty and direct extraction failed")
    except Exception as e:
        out_path = os.path.join(output_file_dir, base_name + ".json")
        with open(out_path, "w", encoding="utf-8") as out_file:
            # Save the entire raw response for debugging
            json_safe_response = {"response_text": candidate_output}
            json.dump(json_safe_response, out_file, indent=2)
        print(f"Parsing failed ({e}). Saved full Gemini response to {out_path}")


def process_directory(input_dir, full_context, output_dir):
    """
    Recursively process all .tsx files in input_dir using process_file.
    """
    for root, _, files in os.walk(input_dir):
        for fname in files:
            if not fname.endswith(".tsx"):
                continue
            file_path = os.path.join(root, fname)
            process_file(file_path, full_context, input_dir, output_dir)

def _fix_code_formatting(code_text):
    """
    Fix common formatting issues in the code, especially escaped newlines and other characters.
    """
    if not code_text:
        return code_text

    # First, detect if this is a code with escaped newlines
    if "\\n" in code_text:
        try:
            # Try to interpret the escaped string
            # import re

            # Replace escaped newlines with actual newlines
            code_text = code_text.replace("\\n", "\n")

            # Replace other common escaped characters
            replacements = {
                "\\\"": "\"",
                "\\\'": "'",
                "\\\\": "\\",
                "\\t": "\t",
                "\\r": ""  # Remove carriage returns
            }

            for escaped, char in replacements.items():
                code_text = code_text.replace(escaped, char)

            # Handle unicode escapes like \u00A0
            code_text = re.sub(r'\\u([0-9a-fA-F]{4})',
                              lambda m: chr(int(m.group(1), 16)),
                              code_text)
        except Exception as e:
            print(f"Error fixing code formatting: {e}")

    # Fix common import issues:

    # 1. Ensure proper import spacing
    code_text = re.sub(r'import\s*{', r'import {', code_text)
    code_text = re.sub(r'}\s*from', r'} from', code_text)

    # 2. Fix missing semicolons after imports
    code_text = re.sub(r'(import .+? from .+?["\'])(?!\s*;)', r'\1;', code_text)

    # 3. Make sure imports are at the top - collect and reorder if needed
    import_lines = []
    other_lines = []
    in_comment_block = False

    for line in code_text.split('\n'):
        # Skip empty lines at the beginning
        if not line.strip():
            continue

        # Handle comment blocks
        if line.strip().startswith('/*'):
            in_comment_block = True
            other_lines.append(line)
        elif in_comment_block and '*/' in line:
            in_comment_block = False
            other_lines.append(line)
        # Extract import statements
        elif not in_comment_block and (line.strip().startswith('import ') or
                                       (line.strip().startswith('// import') and '// import' in line)):
            # Clean up commented imports but keep the comment marker
            if line.strip().startswith('// import'):
                import_lines.append(line)
            else:
                # Ensure each import ends with semicolon
                if not line.strip().endswith(';'):
                    line = line.rstrip() + ';'
                import_lines.append(line)
        else:
            other_lines.append(line)

    # Reconstruct the code with imports at the top
    if import_lines:
        # Group and sort imports logically
        react_imports = []
        custom_imports = []
        relative_imports = []

        for imp in import_lines:
            if 'react' in imp.lower() or 'react-dom' in imp.lower():
                react_imports.append(imp)
            elif './' in imp or '../' in imp:
                relative_imports.append(imp)
            else:
                custom_imports.append(imp)

        # Join with the proper order: React first, then libraries, then relative imports
        ordered_imports = react_imports + custom_imports + relative_imports
        code_text = '\n'.join(ordered_imports) + '\n\n' + '\n'.join(other_lines)

    return code_text

def parse_candidate_output(text):
    """
    Remove markdown code block markers and attempt to parse candidate output as JSON.
    Much more robust handling of various response formats.
    """
    if not text or not text.strip():
        return {"updatedCode": "", "notes": "Empty response from API"}

    text = text.strip()

    # Remove markdown code blocks if present
    if text.startswith("```"):
        # Remove the first line (which might be ```json or just ```)
        lines = text.splitlines()
        start_idx = 1
        if len(lines) > 1 and lines[0].startswith("```"):
            lines = lines[start_idx:]

        # Remove the last line if it's just ```
        if lines and lines[-1].strip() == "```":
            lines = lines[:-1]

        text = "\n".join(lines).strip()

    # Try to extract JSON from text that might contain other content
    try:
        # First try direct JSON parsing
        return json.loads(text)
    except json.JSONDecodeError:
        # Try to find JSON-like structure in the text
        try:
            # Look for a pattern that might be JSON - between curly braces
            # import re
            # More aggressive JSON extraction - try to find the largest JSON object
            json_pattern = re.search(r'(\{(?:[^{}]|(?:\{(?:[^{}]|(?:\{[^{}]*\}))*\}))*\})', text, re.DOTALL)
            if json_pattern:
                json_text = json_pattern.group(1)
                # Fix common JSON formatting issues
                json_text = json_text.replace('\n', '\\n').replace('\r', '\\r')
                json_text = re.sub(r'(?<!\\)"(.*?)(?<!\\)"(\s*:\s*)"', r'"\1"\2', json_text)
                return json.loads(json_text)
        except Exception as json_ex:
            print(f"JSON extraction attempt failed: {json_ex}")
            pass

        # If we can't extract JSON, look for common fields we might extract manually
        try:
            # Try to extract updatedCode between quotes after the updatedCode key
            code_pattern = re.search(r'"updatedCode"\s*:\s*"((?:\\.|[^"\\])*)"', text, re.DOTALL)
            if code_pattern:
                updated_code = code_pattern.group(1)
                # Unescape any escaped quotes and other escape sequences
                updated_code = json.loads(f'"{updated_code}"')
                return {"updatedCode": updated_code, "notes": "Manually extracted from double quotes"}

            # Try with single quotes or backticks too
            code_pattern = re.search(r'"updatedCode"\s*:\s*[\'`]((?:\\.|[^\'`\\])*)[\'\`]', text, re.DOTALL)
            if code_pattern:
                updated_code = code_pattern.group(1)
                return {"updatedCode": updated_code, "notes": "Manually extracted from alternative quotes"}

            # Try multiline string extraction
            code_pattern = re.search(r'"updatedCode"\s*:\s*`([^`]*)`', text, re.DOTALL)
            if code_pattern:
                updated_code = code_pattern.group(1)
                return {"updatedCode": updated_code, "notes": "Extracted from backticks"}

        except Exception as ex:
            print(f"Manual extraction attempt failed: {ex}")
            pass

    # When all else fails, try to find any code block that might be the converted code
    try:
        # Check for TSX, React, or JavaScript code blocks
        code_block_patterns = [
            r'```jsx\n(.*?)```',
            r'```tsx\n(.*?)```',
            r'```react\n(.*?)```',
            r'```javascript\n(.*?)```',
            r'```js\n(.*?)```'
        ]

        for pattern in code_block_patterns:
            code_blocks = re.findall(pattern, text, re.DOTALL)
            if code_blocks:
                return {"updatedCode": code_blocks[0], "notes": f"Extracted from {pattern} code block"}
    except Exception as ex:
        print(f"Code block extraction failed: {ex}")
        pass

    # Last resort - just check if the whole response looks like TSX code
    if '<' in text and '>' in text and ('import' in text or 'export' in text or 'function' in text or 'const' in text):
        # Clean up the text by removing any markdown or explanation surrounding the code
        # Find the first import statement as the likely start of the code
        import_match = re.search(r'(import\s+.*?from\s+.*?[\'"];)', text, re.DOTALL)
        if import_match:
            start_idx = text.find(import_match.group(1))
            if start_idx > 0:
                text = text[start_idx:]

        # Find export statement to help determine the code boundaries
        export_match = re.search(r'(export\s+(?:default\s+)?(?:function|class|const)\s+\w+)', text)
        if export_match and export_match.start() > 100:  # If export is far from the beginning, there might be explanations
            text = text[export_match.start():]

        # Add formatting fix for escaped characters in the code
        text = _fix_code_formatting(text)

        return {"updatedCode": text, "notes": "Raw response appears to be TSX (cleaned)"}

    # If nothing works, return error
    print("All parsing attempts failed. Raw response does not appear to be valid JSON or TSX.")
    # Return an empty object but also save the original text for debugging
    return {"updatedCode": "", "notes": "Failed to parse response", "rawText": text[:500] + "..." if len(text) > 500 else text}


def test_parse_output(response_file):
    """
    Utility function to test parsing a response file.
    Usage: python agent.py --test /path/to/response.json
    """
    try:
        with open(response_file, "r", encoding="utf-8") as f:
            content = f.read()
        print(f"Testing parsing of {response_file}...")
        parsed = parse_candidate_output(content)
        if "updatedCode" in parsed and parsed["updatedCode"]:
            print("✅ Successfully parsed! Found updatedCode.")
            code_preview = parsed["updatedCode"][:200] + "..." if len(parsed["updatedCode"]) > 200 else parsed["updatedCode"]
            print(f"Code preview: {code_preview}")
            print(f"Notes: {parsed.get('notes', 'None')}")
            return True
        else:
            print("❌ Failed to extract updatedCode!")
            print(f"Parsed result: {parsed}")
            return False
    except Exception as e:
        print(f"❌ Error during parsing test: {e}")
        return False

def main():
    # Test mode for debugging parsing issues
    import sys
    if len(sys.argv) > 1 and sys.argv[1] == "--test" and len(sys.argv) > 2:
        test_parse_output(sys.argv[2])
        return

    # Set your project root.
    project_root = "/content/react_starter_template"  # Update as needed.
    full_context = build_full_context(project_root)
    
    # Cache disabled for Gemini 2.5
    # cache_name = cache_context(full_context)

    with open("final_prompt.txt", "w", encoding="utf-8") as f:
        f.write(full_context)
    print("Full context saved to final_prompt.txt")

    mode = input("Process a single file (f) or an entire directory (d)? (f/d): ").strip().lower()
    output_dir = os.path.join(os.getcwd(), "generated")
    os.makedirs(output_dir, exist_ok=True)

    if mode == "f":
        snippet_path = input("Enter the full path to the React code snippet file: ").strip()
        if not os.path.isfile(snippet_path):
            print("File not found. Exiting.")
            return
        process_file(snippet_path, full_context, project_root, output_dir)
    elif mode == "d":
        input_dir = input("Enter the full path to the directory to process: ").strip()
        if not os.path.isdir(input_dir):
            print("Directory not found. Exiting.")
            return
        process_directory(input_dir, full_context, output_dir)
    else:
        print("Invalid choice. Exiting.")

if __name__ == "__main__":
    main()
