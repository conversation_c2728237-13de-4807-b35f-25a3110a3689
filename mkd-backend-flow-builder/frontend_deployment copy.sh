#! /usr/bin/sh

project=$1; # this is <project_name>
branch=$2; # this is the branch name = master
z="${project}.manaknightdigital.com";
r="$1_frontend";

echo `pwd`;

cd /home/<USER>/$r;
echo -e "ln8kmyhfezQ4kjyngk2cgA\n" | sudo -S git checkout $branch;


echo -e "ln8kmyhfezQ4kjyngk2cgA\n" | sudo -S mkdir -p /var/www/$z/html;
echo -e "ln8kmyhfezQ4kjyngk2cgA\n" | sudo -S chown -R $USER:$USER /var/www/$z/html;
echo -e "ln8kmyhfezQ4kjyngk2cgA\n" | sudo -S chmod -R 755 /var/www/$z;

echo "copy to domain";
echo -e "ln8kmyhfezQ4kjyngk2cgA\n" | sudo -S cp -r /home/<USER>/$r/dist/* /var/www/$z;
echo -e "ln8kmyhfezQ4kjyngk2cgA\n" | sudo -S mv /var/www/$z/index.html /var/www/$z/index.php;

echo "set domain";

server="server { \n
       \t listen 80;\n
        \t server_name $z; \n
        \t root /var/www/$z; \n
              \t  index index.php index.html index.htm; \n

location / { \n
 \t try_files \$uri \$uri/ /index.php\$is_args\$args; \n
} \n
location ~ \.php$ { \n
       \t include fastcgi_params; \n
       \t include snippets/fastcgi-php.conf; \n
 \t fastcgi_pass unix:/run/php/php7.4-fpm.sock; \n
}\n
}";

echo $server | sudo -S tee /etc/nginx/sites-available/$z;


echo -e "ln8kmyhfezQ4kjyngk2cgA\n" | sudo -S ln -s /etc/nginx/sites-available/$z /etc/nginx/sites-enabled/; 
#sudo certbot --nginx -d $z;
echo -e "ln8kmyhfezQ4kjyngk2cgA\n" | sudo -S certbot run -n --nginx --agree-tos -d $z  -m  <EMAIL>  --redirect
echo -e "ln8kmyhfezQ4kjyngk2cgA\n" | sudo -S service nginx restart;

response = "`wget -qO- https://mkdlabs.com/v5/api/deployments/finish-deployment/$project?x-project=bWFuYWtuaWdodDo1ZmNoeG41bThoYm82amN4aXEzeGRkb2ZvZG9hY3NreWUx`";
