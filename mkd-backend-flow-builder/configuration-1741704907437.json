{"settings": {"globalKey": "key_1741704088171_vgoax8qe3", "databaseType": "mysql", "authType": "session", "timezone": "UTC", "dbHost": "localhost", "dbPort": "3306", "dbUser": "root", "dbPassword": "root", "dbName": "database_2025-03-11", "id": "project_1741704088171_em6h0kfcc", "isPWA": false, "isMultiTenant": false, "model_namespace": "adds", "payment_option": "subscription"}, "models": [{"id": "model_1741704108311_sq50zrm80", "name": "job", "fields": [{"name": "id", "type": "primary key", "defaultValue": "", "validation": ""}, {"name": "task", "type": "string", "defaultValue": "", "validation": "required"}, {"name": "arguments", "type": "json", "defaultValue": "", "validation": ""}, {"name": "time_interval", "type": "string", "defaultValue": "once", "validation": ""}, {"name": "retries", "type": "integer", "defaultValue": "1", "validation": ""}, {"name": "status", "type": "mapping", "mapping": "0:Pending,1:Failed,2:Processing,3:Completed", "defaultValue": "0", "validation": ""}, {"name": "created_at", "type": "timestamp", "defaultValue": "CURRENT_TIMESTAMP", "validation": "date"}, {"name": "updated_at", "type": "timestamp", "defaultValue": "CURRENT_TIMESTAMP", "validation": "date"}]}, {"id": "model_1741704108311_ug6jii0w2", "name": "cms", "fields": [{"name": "id", "type": "primary key", "defaultValue": "", "validation": ""}, {"name": "label", "type": "string", "defaultValue": "", "validation": "required"}, {"name": "type", "type": "mapping", "mapping": "0:Text,1:Number,2:Image,3:Raw", "defaultValue": "0", "validation": "required,enum:0,1,2,3"}, {"name": "value", "type": "long text", "defaultValue": "", "validation": "required"}]}, {"id": "model_1741704108311_x7f2ddl3u", "name": "uploads", "fields": [{"name": "id", "type": "primary key", "defaultValue": "", "validation": ""}, {"name": "url", "type": "string", "defaultValue": "", "validation": "required"}, {"name": "caption", "type": "string", "defaultValue": "", "validation": ""}, {"name": "user_id", "type": "foreign key", "defaultValue": "", "validation": ""}, {"name": "width", "type": "integer", "defaultValue": "", "validation": ""}, {"name": "height", "type": "integer", "defaultValue": "", "validation": ""}, {"name": "type", "type": "mapping", "mapping": "0:Image,1:s3,2:Video,3:base64", "defaultValue": "0", "validation": "required,enum:0,1,2,3"}, {"name": "created_at", "type": "timestamp", "defaultValue": "CURRENT_TIMESTAMP", "validation": "date"}, {"name": "updated_at", "type": "timestamp", "defaultValue": "CURRENT_TIMESTAMP", "validation": "date"}]}, {"id": "model_1741704108311_c3d39c7uw", "name": "preference", "fields": [{"name": "id", "type": "primary key", "defaultValue": "", "validation": ""}, {"name": "first_name", "type": "string", "defaultValue": "", "validation": ""}, {"name": "last_name", "type": "string", "defaultValue": "", "validation": ""}, {"name": "phone", "type": "string", "defaultValue": "", "validation": ""}, {"name": "photo", "type": "string", "defaultValue": "", "validation": ""}, {"name": "user_id", "type": "integer", "defaultValue": "", "validation": "required"}]}, {"id": "model_1741704108311_225xjvipq", "name": "user", "fields": [{"name": "id", "type": "primary key", "defaultValue": "", "validation": ""}, {"name": "email", "type": "string", "defaultValue": "", "validation": "required,email"}, {"name": "password", "type": "password", "defaultValue": "", "validation": "required"}, {"name": "login_type", "type": "mapping", "mapping": "0:<PERSON>,1:Google,2:Microsoft,3:Apple,4:Twitter,5:Facebook", "defaultValue": "0", "validation": "required,enum:0,1,2,3,4,5"}, {"name": "role_id", "type": "string", "defaultValue": "", "validation": ""}, {"name": "data", "type": "json", "defaultValue": "", "validation": ""}, {"name": "status", "type": "mapping", "mapping": "0:Active,1:Inactive,2:Suspend", "defaultValue": "0", "validation": "required,enum:0,1,2"}, {"name": "verify", "type": "boolean", "defaultValue": "0", "validation": "required"}, {"name": "two_factor_authentication", "type": "boolean", "defaultValue": "0", "validation": ""}, {"name": "company_id", "type": "integer", "defaultValue": "0", "validation": ""}, {"name": "stripe_uid", "type": "string", "defaultValue": "", "validation": ""}, {"name": "created_at", "type": "timestamp", "defaultValue": "CURRENT_TIMESTAMP", "validation": "date"}]}, {"id": "model_1741704108311_iyorh6ktq", "name": "tokens", "fields": [{"name": "id", "type": "primary key", "defaultValue": "", "validation": ""}, {"name": "user_id", "type": "foreign key", "defaultValue": "", "validation": "required"}, {"name": "token", "type": "string", "defaultValue": "", "validation": "required"}, {"name": "code", "type": "string", "defaultValue": "", "validation": "required"}, {"name": "type", "type": "mapping", "mapping": "0:Access,1:<PERSON><PERSON><PERSON>,2:<PERSON><PERSON>,3:<PERSON><PERSON><PERSON>,4:<PERSON>", "defaultValue": "0", "validation": "required,enum:0,1,2,3,4"}, {"name": "data", "type": "json", "defaultValue": "", "validation": ""}, {"name": "status", "type": "mapping", "mapping": "0:Inactive,1:Active", "defaultValue": "1", "validation": "required,enum:0,1"}, {"name": "created_at", "type": "timestamp", "defaultValue": "CURRENT_TIMESTAMP", "validation": "date"}, {"name": "updated_at", "type": "timestamp", "defaultValue": "CURRENT_TIMESTAMP", "validation": "date"}, {"name": "expired_at", "type": "timestamp", "defaultValue": "", "validation": "date"}]}, {"id": "model_1741704142008_x1nq2mndo", "name": "tokens (Copy)", "fields": [{"name": "id", "type": "primary key", "defaultValue": "", "validation": ""}, {"name": "user_id", "type": "foreign key", "defaultValue": "", "validation": "required"}, {"name": "token", "type": "string", "defaultValue": "", "validation": "required"}, {"name": "code", "type": "string", "defaultValue": "", "validation": "required"}, {"name": "type", "type": "mapping", "mapping": "0:Access,1:<PERSON><PERSON><PERSON>,2:<PERSON><PERSON>,3:<PERSON><PERSON><PERSON>,4:<PERSON>", "defaultValue": "0", "validation": "required,enum:0,1,2,3,4"}, {"name": "data", "type": "json", "defaultValue": "", "validation": ""}, {"name": "status", "type": "mapping", "mapping": "0:Inactive,1:Active", "defaultValue": "1", "validation": "required,enum:0,1"}, {"name": "created_at", "type": "timestamp", "defaultValue": "CURRENT_TIMESTAMP", "validation": "date"}, {"name": "updated_at", "type": "timestamp", "defaultValue": "CURRENT_TIMESTAMP", "validation": "date"}, {"name": "expired_at", "type": "timestamp", "defaultValue": "", "validation": "date"}]}, {"id": "model_1741704903621_4ek3umgzn", "name": "stripe_product", "fields": [{"name": "id", "type": "primary key", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "created_at", "type": "date", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "updated_at", "type": "datetime", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "name", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "product_id", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "stripe_id", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "object", "type": "long text", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "status", "type": "integer", "defaultValue": "", "validation": "", "validationOptions": {}}]}, {"id": "model_1741704903621_zhgdpka7s", "name": "stripe_price", "fields": [{"name": "id", "type": "primary key", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "created_at", "type": "date", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "updated_at", "type": "datetime", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "name", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "product_id", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "stripe_id", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "is_usage_metered", "type": "integer", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "usage_limit", "type": "integer", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "object", "type": "medium text", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "amount", "type": "float", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "trial_days", "type": "integer", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "type", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "status", "type": "integer", "defaultValue": "", "validation": "", "validationOptions": {}}]}, {"id": "model_1741704903621_un90hxzcp", "name": "stripe_subscription", "fields": [{"name": "id", "type": "primary key", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "stripe_id", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "price_id", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "user_id", "type": "integer", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "object", "type": "json", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "status", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "is_lifetime", "type": "boolean", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "created_at", "type": "date", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "updated_at", "type": "datetime", "defaultValue": "", "validation": "", "validationOptions": {}}]}, {"id": "model_1741704903621_irabt7y1p", "name": "stripe_checkout", "fields": [{"name": "id", "type": "primary key", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "user_id", "type": "integer", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "stripe_id", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "object", "type": "json", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "created_at", "type": "date", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "updated_at", "type": "datetime", "defaultValue": "", "validation": "", "validationOptions": {}}]}, {"id": "model_1741704903621_nbs7bvvok", "name": "stripe_webhook", "fields": [{"name": "id", "type": "primary key", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "stripe_id", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "idempotency_key", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "description", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "event_type", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "resource_type", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "object", "type": "json", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "is_handled", "type": "boolean", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "created_at", "type": "date", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "updated_at", "type": "datetime", "defaultValue": "", "validation": "", "validationOptions": {}}]}, {"id": "model_1741704903621_w5oo6ahum", "name": "stripe_setting", "fields": [{"name": "id", "type": "primary key", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "key", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "value", "type": "medium text", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "created_at", "type": "date", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "updated_at", "type": "datetime", "defaultValue": "", "validation": "", "validationOptions": {}}]}, {"id": "model_1741704903621_8xibuw4d4", "name": "stripe_order", "fields": [{"name": "id", "type": "primary key", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "user_id", "type": "integer", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "price_id", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "stripe_id", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "object", "type": "json", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "created_at", "type": "date", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "updated_at", "type": "datetime", "defaultValue": "", "validation": "", "validationOptions": {}}]}, {"id": "model_1741704903621_4c9x6qizr", "name": "stripe_invoice", "fields": [{"name": "id", "type": "primary key", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "user_id", "type": "integer", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "stripe_id", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "object", "type": "json", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "created_at", "type": "date", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "updated_at", "type": "datetime", "defaultValue": "", "validation": "", "validationOptions": {}}]}, {"id": "model_1741704903621_z4ofl3deh", "name": "stripe_refund", "fields": [{"name": "id", "type": "primary key", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "user_id", "type": "integer", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "stripe_id", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "object", "type": "json", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "charge_id", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "subscription_id", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "amount", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "currency", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "reason", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "status", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "created_at", "type": "date", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "updated_at", "type": "datetime", "defaultValue": "", "validation": "", "validationOptions": {}}]}, {"id": "model_1741704903621_ir0jrkgv2", "name": "stripe_dispute", "fields": [{"name": "id", "type": "primary key", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "user_id", "type": "integer", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "stripe_id", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "subscription_id", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "object", "type": "json", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "amount", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "reason", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "reason_description", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "status", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "created_at", "type": "date", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "updated_at", "type": "datetime", "defaultValue": "", "validation": "", "validationOptions": {}}]}], "routes": [{"id": "route_1741704216697_eq966d5ad", "name": "Get campaigns", "flowData": {"nodes": [{"id": "url_node_1741704580623", "type": "mock-api", "position": {"x": 45, "y": 45}, "data": {"label": "Mock API", "apiname": "Get campaigns", "path": "/v1/api/sec/member/sec/get-campaigns", "method": "GET", "description": "Todo: fetch users campaigns from db\nTodo: format them\n Todo: return them", "fields": [], "queryFields": [], "responseFields": [{"name": "error", "type": "boolean", "validation": ""}, {"name": "list", "type": "array", "value": {"type": "object", "fields": [{"name": "id", "type": "number"}, {"name": "campaign_name", "type": "string"}]}}], "authType": "none", "outputType": "json", "statusCode": 200}, "width": 180, "height": 56, "selected": true, "positionAbsolute": {"x": 45, "y": 45}, "dragging": false}], "edges": []}}], "roles": [{"id": "role_admin_1741704108311", "name": "Super Admin", "slug": "super_admin", "permissions": {"routes": [], "canCreateUsers": true, "canEditUsers": true, "canDeleteUsers": true, "canManageRoles": true, "canUpdateOtherUsers": true}}, {"id": "role_member_1741704108311", "name": "Member", "slug": "member", "permissions": {"routes": [], "canCreateUsers": false, "canEditUsers": false, "canDeleteUsers": false, "canManageRoles": false, "canLogin": true, "canRegister": true, "canForgot": false, "canReset": false, "canGoogleLogin": true, "canAppleLogin": false, "canMicrosoftLogin": false, "canMagicLinkLogin": false, "canTwitterLogin": false, "needs2FA": true, "canSetPermissions": false, "canPreference": true, "canVerifyEmail": false, "canUpload": false, "canStripe": false, "canStripeWebhook": false, "canRealTime": false, "canAI": false, "canUpdateEmail": true, "canUpdatePassword": false, "canUpdateOtherUsers": true, "treeql": {"enabled": false, "models": {}}, "companyScoped": false}}]}