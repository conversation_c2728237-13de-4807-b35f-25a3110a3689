{"settings": {"model_namespace": "sample_project", "projectId": "sample_project", "payment_option": "none"}, "roles": [{"id": "role_1", "name": "Admin", "slug": "admin", "permissions": {"canLogin": true, "canRegister": true, "routes": ["route_1", "route_2"], "treeql": {"enabled": true, "models": {"user": {"allowed": true, "operations": {"get": true, "getOne": true, "post": true, "put": true, "delete": true}}}}}}, {"id": "role_2", "name": "User", "slug": "user", "permissions": {"canLogin": true, "canRegister": true, "routes": ["route_2"], "treeql": {"enabled": true, "models": {"user": {"allowed": true, "operations": {"get": true, "getOne": true}}}}}}], "models": [{"name": "user", "fields": [{"name": "id", "type": "primary key"}, {"name": "name", "type": "string", "validation": ["required"]}, {"name": "email", "type": "string", "validation": ["required", "email"]}, {"name": "password", "type": "password", "validation": ["required"]}]}, {"name": "product", "fields": [{"name": "id", "type": "primary key"}, {"name": "name", "type": "string", "validation": ["required"]}, {"name": "price", "type": "double", "validation": ["required"]}, {"name": "description", "type": "text"}]}], "routes": [{"id": "route_1", "name": "Get All Products", "method": "GET", "description": "Retrieve all products", "flowData": {"nodes": [{"id": "node_1", "type": "mock-api", "data": {"apiname": "Get All Products", "path": "/api/products", "method": "GET", "description": "Retrieve all products in the system", "authType": "bearer", "statusCode": 200, "outputType": "json", "responseFields": [{"name": "id", "type": "integer"}, {"name": "name", "type": "string"}, {"name": "price", "type": "double"}, {"name": "description", "type": "string"}]}}], "edges": []}}, {"id": "route_2", "name": "Get Product By ID", "method": "GET", "description": "Retrieve a product by ID", "flowData": {"nodes": [{"id": "node_2", "type": "mock-api", "data": {"apiname": "Get Product By ID", "path": "/api/products/:id", "method": "GET", "description": "Retrieve a specific product by ID", "authType": "bearer", "statusCode": 200, "outputType": "json", "queryFields": [{"name": "id", "type": "integer", "validation": ["required"]}], "responseFields": [{"name": "id", "type": "integer"}, {"name": "name", "type": "string"}, {"name": "price", "type": "double"}, {"name": "description", "type": "string"}]}}], "edges": []}}, {"id": "route_3", "name": "Create Product", "method": "POST", "description": "Create a new product", "flowData": {"nodes": [{"id": "node_3", "type": "mock-api", "data": {"apiname": "Create Product", "path": "/api/products", "method": "POST", "description": "Create a new product in the system", "authType": "bearer", "statusCode": 201, "outputType": "json", "fields": [{"name": "name", "type": "string", "validation": ["required"]}, {"name": "price", "type": "double", "validation": ["required"]}, {"name": "description", "type": "string"}], "responseFields": [{"name": "id", "type": "integer"}, {"name": "name", "type": "string"}, {"name": "price", "type": "double"}, {"name": "description", "type": "string"}]}}], "edges": []}}]}