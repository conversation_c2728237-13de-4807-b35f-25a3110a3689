[05/11/2024, 12:24:55] Jay: https://docs.google.com/spreadsheets/d/1oXKqyXOmmMrQXuiZHig7yKzdO1yVPOuH9bs9ql0y_OY/edit?usp=sharing
[05/11/2024, 12:29:44] Jay: https://docs.google.com/spreadsheets/d/1U_kwkSncF0q0mOCq92hzRrZyzPbEijPP-hgushYC484/edit?usp=sharing
[05/11/2024, 14:26:11] Jay: Checkout 
work summary 

Courtmatchup
- Adjusted time slot generation service
- Modified selection of court and mapping of calendar slots 
- Generating data for next month for club, coach and court data
- Modified validation methods for the availability jsons and account jsons

Coinchord
- Modified expanded profile for users api to save first_time field and also created new api to fetch the profile with updated first_time status
Skillgames
- Added method for updating age based on the dob submitted


https://app.mytechpassport.com/public/video-view?id=1e307Eywgr62andzJN1UJKlIgA8HopNvQ&st=public ‎<This message was edited>
[05/11/2024, 14:27:36] Jay: check in
projects
- courtmatchup
‎[06/11/2024, 03:19:09] Jay: ‎video omitted
‎[06/11/2024, 10:43:01] Jay: ‎video omitted
‎[06/11/2024, 10:43:01] Jay: ‎video omitted
[06/11/2024, 15:58:34] Jay: /v3/api/custom/coinchord/user/payment-intent/create
amount
‎[06/11/2024, 16:10:29] Jay: ‎image omitted
[06/11/2024, 22:00:04] Jay: 2024-11-06 18:14:59 newsai_app  | 2024-11-06 17:14:59.866 | ERROR    | ainews:generate_news_summary:63 - Error processing request: TextEncodeInput must be Union[TextInputSequence, Tuple[InputSequence, InputSequence]]
2024-11-06 18:14:59 newsai_app  | INFO:     172.19.0.1:48592 - "POST /news/summary HTTP/1.1" 500 Internal Server Error
[07/11/2024, 13:59:23] Jay: Checkout
Work summary


Courtmatchup
- Created default forgot and reset password using sms code and number for all roles
- Updated time slot search query

Coinchord 
- Created new intent api for generating intent before tax payment 

voiceoutreach
- Modified book-appointment api on Arins server to store call details based on user to track it on admin.

https://app.mytechpassport.com/public/video-view?id=1p7ptNdj2mlCGot231Yni6Biwr0w9-m1A&st=public
[07/11/2024, 14:06:45] Jay: check in
projects
- voiceoutreach
- ⁠courtmatchup
- ⁠coinchord
[11/11/2024, 14:25:12] Jay: Check out 
Work summary

Voiceotuerach
- Modified deepgram logic to be able to track speaker diarization and only receive from speaker 0 transcript at all times in other to prevent the issues associate with the bi directional channel
- Modified telynx stream service for sending the converted u_law audio
- Webhook connection delay reduced by making sure the first message plays even before the rest of the services that are trying to connect and they keep connecting in the background 

Auth8
- Added notification logic to expert rest apis for expert assign logic
- Yet to modify service to loop



https://app.mytechpassport.com/public/video-view?id=11FR8pK_eodHMf4L56urGutuLAMl0pzXh&st=public
[11/11/2024, 14:25:31] Jay: check in
projects
- vocieoutreach
- ⁠auth8
- ⁠courtmatchup
[11/11/2024, 19:25:27] Jay: 1. List of users emails
2. Dollar amount for authentications

3. Community votes for
- no results
- passes ‎<This message was edited>
[11/11/2024, 20:35:52] Jay: Or I can explain here
[11/11/2024, 20:39:08] Jay: Basically
We have 
Expert vote and community vote

If an expert votes then the aithentication is what the expert chose
Pass fail or undetermined

Is the community votes 
Then it takes 4 passes to mark it as pass or 4 fails to mark it as fail and if not up to 4 then it is under "undetermined"

I'm now going to send you all community final decisions

Pass
Fail
Undetermined
[11/11/2024, 20:39:16] Jay: Its alot
[11/11/2024, 21:23:25] Jay: RingCentral’s AI offerings, centered around the RingSense platform, enhance business communications through automated insights, interaction analysis, and productivity tools. Key features include:

1. RingSense for Sales: Analyzes sales interactions to provide summaries, automated follow-ups, keyword tracking, and insights to improve sales performance.
2. AI-Enhanced Meetings: Offers summaries, action items, and highlights for easier meeting follow-ups.
3. Sentiment Analysis & Transcription: Converts voice and video interactions into actionable insights.
4. Developer API: Enables custom integrations and data access across voice, video, and messaging.


Conversation Intelligence: Transcribes and analyzes voice interactions for insights, ideal for customer service and sales.
RingSense for Sales: Specifically analyzes sales calls to provide summaries, identify key insights, and suggest follow-ups.
Sentiment Analysis: Analyzes the tone of conversations to understand customer emotions.
[12/11/2024, 14:10:32] Jay: Check out
Work summary

auth8
- Updated v2 datasets api to include “undecided” metrics from users community voted.
- Updated mapping being returned and allowing for final decision pass or fail to be returned along with community users vote for that item

Coinchord
- Modified payment status apis
- Added email to payments api being returned

decision tree
-  Created separate test service for vonage to verify logic 
- Added details to be able to test call quality

https://app.mytechpassport.com/public/video-view?id=1_0DDlpwu7RHCHXurIWDFcqT3_DwXOwDn&st=public
[12/11/2024, 14:23:03] Jay: check in
projects
- voiceoutreach
- ⁠auth8
[12/11/2024, 17:14:21] Jay: direct link
no image
[12/11/2024, 19:42:16] Jay: https://meet.google.com/bnh-eiki-bom
[12/11/2024, 20:31:03] Jay: caller id
sms 
voice
script
‎[13/11/2024, 09:51:21] Jay: ‎audio omitted
[13/11/2024, 14:26:51] Jay: Check out
Work summary

Voiceoutreach
- Set up new monitor service for idle calls to prevent twilio from debiting and switch call to “completed” status automatically 
- Updated call_data settings to distinguish calls being sent from which platform eg automateintel or voice subdomain and restrict accordingly 
- Created new script and making use of new voice to create more epressiveness

Coinchord
- Fixed update-payment status api for admin and user to switch to failed
- Switched from order by date to id for payments api

https://app.mytechpassport.com/public/video-view?id=1YOs_xiKdXTRBTODtqvOZqvMVpr6VL7yV&st=public
[13/11/2024, 14:27:11] Jay: check in
projects
- voiceoutreach 
- ⁠auth8
‎[13/11/2024, 20:59:58] Jay: ‎image omitted
[14/11/2024, 15:45:58] Jay: Checkout
Work summary

Voiceoutreach

- Added expressiveness and more tone changes
- Updated script and voice
- Working on new api with voice ids for marketing site demo call
- Updated voyage service and apis for response
- Created vonage application using default voiceoutreach application id for testing
- Updated voice ds stream service 

Courtmatchup
- Updated api for fetching reservations to search and generate reservations based on all the courts in the system


https://app.mytechpassport.com/public/video-view?id=1U1sXshL5ckdAwy_Ea4p89PaO37xbF7ii&st=public
[14/11/2024, 15:59:24] Jay: check in
projects
- voiceoutreach
- ⁠courtmatchup
[14/11/2024, 17:52:42] Jay: https://ui.idp.vonage.com/mailVerify/981414a9-2fd4-4d18-b37b-48e1d9ca007b?continue_to=https%3a%2f%2fiam.idp.vonage.com%2fself-service%2fverification%3fflow%3d2ad524a0-1ccf-4cda-86a3-9fd6ffe4ebc9%26token%3d6CgZqC4PEZ2m3Cbp1LyqpwINMeIh0oRh
[15/11/2024, 14:25:45] Jay: check in
projects
- voiceoutreach
- ⁠coinchord
‎[15/11/2024, 14:46:51] Jay: RE97a1a08f0b854dc2ae83dd79eccc4b13 (1).wav ‎document omitted
[15/11/2024, 15:15:02] Jay: After setting up a merchant account and adding a developer on aithorize.net

How do I get the creeds fit that developer email?
[15/11/2024, 16:54:33] Jay: Hubspot
Authorise.net card payment
Interac - bank transfers
Crypto pay - depending on outcome of meeting with client
[16/11/2024, 04:50:14] Jay: Checkout 
Work summary

Courtmatchup
- Updated user registration api to include the “join club” logic
- Modified logic for age group calculation

Coinchord
- Added search functionality to admin payments and user payments apis
- Modified status fields

Voiceoutreach
- Modified demo_call api for marketing page. Added new scripts for pitching automateintel
- Worked on login screen ui
- Added sms outbound campaign and inbound campaign 
- Added sms assistant test page similar to calls
- Working on sms chat logs

https://app.mytechpassport.com/public/video-view?id=1Ih8CnW9EC5rR2IIpY1XnBuCMB15KGKS2&st=public
[16/11/2024, 13:39:07] Jay: https://www.linkedin.com/posts/codingwithroby_want-to-be-a-great-software-engineer-pick-activity-7263521191519240192-7xYW?utm_source=screenshot_social_share&utm_medium=android_app&utm_campaign=whatsapp
‎[17/11/2024, 07:20:51] Jay: ‎image omitted
‎[19/11/2024, 11:08:16] Jay: ‎video omitted
‎[19/11/2024, 11:08:16] Jay: ‎video omitted
‎[19/11/2024, 11:38:41] Jay: 7e9f94cc-a5e9-11ef-832d-02420aef6420-1731960774.wav ‎document omitted
[19/11/2024, 14:35:06] Jay: Checkout

Work summary

Voiceoutreach
- Worked on telnyx service updates
- Modified sms logs page ui
- Worked on call ended status and updated from_display_number

Courtmatchup
- Made updates to club profile set up
- Modified staff email invite 

Coinchord
- Worked on crypto link payment logic for Eukapay
- Working on service for eukaypay
- Working on updated service for authorize.net payments

Auth8
- Modified get queue api to fetch posts using jwt instead of sessions


https://app.mytechpassport.com/public/video-view?id=1TohrawNYvE8fT03jARzd1fxeoxCMLE11&st=public
[19/11/2024, 14:35:25] Jay: check in
projects
- auth8
- ⁠coinchord
- ⁠courtmatchup
- ⁠voiceoutreach
[19/11/2024, 17:49:14] Jay: Voice
- voice cloning
- outbound campaigns
- inbound campaigns
-  voice mail handling
- call forwarding
- call summary insights eg meeting notes, transcriptions, call recordings etc
- meeting scheduling - google calendar, ghl, cal.com
- sms reminder before meeting

Sms followup
- inbound campaigns
- outbound campaigns
- sms chat logs
[19/11/2024, 19:07:36] Jay: 1. Home Screen Design:  (5 hours)
Basic Layout: Keep the current design in boxes.
Category Filter: Add a top section where users can select the language, country, and category groups.
Category Sorting: Enable categories to be ordered from the admin panel, with options for drag-and-drop to set the display order (e.g., greetings first).
Scenario Navigation: After selecting a category, display the associated scenarios. Each category should be separated by a line and title.
Steps to update:
Implement new changes changes
Update design, add collection view for categories
Update scenario collectionView design to add category sections 
Implement get category api, and display on category collection view 
Update scenario functionality and display scenarios by category wise 


2. Admin Panel: (2 days)
Category Sorting: Add a drag-and-drop functionality in the admin panel to sort categories.
Image Upload: Enable image upload for scenarios to display on the landing page.
Organize Lessons: Separate options in the admin panel for flashcards, lessons, and AI practice.
Image Management: Associate images with words and assign IDs to make fetching easier.
3. Landing Page for Scenarios: (3 hours)
Options Displayed: Display options - Flashcards, Lesson, and Practice with AI.
Navigation: Add a back button to return to the home screen.
New Options: Flashcards (with hint, translation, and sentence), lessons with various activity types, and practice with AI (as currently implemented).

Steps:
Create new screen along with background image and buttons
Add api to fetch image 
4. Flashcard Feature: (2 days)
Flashcard Content: Display a word or sentence with an image, flip to show translation, sentence, and translation of the sentence.
Hint Button: Provide hints in the user's native language.
Deck Options: Buttons to keep the card in the deck or remove it until the deck is complete.
Dictionary Saving: A download button to save the word to a dictionary.
Steps:
Create new screen for flashcard
Implement flashcard using third party lib
Create screen using storyboard components 
Integrate api to fetch and display flashcard content


5. Lesson Activities: (4 days)
Activity Types: Different lesson activities include:
Listening and selecting the word.
Selecting the correct translation.
Selecting the missing word in a sentence.
Choosing the word heard without images.
Conversation activities with multiple-choice responses.
Multiple Guesses: Users can make multiple guesses until they select the correct answer.
Prescripted Conversations: The app will feature a conversation flow with different responses to simulate language use.
Settings: Options to control voice and speed.

Steps:
Create new screens for different activities 
Need to handle everything manually
Create all screen using labels and button and handle selection manually 
Integrate api to fetch and activities content and results 


6. Activity Skipping: (1 hour )
Skip Options: Users can skip listening or speaking activities.
Alternate Display: If a user skips listening, the text is displayed instead; if skipping speaking, activities default to multiple choice.
7. Data Structure and Back-end Setup + Custom Apis for Lesson and Flashcards: (2 days)
Spreadsheet: Create an Excel sheet with lesson ID, activity ID, activity type, language localization, question, correct answer, answer choices, and word order.
Skippable Activities: Add a column to indicate skippable activities.
Custom Apis
8. Flashcard Animations: (2-4 hours)
Simple Animations: Add basic animations for flashcards, such as popping in and out or swiping to the back of the deck.
Steps:
Use third party lib for flashbard and animation
9. Additional Components: (2 hours)
Completion Actions: After finishing flashcards, display buttons for resetting the deck, going to lessons, or practicing with AI.
Navigation Icons: A home icon on the top for easy navigation back to the main screen
[20/11/2024, 14:21:21] Jay: Checkout
Work summary

Auth8
- Modified queue api for experts.
- Updated all mappings and output format for experts to match that from grqphql users format

Coinchord
- Created new api for returning invoices
- Modified dispute logic to include reasons

Voiceoutreach
- Added from_display_name dynamically to test call api
- Worked on call recording logic
- Made update to call summary page for transcription

Courtmatchup
- Modified staff auth api


https://app.mytechpassport.com/public/video-view?id=1TohrawNYvE8fT03jARzd1fxeoxCMLE11&st=public
[20/11/2024, 14:21:40] Jay: check in
projects
- coinchord
- ⁠voiceoutreach
- ⁠courtmatchup
[20/11/2024, 22:27:32] Jay: curl -X GET "https://graph.facebook.com/oauth/access_token?client_id=YOUR_CLIENT_ID&client_secret=YOUR_CLIENT_SECRET&grant_type=client_credentials"
[20/11/2024, 22:28:05] Jay: Meta 
Client Key : 1421765342544303
Secret id: 3ce252673497d17036c7556ed55d7a2e

Youtube
AIzaSyBlC2ArLJ6fvc1G6oxmfGx19gb6InSk_As
[21/11/2024, 14:14:39] Jay: Check in
projects

Coinchord
- Modified tracking_id to be a hexcode instead
- Added reference_id with TAX keywords
- Completed eukapay invoice generation link
- Modified hubspot ticketing apis to make use of tracking_id to allow it to be filterable
- Created payment api for authorize 

courtmatchup
- Allowing for password less user creation
- Modified club home ui settings to include title and descriptions
- Allowing for email sending

Pmj
- Modified custom public apis groupby queries

Voiceoutreach
- Added number filters
- Added logic for downloading call recording links after calls
- Modified queue status
- Renamed older apis and updated frontend sdk


https://app.mytechpassport.com/public/video-view?id=16ei4DvRMqw9qgu3CK1k4251tXwoiS4y9&st=public
[21/11/2024, 14:14:56] Jay: check in
projects
- vocieoutreach
- ⁠coinchord
‎[21/11/2024, 17:16:17] Jay: Auth8 Breakdown.zip ‎document omitted
‎[21/11/2024, 17:16:18] Jay: Auth8 2024 Data.pdf • ‎1 page ‎document omitted
‎[21/11/2024, 17:21:39] Jay: User Emails.csv ‎document omitted
‎[21/11/2024, 17:21:40] Jay: Auth8 Breakdown.zip ‎document omitted
‎[21/11/2024, 17:21:41] Jay: Auth8 2024 Data.pdf • ‎1 page ‎document omitted
[21/11/2024, 19:04:14] Jay: https://graph.facebook.com/oauth/access_token?client_id=YOUR_CLIENT_ID&client_secret=YOUR_CLIENT_SECRET&grant_type=client_credentials&scope=pages_read_engagement
[21/11/2024, 22:49:49] Jay: # Major Milestones in Data Communication and Computer Network Evolution

## 1830s - 1940s: Early Foundations
- **1837**: First electrical telegraph by Cooke and Wheatstone
- **1844**: Morse code and telegraph network deployment
- **1876**: Alexander Graham Bell invents the telephone
- **1927**: First commercial transatlantic telephone service

## 1950s - 1960s: Early Computer Networks
- **1957**: First computer modem developed by Bell Labs
- **1958**: First commercial modem released
- **1965**: First wide-area computer network (WAN) connecting two computers
- **1969**: ARPANET established - the precursor to modern internet
  - First node-to-node communication between UCLA and Stanford
  - Development of packet-switching technology
  - Implementation of Network Control Protocol (NCP)

## 1970s: Protocol Development
- **1970**: ALOHAnet - First wireless packet data network
- **1972**: 
  - First email program developed
  - Telnet protocol specification
- **1973**: Ethernet invented at Xerox PARC
- **1974**: TCP/IP design proposed
- **1976**: X.25 protocol approved
- **1979**: USENET established

## 1980s: Network Expansion
- **1981**: CSNET created for networking research
- **1982**: TCP/IP protocol standardized
- **1983**: 
  - DNS (Domain Name System) introduced
  - ARPANET transitions to TCP/IP
- **1985**: First registered domain name (symbolics.com)
- **1988**: First trans-Atlantic fiber optic cable
- **1989**: World Wide Web conceived by Tim Berners-Lee

## 1990s: Internet Age
- **1990**: First commercial Internet service provider (ISP)
- **1991**: 
  - World Wide Web publicly released
  - First web browser created
- **1995**: 
  - Amazon and eBay launched
  - IPv6 proposed
- **1998**: Google founded
- **1999**: WiFi (802.11b) standardized

## 2000s - Present: Modern Era
- **2000**: Broadband cable internet becomes widespread
- **2003**: First 3G networks deployed widely
- **2007**: iPhone launch revolutionizes mobile internet
- **2009**: 4G networks begin deployment
- **2012**: IPv6 publicly launched
- **2019**: First 5G networks deployed
- **2020s**: Development of 6G technology begins

## Key Technical Innovations Throughout History
1. **Transmission Technologies**
   - Circuit switching to packet switching
   - Copper to fiber optics
   - Wireless technologies (WiFi, cellular)

2. **Protocol Development**
   - TCP/IP suite
   - Routing protocols
   - Security protocols

3. **Network Architecture**
   - Client-server model
   - Peer-to-peer networking
   - Cloud computing infrastructure
   - Edge computing

4. **Security Advancements**
   - Encryption standards
   - Firewalls
   - VPN technology
   - Blockchain networks
[21/11/2024, 22:50:07] Jay: No 1
[21/11/2024, 22:51:37] Jay: # Unicode: A Comprehensive Guide

## 1. What is Unicode?
Unicode is a universal character encoding standard that provides a unique number for every character, regardless of platform, program, or language. It aims to be:
- Universal: Cover all the world's writing systems
- Efficient: Enable rapid text processing
- Unambiguous: Each character has a single, unique code point

## 2. Core Concepts

### Code Points
- Fundamental Unicode building blocks
- Written as U+[hexadecimal number]
- Range: U+0000 to U+10FFFF
- Total capacity: 1,114,112 code points

### Planes
1. **Basic Multilingual Plane (BMP)** - Plane 0: U+0000 to U+FFFF
   - Most commonly used characters
   - Latin, Cyrillic, Greek, Chinese, Japanese, Korean

2. **Supplementary Planes** - Planes 1-16
   - Mathematical symbols
   - Historic scripts
   - Emoji
   - Rare characters

### Encoding Forms
1. **UTF-8**
   - Variable-width encoding (1-4 bytes)
   - ASCII-compatible
   - Most common on the web
   - Space-efficient for Latin script

```
UTF-8 Bit Patterns:
1 byte:  0xxxxxxx                            (ASCII)
2 bytes: 110xxxxx 10xxxxxx
3 bytes: 1110xxxx 10xxxxxx 10xxxxxx
4 bytes: 11110xxx 10xxxxxx 10xxxxxx 10xxxxxx
```

2. **UTF-16**
   - Variable-width (2 or 4 bytes)
   - Used in Windows, Java
   - Surrogate pairs for characters outside BMP

3. **UTF-32**
   - Fixed-width (4 bytes)
   - Direct code point representation
   - Memory-intensive but simple processing

## 3. Special Characters and Concepts

### Combining Characters
- Diacritical marks
- Multiple characters forming single visual unit
- Example: e + ́  = é

### Control Characters
- Line breaks (LF, CR)
- Bidirectional text control
- Zero-width joiners/non-joiners

### Normalization Forms
1. **NFC**: Canonical Decomposition + Canonical Composition
2. **NFD**: Canonical Decomposition
3. **NFKC**: Compatibility Decomposition + Canonical Composition
4. **NFKD**: Compatibility Decomposition

## 4. Implementation Guidelines

### Best Practices
1. **Storage**
   - Use UTF-8 for storage and transmission
   - Specify encoding in metadata
   - Consider normalization requirements

2. **Processing**
   - Use library functions for string operations
   - Handle surrogate pairs correctly
   - Consider bidirectional text

3. **Database**
   - Use Unicode collation
   - Set appropriate character sets
   - Consider case sensitivity

### Common Pitfalls
1. **Encoding Confusion**
   - Mixing encodings
   - Incorrect charset declarations
   - Missing encoding detection

2. **String Length**
   - Counting bytes vs characters
   - Handling surrogate pairs
   - Combining character sequences

3. **Comparison Issues**
   - Case-insensitive comparison
   - Normalization differences
   - Collation ordering



#5. Common Unicode Ranges

1. **Basic Latin**: U+0000 to U+007F
2. **Latin-1 Supplement**: U+0080 to U+00FF
3. **CJK Unified Ideographs**: U+4E00 to U+9FFF
4. **Emoji**: U+1F300 to U+1F9FF ‎<This message was edited>
[22/11/2024, 14:23:49] Jay: Checkout 
Work summary

Auth8
- Added authentication type to  custom expert queue api
- Modified graphql for getPost to allow experts to see other user posts after being assigned

Fluentflow
- Updated categories + themes. Added ordering logic.
- Created new table for lessons and its 4 types then for flashcards and activity_history for tracking answers to flashcard quizzes or resetting them
- Working on flashcard logic based on the category selected and “keep in deck” logic for removing from list or keeping in user list

Coinchord
- Added billing details for all payment methods

Courtmatchup
- Added daily_breaks and days_off logic to calendar logic and club settings api

Pmj 
- Modified notifications template and cronjob for email and sms to make use of “pick_text” over “picks and opponent and odds”


https://app.mytechpassport.com/public/video-view?id=1nve4w5vLjbdzcDW1K24cyCh8RkPIqY6T&st=public
[22/11/2024, 14:25:03] Jay: check in
projects
- fluentflow
- ⁠voiceoutreach
- ⁠courtmatchup
[25/11/2024, 13:52:40] Jay: Checkout
Work summary

Auth8
- Created new notification function for expert assigned authentications
- Modified getPost graphql function to fetch for all experts regardless of assign status
- Modified expert rest api for getting assigned queue items to include post information

Fluentflow
- Completed v2 apis for scenarios, flashcards and activities

Gacha
- Updated simulation job


https://app.mytechpassport.com/public/video-view?id=1pdR6ju3Kas1fsDeoGm9kmkkAaCNCZSIh&st=public
[25/11/2024, 13:53:01] Jay: check in
projects
- voiceoutreach
- ⁠coinchord
[25/11/2024, 15:52:15] Jay: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJjcmVkZW50aWFsX2lkIjo2LCJ1c2VyIjp7ImlkIjo2fSwicm9sZV9pZCI6MywiaWF0IjoxNzMyNTQyMjE0LCJleHAiOjE3MzMxNDcwMTR9.BSv0YFy0shFG5E_ngnIYkNLXJeYZ3ZjXf-2d1Uk3x2A
[25/11/2024, 15:52:15] Jay: query($limit: Int, $after: String) {
    getNotifications(limit: $limit, after: $after) {
      data {
        pageInfo {
          hasMore
          endCursor
        }
        edges {
          ... on NOTIFICATION_MASS_NOTIFICATION {
            id
            title
            message
            is_read
            type
            created_at
          }
          ... on NOTIFICATION_PLAIN {
            id
            title
            message
            is_read
            type
            category
            created_at
          }
          ... on NOTIFICATION_NEW_VOTE_ON_POST {
            id
            title
            message
            is_read
            type
            category
            created_at
            post_id
            post {
              id
              title
              images {
                id
                front
                closeup_front
                featured
              }
            }
            vote {
              id
              decision
              appraisal_value
              remarks
              created_at
              user {
                id
                username
                image
                role
              }
            }
          }
          ... on NOTIFICATION_ACCOUNT_FOLLOWED {
            id
            title
            message
            is_read
            type
            category
            created_at
            followed_by {
              id
              username
              image
              role
            }
          }
          ... on NOTIFICATION_AUTHENTICATION_ACCEPTED {
            id
            title
            message
            is_read
            type
            category
            created_at
            post_id
            queue_id
            post {
              id
              title
              images {
                id
                front
                closeup_front
                featured
              }
            }
          }
          ... on NOTIFICATION_AUTHENTICATION_REJECTED {
            id
            title
            message
            is_read
            type
            category
            created_at
            queue_id
            rejection {
              id
              description
            }
            queue {
              id
              title
              images {
                id
                front
                closeup_front
                featured
              }
            }
          }

          ... on NOTIFICATION_NEW_COMMENT_ON_POST {
            id
            title
            message
            is_read
            type
            category
            created_at
            post_id
            comment {
              id
              message
              imageUrl
              message_type
              is_reply_allowed
              created_at
              user {
                id
                username
                image
                role
              }
              post {
                id
                title
              }
            }
          }
          ... on NOTIFICATION_NEW_COMMENT_ON_POST_IMAGE {
            id
            title
            message
            is_read
            type
            category
            created_at
            post_id
            comment {
              id
              message
              imageUrl
              created_at
              user {
                id
                username
                image
                role
              }
              post {
                id
                title
              }
              item_image
            }
          }
          ... on NOTIFICATION_NFT_TRANSFERRED {
            id
            type
            category
            post_id
            created_at
            post {
              id
              title
              images {
                id
                front
                closeup_front
                featured
              }
            }
          }
          ... on NOTIFICATION_AUTHENTICATION_REVIEWED_BY_EXPERT {
            id
            type
            category
            post_id
            created_at
            post {
              id
              title
              images {
                id
                front
                closeup_front
                featured
              }
            }
          }
          ... on NOTIFICATION_NEW_MENTION_ON_POST_COMMENT {
            id
            title
            message
            is_read
            type
            created_at
            post_id
            comment {
              id
              user {
                id
                username
                image
                role
              }
              post {
                id
                title
              }
            }
          }
          ... on NOTIFICATION_NEW_MENTION_ON_POST_IMAGE_COMMENT {
            id
            title
            message
            is_read
            type
            created_at
            post_id
            comment {
              id
              item_image
              user {
                id
                username
                image
                role
              }
              post {
                id
                title
              }
            }
          }
          ... on NOTIFICATION_NEW_LIKE_ON_POST {
            id
            title
            message
            type
            created_at
            post_id
            user {
              id
              username
              role
              image
            }
            post {
              id
              title
            }
          }
          ... on NOTIFICATION_OWNERSHIP_HISTORY_TAGGED {
            id
            title
            message
            type
            created_at
            post_id
            post_ownership_id
            user {
              id
              username
              role
              image
            }
            post {
              id
              title
            }
          }
          ... on NOTIFICATION_OWNERSHIP_HISTORY_REQUESTED_TO_BE_TAGGED {
            id
            title
            message
            type
            created_at
            post_id
            post_ownership_id
            user {
              id
              username
              role
              image
            }
            post {
              id
              title
            }
          }
          ... on NOTIFICATION_SUBSCRIPTION_NEW_VOTE_TO_POST {
            id
            title
            message
            is_read
            type
            category
            created_at
            post_id
            post {
              id
              title
              images {
                id
                front
                closeup_front
                featured
              }
            }
            vote {
              id
              decision
              appraisal_value
              remarks
              created_at
              user {
                id
                username
                image
                role
              }
            }
          }
          ... on NOTIFICATION_SUBSCRIPTION_NEW_COMMENT_TO_POST {
            id
            title
            message
            is_read
            type
            category
            created_at
            post_id
            comment {
              id
              message
              imageUrl
              message_type
              is_reply_allowed
              created_at
              user {
                id
                username
                image
                role
              }
              post {
                id
                title
              }
            }
          }
          ... on NOTIFICATION_SUBSCRIPTION_NEW_COMMENT_TO_POST_IMAGE {
            id
            title
            message
            is_read
            type
            category
            created_at
            post_id
            comment {
              id
              message
              imageUrl
              created_at
              user {
                id
                username
                image
                role
              }
              post {
                id
                title
              }
              item_image
            }
          }
          ... on NOTIFICATION_SUBSCRIPTION_AUTHENTICATION_REVIEWED_BY_EXPERT {
            id
            type
            category
            post_id
            created_at
            post {
              id
              title
              images {
                id
                front
                closeup_front
                featured
              }
            }
          }
          ... on NOTIFICATION_COMMUNITY_AUTHENTICATION_RESULTS_PUBLISHED {
            id
            type
            category
            post_id
            created_at
            post {
              id
              title
              images {
                id
                front
                closeup_front
                featured
              }
            }
          }
          ... on NOTIFICATION_SUBSCRIPTION_COMMUNITY_AUTHENTICATION_RESULTS_PUBLISHED {
            id
            type
            category
            post_id
            created_at
            post {
              id
              title
              images {
                id
                front
                closeup_front
                featured
              }
            }
          }
        }
      }
    }
  }
[25/11/2024, 17:51:32] Jay: ... on NOTIFICATION_AUTHENTICATION_ASSIGNED_EXPERT {
            id
            title
            message
            type
            created_at
          }
[26/11/2024, 14:29:46] Jay: Pmj
- Modified picks endpoints to make use of the “canreceive” v2 function I created
- Added restrictions based on those with bookies set in their profile as well 

courtmatchup
- Added account settings

Auth8
- Fixed notification error on assigned expert and on expert accept

Gacha
- Improved queries and simulation speed for fetching inventory protection

Coinchord
- Added the generated hexcode to the returned data on successful payment

https://app.mytechpassport.com/public/video-view?id=1tYKa4Xjc7UJ53I0cnThcecW7qPGPI6ej&st=public
[26/11/2024, 14:29:59] Jay: check in
projects
- voiceoutreach
‎[26/11/2024, 16:54:02] Jay: ‎image omitted
[26/11/2024, 23:06:21] Jay: Check out
Work summary


Voiceoutreach
- Modified scheduling routes
- Modified call recording service for telnyx and duration calculation to return the right s3 link for download from telnyx after calls
- Updated sms logs chat ui
- Added text filters/purifiers to remove unwanted characters on arins and our software

Auth8
- Completed updates on expert notifications service

Courtmatchup
- Made updates to reservations apis for being used on dashboard

Pmj
- Created new custom function for checking if user is logged in or not without using the bearer token so as not to cut them off at the middleware and return picks based on that

Gacha
- Reordered items on inventory 


https://app.mytechpassport.com/public/video-view?id=1VmaCmFNHJIAiEiZZ4vDMHvjkmpxUW6Zv&st=public
‎[27/11/2024, 16:00:00] Jay: ‎image omitted
[28/11/2024, 14:13:06] Jay: Checkout 
Work summary

courtmatchup
- Created new specific group endpoint for removing single user or fetching members reservations
- Updated dashboard api to return hours left and other associated data including programs and requests
- Added new pricing logic for type surface and sport id

Pmj
- Modified enums for notes

auth8
- Updated expert status to certified for tC user

Fluentflow
- Added emoji to category and updated all endpoints associated with the category data

Voiceoutreach
- Added “+” checker for telnyx service on the backend

https://app.mytechpassport.com/public/video-view?id=1akiAHKL5N5CQe-1QiTGHccuCzgCJNpKl&st=public
[28/11/2024, 14:16:34] Jay: check in
projects
- saba
- ⁠voieoutreach
- ⁠courtmatchup
[28/11/2024, 17:11:18] Jay: followers
total posts
total likes
most recent posts of influencers
[29/11/2024, 14:19:47] Jay: Checkout
Work summary

Riddlequiz
- Modified existing apis for character accuracy to check for restricted words and flag them and return the specific word that was flagged itself
- Created new api to check answers and flag them if any word is found

Saba
- Created utilities function for fetching short lived tokens and long lived tokens from the app id and secret which typically lasts 2 months
- Worked on test users logic

Coinchord
- Modified api to create a contact on hubspot crm for users generating tickets and store in the db
- Added contacts to tickets being sent to hubspot for quick reply to the contact directly using their pipeline

Courtmatchup
- Modified create event for clubs to include recurring dates as well

Voiceoutreach
- Fixed timezone issues from claude on arin and added updates to ours
- Modified sample formats being sent to ds side to contain the actual logic

https://app.mytechpassport.com/public/video-view?id=19c84tN4_tPaRXaUbtXpKrKwl6nFQwRmk&st=public
[29/11/2024, 14:20:02] Jay: check in
projects
- saba
- ⁠voiceoutreach
[02/12/2024, 14:15:21] Jay: Checkout
Work summary

Saba 
- Generated new creds for instagram basic display profile to try and fix fetching restrictions placed by meta
- Updated api json formats 
- Added restrictions to the graph fetching using long lived tokens

Voicoutreach
- Updated phone input box to show drop down of country codes and numbers

Courtmatchup
- Updated reservations list for club fetching
- Allowing multiple accounts and plans and permissions based on settings


https://app.mytechpassport.com/public/video-view?id=1S51A7KJWEPGliaWpyItZL8Bj5iiBh8D_&st=public
[02/12/2024, 14:15:51] Jay: check in
projects
- saba
- ⁠voiceoutreach
- ⁠auth8
[02/12/2024, 15:22:49] Jay: Hey Ryan sorry for the late message. I wanted to test a live call
Calendar appointment
Connect to Jarice ext.
Voice message 
No answer 
Wrong number 
Client asking to opt out. 

I also have a revised list client list which I will send on Monday. Since the insurance broker delayed us the client list has changed based on renewal date.
[03/12/2024, 14:14:54] Jay: Check out
Work summary

Voiceoutreach
- Remade voicemail service to hangup if stuck in users voicemail to avoid unnecssary costs
- Working on amd detection with deepgram as a plan b for telnyx amd issues
- Updated google calendar logic to include Jarice as a guest and also updated the calendar invite to contain more information sent from ds side in the meeting
- Added fallback check for recordings and if this still fails then it leaves them blank

Saba
- Attempted to use tavity  perplexity to fix instagram fetching logic for user data
- Fetching content data required for yt

Courtmatchup
- Updated pricing for club schema


https://app.mytechpassport.com/public/video-view?id=1x3rRlHh32TrlGCQotyC1Vm5LaNMs7dV_&st=public
[03/12/2024, 14:15:13] Jay: check in
projects
- voiceoutreach
- ⁠saba
- ⁠auth8
‎[03/12/2024, 15:37:57] Jay: ‎image omitted
[03/12/2024, 21:39:19] Jay: https://developers.telnyx.com/docs/voice/programmable-voice/answering-machine-detection
‎[04/12/2024, 08:24:55] Jay: ‎video omitted
[04/12/2024, 11:06:40] Jay: {
   "authentication":{
      "login":true,
      "register":true,
      "forgot":true,
      "reset":true,
      "lambdaLogin":true,
      "lambdaCaptcha":true,
      "lambdaMagic_Login":true,
      "lambdaRegister":true,
      "lambdatwofa":true,
      "lambdaFacebook":true,
      "lambdaGoogle":true,
      "lambdaMicrosoft":true,
      "lambdaPreference":true,
      "lambdaProfile":true,
      "lambdaRealtime":true,
      "lambdaReset":true,
      "lambdaSend_Mail":true,
      "lambdaUpdate_Email":true,
      "lambdaUpdate_Password":true,
      "lambdaAnalytics":true,
      "lambdaMultitenant":true,
      "lambdaUpload":true,
      "lambdaCheck":true,
      "lambdaQna":true,
      "lambdaForgot":true,
      "lambdaRefresh_Token":true,
      "lambda2FA":true,
      "lambdaEmail":true,
      "lambdaVerify-email":true,
      "lambdaVideo_platform":true,
      "lambdaVerify_user":true,
      "lambdaCms":true,
      "lambdaComment":true,
      "lambdaCrm":true,
      "lambdaEcom":true,
      "lambdaSupport-ticket":true,
      "lambdaApple":true,
      "lambdaHealth":true,
      "lambdaMail":true,
      "lambdaSSO":true,
      "lambdaStripe":true,
      "lambdaSubscription":true,
      "lambdaPassword":true,
      "lambdaVideo":true,
      "lambdaScheduling":true,
      "lambdaProject_management":true,
      "lambdaQuiz":true,
      "lambdaBlog":true
   },
   "models":{
      "user":{
         "POST":true,
         "PUT":true,
         "GET":true,
         "DELETE":true,
         "EXPORT":true,
         "AUTOCOMPLETE":true,
         "IMPORT":true,
         "GETALL":true,
         "PAGINATE":true,
         "PUTWHERE":true
      },
      "token":{
         "POST":true,
         "PUT":true,
         "GET":true,
         "DELETE":true,
         "EXPORT":true,
         "AUTOCOMPLETE":true,
         "IMPORT":true,
         "GETALL":true,
         "PAGINATE":true,
         "PUTWHERE":true
      },
      "photo":{
         "POST":true,
         "PUT":true,
         "GET":true,
         "DELETE":true,
         "EXPORT":true,
         "AUTOCOMPLETE":true,
         "IMPORT":true,
         "GETALL":true,
         "PAGINATE":true,
         "PUTWHERE":true
      },
      "email":{
         "POST":true,
         "PUT":true,
         "GET":true,
         "DELETE":true,
         "EXPORT":true,
         "AUTOCOMPLETE":true,
         "IMPORT":true,
         "GETALL":true,
         "PAGINATE":true,
         "PUTWHERE":true
      },
      "profile":{
         "POST":true,
         "PUT":true,
         "GET":true,
         "DELETE":true,
         "EXPORT":true,
         "AUTOCOMPLETE":true,
         "IMPORT":true,
         "GETALL":true,
         "PAGINATE":true,
         "PUTWHERE":true
      },
      "permission":{
         "POST":true,
         "PUT":true,
         "GET":true,
         "DELETE":true,
         "EXPORT":true,
         "AUTOCOMPLETE":true,
         "IMPORT":true,
         "GETALL":true,
         "PAGINATE":true,
         "PUTWHERE":true
      },
      "room":{
         "POST":true,
         "PUT":true,
         "GET":true,
         "DELETE":true,
         "EXPORT":true,
         "AUTOCOMPLETE":true,
         "IMPORT":true,
         "GETALL":true,
         "PAGINATE":true,
         "PUTWHERE":true
      },
      "chat":{
         "POST":true,
         "PUT":true,
         "GET":true,
         "DELETE":true,
         "EXPORT":true,
         "AUTOCOMPLETE":true,
         "IMPORT":true,
         "GETALL":true,
         "PAGINATE":true,
         "PUTWHERE":true
      },
      "coach":{
         "AUTOCOMPLETE":true,
         "DELETE":true,
         "EXPORT":true,
         "GET":true,
         "GETALL":true,
         "IMPORT":true,
         "POST":true,
         "PUT":true,
         "PUTWHERE":true,
         "PAGINATE":true
      },
      "clinic_bookings":{
         "AUTOCOMPLETE":true,
         "DELETE":true,
         "EXPORT":true,
         "GET":true,
         "GETALL":true,
         "IMPORT":true,
         "POST":true,
         "PUT":true,
         "PUTWHERE":true,
         "PAGINATE":true
      },
      "stripe_webhook":{
         "AUTOCOMPLETE":true,
         "DELETE":true,
         "EXPORT":true,
         "GET":true,
         "GETALL":true,
         "IMPORT":true,
         "POST":true,
         "PUT":true,
         "PUTWHERE":true,
         "PAGINATE":true
      },
      "support_tickets":{
         "AUTOCOMPLETE":true,
         "DELETE":true,
         "EXPORT":true,
         "GET":true,
         "GETALL":true,
         "IMPORT":true,
         "POST":true,
         "PUT":true,
         "PUTWHERE":true,
         "PAGINATE":true
      },
      "payments":{
         "AUTOCOMPLETE":true,
         "DELETE":true,
         "EXPORT":true,
         "GET":true,
         "GETALL":true,
         "IMPORT":true,
         "POST":true,
         "PUT":true,
         "PUTWHERE":true,
         "PAGINATE":true
      },
      "reservation":{
         "AUTOCOMPLETE":true,
         "DELETE":true,
         "EXPORT":true,
         "GET":true,
         "GETALL":true,
         "IMPORT":true,
         "POST":true,
         "PUT":true,
         "PUTWHERE":true,
         "PAGINATE":true
      },
      "club_settings":{
         "AUTOCOMPLETE":true,
         "DELETE":true,
         "EXPORT":true,
         "GET":true,
         "GETALL":true,
         "IMPORT":true,
         "POST":true,
         "PUT":true,
         "PUTWHERE":true,
         "PAGINATE":true
      },
      "stripe_price":{
         "AUTOCOMPLETE":true,
         "DELETE":true,
         "EXPORT":true,
         "GET":true,
         "GETALL":true,
         "IMPORT":true,
         "POST":true,
         "PUT":true,
         "PUTWHERE":true,
         "PAGINATE":true
      },
      "buddy_subscription":{
         "AUTOCOMPLETE":true,
         "DELETE":true,
         "EXPORT":true,
         "GET":true,
         "GETALL":true,
         "IMPORT":true,
         "POST":true,
         "PUT":true,
         "PUTWHERE":true,
         "PAGINATE":true
      },
      "reviews":{
         "AUTOCOMPLETE":true,
         "DELETE":true,
         "EXPORT":true,
         "GET":true,
         "GETALL":true,
         "IMPORT":true,
         "POST":true,
         "PUT":true,
         "PUTWHERE":true,
         "PAGINATE":true
      },
      "analytic_log":{
         "AUTOCOMPLETE":true,
         "DELETE":true,
         "EXPORT":true,
         "GET":true,
         "GETALL":true,
         "IMPORT":true,
         "POST":true,
         "PUT":true,
         "PUTWHERE":true,
         "PAGINATE":true
      },
      "club_admin_activity":{
         "AUTOCOMPLETE":true,
         "DELETE":true,
         "EXPORT":true,
         "GET":true,
         "GETALL":true,
         "IMPORT":true,
         "POST":true,
         "PUT":true,
         "PUTWHERE":true,
         "PAGINATE":true
      },
      "court_bookings":{
         "AUTOCOMPLETE":true,
         "DELETE":true,
         "EXPORT":true,
         "GET":true,
         "GETALL":true,
         "IMPORT":true,
         "POST":true,
         "PUT":true,
         "PUTWHERE":true,
         "PAGINATE":true
      },
      "reservation_logs":{
         "AUTOCOMPLETE":true,
         "DELETE":true,
         "EXPORT":true,
         "GET":true,
         "GETALL":true,
         "IMPORT":true,
         "POST":true,
         "PUT":true,
         "PUTWHERE":true,
         "PAGINATE":true
      },
      "cms":{
         "AUTOCOMPLETE":true,
         "DELETE":true,
         "EXPORT":true,
         "GET":true,
         "GETALL":true,
         "IMPORT":true,
         "POST":true,
         "PUT":true,
         "PUTWHERE":true,
         "PAGINATE":true
      },
      "booking":{
         "AUTOCOMPLETE":true,
         "DELETE":true,
         "EXPORT":true,
         "GET":true,
         "GETALL":true,
         "IMPORT":true,
         "POST":true,
         "PUT":true,
         "PUTWHERE":true,
         "PAGINATE":true
      },
      "coach_sports":{
         "AUTOCOMPLETE":true,
         "DELETE":true,
         "EXPORT":true,
         "GET":true,
         "GETALL":true,
         "IMPORT":true,
         "POST":true,
         "PUT":true,
         "PUTWHERE":true,
         "PAGINATE":true
      },
      "stripe_checkout":{
         "AUTOCOMPLETE":true,
         "DELETE":true,
         "EXPORT":true,
         "GET":true,
         "GETALL":true,
         "IMPORT":true,
         "POST":true,
         "PUT":true,
         "PUTWHERE":true,
         "PAGINATE":true
      },
      "stripe_subscription":{
         "AUTOCOMPLETE":true,
         "DELETE":true,
         "EXPORT":true,
         "GET":true,
         "GETALL":true,
         "IMPORT":true,
         "POST":true,
         "PUT":true,
         "PUTWHERE":true,
         "PAGINATE":true
      },
      "stripe_invoice":{
         "AUTOCOMPLETE":true,
         "DELETE":true,
         "EXPORT":true,
         "GET":true,
         "GETALL":true,
         "IMPORT":true,
         "POST":true,
         "PUT":true,
         "PUTWHERE":true,
         "PAGINATE":true
      },
      "staff":{
         "AUTOCOMPLETE":true,
         "DELETE":true,
         "EXPORT":true,
         "GET":true,
         "GETALL":true,
         "IMPORT":true,
         "POST":true,
         "PUT":true,
         "PUTWHERE":true,
         "PAGINATE":true
      },
      "club_sports":{
         "AUTOCOMPLETE":true,
         "DELETE":true,
         "EXPORT":true,
         "GET":true,
         "GETALL":true,
         "IMPORT":true,
         "POST":true,
         "PUT":true,
         "PUTWHERE":true,
         "PAGINATE":true
      },
      "find_a_buddy_requests":{
         "AUTOCOMPLETE":true,
         "DELETE":true,
         "EXPORT":true,
         "GET":true,
         "GETALL":true,
         "IMPORT":true,
         "POST":true,
         "PUT":true,
         "PUTWHERE":true,
         "PAGINATE":true
      },
      "api_keys":{
         "AUTOCOMPLETE":true,
         "DELETE":true,
         "EXPORT":true,
         "GET":true,
         "GETALL":true,
         "IMPORT":true,
         "POST":true,
         "PUT":true,
         "PUTWHERE":true,
         "PAGINATE":true
      },
      "user_groups":{
         "AUTOCOMPLETE":true,
         "DELETE":true,
         "EXPORT":true,
         "GET":true,
         "GETALL":true,
         "IMPORT":true,
         "POST":true,
         "PUT":true,
         "PUTWHERE":true,
         "PAGINATE":true
      },
      "sports":{
         "AUTOCOMPLETE":true,
         "DELETE":true,
         "EXPORT":true,
         "GET":true,
         "GETALL":true,
         "IMPORT":true,
         "POST":true,
         "PUT":true,
         "PUTWHERE":true,
         "PAGINATE":true
      },
      "job":{
         "AUTOCOMPLETE":true,
         "DELETE":true,
         "EXPORT":true,
         "GET":true,
         "GETALL":true,
         "IMPORT":true,
         "POST":true,
         "PUT":true,
         "PUTWHERE":true,
         "PAGINATE":true
      },
      "club_court":{
         "AUTOCOMPLETE":true,
         "DELETE":true,
         "EXPORT":true,
         "GET":true,
         "GETALL":true,
         "IMPORT":true,
         "POST":true,
         "PUT":true,
         "PUTWHERE":true,
         "PAGINATE":true
      },
      "court_pricing":{
         "AUTOCOMPLETE":true,
         "DELETE":true,
         "EXPORT":true,
         "GET":true,
         "GETALL":true,
         "IMPORT":true,
         "POST":true,
         "PUT":true,
         "PUTWHERE":true,
         "PAGINATE":true
      },
      "notifications":{
         "AUTOCOMPLETE":true,
         "DELETE":true,
         "EXPORT":true,
         "GET":true,
         "GETALL":true,
         "IMPORT":true,
         "POST":true,
         "PUT":true,
         "PUTWHERE":true,
         "PAGINATE":true
      },
      "buddy":{
         "AUTOCOMPLETE":true,
         "DELETE":true,
         "EXPORT":true,
         "GET":true,
         "GETALL":true,
         "IMPORT":true,
         "POST":true,
         "PUT":true,
         "PUTWHERE":true,
         "PAGINATE":true
      },
      "clinics":{
         "AUTOCOMPLETE":true,
         "DELETE":true,
         "EXPORT":true,
         "GET":true,
         "GETALL":true,
         "IMPORT":true,
         "POST":true,
         "PUT":true,
         "PUTWHERE":true,
         "PAGINATE":true
      },
      "stripe_product":{
         "AUTOCOMPLETE":true,
         "DELETE":true,
         "EXPORT":true,
         "GET":true,
         "GETALL":true,
         "IMPORT":true,
         "POST":true,
         "PUT":true,
         "PUTWHERE":true,
         "PAGINATE":true
      },
      "coaching_sessions":{
         "AUTOCOMPLETE":true,
         "DELETE":true,
         "EXPORT":true,
         "GET":true,
         "GETALL":true,
         "IMPORT":true,
         "POST":true,
         "PUT":true,
         "PUTWHERE":true,
         "PAGINATE":true
      },
      "setting":{
         "AUTOCOMPLETE":true,
         "DELETE":true,
         "EXPORT":true,
         "GET":true,
         "GETALL":true,
         "IMPORT":true,
         "POST":true,
         "PUT":true,
         "PUTWHERE":true,
         "PAGINATE":true
      },
      "test":{
         "AUTOCOMPLETE":true,
         "DELETE":true,
         "EXPORT":true,
         "GET":true,
         "GETALL":true,
         "IMPORT":true,
         "POST":true,
         "PUT":true,
         "PUTWHERE":true,
         "PAGINATE":true
      },
      "clubs":{
         "AUTOCOMPLETE":true,
         "DELETE":true,
         "EXPORT":true,
         "GET":true,
         "GETALL":true,
         "IMPORT":true,
         "POST":true,
         "PUT":true,
         "PUTWHERE":true,
         "PAGINATE":true
      },
      "trigger_type":{
         "AUTOCOMPLETE":true,
         "DELETE":true,
         "EXPORT":true,
         "GET":true,
         "GETALL":true,
         "IMPORT":true,
         "POST":true,
         "PUT":true,
         "PUTWHERE":true,
         "PAGINATE":true
      },
      "coach_requests":{
         "AUTOCOMPLETE":true,
         "DELETE":true,
         "EXPORT":true,
         "GET":true,
         "GETALL":true,
         "IMPORT":true,
         "POST":true,
         "PUT":true,
         "PUTWHERE":true,
         "PAGINATE":true
      },
      "clinic_coaches":{
         "AUTOCOMPLETE":true,
         "DELETE":true,
         "EXPORT":true,
         "GET":true,
         "GETALL":true,
         "IMPORT":true,
         "POST":true,
         "PUT":true,
         "PUTWHERE":true,
         "PAGINATE":true
      },
      "stripe_setting":{
         "AUTOCOMPLETE":true,
         "DELETE":true,
         "EXPORT":true,
         "GET":true,
         "GETALL":true,
         "IMPORT":true,
         "POST":true,
         "PUT":true,
         "PUTWHERE":true,
         "PAGINATE":true
      },
      "alerts":{
         "AUTOCOMPLETE":true,
         "DELETE":true,
         "EXPORT":true,
         "GET":true,
         "GETALL":true,
         "IMPORT":true,
         "POST":true,
         "PUT":true,
         "PUTWHERE":true,
         "PAGINATE":true
      },
      "reservation_team":{
         "AUTOCOMPLETE":true,
         "DELETE":true,
         "EXPORT":true,
         "GET":true,
         "GETALL":true,
         "IMPORT":true,
         "POST":true,
         "PUT":true,
         "PUTWHERE":true,
         "PAGINATE":true
      },
      "stripe_order":{
         "AUTOCOMPLETE":true,
         "DELETE":true,
         "EXPORT":true,
         "GET":true,
         "GETALL":true,
         "IMPORT":true,
         "POST":true,
         "PUT":true,
         "PUTWHERE":true,
         "PAGINATE":true
      },
      "buddy_request":{
         "AUTOCOMPLETE":true,
         "DELETE":true,
         "EXPORT":true,
         "GET":true,
         "GETALL":true,
         "IMPORT":true,
         "POST":true,
         "PUT":true,
         "PUTWHERE":true,
         "PAGINATE":true
      },
      "posts":{
         "AUTOCOMPLETE":true,
         "DELETE":true,
         "EXPORT":true,
         "GET":true,
         "GETALL":true,
         "IMPORT":true,
         "POST":true,
         "PUT":true,
         "PUTWHERE":true,
         "PAGINATE":true
      },
      "surface":{
         "AUTOCOMPLETE":true,
         "DELETE":true,
         "EXPORT":true,
         "GET":true,
         "GETALL":true,
         "IMPORT":true,
         "POST":true,
         "PUT":true,
         "PUTWHERE":true,
         "PAGINATE":true
      },
      "activity_logs":{
         "AUTOCOMPLETE":true,
         "DELETE":true,
         "EXPORT":true,
         "GET":true,
         "GETALL":true,
         "IMPORT":true,
         "POST":true,
         "PUT":true,
         "PUTWHERE":true,
         "PAGINATE":true
      },
      "age_groups":{
         "AUTOCOMPLETE":true,
         "DELETE":true,
         "EXPORT":true,
         "GET":true,
         "GETALL":true,
         "IMPORT":true,
         "POST":true,
         "PUT":true,
         "PUTWHERE":true,
         "PAGINATE":true
      },
      "family_roles":{
         "AUTOCOMPLETE":true,
         "DELETE":true,
         "EXPORT":true,
         "GET":true,
         "GETALL":true,
         "IMPORT":true,
         "POST":true,
         "PUT":true,
         "PUTWHERE":true,
         "PAGINATE":true
      }
   }
}
[04/12/2024, 11:07:31] Jay: {
   "authentication":{
      "login":true,
      "register":true,
      "forgot":true,
      "reset":true,
      "lambdaLogin":true,
      "lambdaCaptcha":true,
      "lambdaMagic_Login":true,
      "lambdaRegister":true,
      "lambdatwofa":true,
      "lambdaFacebook":true,
      "lambdaGoogle":true,
      "lambdaMicrosoft":true,
      "lambdaPreference":true,
      "lambdaProfile":true,
      "lambdaRealtime":true,
      "lambdaReset":true,
      "lambdaSend_Mail":true,
      "lambdaUpdate_Email":true,
      "lambdaUpdate_Password":true,
      "lambdaAnalytics":true,
      "lambdaMultitenant":true,
      "lambdaUpload":true,
      "lambdaCheck":true,
      "lambdaQna":true,
      "lambdaForgot":true,
      "lambdaRefresh_Token":true,
      "lambda2FA":true,
      "lambdaEmail":true,
      "lambdaVerify-email":true,
      "lambdaVideo_platform":true,
      "lambdaVerify_user":true,
      "lambdaCms":true,
      "lambdaComment":true,
      "lambdaCrm":true,
      "lambdaEcom":true,
      "lambdaSupport-ticket":true,
      "lambdaApple":true,
      "lambdaHealth":true,
      "lambdaMail":true,
      "lambdaSSO":true,
      "lambdaStripe":true,
      "lambdaSubscription":true,
      "lambdaPassword":true,
      "lambdaVideo":true,
      "lambdaScheduling":true,
      "lambdaProject_management":true,
      "lambdaQuiz":true,
      "lambdaBlog":true
   },
   "models":{
      "user":{
         "POST":true,
         "PUT":true,
         "GET":true,
         "DELETE":true,
         "EXPORT":true,
         "AUTOCOMPLETE":true,
         "IMPORT":true,
         "GETALL":true,
         "PAGINATE":true,
         "PUTWHERE":true
      },
      "token":{
         "POST":true,
         "PUT":true,
         "GET":true,
         "DELETE":true,
         "EXPORT":true,
         "AUTOCOMPLETE":true,
         "IMPORT":true,
         "GETALL":true,
         "PAGINATE":true,
         "PUTWHERE":true
      },
      "photo":{
         "POST":true,
         "PUT":true,
         "GET":true,
         "DELETE":true,
         "EXPORT":true,
         "AUTOCOMPLETE":true,
         "IMPORT":true,
         "GETALL":true,
         "PAGINATE":true,
         "PUTWHERE":true
      },
      "email":{
         "POST":true,
         "PUT":true,
         "GET":true,
         "DELETE":true,
         "EXPORT":true,
         "AUTOCOMPLETE":true,
         "IMPORT":true,
         "GETALL":true,
         "PAGINATE":true,
         "PUTWHERE":true
      },
      "profile":{
         "POST":true,
         "PUT":true,
         "GET":true,
         "DELETE":true,
         "EXPORT":true,
         "AUTOCOMPLETE":true,
         "IMPORT":true,
         "GETALL":true,
         "PAGINATE":true,
         "PUTWHERE":true
      },
      "permission":{
         "POST":true,
         "PUT":true,
         "GET":true,
         "DELETE":true,
         "EXPORT":true,
         "AUTOCOMPLETE":true,
         "IMPORT":true,
         "GETALL":true,
         "PAGINATE":true,
         "PUTWHERE":true
      },
      "room":{
         "POST":true,
         "PUT":true,
         "GET":true,
         "DELETE":true,
         "EXPORT":true,
         "AUTOCOMPLETE":true,
         "IMPORT":true,
         "GETALL":true,
         "PAGINATE":true,
         "PUTWHERE":true
      },
      "chat":{
         "POST":true,
         "PUT":true,
         "GET":true,
         "DELETE":true,
         "EXPORT":true,
         "AUTOCOMPLETE":true,
         "IMPORT":true,
         "GETALL":true,
         "PAGINATE":true,
         "PUTWHERE":true
      },
      "coach":{
         "AUTOCOMPLETE":true,
         "DELETE":true,
         "EXPORT":true,
         "GET":true,
         "GETALL":true,
         "IMPORT":true,
         "POST":true,
         "PUT":true,
         "PUTWHERE":true,
         "PAGINATE":true
      },
      "clinic_bookings":{
         "AUTOCOMPLETE":true,
         "DELETE":true,
         "EXPORT":true,
         "GET":true,
         "GETALL":true,
         "IMPORT":true,
         "POST":true,
         "PUT":true,
         "PUTWHERE":true,
         "PAGINATE":true
      },
      "stripe_webhook":{
         "AUTOCOMPLETE":true,
         "DELETE":true,
         "EXPORT":true,
         "GET":true,
         "GETALL":true,
         "IMPORT":true,
         "POST":true,
         "PUT":true,
         "PUTWHERE":true,
         "PAGINATE":true
      },
      "support_tickets":{
         "AUTOCOMPLETE":true,
         "DELETE":true,
         "EXPORT":true,
         "GET":true,
         "GETALL":true,
         "IMPORT":true,
         "POST":true,
         "PUT":true,
         "PUTWHERE":true,
         "PAGINATE":true
      },
      "payments":{
         "AUTOCOMPLETE":true,
         "DELETE":true,
         "EXPORT":true,
         "GET":true,
         "GETALL":true,
         "IMPORT":true,
         "POST":true,
         "PUT":true,
         "PUTWHERE":true,
         "PAGINATE":true
      },
      "reservation":{
         "AUTOCOMPLETE":true,
         "DELETE":true,
         "EXPORT":true,
         "GET":true,
         "GETALL":true,
         "IMPORT":true,
         "POST":true,
         "PUT":true,
         "PUTWHERE":true,
         "PAGINATE":true
      },
      "club_settings":{
         "AUTOCOMPLETE":true,
         "DELETE":true,
         "EXPORT":true,
         "GET":true,
         "GETALL":true,
         "IMPORT":true,
         "POST":true,
         "PUT":true,
         "PUTWHERE":true,
         "PAGINATE":true
      },
      "stripe_price":{
         "AUTOCOMPLETE":true,
         "DELETE":true,
         "EXPORT":true,
         "GET":true,
         "GETALL":true,
         "IMPORT":true,
         "POST":true,
         "PUT":true,
         "PUTWHERE":true,
         "PAGINATE":true
      },
      "buddy_subscription":{
         "AUTOCOMPLETE":true,
         "DELETE":true,
         "EXPORT":true,
         "GET":true,
         "GETALL":true,
         "IMPORT":true,
         "POST":true,
         "PUT":true,
         "PUTWHERE":true,
         "PAGINATE":true
      },
      "reviews":{
         "AUTOCOMPLETE":true,
         "DELETE":true,
         "EXPORT":true,
         "GET":true,
         "GETALL":true,
         "IMPORT":true,
         "POST":true,
         "PUT":true,
         "PUTWHERE":true,
         "PAGINATE":true
      },
      "analytic_log":{
         "AUTOCOMPLETE":true,
         "DELETE":true,
         "EXPORT":true,
         "GET":true,
         "GETALL":true,
         "IMPORT":true,
         "POST":true,
         "PUT":true,
         "PUTWHERE":true,
         "PAGINATE":true
      },
      "club_admin_activity":{
         "AUTOCOMPLETE":true,
         "DELETE":true,
         "EXPORT":true,
         "GET":true,
         "GETALL":true,
         "IMPORT":true,
         "POST":true,
         "PUT":true,
         "PUTWHERE":true,
         "PAGINATE":true
      },
      "court_bookings":{
         "AUTOCOMPLETE":true,
         "DELETE":true,
         "EXPORT":true,
         "GET":true,
         "GETALL":true,
         "IMPORT":true,
         "POST":true,
         "PUT":true,
         "PUTWHERE":true,
         "PAGINATE":true
      },
      "reservation_logs":{
         "AUTOCOMPLETE":true,
         "DELETE":true,
         "EXPORT":true,
         "GET":true,
         "GETALL":true,
         "IMPORT":true,
         "POST":true,
         "PUT":true,
         "PUTWHERE":true,
         "PAGINATE":true
      },
      "cms":{
         "AUTOCOMPLETE":true,
         "DELETE":true,
         "EXPORT":true,
         "GET":true,
         "GETALL":true,
         "IMPORT":true,
         "POST":true,
         "PUT":true,
         "PUTWHERE":true,
         "PAGINATE":true
      },
      "booking":{
         "AUTOCOMPLETE":true,
         "DELETE":true,
         "EXPORT":true,
         "GET":true,
         "GETALL":true,
         "IMPORT":true,
         "POST":true,
         "PUT":true,
         "PUTWHERE":true,
         "PAGINATE":true
      },
      "coach_sports":{
         "AUTOCOMPLETE":true,
         "DELETE":true,
         "EXPORT":true,
         "GET":true,
         "GETALL":true,
         "IMPORT":true,
         "POST":true,
         "PUT":true,
         "PUTWHERE":true,
         "PAGINATE":true
      },
      "stripe_checkout":{
         "AUTOCOMPLETE":true,
         "DELETE":true,
         "EXPORT":true,
         "GET":true,
         "GETALL":true,
         "IMPORT":true,
         "POST":true,
         "PUT":true,
         "PUTWHERE":true,
         "PAGINATE":true
      },
      "stripe_subscription":{
         "AUTOCOMPLETE":true,
         "DELETE":true,
         "EXPORT":true,
         "GET":true,
         "GETALL":true,
         "IMPORT":true,
         "POST":true,
         "PUT":true,
         "PUTWHERE":true,
         "PAGINATE":true
      },
      "stripe_invoice":{
         "AUTOCOMPLETE":true,
         "DELETE":true,
         "EXPORT":true,
         "GET":true,
         "GETALL":true,
         "IMPORT":true,
         "POST":true,
         "PUT":true,
         "PUTWHERE":true,
         "PAGINATE":true
      },
      "staff":{
         "AUTOCOMPLETE":true,
         "DELETE":true,
         "EXPORT":true,
         "GET":true,
         "GETALL":true,
         "IMPORT":true,
         "POST":true,
         "PUT":true,
         "PUTWHERE":true,
         "PAGINATE":true
      },
      "club_sports":{
         "AUTOCOMPLETE":true,
         "DELETE":true,
         "EXPORT":true,
         "GET":true,
         "GETALL":true,
         "IMPORT":true,
         "POST":true,
         "PUT":true,
         "PUTWHERE":true,
         "PAGINATE":true
      },
      "find_a_buddy_requests":{
         "AUTOCOMPLETE":true,
         "DELETE":true,
         "EXPORT":true,
         "GET":true,
         "GETALL":true,
         "IMPORT":true,
         "POST":true,
         "PUT":true,
         "PUTWHERE":true,
         "PAGINATE":true
      },
      "api_keys":{
         "AUTOCOMPLETE":true,
         "DELETE":true,
         "EXPORT":true,
         "GET":true,
         "GETALL":true,
         "IMPORT":true,
         "POST":true,
         "PUT":true,
         "PUTWHERE":true,
         "PAGINATE":true
      },
      "user_groups":{
         "AUTOCOMPLETE":true,
         "DELETE":true,
         "EXPORT":true,
         "GET":true,
         "GETALL":true,
         "IMPORT":true,
         "POST":true,
         "PUT":true,
         "PUTWHERE":true,
         "PAGINATE":true
      },
      "sports":{
         "AUTOCOMPLETE":true,
         "DELETE":true,
         "EXPORT":true,
         "GET":true,
         "GETALL":true,
         "IMPORT":true,
         "POST":true,
         "PUT":true,
         "PUTWHERE":true,
         "PAGINATE":true
      },
      "job":{
         "AUTOCOMPLETE":true,
         "DELETE":true,
         "EXPORT":true,
         "GET":true,
         "GETALL":true,
         "IMPORT":true,
         "POST":true,
         "PUT":true,
         "PUTWHERE":true,
         "PAGINATE":true
      },
      "club_court":{
         "AUTOCOMPLETE":true,
         "DELETE":true,
         "EXPORT":true,
         "GET":true,
         "GETALL":true,
         "IMPORT":true,
         "POST":true,
         "PUT":true,
         "PUTWHERE":true,
         "PAGINATE":true
      },
      "court_pricing":{
         "AUTOCOMPLETE":true,
         "DELETE":true,
         "EXPORT":true,
         "GET":true,
         "GETALL":true,
         "IMPORT":true,
         "POST":true,
         "PUT":true,
         "PUTWHERE":true,
         "PAGINATE":true
      },
      "notifications":{
         "AUTOCOMPLETE":true,
         "DELETE":true,
         "EXPORT":true,
         "GET":true,
         "GETALL":true,
         "IMPORT":true,
         "POST":true,
         "PUT":true,
         "PUTWHERE":true,
         "PAGINATE":true
      },
      "buddy":{
         "AUTOCOMPLETE":true,
         "DELETE":true,
         "EXPORT":true,
         "GET":true,
         "GETALL":true,
         "IMPORT":true,
         "POST":true,
         "PUT":true,
         "PUTWHERE":true,
         "PAGINATE":true
      },
      "clinics":{
         "AUTOCOMPLETE":true,
         "DELETE":true,
         "EXPORT":true,
         "GET":true,
         "GETALL":true,
         "IMPORT":true,
         "POST":true,
         "PUT":true,
         "PUTWHERE":true,
         "PAGINATE":true
      },
      "stripe_product":{
         "AUTOCOMPLETE":true,
         "DELETE":true,
         "EXPORT":true,
         "GET":true,
         "GETALL":true,
         "IMPORT":true,
         "POST":true,
         "PUT":true,
         "PUTWHERE":true,
         "PAGINATE":true
      },
      "coaching_sessions":{
         "AUTOCOMPLETE":true,
         "DELETE":true,
         "EXPORT":true,
         "GET":true,
         "GETALL":true,
         "IMPORT":true,
         "POST":true,
         "PUT":true,
         "PUTWHERE":true,
         "PAGINATE":true
      },
      "setting":{
         "AUTOCOMPLETE":true,
         "DELETE":true,
         "EXPORT":true,
         "GET":true,
         "GETALL":true,
         "IMPORT":true,
         "POST":true,
         "PUT":true,
         "PUTWHERE":true,
         "PAGINATE":true
      },
      "test":{
         "AUTOCOMPLETE":true,
         "DELETE":true,
         "EXPORT":true,
         "GET":true,
         "GETALL":true,
         "IMPORT":true,
         "POST":true,
         "PUT":true,
         "PUTWHERE":true,
         "PAGINATE":true
      },
      "clubs":{
         "AUTOCOMPLETE":true,
         "DELETE":true,
         "EXPORT":true,
         "GET":true,
         "GETALL":true,
         "IMPORT":true,
         "POST":true,
         "PUT":true,
         "PUTWHERE":true,
         "PAGINATE":true
      },
      "trigger_type":{
         "AUTOCOMPLETE":true,
         "DELETE":true,
         "EXPORT":true,
         "GET":true,
         "GETALL":true,
         "IMPORT":true,
         "POST":true,
         "PUT":true,
         "PUTWHERE":true,
         "PAGINATE":true
      },
      "coach_requests":{
         "AUTOCOMPLETE":true,
         "DELETE":true,
         "EXPORT":true,
         "GET":true,
         "GETALL":true,
         "IMPORT":true,
         "POST":true,
         "PUT":true,
         "PUTWHERE":true,
         "PAGINATE":true
      },
      "clinic_coaches":{
         "AUTOCOMPLETE":true,
         "DELETE":true,
         "EXPORT":true,
         "GET":true,
         "GETALL":true,
         "IMPORT":true,
         "POST":true,
         "PUT":true,
         "PUTWHERE":true,
         "PAGINATE":true
      },
      "stripe_setting":{
         "AUTOCOMPLETE":true,
         "DELETE":true,
         "EXPORT":true,
         "GET":true,
         "GETALL":true,
         "IMPORT":true,
         "POST":true,
         "PUT":true,
         "PUTWHERE":true,
         "PAGINATE":true
      },
      "alerts":{
         "AUTOCOMPLETE":true,
         "DELETE":true,
         "EXPORT":true,
         "GET":true,
         "GETALL":true,
         "IMPORT":true,
         "POST":true,
         "PUT":true,
         "PUTWHERE":true,
         "PAGINATE":true
      },
      "reservation_team":{
         "AUTOCOMPLETE":true,
         "DELETE":true,
         "EXPORT":true,
         "GET":true,
         "GETALL":true,
         "IMPORT":true,
         "POST":true,
         "PUT":true,
         "PUTWHERE":true,
         "PAGINATE":true
      },
      "stripe_order":{
         "AUTOCOMPLETE":true,
         "DELETE":true,
         "EXPORT":true,
         "GET":true,
         "GETALL":true,
         "IMPORT":true,
         "POST":true,
         "PUT":true,
         "PUTWHERE":true,
         "PAGINATE":true
      },
      "buddy_request":{
         "AUTOCOMPLETE":true,
         "DELETE":true,
         "EXPORT":true,
         "GET":true,
         "GETALL":true,
         "IMPORT":true,
         "POST":true,
         "PUT":true,
         "PUTWHERE":true,
         "PAGINATE":true
      },
      "posts":{
         "AUTOCOMPLETE":true,
         "DELETE":true,
         "EXPORT":true,
         "GET":true,
         "GETALL":true,
         "IMPORT":true,
         "POST":true,
         "PUT":true,
         "PUTWHERE":true,
         "PAGINATE":true
      },
      "surface":{
         "AUTOCOMPLETE":true,
         "DELETE":true,
         "EXPORT":true,
         "GET":true,
         "GETALL":true,
         "IMPORT":true,
         "POST":true,
         "PUT":true,
         "PUTWHERE":true,
         "PAGINATE":true
      },
      "activity_logs":{
         "AUTOCOMPLETE":true,
         "DELETE":true,
         "EXPORT":true,
         "GET":true,
         "GETALL":true,
         "IMPORT":true,
         "POST":true,
         "PUT":true,
         "PUTWHERE":true,
         "PAGINATE":true
      },
      "age_groups":{
         "AUTOCOMPLETE":true,
         "DELETE":true,
         "EXPORT":true,
         "GET":true,
         "GETALL":true,
         "IMPORT":true,
         "POST":true,
         "PUT":true,
         "PUTWHERE":true,
         "PAGINATE":true
      },
      "family_roles":{
         "AUTOCOMPLETE":true,
         "DELETE":true,
         "EXPORT":true,
         "GET":true,
         "GETALL":true,
         "IMPORT":true,
         "POST":true,
         "PUT":true,
         "PUTWHERE":true,
         "PAGINATE":true
      },
  "lessons":{
         "AUTOCOMPLETE":true,
         "DELETE":true,
         "EXPORT":true,
         "GET":true,
         "GETALL":true,
         "IMPORT":true,
         "POST":true,
         "PUT":true,
         "PUTWHERE":true,
         "PAGINATE":true
      }
   }
}
[04/12/2024, 14:06:34] Jay: Checkout
Work summary

Courtmatchup
- Created new api for handling the conditional email sending and repeating email sending with days logic
- Modified lessons structure for clubs

Voiceoutreach
- Modified calendar scheduling to include email of the person that was called in the participants list
- Modified cron-schedule being called from ds to pass all details required for the user
- Worked on manual voicemail detection logic

https://app.mytechpassport.com/public/video-view?id=1FvYiHZO4TJ7sl_DZtv8TZ47oNKH9R-gb&st=public
[04/12/2024, 14:07:00] Jay: Check in
Projects

- voiceoutreach
- saba
- auth8
‎[04/12/2024, 19:53:47] Jay: ‎image omitted
[05/12/2024, 14:30:49] Jay: Check out
Work summary

Voiceoutreach

- Handling automated hangups if in voicemail
- Fixed telynx and detection issue for machine/human
- Added streaming hangup if voicemail detected to prepare right Audio during 30s delay
- Implemented solution for telynx voicemail sending. Still needs more tests

Saba
- Generated meta media link scrapper for influencers
- Unable to retrieve recent data using the new service I made

https://app.mytechpassport.com/public/video-view?id=1DX6gLCXOi76JJBaLV50ijoa2BJHHNzzE&st=public
[05/12/2024, 14:31:03] Jay: check in
projects
- voiceoutreach
- saba
- auth8
[05/12/2024, 19:46:01] Jay: https://www.google.com/url?source=meet&sa=D&q=https%3A%2F%2Fdrive.google.com%2Fdrive%2Ffolders%2F1TT3mLW8CH4d_i61J9D7H2MOaItEjFgRj%3Fusp%3Ddrive_link&hl=en-GB
[06/12/2024, 14:19:06] Jay: - Create table schema
- Viewmodel
- Add page
- Edit page
- List page
- Delete
- Link to items/posts/queue
- Add tag detail when sending to ds side - update api
- Api to update existing with tag id
[06/12/2024, 14:19:06] Jay: /v3/api/custom/coinchord/admin/register
[06/12/2024, 14:21:41] Jay: Checkout
Work summary


Auth8
- Worked on the Create table schema, Viewmodel, Add pages ,Edit page,List page
- Working on the Link to items/posts/queue and the ds api for fetching and updating

Saba
- Tried making use of custom scraper model with perplexity to fetch details for latest posts

Coinchord
- updated webhook for hubspot to receive replies from hubspot crm and save to our db for the user and the admin portal
- Added logic for super admin - multiple admin - admins - 3 admins max
- Logging these activities

Voiceoutreach
- Completed voicemail logic using the playback start and end and closing of sockets to avoid the ai starting speech
- Added fallback hangup socket for calls if call is still on after being ended


https://app.mytechpassport.com/public/video-view?id=1Xi3MgO1dM81Xbk0wxo65VQOmMN5A-9ZC&st=public ‎<This message was edited>
[06/12/2024, 14:22:10] Jay: check in 
projects
- saba
- ⁠auth8
- ⁠coinchord
- ⁠voiceoutreach ‎<This message was edited>
[06/12/2024, 14:23:44] Jay: done
- Create table schema
- Viewmodel
- Add page
- List page
- ⁠Edit page
-pending
- Delete
- Link to items/posts/queue
- Add tag detail when sending to ds side - update api
- Api to update existing with tag id ‎<This message was edited>
[06/12/2024, 18:25:55] Jay: He pointed out 2 things
1. Timezone was in gmt which I've resolved to -5. 
2. If you keep telling the ai goodbye at the end it keeps responding with goodbye or have a nice day instead of hanging up the call
[09/12/2024, 13:08:00] Jay: https://www.defunkd.com/vintage-t-shirt-tag-dating-guide/
[09/12/2024, 14:32:54] Jay: Check out
Work summary

Auth8
- Completed tag guide logic for year ranges
- Updated db schemas for existing tables in the sequelize models and also updated queue logic for attaching tags
- Created new apis for ds side to pass existing tags and also to allow for posts by authentication key to be updated in the db

Coinchord
- Created engagements api
- Fetching engagements for an existing user and storing
- Allowing dynamic sending of responses through api through hubspot engagement email api

Voiceoutreach
- Modified opt_out logic to not fetch whitelisted numbers
- Modifying camping summary api to include opt out contacts
- Modifiying summary page

Courtmatchup
- Created new api for google login mobile using client creds

Saba
- Attempted to fix scrapper for meta and I blocking
- Failed to fetch multiple latest videos


https://app.mytechpassport.com/public/video-view?id=1ej5o-BxKOg8tfBbocCY4DBb9TQiP8OVc&st=public
[09/12/2024, 14:33:34] Jay: check in
projects
- voiceoutreach
- ⁠saba
- ⁠coinchord
- ⁠fluentflow
[09/12/2024, 18:51:35] Jay: {
    "splash_screen": "{\"bio\":\"Hello, welcome to mkd test club\",\"images\":[\"https://s3.us-east-2.amazonaws.com/com.mkdlabs.images/baas/courtmatchup/062198164323MrBeast_2023_%28cropped%29.jpg\"]}",
    "sport_ids": [
        {
            "sport_id": 106,
            "club_sport_id": 90
        },
        {
            "sport_id": 105,
            "club_sport_id": 89
        },
        {
            "sport_id": 107,
            "club_sport_id": 91
        }
    ],
    "courts": [
        {
            "sport_id": "105",
            "name": "Court A",
            "type": "1",
            "surface_id": "3",
            "court_id": 1733764960690
        },
        {
            "sport_id": "107",
            "name": "Court B",
            "type": "2",
            "surface_id": null,
            "court_id": 1733764960691
        }
    ],
    "club_location": "{\"lat\":43.8178667,\"lng\":-79.**************}",
    "sport_list": [
        {
            "name": "Cricket",
            "id": 106,
            "club_sport_id": 90
        },
        {
            "name": "Tennis",
            "id": 105,
            "club_sport_id": 89
        },
        {
            "name": "Pickleball",
            "id": 107,
            "club_sport_id": 91
        }
    ],
    "days_off": [],
    "opening_time": "08:00:00",
    "closing_time": "11:00:00",
    "max_players": 10,
    "account_details": [
        {
            "account_nickname": "Primary Account",
            "account_number": "************",
            "account_routing_number": "384",
            "account_type": "savings"
        }
    ],
    "account_settings": {
        "revenue_account": {
            "account_nickname": "Primary Account",
            "account_number": "************",
            "account_routing_number": "384",
            "account_type": "savings"
        },
        "coaches_account": {
            "account_nickname": "Primary Account",
            "account_number": "************",
            "account_routing_number": "384",
            "account_type": "savings"
        },
        "staff_account": {
            "account_nickname": "Primary Account",
            "account_number": "************",
            "account_routing_number": "384",
            "account_type": "savings"
        }
    },
    "pricing": [
        {
            "sport_id": "105",
            "type": "1",
            "surface_id": "3",
            "price_by_hours": [
                {
                    "start_time": "10:00:00",
                    "end_time": "15:00:00",
                    "rate": "50"
                },
                {
                    "start_time": "10:00:00",
                    "end_time": "14:00:00",
                    "rate": "70"
                }
            ]
        }
    ]
}
[10/12/2024, 14:15:16] Jay: Checkout
Work summary

Courtmatchup
- Created new programs dashboard api for users
- Modified existing schema for clubs pricing 

Coinchord
- Found a way to fetch the right engagements for the users and completed pending 
- Updated api and now rzeturning precise engagement replies via email for a particular ticket id

Voiceoutreach
- Added 10s window between farewell checks. Modified tool for hangup to still function besides the farewell checks since they are unreliable
- Added play assistance logic to check if the call has already been recognised as silence and ignore the beep_detected


https://app.mytechpassport.com/public/video-view?id=1lmjqFvjA4waRZh52MFYb95V-S-LKekgo&st=public
[10/12/2024, 14:16:57] Jay: check in
projects
- saba
- ⁠fluentflow
- ⁠voiceoureach
[10/12/2024, 14:39:33] Jay: Android: 
Username: <EMAIL>
PW: Sunnyday2099!
[10/12/2024, 16:15:33] Jay: /v3/api/custom/courtmatchup/user/clinics?filter=saturday
[11/12/2024, 14:03:27] Jay: Check out
Work summary


Voiceoutreach
- Wrote more async monitor services to control hangup which is not working properly
- Working on new fuzzy word service to handle it without using claude since it increases costs and fails

Saba
- Making use of page_read_engagement from fb and ig to attempt to get details for the personal profile by making an advance request access and handle invalid scopes manage_pages, pages_show_list

Courtmatchup
- Fixed mobile code login
- Added extra filters for programs and modified sql for fetching based on weeks and days


https://app.mytechpassport.com/public/video-view?id=1Y0T2Cu5rHh-36AoGNZED_6-JRyRZ0fEs&st=public
[11/12/2024, 14:04:05] Jay: check in
projects
- saba
- ⁠voiceoureach
- ⁠⁠fluentflow
[12/12/2024, 14:16:16] Jay: Checkout 
Work summary

Voiceoutreach
- Created alternative to isFinal from elevenlabs stream by making use of message gaps to identify the final goodbye for hangup
- Worked on new way to speed up interruption immediately we have a broken message being received
- Handling duplicates and added ignore_audio
- Attempted to speedup stop signal by processing chunks as they came in

Courtmatchup
- Updated booking flow for clinic and lesson
- Modified stats api

https://app.mytechpassport.com/public/video-view?id=1isE9xBEuzb8BPrz5MG_Hrs46ayMYpcqE&st=public
[12/12/2024, 14:16:29] Jay: check in
projects
- voiceoutreach
- ⁠saba
[12/12/2024, 14:16:59] Jay: Checkout 
Work summary

Voiceoutreach
- Created alternative to isFinal from elevenlabs stream by making use of message gaps to identify the final goodbye for hangup
- Worked on new way to speed up interruption immediately we have a broken message being received
- Handling duplicates and added ignore_audio
- Attempted to speedup stop signal by processing chunks as they came in

Courtmatchup
- Updated booking flow for clinic and lesson
- Modified stats api

https://app.mytechpassport.com/public/video-view?id=1isE9xBEuzb8BPrz5MG_Hrs46ayMYpcqE&st=public
‎[12/12/2024, 22:16:27] Jay: ‎image omitted
‎[12/12/2024, 22:16:28] Jay: ‎image omitted
‎[12/12/2024, 22:20:08] Jay: ‎image omitted
‎[12/12/2024, 22:20:43] Jay: ‎image omitted
‎[12/12/2024, 22:21:46] Jay: ‎image omitted
‎[13/12/2024, 10:41:23] Jay: ‎image omitted
[13/12/2024, 14:44:21] Jay: Check out
Work summary


Voiceoutreach
- Modified ignore_audio logic
- Allowing multiple clear logic 
- Blocking all add threads while stop signal has not been refreshed for better quality

Courtmatchup
- Fixed profile api
- Modified reservation apis for mobile
- Added sync for programs and lessons on mobile

Fluentflow
- Completed new scenario api logic for admin


https://app.mytechpassport.com/public/video-view?id=1Dy5bwFeKOLCOrqJKCz5naak0my-Owcsv&st=public
[13/12/2024, 14:44:35] Jay: check in
projects
- voiceoutreach
- ⁠saba
[16/12/2024, 13:04:50] Jay: https://medium.com/@yassineelboustani666/scraping-facebook-comments-using-python-e8b29b025d73
[16/12/2024, 13:16:17] Jay: https://github.com/itishosseinian/facebook-scraper
[16/12/2024, 14:33:05] Jay: Checkout
Work summary

Saba
- Created new services for Facebook and instagram
- Completed logic for Facebook scrapper for scrapping user details and tested in collab notebook
- Worked on instagram scrapper logic for fetching public profile data
- Unable to bypass popups yet

Vocieoutreach
- Worked on sudden hangup issues
- Made changes in elevenlabs service for “isFinish” marker


Courtmatchup
- Worked on update to lessons query logic 

fluentlfow
- Fixed blank theme error for admin api


https://app.mytechpassport.com/public/video-view?id=1cJLuVvdCppndXRbLP5zFG1VQFo2Z42ST&st=public
[16/12/2024, 14:33:18] Jay: check in
projects
- saba
- ⁠voiceoutreach
- ⁠pmj ‎<This message was edited>
[17/12/2024, 14:13:04] Jay: Check out
Work summary


Saba
- Completed a working service for fetching instagram profile, posts and data
- Updated fb scrapper to include latest links 
- Working on integration of Facebook data into category apis and logic to gather current 3 social media details and have them returned

Auth8
- Modified existing update api to include “new_tag” name is the existing tags in the db could not be used to classify the post on ds side

Courtmatchup
- Updated scheduling api for before logic
- Modified and formatted all existing timeslots


https://app.mytechpassport.com/public/video-view?id=1F-IeAVQKtc-QwS56Ia0M409sJQhp-EXf&st=public
[17/12/2024, 14:13:11] Jay: check in
projects
- saba
- ⁠voiceoutreach
[18/12/2024, 14:19:30] Jay: Checkout 
Work summary

Saba
- Fixed instagram scrapper logic
- Created new task apis for handling all seperate tasks at once in different asyncio threads
- Integrated fallback for each influencer social media name incase one does not exist inthe object and then make use of that to search for their videos pictures and data

Courtmactchup
- Created new apis for admin to edit club profile and fixed scheduler issue for attachments in emails
- Modified reservations api to not need type if reservation type is lesson

Voiceoutreach
- Modified ghl calendar prompt instruction on Arins side and our side to fix client issue with  non existing slots  being added
- Switched number field to allow for brackets and other foreign characters to be stored
- Created new settings object for storing keys on user settings table
- Fetching keys similar to ghl config keys
- Working on limits checker


https://app.mytechpassport.com/public/video-view?id=1Sqot7q2OW-RUsoPwmD5ypBVa76qZrAPw&st=public
[18/12/2024, 14:19:41] Jay: check in
projects
- saba
- ⁠vocieoutreach
[19/12/2024, 09:57:14] Jay: Skip to content
Skip to footer


Community
Jobs
Companies
Salaries
For Employers
HelpTerms of UsePrivacy & Ad ChoicesDo Not Sell Or Share My InformationCookie Consent ToolSecurity
Copyright ©️ 2008-2024. Glassdoor LLC. "Glassdoor," "Worklife Pro," "Bowls," and logo are proprietary trademarks of Glassdoor LLC.



Search
Location

Sign In

Software Engineering
Software Engineer
2d


I recently had a second technical interview which contained a live coding challenge, which I usually freeze up on . One of the issues was finding a bug in code. Even though I explained where the bug was in their business logic, I did not find the exact line of code. The feedback I got was very condescending, saying things like “I only knew how to talk to talk but struggled when coding”. It made me feel like I was lying to them even though I‘ve been coding for over 30 years. Any advice?

24 Comments

Share
14

Log in to comment
Apple 1
2d

I can sympathize. My pet peeve about these coding tests are that they promote the wrong type of behavior - for entry level kids out of school, they do significant practice time on leetcode and coding prep. These are all toy problems with some needed twist or insight to solve. However, in real life, we often need to work with other people’s code, novel code never written before or collaborate with other developers. Maybe coding assignments make sense for junior developers who don’t have a body of work , but they do put the more senior developers at a disadvantage because we typically don’t have time to do leetcode prep or even want to spend out precious free time doing that instead of other more fulfilling hobbies or time with loved ones. Having said that, I did prepare by reading and working on the entire problem sets from coding interview books but i resent having to do this. No other job makes you revert back to your school days.

Reply

Share
24
View 3 more replies
Apple 1
16h

Just one more point to add - these tests are mainly an exercise in pattern recognition. The more problems you practice, the more likely you will encounter a problem similar to what you have done before. So instead of truly measuring how good you are at thinking or analyzing a problem on the fly, the candidate saves time by immediately knowing the approach - this reduces stress because you don’t have to figure things out but can focus on presenting a clear explanation to the interviewer. It’s all literally gaming this system.

Share
2
C# Software Developer 1
1d

These code challenges can be demeaning and anxiety-inducing. Or we can flip the script. They want to see your skill, you show them. Take it as an invitation to show you don't need handholding when it comes to interpreting requirements, that you own your work, and you leverage your team resources.

Start by interrogateing the team about the product and dependencies, like you would when rolling onto a project, or building a unit for a peer's black box.

Then,
Assign roles to your reviewers: a stakeholder or user, a peer dev, and a Business Analyst.
-Make them clarify the business case and then write out a User Story with them.
-Make the peer dev describe the behavior of the bug just like it's a support ticket. If they have a problem with that, lean into the roleplay and subtly accuse them of throwing this problem over the wall for someone else to deal with. Treat this as an opportunity to learn about their culture of ownership.
-Emphasize how ridiculous it is to suppose a programmer would ever hand over code without building and testing it. So show them how you work with snippets in either AI or something like JSFiddle or .NetFiddle.
-Show off your good buddy Google Gmini: Extrapolate what's next in the chain of control, then identify a relevant design pattern and make AI prototype it on the fly, "to test something."
-Find some opportunity to search your github on-screen for some similar snippet of (well styled) code.
-Say "What I'm looking for here is..." That really gets them going.
-Toss in console output, preprocessor definitions, code comments, style improvements, support for some possible future use case and where that would fit.
-Send off an email to the stakeholder thanking them for their time.

Reply

Share
3
Software Engineer
Author
1d

Thank you for your very impressive and concise response. I never considered utilizing the AI tools that you mentioned. But that is a great idea. The one part that really struck me as odd was even though I pointed out the area of their business logic where I believe the bug was, they dismissed it and insisted that I focus on writing a unit test to reveal the bug. But later in the interview, the other member of the team, the team lead, stopped the interview short and told me that the bug was actually in the area I identified and was an inverse line of code in an embedded for loop which he admitted was very complex. So again, as another commenter wrote I think I dodged a bullet because this team either had a chip on their shoulder in regards to me or was just not a very reasonable group of people to work with. I can do much better. Thank you once again for your excellent suggestions and the best of luck to you.

Share
2
Senior Software Engineer 1
1d

It means that you dodged a bullet. I'm similar to you in that I'm more than 30 years in and self-taught. The thing that continues to annoy me is when I have a panel of fresh out of college people who are trying to use the same process that they were taught in college in the real world (I ran into these in Silicon Valley) and people who have such a narrow scope of what's acceptable for no reasonable or rational reason. They're usually disorganized, condescending buck passing types that make life one giant headache for no reason other than they're trying to entertain themselves at the expense of everyone else. Just realize that you might be in a bad spot now and need a paycheck, but getting accepted by the wrong employer can cause you health problems usually sooner rather than later. It's not exactly a comforting thought, but something to keep in mind.

Reply

Share
7
View 2 more replies
Applications Engineering 1
1d

I think this comment has merit. If they were condescending or rude during the interview it was no loss.

Share
4
SR. Fullstack Software Engineer 1
1d

I wish I could tell you there's some secret sauce but after 100+ interviews and 20yrs (30+ languages) it's really up to age and wage. If you cost or you're old, to Apple1's point, there's a LITANY of entry level kids that will cost less and do more for it.
If you didn't get it based on the tech, don't sweat it. It's not you, it's them looking for a young unicorn at a budget barrel price :) Treat it like dating: it's numbers and chemistry so you can't really force it

Reply

Share
2
Software Engineer
Author
1d

Thank you for your very unique and helpful perspective. One of my fail I think is I constantly question my abilities when someone else can do something I can’t. Even though I have coded in a variety of languages an OSes I wonder if I am keeping up enough on current things like AI or whatever to warrant consideration for some of the senior positions. I don’t work until three or 4 AM on code or building a Linux kernel like I used to, But should I really have to? That’s the question running around in my head. Thank you again and Merry Christmas 🎄.

Share
2
Software Engineering Manager 1
1d

Coding tests are a subtle age filter biased toward younger engineers. There a few ways to fight against the powers that be 1) Drill leet code for a few months 2) Aim for higher level positions that may not require a coding test (because your job is less about code 3) Publish a large body of work on GitHub and use that to "test out" of coding challenges. 4) Get in through a referral with the hiring team

Reply

Share
Software Engineer
Author
1d

Those are good suggestions. I have tried a couple times by sending various coding assessments that I’ve taken which I’ve scored very well on. I haven’t tried sending GitHub links but will also try that. Unfortunately, most of the places that I have interviewed with will not take no for an answer in regards to a coding challenge, even though my 40 years of experience and vast credentials should prove that I have not faked it over that time. But I will say that the vast majority of the problems I’ve had have been with younger teams doing the. Not that that means anything, but it is interesting. And I think at times I’ve done better on these coding challenges when it turns out that there’s always somebody that does better than me apparently.

Share
SD
Stuart Dryden
20h

I got GPT4o to generate coding examples with something wrong that i had to debug. I used this to pretty much learn the Bicep language from scratch (i know terraform). Before the interview i was using this method for a refresher and to challenge me with powershell and terraform. It is good because you can have a conversation with it, get feedback, ask it to keep score, and get it to explain the answer in detail when you fail an example. It can identify areas of weakness and you can ask for a tutorial example of anything it identifies. GPT... the new interview prep best friend :)

Reply

Share
Researcher 1
19h

Make them pay. It is the only way they can learn and makes you feel better. Send them a condescending email back to give them a taste of their own. They do it to you.

Reply

Share
Software Engineer
Author
12h

They are just not worth my effort. These were a bunch of Primadonna wannabes that probably just enjoy putting other people down that are more capable than they are.

Share
SR. Distinguished Engineer 1
16h

Also have 30+ years of experience. After 20 years at same company, I decided I needed something else. Found the modern software job interview process *REALLY* gruelling and the coding challenges were a big part of that. Even though the bulk of my career was done in open source and the interviewer just need to go to my github page to see the hundreds of thousands of lines of code I had written over the years, I still had to do these code challenges.

So, what I found out was that even though I did mediocre to bad on these challenges, nobody seemed to care. My resume was stellar and I was stilled offered the job at most of the places I was hired at.

Reply

Share
4
Software Engineer
Author
12h

I really appreciate your input and that’s a great idea. But I feel much better now after last night because a recruiter asked me to take a coding challenge at Hackerrank last night that they use to screen applicants. It was related to Java and consisted of four multiple-choice questions and a coding challenge that required me to write code to solve a pretty intense Java threading issue. I got three out of the four questions correct and did very well on the coding challenge as it took me only roughly 15 minutes to write the code and all tests passed the first time. It was a 90 minute challenge and I got the work done with over an hour remaining and scored 92.6%. So I feel pretty good about that and reassured myself that these idiots that interviewed me previously really don’t know what they’re talking about.

Share
1
Embedded Software Engineer 1
14h

I don't know if this is helpful, but when doing one of these exercises talk a lot as I go through the process, demonstrating my thinking and process. That way they can see my work and process even if I don't get the solution quite right.

Reply

Share
Software Engineer 2
11h

Same. I just freeze. I think I’m going to take early retirement. I’ve coded but not full time because of migration work , setting up pipelines as it seems they always change framework. Mostly did data I test and loading. Doesn’t seem to be a market for this and I don’t study as well as I used to

Reply

Share
Lead Software Developer 1
8h

Maybe consider talking through that problem out loud with a friend or family member who knows nothing about coding and see where they are getting confused and try to "dumb it down" enough for them to understand.

Reply

Share
Software Engineer 3
4h

find a company that uses real testing techniques instead of trap questions... good interviews for engineers are from engineers who both speak their language and understand the nature of an appropriate challenge inquiry. You experience should sing loudly in an appropriate interview.

Reply

Share
Related bowls
Explore All Bowls
image for bowl
Career Pivot
A place to share advice, questions, vent and discuss how to change careers and pivot in to and out of jobs.

363K

View

Join
image for bowl
Ask A Recruiter - Law
Interested in learning about your legal market? Professionals can help you navigate and find new opportunities.

638K

View

Join
image for bowl
Tech Strategy & Product
Bowl for existing folks in tech in strategy roles (e.g business ops) or product management

927K

View

Join
image for bowl
Interview Tips
Feel free to post any interview questions you might be asked and I’ll help you compose an effective response :)

341K

View

Join
image for bowl
Career Advice for Students
A place for students to ask questions and get advice from working professionals. Please do not post job referral requests.

2M

View

Join
Related Posts
avatar
Consulting Exit Opportunities
works at Deloitte
4y

When do you stop interviewing after getting a verbal offer that you verbally accepted (it may not be the dream job but checks right boxes—comp, benefits, good company), but you haven’t received the fo... read more
avatar
Tech
Principal Software Engineer
2y

So what do we think about working at FAANG companies? I personally have stayed away from interviewing with them, as I have heard horror stories about work life balance.
avatar
IBM GBS
works at Cognizant
2y

Hi all, after joining at ibm its been 1 month and,, i received 1 project call and they took my interview and directly assigned me on that project( time of interview i asked about the project,, but int... read more
avatar
Salary Negotiations
works at Southwire
2y

I got a promotion recently to Production Planner
During interviews I was asked my minimum salary I would accept, and that was eventually the amount I was offered. I accepted because of the amount of e... read more
avatar
Interview Tips
Senior Consultant
2y

I have a customer success manager interview. Looking for tips, advice, ways to prepare for the interview as it’s a transition from what I’m doing now.
avatar
Case Interview Prep
Change Manager
2y

Anyone looking to case prep for McK? If so, feel free to schedule some time.

calendly.com
Case Practice for McKinsey - Joe W
avatar
IT India Jobs
works at Infosys
2y

Hii Fishes,

Anybody know that after giving interview in Dell. In how many days i will get the feedback?
avatar
WITCH (Wipro/Infosys/Tata/Capgenini/HCL)
works at HCL Technologies
3y

I asked for interview reschedule at citius tech ..whether it will create a problem? bcz I have informed them to reschedule interview before 20 mins of interview
avatar
Sales
Account Manager
2y

Amazon Hey everyone , hope you are all well! I’m currently working as an account manager for and Eyewear company Luxottica managing retail and e-commerce distribution relationships. Amazon reached out... read more
avatar
Job Referrals in Canada
Senior Policy Advisor
2y

Hi everyone! I’m looking for a referral to Shopify. I’m highly experienced for the role I’m applying to and feel confident I can land an interview with a referral. Please let me know!
avatar
Jobs in Healthcare
works at Midwest Orthopaedics at RUSH
2y

I’m looking to change careers from CMA to Project coordinating/program managing. Any recommendations on how to increase my interview intake and/or how much I should be asking for?
Anything helps!! I a... read more
avatar
Walmart Global Tech
works at HCL Technologies
2y

can anyone help me with interview questions for Senior QA Engineer in walmart bangalore ?
avatar
Infosys STG
works at Tata Consultancy
2y

Why doesn't Infosys just declare result of an interview and REJECT if they don't wanna hire?? What's the point of having Review in progress status in portal since 2months ..I have already moved on ..i... read more
avatar
SAP Consultants India
works at Infosys
3y

What exactly do they askn in partner round-deloitte
Domain: Business consulting and Strategy.
avatar
Salaries in Tech
Assistant Vice President
2y

For those that recently got a job offer, how long did it take from Final Interview to Offer? And then how long from offer to start date?
avatar
KPMG Global Services
works at Wipro
2y

can someone tell their experience about kgs(kpmg global services), work culture and appraisals, I got interview invite via consultant, 5 yoe frond end developer

thanks in advance
avatar
Healthcare Administrators/Healthcare Leadership
Occupational Therapist
2y

I’m a fairly new grad. Got licensed in January and started working in February. I moved across the state for this job taking my family with me. Now 4 months in it’s such a toxic environment. Unethical... read more
avatar
All Things MBB
Senior Consultant
3y

Botched the math question on my BCG online assessment. The nerves got to me and I fell apart. I think I got the rest correct but Am I screwed now for an interview invite?
avatar
Middle East Consultants
Senior Associate
3y

Looking to relocate to the UAE in a strategy consulting role.
Current profile: 6 YOE, KPMG, Advisory support role in India


Does messaging HR in Linkedin work? I've been trying to get some leads, b... read more
avatar
Science & Research
works at Curia
2y

I have a job interview for a process chemist position later this week. What are some interview questions that are likely to pop up? Any advise on what questions to prepare and how to answer them?
Community Platform by Glassdoor.
Ask questions, find answers at the intersection of work and life. Post your answer above. You must log in or register to reply here. Want to read more from Glassdoor’s Career Forum? Browse other topics in Software Engineering: Post about code, products you are ideating, cool ideas for projects, or other engineering topics you are interested in.

See this content in...
App
Open
Browser-IconChrome
Continue
[19/12/2024, 14:00:58] Jay: Checkout
Work summary


Saba
- Created rate limiter services for preventing our ip from being banned
- Added monitor for threads to prevent data from being broken 
- Created YouTube scrapper service as backup if the apis hit their rate limits
- Working on proxies to handle ip being blocked

Courtmatchup
- Modified login api and added public api for fetching clubs on the mobile side


https://app.mytechpassport.com/public/video-view?id=142AAp_Yu6OKH_WfGreq9Qndp0boCmePX&st=public
[19/12/2024, 14:03:14] Jay: check in
projects
- voiceoutreach
- ⁠saba
‎[20/12/2024, 07:46:11] Jay: ‎video omitted
[20/12/2024, 14:24:39] Jay: Checkout 
Work summary

Skillgames
- Updated registration apis for staff and players
- Linking email templates to emails for custom mail sending including the default verification and confirmation mails
- Updated filter for admin portal for payers and their max scores

Courtmatchup
- Created tickets logic and added chat closed logic for either options to be used

Voiceoutreach
- Added drop-down and fluid slide for horizontal bar and centered all
- Working on identifying when one key is about to expire or estimate the duration a call is estimated to last so as not for calls to end abruptly

https://app.mytechpassport.com/public/video-view?id=1-SlAaX6blcvRbUsOqoodlxNGOZQyDT0l&st=public
[20/12/2024, 14:24:52] Jay: check in
projects
- voiceoutreach
- ⁠skillgames
- ⁠Saba
[22/12/2024, 08:52:14] Jay: https://www.loom.com/share/23496830b38b40deb4788f589a41bf28
[23/12/2024, 14:05:24] Jay: Checkout
Work summary

Voiceoutreach
- Worked on navbar ui 
- Created new settings page and added call configurations to control voice delays and output sensitivities
- Working on updated logic for keys 

Auth8
- added is_private field to existing api for displaying private keys/posts

Skillgames
- Worked on invalid token
- Added user email check before sending 
- Working on congratulations email delay/fail

Saba
- Working on request limit errors and ip blocking


https://app.mytechpassport.com/public/video-view?id=1IaXw8QgHqazJocsvKT8D5raIp9Z3izIk&st=public
[23/12/2024, 14:05:40] Jay: check in
projects
- saba
- ⁠voiceoutreach
[23/12/2024, 14:54:48] Jay: The backend and everything is working fine for 4,510 influencers
But the moment we start running the apis and threads

If they crape more than 7 a minute or a certain number per hour instagram and facebook flag us

For YouTube the api is useless for more than a certain number of requests per month and scrapping their data works but we are flagged after 7 per minute
[23/12/2024, 14:55:02] Jay: The backend and everything is working fine for 4,510 influencers
But the moment we start running the apis and threads
[23/12/2024, 14:55:10] Jay: If they crape more than 7 a minute or a certain number per hour instagram and facebook flag us

For YouTube the api is useless for more than a certain number of requests per month and scrapping their data works but we are flagged after 7 per minute
[24/12/2024, 14:10:41] Jay: Checkout
Work summary

Saba
- Created new rate limiting service and added a default size to the base category model being used in the api
- Working on socks and mitmproxy service to control traffic away from the main ip
- Working on separated ig-x code requests on seperate port

Skillgames
- Updated services for ages across the backend
- Added the update service directly to the login api to fetch and confirm if the age and age group are not a mismatch based on the users date of birth and update if it is
- Updated cronjob

Voiceoutreach
- Working on individual page updates and tab updates
- Updated colors and tabs


https://app.mytechpassport.com/public/video-view?id=1tuQ1qgYvuifdX8rW1cEc8Uci6P8IqxhK&st=public
[24/12/2024, 14:10:49] Jay: check in
projects
- saba
- ⁠voiceoutreach
[24/12/2024, 16:10:18] Jay: https://manaknightdigital.com/
‎[26/12/2024, 05:08:03] Jay: ‎image omitted
[27/12/2024, 11:39:40] Jay: @2348185760425 we need timeslots in form of ranges
Instead of

9:00
9:30
10:00

We need

9:00 - 09:30
09:30 - 10:00
[27/12/2024, 11:39:40] Jay: @2348185760425 please read ticket 21 as well, and provide me the api to save the settings
[27/12/2024, 11:39:40] Jay: @2348185760425 we would need to change the sports selection flow

We need to select SPORTS first
and based on that if there are other types, then only we show it
‎[27/12/2024, 11:39:40] Jay: ‎image omitted
[27/12/2024, 11:39:40] Jay: @2348185760425
[27/12/2024, 11:39:40] Jay: @2348185760425 need find buddy flow apis
[27/12/2024, 11:39:40] Jay: @2348185760425  please check the reservation api,
https://courtmatchup.mkdlabs.com/v3/api/custom/courtmatchup/club/reservations
{"error":true,"message":"All fields are required"}

what other required fields we need again?
{
    "sport_id": 109,
    "type": 1,
    "surface_id": 3,
    "date": "2024-12-27",
    "player_ids": [
        109,
        110,
        100
    ],
    "start_time": "19:00",
    "end_time": "21:00",
    "price": 0
}
[27/12/2024, 11:39:51] Jay: https://skillgames.mkdlabs.com/v3/api/custom/skillgames/staff/player-details?order=id,desc&page=1&limit=10&participant_id=160
Request Method:
GET
Status Code:
500 Internal Server Error


{
    "error": true,
    "message": "Unknown column 'skillgames_user.user_id' in 'where clause'"
}
[27/12/2024, 11:41:03] Jay: See here all of the images have individual year first image is 1980 is this data stored?
‎[27/12/2024, 11:41:03] Jay: ‎image omitted
‎[27/12/2024, 11:42:57] Jay: vulnerability_report_04_12_2024.pdf • ‎11 pages ‎document omitted
[27/12/2024, 14:21:32] Jay: Check out
Work summary

Skillgames
- Updated age group logic on create card apis
- Modified structure of ranges

Auth8
- Modified queues structure added specific year
- Updated all apis being used on ds side

Courtmacthup
- Modified duration for reservation hours logic being used on club and on mobile side

https://app.mytechpassport.com/public/video-view?id=1tuQ1qgYvuifdX8rW1cEc8Uci6P8IqxhK&st=public
[27/12/2024, 14:22:28] Jay: check in
projects
- ohie
- ⁠courtmatchup
- ⁠saba
- ⁠voiceoutreach
[30/12/2024, 13:33:24] Jay: good start. the container below menu need to be consistent. so do container with white background and fit ui in it
[30/12/2024, 13:33:25] Jay: you need consistent padding from top
[30/12/2024, 13:33:26] Jay: some is 50% and some is none some is 20px
[30/12/2024, 13:33:27] Jay: consistent
[30/12/2024, 13:33:28] Jay: everything should align left
[30/12/2024, 13:33:29] Jay: and tabs buttons like in mtp
[30/12/2024, 13:33:29] Jay: bot icon like u do
‎[30/12/2024, 13:33:30] Jay: ‎image omitted
‎[30/12/2024, 13:33:30] Jay: ‎image omitted
[30/12/2024, 13:33:31] Jay: change all buttons to black from dark blue
‎[30/12/2024, 13:33:31] Jay: ‎image omitted
‎[30/12/2024, 13:33:32] Jay: ‎image omitted
‎[30/12/2024, 13:33:32] Jay: ‎image omitted
‎[30/12/2024, 13:33:32] Jay: ‎image omitted
‎[30/12/2024, 13:33:33] Jay: ‎image omitted
‎[30/12/2024, 13:33:33] Jay: ‎image omitted
[30/12/2024, 13:33:33] Jay: [23/12/2024, 00:07:49] ~ Ryan Hk: good start. the container below menu need to be consistent. so do container with white background and fit ui in it
[23/12/2024, 00:08:10] ~ Ryan Hk: you need consistent padding from top
[23/12/2024, 00:08:23] ~ Ryan Hk: some is 50% and some is none some is 20px
[23/12/2024, 00:08:25] ~ Ryan Hk: consistent
[23/12/2024, 00:08:50] ~ Ryan Hk: everything should align left
[23/12/2024, 00:09:00] ~ Ryan Hk: and tabs buttons like in mtp
[23/12/2024, 00:09:07] ~ Ryan Hk: bot icon like u do
‎[23/12/2024, 00:09:27] ~ Ryan Hk: ‎<attached: 00003241-PHOTO-2024-12-23-00-09-27.jpg>
‎[23/12/2024, 00:09:43] ~ Ryan Hk: ‎<attached: 00003242-PHOTO-2024-12-23-00-09-43.jpg>
[23/12/2024, 00:09:59] ~ Ryan Hk: change all buttons to black from dark blue
‎[23/12/2024, 00:10:22] ~ Ryan Hk: ‎<attached: 00003244-PHOTO-2024-12-23-00-10-22.jpg>
‎[23/12/2024, 00:10:34] ~ Ryan Hk: ‎<attached: 00003245-PHOTO-2024-12-23-00-10-34.jpg>
‎[23/12/2024, 00:10:58] ~ Ryan Hk: ‎<attached: 00003246-PHOTO-2024-12-23-00-10-58.jpg>
‎[23/12/2024, 00:11:23] ~ Ryan Hk: ‎<attached: 00003247-PHOTO-2024-12-23-00-11-23.jpg>
‎[23/12/2024, 00:11:34] ~ Ryan Hk: ‎<attached: 00003248-PHOTO-2024-12-23-00-11-34.jpg>
[30/12/2024, 14:26:56] Jay: Checkout
Work summary

Ohie
- Modified backendsdk blacklisted fields to make sure details with ids and passports are always filtered out by default
- Modified backend apis where professionals are being fetched
- Adjusted permissions for user tables and removed all user profile and professional profile from customers
- Updated all custom queries and update apis

Courtmahtchup
- Made changes to apis for settings
- Created new apis for buddy subscription and unsubscribing
- Updated apis for joining a team and also seeing available requests

voiceoutreach
- Made feedback changes on the frontend for most pages


https://app.mytechpassport.com/public/video-view?id=1lbJC-pJkTu9tB0DLo7kY-svHgeqjiCsL&st=public
[30/12/2024, 14:27:37] Jay: check in
projects
- voiceoutreach
- ⁠saba
[31/12/2024, 13:55:19] Jay: In 2022 I journalled the whole year, every single day I wrote down the events of the day in a small pocket book and read it all on the 31st of dec 2022😂

In 2023 I started again but got lazy and ended up using memories from snap on the 31st to remember what had happened

In 2024 however I got lazier but smarter😂😂
3 things
1. I created a second snap account and added myself, then sent snaps to that account every day and I plan to login to it now to view them from january 1st till now
2. I paid for exbought a server and wrote scripts
[31/12/2024, 14:06:45] Jay: Checkout
Work summary

Courtmacthup
- Modified apis for subscription and fetching buddy requests to include weekly logic and also ntrp filter
- Modified club auth flow
- Worked on new club settings logic

Voiceoutreach
- Remade entire ui from feedback updates

Saba
- Created function to rotate user agents for instagram


https://app.mytechpassport.com/public/video-view?id=1zzNfPgxBlN4pVle8AeISDVu066SYUia_&st=public
[31/12/2024, 14:07:03] Jay: check in
projects
- voiceoutreach
‎[31/12/2024, 18:20:35] Jay: ‎video omitted
‎[31/12/2024, 19:07:30] Jay: ‎video omitted
‎[31/12/2024, 19:07:30] Jay: ‎video omitted
‎[31/12/2024, 19:07:30] Jay: ‎video omitted
‎[31/12/2024, 19:07:30] Jay: ‎video omitted
‎[31/12/2024, 19:07:31] Jay: ‎video omitted
‎[31/12/2024, 19:07:31] Jay: ‎video omitted
‎[31/12/2024, 20:01:53] Jay: ‎video omitted
[01/01/2025, 14:09:54] Jay: Checkout 
Work summary

Saba
- Debugged failing tasks to try and fix them
- Added another error wrapper to return an empty list and the ones it got instead of failing 

courtmatchup
- Modifying payment logic for clubs to 1% + 49 cents of what the charge their own members/non members  back as technology fee
- Created new cancel api for those who have previously requested to join a team
- Restricted editing and modifying to the main owner
- Modified club registration process and added slug field

Voiceoutreach
- Modified ui and worked on responsiveness


https://app.mytechpassport.com/public/video-view?id=1pI4fkxSYW0yMXk_Ah-e7n7etXii0pIdg&st=public
[01/01/2025, 14:10:09] Jay: check in
projects
- voiceoutreach
- ⁠auth8
- ⁠saba
‎[02/01/2025, 14:17:00] Jay: CourtMatchUp Buddy APIs.postman_collection (2).json ‎document omitted
‎[03/01/2025, 12:00:11] Jay: ‎audio omitted
[03/01/2025, 14:03:08] Jay: checkout
Work summary

Skillgames
- Created new api for both staffs and admins to be able to fetch players max 10 at a time for an event with their event stats all calculated live on the backend including all their stats based on age and gender from the reference sheet

Voicoeutreach
- Completed all pages based off new ui

Courtmatchup
- Modified my-requests api to include the requests to join in each request and also the all-requests to include the “sent” field for requests which can be cancelled by them
- Added fee_settings on club flow

https://app.mytechpassport.com/detail/83
[03/01/2025, 14:03:51] Jay: check in
projects
- saba
- ⁠voiceoutreach
‎[06/01/2025, 08:36:22] Jay: ‎image omitted
‎[06/01/2025, 08:37:11] Jay: ‎image omitted
[06/01/2025, 14:11:10] Jay: Checkout 
work summary

Courtmatchup 
- Created new api for editing details of an existing buddy request before reservation and if expired
- Modified all-requests and my-requests to include the ntrp for each player in player-details

Skillgames
- Working on updated from new excel file including script to upsert into the db 
- Worked on shipping address being called from user profile and also shipping fee based on country if not free for shipping


https://app.mytechpassport.com/public/video-view?id=1Kg6MVPVd2qbhoBBssloKQ4gpB_F-riPV&st=public
[06/01/2025, 14:12:51] Jay: check in
projects
- ⁠auth8
- ⁠skillgames
- ⁠voiceoutreach ‎<This message was edited>
[07/01/2025, 12:54:10] Jay: Baas V5
- Test 2fa
- Figure out and test Google login
- Figure out and test Apple login
- Figure out and test Facebook login
- Test and optimise existing Cronjobs on baas v5
- Backend db Sdk optimisation to prevent injection
- Server deployment ‎<This message was edited>
[07/01/2025, 14:31:33] Jay: Checkout
Work summary

Skillgames
- Updated backend with zip and shipping amounts logic
- Created new column and data for 35+ using the same data for 18+
- Created new seed logic and file

baas_v5
- Created script to make the folders and set up structure at once
- Tested with docker vs manual script
- Updated manual script with path flow
- Working on 2fa lambda update

Auth8
- Scraped all relevant data and working ons script to upload all in to s3. 
- Created new table model and working on joins for it

Courtmatchup
- Updated custom_fields for clubs to be able to decide which get which when logging in on the mobile side

Voiceoutreach
- Worked on feedback on buttons and padding
- Updated profile page and the others

https://app.mytechpassport.com/public/video-view?id=1Kg6MVPVd2qbhoBBssloKQ4gpB_F-riPV&st=public
[07/01/2025, 14:32:54] Jay: check in
projects
- baas v5
- ⁠auth8
- ⁠voiceoutreach ‎<This message was edited>
[07/01/2025, 14:36:08] Jay: Baas V5
- Test 2fa
- Figure out and test Google login
- Figure out and test Apple login
- Figure out and test Facebook login
- Test and optimise existing Cronjobs on baas v5
- Backend db Sdk optimisation to prevent injection
- Server deployment
- ⁠docker failing
[08/01/2025, 14:25:22] Jay: Check out 
Work summary

Courtmatchup
- Created new logic for extra data columns incase a club creates a new field not existing in the system so as for it to be stored
- Updated custom fields format structure

Auth8
- Created script to scrape and store all images from the defunkd website with their categories in a json format
- Manually cropped all 232 images from each of the 24 images scrapped by the script
- Created new model and table for the tag-guides

baas_v5
- Updated 2fa to copy over the lambda for custom projects
- Maintaining the original callback for social login
- Created new logic to allow for custom code imports
- Working on logic to make the google login work without extras after copying the lambdas

Saba
- Attempted to modify the category service to make a stricter response


https://app.mytechpassport.com/public/video-view?id=1XnxrxzgU7MvfpW8YJ_AyzW_L1A6Hu4Lf&st=public
[08/01/2025, 14:25:49] Jay: check in
projects
- baas_v5
- ⁠courtmatchup
- ⁠auth8
