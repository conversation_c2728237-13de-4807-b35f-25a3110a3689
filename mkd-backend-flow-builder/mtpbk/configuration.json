{"settings": {"globalKey": "key_1739375181123_vabwehfst", "databaseType": "mysql", "authType": "jwt", "timezone": "UTC", "dbHost": "localhost", "dbPort": "3306", "dbUser": "root", "dbPassword": "root", "dbName": "database_2025-02-12", "id": "project_1739375181123_mg9f998zm", "isPWA": false, "isMultiTenant": false, "model_namespace": "dealmaker", "payment_option": "one_time_payment"}, "models": [{"id": "model_1739375213616_d793h12az", "name": "user", "fields": [{"name": "id", "type": "primary key", "defaultValue": "", "validation": ""}, {"name": "email", "type": "string", "defaultValue": "", "validation": "required,email"}, {"name": "password", "type": "password", "defaultValue": "", "validation": "required"}, {"name": "login_type", "type": "mapping", "mapping": "0:<PERSON>,1:Google,2:Microsoft,3:Apple,4:Twitter,5:Facebook", "defaultValue": "0", "validation": "required,enum:0,1,2,3,4,5"}, {"name": "role_id", "type": "string", "defaultValue": "", "validation": ""}, {"name": "data", "type": "json", "defaultValue": "", "validation": ""}, {"name": "status", "type": "mapping", "mapping": "0:Active,1:Inactive,2:Suspend", "defaultValue": "0", "validation": "required,enum:0,1,2"}, {"name": "verify", "type": "boolean", "defaultValue": "0", "validation": "required"}, {"name": "two_factor_authentication", "type": "boolean", "defaultValue": "0", "validation": ""}, {"name": "company_id", "type": "integer", "defaultValue": "0", "validation": ""}, {"name": "created_at", "type": "timestamp", "defaultValue": "CURRENT_TIMESTAMP", "validation": "date"}]}, {"id": "model_1739375213616_7fgxumsnz", "name": "uploads", "fields": [{"name": "id", "type": "primary key", "defaultValue": "", "validation": ""}, {"name": "url", "type": "string", "defaultValue": "", "validation": "required"}, {"name": "caption", "type": "string", "defaultValue": "", "validation": ""}, {"name": "user_id", "type": "foreign key", "defaultValue": "", "validation": ""}, {"name": "width", "type": "integer", "defaultValue": "", "validation": ""}, {"name": "height", "type": "integer", "defaultValue": "", "validation": ""}, {"name": "type", "type": "mapping", "mapping": "0:Image,1:s3,2:Video,3:base64", "defaultValue": "0", "validation": "required,enum:0,1,2,3"}, {"name": "created_at", "type": "timestamp", "defaultValue": "CURRENT_TIMESTAMP", "validation": "date"}, {"name": "updated_at", "type": "timestamp", "defaultValue": "CURRENT_TIMESTAMP", "validation": "date"}]}, {"id": "model_1739375213616_hqkiu0yb8", "name": "job", "fields": [{"name": "id", "type": "primary key", "defaultValue": "", "validation": ""}, {"name": "task", "type": "string", "defaultValue": "", "validation": "required"}, {"name": "arguments", "type": "json", "defaultValue": "", "validation": ""}, {"name": "time_interval", "type": "string", "defaultValue": "once", "validation": ""}, {"name": "retries", "type": "integer", "defaultValue": "1", "validation": ""}, {"name": "status", "type": "mapping", "mapping": "0:Pending,1:Failed,2:Processing,3:Completed", "defaultValue": "0", "validation": ""}, {"name": "created_at", "type": "timestamp", "defaultValue": "CURRENT_TIMESTAMP", "validation": "date"}, {"name": "updated_at", "type": "timestamp", "defaultValue": "CURRENT_TIMESTAMP", "validation": "date"}]}, {"id": "model_1739375213616_jzbkemeuq", "name": "tokens", "fields": [{"name": "id", "type": "primary key", "defaultValue": "", "validation": ""}, {"name": "user_id", "type": "foreign key", "defaultValue": "", "validation": "required"}, {"name": "token", "type": "string", "defaultValue": "", "validation": "required"}, {"name": "code", "type": "string", "defaultValue": "", "validation": "required"}, {"name": "type", "type": "mapping", "mapping": "0:Access,1:<PERSON><PERSON><PERSON>,2:<PERSON><PERSON>,3:<PERSON><PERSON><PERSON>,4:<PERSON>", "defaultValue": "0", "validation": "required,enum:0,1,2,3,4"}, {"name": "data", "type": "json", "defaultValue": "", "validation": ""}, {"name": "status", "type": "mapping", "mapping": "0:Inactive,1:Active", "defaultValue": "1", "validation": "required,enum:0,1"}, {"name": "created_at", "type": "timestamp", "defaultValue": "CURRENT_TIMESTAMP", "validation": "date"}, {"name": "updated_at", "type": "timestamp", "defaultValue": "CURRENT_TIMESTAMP", "validation": "date"}, {"name": "expired_at", "type": "timestamp", "defaultValue": "", "validation": "date"}]}, {"id": "model_1739375213616_4vk0dntt7", "name": "preference", "fields": [{"name": "id", "type": "primary key", "defaultValue": "", "validation": ""}, {"name": "first_name", "type": "string", "defaultValue": "", "validation": ""}, {"name": "last_name", "type": "string", "defaultValue": "", "validation": ""}, {"name": "phone", "type": "string", "defaultValue": "", "validation": ""}, {"name": "photo", "type": "string", "defaultValue": "", "validation": ""}, {"name": "user_id", "type": "integer", "defaultValue": "", "validation": "required"}]}, {"id": "model_1739387889651_qu80g5uzb", "name": "stripe_product", "fields": [{"name": "id", "type": "primary key", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "create_at", "type": "date", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "update_at", "type": "datetime", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "name", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "product_id", "type": "int", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "stripe_id", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "object", "type": "longtext", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "status", "type": "int", "defaultValue": "", "validation": "", "validationOptions": {}}]}, {"id": "model_1739387889651_hmbxl7xjk", "name": "stripe_price", "fields": [{"name": "id", "type": "primary key", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "create_at", "type": "date", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "update_at", "type": "datetime", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "name", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "product_id", "type": "int", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "stripe_id", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "is_usage_metered", "type": "int", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "usage_limit", "type": "int", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "object", "type": "mediumtext", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "amount", "type": "float", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "trial_days", "type": "int", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "type", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "status", "type": "int", "defaultValue": "", "validation": "", "validationOptions": {}}]}, {"id": "model_1739389510607_6u480kzzl", "name": "community", "fields": [{"name": "id", "type": "primary key", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "title", "type": "string", "defaultValue": "", "validation": "required", "validationOptions": {}, "relation": ""}, {"name": "description", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}, "relation": ""}, {"name": "industry_id", "type": "integer", "defaultValue": "", "validation": "required", "validationOptions": {}, "relation": ""}, {"name": "guidelines", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}, "relation": ""}, {"name": "privacy", "type": "string", "defaultValue": "private", "validation": "required", "validationOptions": {}, "relation": ""}, {"name": "user_id", "type": "integer", "defaultValue": "", "validation": "required", "validationOptions": {}, "relation": "user"}, {"name": "created_at", "type": "datetime", "defaultValue": "", "validation": "required", "validationOptions": {}, "relation": ""}], "relations": []}, {"id": "model_1739389846824_ofyfrh8xf", "name": "industry", "fields": [{"name": "id", "type": "primary key", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "updated_at", "type": "datetime", "defaultValue": "", "validation": "required", "validationOptions": {}, "relation": ""}, {"name": "created_at", "type": "datetime", "defaultValue": "", "validation": "required", "validationOptions": {}, "relation": ""}, {"name": "name", "type": "string", "defaultValue": "", "validation": "required", "validationOptions": {}, "relation": ""}], "relations": []}, {"id": "model_1739389898656_2ougaxopl", "name": "community_member", "fields": [{"name": "id", "type": "primary key", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "user_id", "type": "integer", "defaultValue": "", "validation": "required", "validationOptions": {}, "relation": "user"}, {"name": "community_id", "type": "integer", "defaultValue": "", "validation": "required", "validationOptions": {}, "relation": "community"}, {"name": "role", "type": "string", "defaultValue": "", "validation": "required", "validationOptions": {}, "relation": ""}, {"name": "created_at", "type": "datetime", "defaultValue": "", "validation": "required", "validationOptions": {}, "relation": ""}], "relations": []}, {"id": "model_1739390002569_r77<PERSON>ylp", "name": "community_join_request", "fields": [{"name": "id", "type": "primary key", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "updated_at", "type": "datetime", "defaultValue": "", "validation": "required", "validationOptions": {}, "relation": ""}, {"name": "created_at", "type": "datetime", "defaultValue": "", "validation": "required", "validationOptions": {}, "relation": ""}, {"name": "user_id", "type": "integer", "defaultValue": "", "validation": "required", "validationOptions": {}, "relation": "user"}, {"name": "approver_id", "type": "integer", "defaultValue": "", "validation": "required", "validationOptions": {}, "relation": "user"}], "relations": ["user"]}, {"id": "model_1739390122366_t5m87vl9u", "name": "community_invite", "fields": [{"name": "id", "type": "primary key", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "status", "type": "integer", "defaultValue": "", "validation": "required", "validationOptions": {}, "relation": ""}, {"name": "created_at", "type": "datetime", "defaultValue": "", "validation": "required", "validationOptions": {}, "relation": ""}, {"name": "updated_at", "type": "datetime", "defaultValue": "", "validation": "required", "validationOptions": {}, "relation": ""}, {"name": "user_id", "type": "integer", "defaultValue": "", "validation": "required", "validationOptions": {}, "relation": "user"}, {"name": "community_id", "type": "integer", "defaultValue": "", "validation": "required", "validationOptions": {}, "relation": "community"}], "relations": ["community"]}, {"id": "model_1739390258635_zel2g27xd", "name": "referral", "fields": [{"name": "id", "type": "primary key", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "user_id", "type": "integer", "defaultValue": "", "validation": "required", "validationOptions": {}, "relation": "user"}, {"name": "job_title", "type": "string", "defaultValue": "", "validation": "required", "validationOptions": {}, "relation": ""}, {"name": "description", "type": "string", "defaultValue": "", "validation": "required", "validationOptions": {}, "relation": ""}, {"name": "industry_id", "type": "integer", "defaultValue": "", "validation": "required", "validationOptions": {}, "relation": "industry"}, {"name": "poy", "type": "string", "defaultValue": "", "validation": "required", "validationOptions": {}, "relation": ""}, {"name": "client_details", "type": "long text", "defaultValue": "", "validation": "required", "validationOptions": {}, "relation": ""}, {"name": "created_at", "type": "datetime", "defaultValue": "", "validation": "required", "validationOptions": {}, "relation": ""}, {"name": "updated_at", "type": "datetime", "defaultValue": "", "validation": "required", "validationOptions": {}, "relation": ""}], "relations": []}, {"id": "model_1739390432689_cut1lmfn5", "name": "referral_communities", "fields": [{"name": "id", "type": "primary key", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "referral_id", "type": "integer", "defaultValue": "", "validation": "required", "validationOptions": {}, "relation": "referral"}, {"name": "community_id", "type": "integer", "defaultValue": "", "validation": "required", "validationOptions": {}, "relation": "community"}, {"name": "is_primary", "type": "integer", "defaultValue": "0", "validation": "required", "validationOptions": {}, "relation": ""}, {"name": "created_at", "type": "datetime", "defaultValue": "", "validation": "required", "validationOptions": {}, "relation": ""}], "relations": []}, {"id": "model_1739390517810_wauvjbxci", "name": "activity_feed", "fields": [{"name": "id", "type": "primary key", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "updated_at", "type": "datetime", "defaultValue": "", "validation": "required", "validationOptions": {}, "relation": ""}, {"name": "user_id", "type": "integer", "defaultValue": "", "validation": "required", "validationOptions": {}, "relation": "user"}, {"name": "event_details", "type": "long text", "defaultValue": "", "validation": "required", "validationOptions": {}, "relation": ""}, {"name": "event_type", "type": "string", "defaultValue": "", "validation": "required", "validationOptions": {}, "relation": ""}], "relations": []}, {"id": "model_1739390588989_18o1oy010", "name": "meetings", "fields": [{"name": "id", "type": "primary key", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "status", "type": "string", "defaultValue": "", "validation": "required", "validationOptions": {}, "relation": ""}, {"name": "created_at", "type": "datetime", "defaultValue": "", "validation": "required", "validationOptions": {}, "relation": ""}, {"name": "updated_at", "type": "datetime", "defaultValue": "", "validation": "required", "validationOptions": {}, "relation": ""}, {"name": "topic", "type": "string", "defaultValue": "", "validation": "required", "validationOptions": {}, "relation": ""}, {"name": "description", "type": "string", "defaultValue": "", "validation": "required", "validationOptions": {}, "relation": ""}, {"name": "meeting_datetime", "type": "datetime", "defaultValue": "", "validation": "required", "validationOptions": {}, "relation": ""}, {"name": "created_by", "type": "integer", "defaultValue": "", "validation": "required", "validationOptions": {}, "relation": "user"}, {"name": "meeting_link", "type": "string", "defaultValue": "", "validation": "required", "validationOptions": {}, "relation": ""}], "relations": []}, {"id": "model_1739390693632_nhaofp9py", "name": "meeting_attendee", "fields": [{"name": "id", "type": "primary key", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "meeting_id", "type": "integer", "defaultValue": "", "validation": "required", "validationOptions": {}, "relation": "meetings"}, {"name": "user_id", "type": "integer", "defaultValue": "", "validation": "required", "validationOptions": {}, "relation": "user"}, {"name": "created_at", "type": "datetime", "defaultValue": "", "validation": "required", "validationOptions": {}, "relation": ""}, {"name": "updated_at", "type": "datetime", "defaultValue": "", "validation": "required", "validationOptions": {}, "relation": ""}], "relations": []}, {"id": "model_1739390766919_lh8bygfsm", "name": "integration", "fields": [{"name": "id", "type": "primary key", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "status", "type": "string", "defaultValue": "", "validation": "required", "validationOptions": {}, "relation": ""}, {"name": "name", "type": "string", "defaultValue": "", "validation": "required", "validationOptions": {}, "relation": ""}, {"name": "description", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}, "relation": ""}, {"name": "created_at", "type": "datetime", "defaultValue": "", "validation": "required", "validationOptions": {}, "relation": ""}], "relations": []}, {"id": "model_1739390837923_k6w8jn6yc", "name": "analytics_events", "fields": [{"name": "id", "type": "primary key", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "updated_at", "type": "datetime", "defaultValue": "", "validation": "required", "validationOptions": {}, "relation": ""}, {"name": "created_at", "type": "datetime", "defaultValue": "", "validation": "required", "validationOptions": {}, "relation": ""}, {"name": "details", "type": "long text", "defaultValue": "", "validation": "required", "validationOptions": {}, "relation": ""}, {"name": "user_id", "type": "integer", "defaultValue": "", "validation": "required", "validationOptions": {}, "relation": "user"}, {"name": "type", "type": "integer", "defaultValue": "", "validation": "required", "validationOptions": {}, "relation": ""}], "relations": []}, {"id": "model_1739390973868_svde5t77w", "name": "commission", "fields": [{"name": "id", "type": "primary key", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "commission_rate", "type": "string", "defaultValue": "", "validation": "required", "validationOptions": {}, "relation": ""}, {"name": "description", "type": "string", "defaultValue": "", "validation": "required", "validationOptions": {}, "relation": ""}, {"name": "created_at", "type": "datetime", "defaultValue": "", "validation": "required", "validationOptions": {}, "relation": ""}], "relations": []}], "routes": [{"id": "route_1739375223150_9a4tag659", "name": "Get communities", "flowData": {"nodes": [{"id": "url_node_1739384918667", "type": "mock-api", "position": {"x": 0, "y": -60}, "data": {"label": "Mock API", "apiname": "Get communities", "path": "/v1/api/dealmaker/user/community/communities", "method": "GET", "description": " Retrieves a list of available communities. This may include parameters for filtering (e.g., by industry, search terms). : Allows users to browse available communities to join.\n", "fields": [], "queryFields": [{"name": "industry", "type": "string", "validation": ""}, {"name": "search", "type": "string", "validation": "required"}], "responseFields": [{"name": "error", "type": "boolean", "validation": ""}, {"name": "list", "type": "array", "value": {"type": "object", "fields": [{"name": "id", "type": "integer"}, {"name": "title", "type": "string"}, {"name": "description", "type": "string"}, {"name": "guidelines", "type": "string"}, {"name": "privacy", "type": "string"}, {"name": "created_at", "type": "datetime"}, {"name": "updated_at", "type": "datetime"}]}}], "authType": "bearer", "outputType": "json", "statusCode": 200}, "width": 180, "height": 56, "selected": false, "dragging": false, "positionAbsolute": {"x": 0, "y": -60}}, {"id": "node_1739391750838", "type": "outputs", "position": {"x": 15, "y": 120}, "data": {"label": "Outputs", "fields": [{"name": "error", "type": "boolean", "validation": ""}, {"name": "list", "type": "array", "value": {"type": "object", "fields": [{"name": "industry_name", "type": "string"}, {"name": "name", "type": "string"}, {"name": "description", "type": "string"}, {"name": "guidelines", "type": "string"}, {"name": "privacy", "type": "integer"}, {"name": "id", "type": "integer"}]}}], "queryFields": [], "outputType": "json"}, "width": 180, "height": 56, "selected": true, "positionAbsolute": {"x": 15, "y": 120}, "dragging": false}, {"id": "node_1739391789971", "type": "auth", "position": {"x": 15, "y": 30}, "data": {"label": "<PERSON><PERSON>", "fields": [], "queryFields": [], "authType": "bearer"}, "width": 180, "height": 56, "selected": false, "positionAbsolute": {"x": 15, "y": 30}, "dragging": false}], "edges": [{"source": "node_1739391789971", "sourceHandle": null, "target": "node_1739391750838", "targetHandle": null, "id": "reactflow__edge-node_1739391789971-node_1739391750838"}, {"source": "url_node_1739384918667", "sourceHandle": null, "target": "node_1739391789971", "targetHandle": null, "id": "reactflow__edge-url_node_1739384918667-node_1739391789971"}]}}, {"id": "route_1739384922583_8gtz5qht9", "name": "Get single community detail", "flowData": {"nodes": [{"id": "url_node_1739385015380", "type": "mock-api", "position": {"x": 30, "y": -15}, "data": {"label": "Mock API", "apiname": "Get single community detail", "path": "/v1/api/dealmaker/user/community/communities/:community_id", "method": "GET", "description": "Retrieves details for a specific community. This includes information such as the community description, guidelines, and members. : Provides detailed information about a specific community.", "fields": [], "queryFields": [], "responseFields": [{"name": "error", "type": "boolean", "validation": ""}, {"name": "model", "type": "object", "value": [{"name": "id", "type": "integer"}, {"name": "name", "type": "string"}, {"name": "industry_name", "type": "string"}, {"name": "description", "type": "string"}, {"name": "guidelines", "type": "string"}, {"name": "privacy", "type": "string"}, {"name": "industry_description", "type": "string"}, {"name": "members", "type": "array"}]}], "authType": "bearer", "outputType": "json", "statusCode": 200}, "width": 180, "height": 56, "selected": true, "dragging": false, "positionAbsolute": {"x": 30, "y": -15}}], "edges": []}}, {"id": "route_1739385016991_zb2weo099", "name": "Create new community", "flowData": {"nodes": [{"id": "url_node_1739385043011", "type": "mock-api", "position": {"x": 60, "y": 15}, "data": {"label": "Mock API", "apiname": "Create new community", "path": "/v1/api/dealmaker/user/community/communities", "method": "POST", "description": "Creates a new community. Requires parameters like community title, description, industry, guidelines, and privacy settings. : Enables users (with the right permissions) to create new communities.", "fields": [{"name": "title", "type": "string", "validation": ""}, {"name": "description", "type": "string", "validation": ""}, {"name": "guidelines", "type": "string", "validation": ""}, {"name": "privacy", "type": "string", "validation": ""}, {"name": "industry_id", "type": "integer", "validation": ""}], "queryFields": [], "responseFields": [{"name": "error", "type": "boolean", "validation": ""}, {"name": "message", "type": "string", "validation": ""}], "authType": "bearer", "outputType": "json", "statusCode": 200}, "width": 180, "height": 56, "selected": true, "positionAbsolute": {"x": 60, "y": 15}, "dragging": false}], "edges": []}}, {"id": "route_1739385057034_u68filmso", "name": "Update Community", "flowData": {"nodes": [{"id": "url_node_1739385082908", "type": "mock-api", "position": {"x": 100, "y": 100}, "data": {"label": "Mock API", "apiname": "Update Community", "path": "/v1/api/dealmaker/user/community/update-communities/:community_id", "method": "POST", "description": " Updates an existing community. : Allows community admins to modify community details.", "fields": [{"name": "description", "type": "string", "validation": ""}, {"name": "guidelines", "type": "string", "validation": ""}, {"name": "privacy", "type": "string", "validation": ""}, {"name": "title", "type": "string", "validation": ""}], "queryFields": [], "responseFields": [{"name": "error", "type": "boolean", "validation": ""}, {"name": "message", "type": "string", "validation": ""}], "authType": "bearer", "outputType": "json", "statusCode": 200}, "width": 180, "height": 56, "selected": true, "dragging": false}], "edges": []}}, {"id": "route_1739385093054_6ov0b38lv", "name": "Get community members", "flowData": {"nodes": [{"id": "url_node_1739385131077", "type": "mock-api", "position": {"x": 105, "y": 105}, "data": {"label": "Mock API", "apiname": "Get community members", "path": "/v1/api/dealmaker/user/community/communities/:community_id/members", "method": "GET", "description": "Retrieves a list of members within a specific community. : Allows users to see who is participating in a community. Should support pagination.", "fields": [], "queryFields": [], "responseFields": [{"name": "error", "type": "boolean", "validation": ""}, {"name": "list", "type": "array", "value": {"type": "object", "fields": [{"name": "id", "type": "integer"}, {"name": "first_name", "type": "string"}, {"name": "last_name", "type": "string"}, {"name": "email", "type": "string"}]}}], "authType": "bearer", "outputType": "json", "statusCode": 200}, "width": 180, "height": 56, "selected": true, "positionAbsolute": {"x": 105, "y": 105}, "dragging": false}], "edges": []}}, {"id": "route_1739385185061_bxl49bjjr", "name": "Join a Community", "flowData": {"nodes": [{"id": "url_node_1739385214850", "type": "mock-api", "position": {"x": 45, "y": 45}, "data": {"label": "Mock API", "apiname": "Join a Community", "path": "/v1/api/dealmaker/user/community/communities/:community_id/join", "method": "POST", "description": "Allows a user to join a community. : Enables users to become members of a community.", "fields": [], "queryFields": [], "responseFields": [{"name": "error", "type": "boolean", "validation": ""}, {"name": "message", "type": "string", "validation": ""}], "authType": "bearer", "outputType": "json", "statusCode": 200}, "width": 180, "height": 56, "selected": true, "dragging": false, "positionAbsolute": {"x": 45, "y": 45}}], "edges": []}}, {"id": "route_1739385216051_vy3pu7t59", "name": "Leave a community", "flowData": {"nodes": [{"id": "url_node_1739385250376", "type": "mock-api", "position": {"x": 75, "y": 45}, "data": {"label": "Mock API", "apiname": "Leave a community", "path": "/v1/api/dealmaker/user/community/communities/:community_id/leave", "method": "POST", "description": "Allows a user to leave a community. : Allows users to stop being a member.", "fields": [{"name": "reason", "type": "string", "validation": ""}], "queryFields": [], "responseFields": [{"name": "error", "type": "boolean", "validation": ""}, {"name": "message", "type": "string", "validation": ""}], "authType": "none", "outputType": "json", "statusCode": 200}, "width": 180, "height": 56, "selected": true, "positionAbsolute": {"x": 75, "y": 45}, "dragging": false}], "edges": []}}, {"id": "route_1739385261436_pj7faruyo", "name": "Community Join requests", "flowData": {"nodes": [{"id": "url_node_1739385287934", "type": "mock-api", "position": {"x": 105, "y": 105}, "data": {"label": "Mock API", "apiname": "Community Join requests", "path": "/v1/api/dealmaker/user/community/communities/:community_id/requests", "method": "POST", "description": "Handles community join requests if approval is required. Should require approverId and status(approve,deny)", "fields": [{"name": "status", "type": "string", "validation": ""}], "queryFields": [], "responseFields": [{"name": "error", "type": "boolean", "validation": ""}, {"name": "message", "type": "string", "validation": ""}], "authType": "none", "outputType": "json", "statusCode": 200}, "width": 180, "height": 56, "selected": true, "positionAbsolute": {"x": 105, "y": 105}, "dragging": false}], "edges": []}}, {"id": "route_1739385290678_h38a7id5q", "name": "Handle a Join request", "flowData": {"nodes": [{"id": "url_node_1739385410307", "type": "mock-api", "position": {"x": 100, "y": 100}, "data": {"label": "Mock API", "apiname": "Handle a Join request", "path": "/v1/api/dealmaker/user/community/communities/:community_id/requests", "method": "POST", "description": "", "fields": [], "queryFields": [], "responseFields": [], "authType": "none", "outputType": "json", "statusCode": 200}}], "edges": []}}, {"id": "route_1739385419994_3lhn64zn4", "name": "Invite to community", "flowData": {"nodes": [{"id": "url_node_1739385447296", "type": "mock-api", "position": {"x": 100, "y": 100}, "data": {"label": "Mock API", "apiname": "Invite to community", "path": "/v1/api/dealmaker/user/community/communities/:community_id/invite", "method": "POST", "description": "Handles inviting other users to the community.", "fields": [{"name": "email", "type": "string", "validation": ""}, {"name": "user_id", "type": "string", "validation": ""}, {"name": "message", "type": "string", "validation": ""}], "queryFields": [], "responseFields": [], "authType": "bearer", "outputType": "json", "statusCode": 200}, "width": 180, "height": 56, "selected": true, "dragging": false}], "edges": []}}, {"id": "route_1739385448218_1bq4blwg0", "name": "Remove from community", "flowData": {"nodes": [{"id": "url_node_1739385468737", "type": "mock-api", "position": {"x": 100, "y": 100}, "data": {"label": "Mock API", "apiname": "Remove from community", "path": "/v1/api/dealmaker/user/community/communities/:community_id/members/:member_id", "method": "DELETE", "description": "Remove a member from a community (community Admin only).", "fields": [], "queryFields": [], "responseFields": [], "authType": "bearer", "outputType": "json", "statusCode": 200}, "width": 180, "height": 56, "selected": true, "dragging": false}], "edges": []}}, {"id": "route_1739385469770_27x1d2y62", "name": "Get Referrals", "flowData": {"nodes": [{"id": "url_node_1739385489925", "type": "mock-api", "position": {"x": 100, "y": 100}, "data": {"label": "Mock API", "apiname": "Get Referrals", "path": "/v1/api/dealmaker/user/referral/referrals", "method": "GET", "description": "Retrieves a list of referrals. This should support parameters for filtering (e.g., by community, industry, keywords, status, user). : Allows users to browse available referrals.", "fields": [], "queryFields": [{"name": "industry", "type": "string", "validation": ""}, {"name": "status", "type": "string", "validation": "required"}], "responseFields": [{"name": "error", "type": "boolean", "validation": ""}, {"name": "list", "type": "array", "value": {"type": "object", "fields": [{"name": "id", "type": "integer"}, {"name": "first_name", "type": "string"}, {"name": "last_name", "type": "string"}, {"name": "created_at", "type": "datetime"}, {"name": "link", "type": "string"}]}}], "authType": "none", "outputType": "json", "statusCode": 200}, "width": 180, "height": 56, "selected": true, "dragging": false}], "edges": []}}, {"id": "route_1739385500202_y047j4xmn", "name": "Get single referral details", "flowData": {"nodes": [{"id": "url_node_1739385509080", "type": "mock-api", "position": {"x": 100, "y": 100}, "data": {"label": "Mock API", "apiname": "Get single referral details", "path": "/v1/api/dealmaker/referral/referrals/:referral_id", "method": "GET", "description": "Retrieves details for a specific referral. : Provides detailed information about a specific referral post.\n", "fields": [], "queryFields": [], "responseFields": [{"name": "error", "type": "boolean", "validation": ""}, {"name": "model", "type": "object", "value": [{"name": "id", "type": "integer"}, {"name": "first_name", "type": "string"}, {"name": "last_name", "type": "string"}, {"name": "created_at", "type": "datetime"}, {"name": "updated_at", "type": "datetime"}, {"name": "link", "type": "string"}, {"name": "industry_id", "type": "integer"}, {"name": "community_id", "type": "integer"}, {"name": "community_name", "type": "string"}]}], "authType": "none", "outputType": "json", "statusCode": 200}, "width": 180, "height": 56, "selected": true, "dragging": false}], "edges": []}}, {"id": "route_1739385518101_j0jbsmmqf", "name": "Create a new referral", "flowData": {"nodes": [{"id": "url_node_1739385537298", "type": "mock-api", "position": {"x": 100, "y": 100}, "data": {"label": "Mock API", "apiname": "Create a new referral", "path": "/v1/api/dealmaker/referral/referrals", "method": "POST", "description": " Creates a new referral. Requires parameters such as job title, description, industry, pay, client detail etc. : Enables users to submit referral opportunities.\n", "fields": [{"name": "job_title", "type": "string", "validation": ""}, {"name": "description", "type": "string", "validation": ""}, {"name": "pay", "type": "string", "validation": ""}, {"name": "industry_id", "type": "integer", "validation": ""}, {"name": "user_id", "type": "integer", "validation": ""}], "queryFields": [], "responseFields": [{"name": "error", "type": "boolean", "validation": ""}, {"name": "message", "type": "string", "validation": ""}], "authType": "bearer", "outputType": "json", "statusCode": 200}, "width": 180, "height": 56, "selected": true, "dragging": false}], "edges": []}}, {"id": "route_1739385538291_fuzo8lpbm", "name": "Update an existing referral", "flowData": {"nodes": [{"id": "url_node_1739385560749", "type": "mock-api", "position": {"x": 100, "y": 100}, "data": {"label": "Mock API", "apiname": "Update an existing referral", "path": "/v1/api/dealmaker/user/update-referral/referrals/:referral_id", "method": "POST", "description": "Updates an existing referral. : Allows users to edit their own referrals.", "fields": [{"name": "pay", "type": "string", "validation": ""}, {"name": "industry_id", "type": "string", "validation": ""}, {"name": "job_title", "type": "string", "validation": ""}, {"name": "description", "type": "string", "validation": ""}], "queryFields": [], "responseFields": [], "authType": "bearer", "outputType": "json", "statusCode": 200}, "width": 180, "height": 56, "selected": true, "dragging": false}], "edges": []}}, {"id": "route_1739385568235_410dr7vhn", "name": "Delete a referral", "flowData": {"nodes": [{"id": "url_node_1739385585248", "type": "mock-api", "position": {"x": 100, "y": 100}, "data": {"label": "Mock API", "apiname": "Delete a referral", "path": "/v1/api/dealmaker/user/referral/referrals/:referral_id", "method": "DELETE", "description": "Deletes referral. : Allows users to delete their own referrals.", "fields": [], "queryFields": [], "responseFields": [], "authType": "bearer", "outputType": "json", "statusCode": 200}, "width": 180, "height": 56, "selected": true, "dragging": false}], "edges": []}}, {"id": "route_1739385664771_udgvrxnmu", "name": "Repost Referral to another community", "flowData": {"nodes": [{"id": "url_node_1739385685459", "type": "mock-api", "position": {"x": 100, "y": 100}, "data": {"label": "Mock API", "apiname": "Repost Referral to another community", "path": "/v1/api/dealmaker/user/referral/referrals/:referral_id/repost", "method": "POST", "description": "Reposts a referral to a different community. : Allows users to share a referral across multiple communities.", "fields": [{"name": "community_id", "type": "integer", "validation": ""}], "queryFields": [], "responseFields": [{"name": "error", "type": "boolean", "validation": ""}], "authType": "bearer", "outputType": "json", "statusCode": 200}, "width": 180, "height": 56, "selected": true, "dragging": false}], "edges": []}}, {"id": "route_1739385691797_z8gxp0c7d", "name": "Archive a referral", "flowData": {"nodes": [{"id": "url_node_1739385706606", "type": "mock-api", "position": {"x": 100, "y": 100}, "data": {"label": "Mock API", "apiname": "Archive a referral", "path": "/v1/api/dealmaker/user/referral/referrals/:referral_id/archive", "method": "POST", "description": "Archives a referral post. : Allows users to hide referral.", "fields": [{"name": "reason", "type": "string", "validation": ""}], "queryFields": [], "responseFields": [{"name": "error", "type": "boolean", "validation": ""}, {"name": "message", "type": "string", "validation": ""}], "authType": "bearer", "outputType": "json", "statusCode": 200}, "width": 180, "height": 56, "selected": true, "dragging": false}], "edges": []}}, {"id": "route_1739385714933_o0z3w5n6a", "name": "Unarchive a referral", "flowData": {"nodes": [{"id": "url_node_1739385725185", "type": "mock-api", "position": {"x": 100, "y": 100}, "data": {"label": "Mock API", "apiname": "Unarchive a referral", "path": "/v1/api/dealmaker/user/referral/referrals/:referral_id/unarchive", "method": "POST", "description": "Unarchives a referral post. : Allows users to unhide referral.", "fields": [], "queryFields": [], "responseFields": [{"name": "error", "type": "boolean", "validation": ""}, {"name": "message", "type": "string", "validation": ""}], "authType": "bearer", "outputType": "json", "statusCode": 200}, "width": 180, "height": 56, "selected": true, "dragging": false}], "edges": []}}, {"id": "route_1739385731307_c7sewh891", "name": "Get feed", "flowData": {"nodes": [{"id": "url_node_1739385744459", "type": "mock-api", "position": {"x": 100, "y": 100}, "data": {"label": "Mock API", "apiname": "Get feed", "path": "/v1/api/dealmaker/user/feed/feed", "method": "GET", "description": " Retrieves the activity feed for the user. : Allows users to view the latest updates from communities they are part of.", "fields": [], "queryFields": [], "responseFields": [{"name": "error", "type": "boolean", "validation": ""}, {"name": "list", "type": "array", "value": {"type": "object", "fields": [{"name": "id", "type": "integer"}, {"name": "event_type", "type": "string"}, {"name": "event_details", "type": "string"}, {"name": "created_at", "type": "datetime"}]}}], "authType": "bearer", "outputType": "json", "statusCode": 200}, "width": 180, "height": 56, "selected": true, "dragging": false}], "edges": []}}, {"id": "route_1739385753502_oqlewwa7m", "name": "Get single feed event details", "flowData": {"nodes": [{"id": "url_node_1739385776169", "type": "mock-api", "position": {"x": 100, "y": 100}, "data": {"label": "Mock API", "apiname": "Get single feed event details", "path": "/v1/api/dealmaker/user/feed/feed/event_id", "method": "GET", "description": "", "fields": [], "queryFields": [], "responseFields": [{"name": "error", "type": "boolean", "validation": ""}, {"name": "model", "type": "object", "value": [{"name": "id", "type": "integer"}, {"name": "event_type", "type": "string"}, {"name": "event_details", "type": "string"}, {"name": "created_at", "type": "datetime"}]}], "authType": "none", "outputType": "json", "statusCode": 200}}], "edges": []}}, {"id": "route_1739385788064_6kvf6edu6", "name": "Get meetings", "flowData": {"nodes": [{"id": "url_node_1739385797870", "type": "mock-api", "position": {"x": 100, "y": 100}, "data": {"label": "Mock API", "apiname": "Get meetings", "path": "/v1/api/dealmaker/user/meeting/meetings", "method": "GET", "description": "Retrieves a list of scheduled meetings. This should support parameters for filtering by date, community, user. : Enables users to manage their upcoming meetings.", "fields": [], "queryFields": [], "responseFields": [{"name": "error", "type": "boolean", "validation": ""}, {"name": "list", "type": "array", "value": {"type": "object", "fields": [{"name": "id", "type": "integer"}, {"name": "created_at", "type": "datetime"}, {"name": "updated_at", "type": "datetime"}, {"name": "link", "type": "string"}, {"name": "topic", "type": "string"}, {"name": "description", "type": "string"}, {"name": "meeting_datetime", "type": "datetime"}, {"name": "status", "type": "string"}, {"name": "integration", "type": "string"}]}}], "authType": "bearer", "outputType": "json", "statusCode": 200}, "width": 180, "height": 56, "selected": true, "dragging": false}], "edges": []}}, {"id": "route_1739385804262_qt7978o0f", "name": "Get single meeting detail", "flowData": {"nodes": [{"id": "url_node_1739385825126", "type": "mock-api", "position": {"x": 100, "y": 100}, "data": {"label": "Mock API", "apiname": "Get single meeting detail", "path": "/v1/api/dealmaker/user/meeting/meetings/:meeting_id", "method": "GET", "description": "Retrieves details for a specific feed event. For fetching further details about an event on the activity feed", "fields": [], "queryFields": [], "responseFields": [{"name": "error", "type": "boolean", "validation": ""}, {"name": "model", "type": "object", "value": [{"name": "id", "type": "integer"}, {"name": "event_type", "type": "string"}, {"name": "event_details", "type": "object", "fields": []}, {"name": "created_at", "type": "datetime"}, {"name": "updated_at", "type": "datetime"}]}], "authType": "bearer", "outputType": "json", "statusCode": 200}, "width": 180, "height": 56, "selected": true, "dragging": false}], "edges": []}}, {"id": "route_1739385832215_v05g2e705", "name": "Create new meeting", "flowData": {"nodes": [{"id": "url_node_1739385841536", "type": "mock-api", "position": {"x": 100, "y": 100}, "data": {"label": "Mock API", "apiname": "Create new meeting", "path": "/v1/api/dealmaker/user/meeting/meetings", "method": "POST", "description": "", "fields": [], "queryFields": [], "responseFields": [], "authType": "none", "outputType": "json", "statusCode": 200}}], "edges": []}}, {"id": "route_1739385850399_9hbft2has", "name": "Update an existing meeting", "flowData": {"nodes": [{"id": "url_node_1739385866705", "type": "mock-api", "position": {"x": 100, "y": 100}, "data": {"label": "Mock API", "apiname": "Update an existing meeting", "path": "/v1/api/dealmaker/user/meeting/meetings/:meeting_id", "method": "POST", "description": "", "fields": [], "queryFields": [], "responseFields": [], "authType": "none", "outputType": "json", "statusCode": 200}}], "edges": []}}, {"id": "route_1739385873401_qu56nh2xy", "name": "Cancel/Delete a meeting", "flowData": {"nodes": [{"id": "url_node_1739385897318", "type": "mock-api", "position": {"x": 100, "y": 100}, "data": {"label": "Mock API", "apiname": "Cancel/Delete a meeting", "path": "/v1/api/dealmaker/user/meeting/meetings/:meeting_id", "method": "DELETE", "description": "", "fields": [], "queryFields": [], "responseFields": [], "authType": "none", "outputType": "json", "statusCode": 200}}], "edges": []}}, {"id": "route_1739385898273_1vlu53txc", "name": "Join a meeting", "flowData": {"nodes": [{"id": "url_node_1739385917784", "type": "mock-api", "position": {"x": 100, "y": 100}, "data": {"label": "Mock API", "apiname": "Join a meeting", "path": "/v1/api/dealmaker/user/meeting/meetings/:meeting_id/join", "method": "POST", "description": "", "fields": [], "queryFields": [], "responseFields": [], "authType": "none", "outputType": "json", "statusCode": 200}}], "edges": []}}, {"id": "route_1739385952914_23a1ry71i", "name": "Admin - Manage commission plans", "flowData": {"nodes": [{"id": "url_node_1739385979924", "type": "mock-api", "position": {"x": 100, "y": 100}, "data": {"label": "Mock API", "apiname": "Admin - Manage commission plans", "path": "/v1/api/dealmaker/admin/commissions", "method": "POST", "description": "", "fields": [], "queryFields": [], "responseFields": [], "authType": "none", "outputType": "json", "statusCode": 200}}], "edges": []}}, {"id": "route_1739385980711_tgp00ehp1", "name": "List industries", "flowData": {"nodes": [{"id": "url_node_1739386000854", "type": "mock-api", "position": {"x": 100, "y": 100}, "data": {"label": "Mock API", "apiname": "List industries", "path": "/v1/api/dealmaker/user/utility/industries", "method": "GET", "description": "", "fields": [], "queryFields": [], "responseFields": [], "authType": "none", "outputType": "json", "statusCode": 200}}], "edges": []}}, {"id": "route_1739386006565_g5k30cwt9", "name": "Integrations", "flowData": {"nodes": [{"id": "url_node_1739386013203", "type": "mock-api", "position": {"x": 100, "y": 100}, "data": {"label": "Mock API", "apiname": "Integrations", "path": "/v1/api/dealmaker/user/utility/integrations", "method": "GET", "description": "", "fields": [], "queryFields": [], "responseFields": [], "authType": "none", "outputType": "json", "statusCode": 200}}], "edges": []}}, {"id": "route_1739386041212_qj8tyjr7n", "name": "Admin - set commissions ", "flowData": {"nodes": [{"id": "url_node_1739386052616", "type": "mock-api", "position": {"x": 100, "y": 100}, "data": {"label": "Mock API", "apiname": "Admin - set commissions ", "path": "/v1/api/dealmaker/admin/commissions", "method": "POST", "description": "", "fields": [], "queryFields": [], "responseFields": [], "authType": "none", "outputType": "json", "statusCode": 200}}], "edges": []}}, {"id": "route_1739386062398_ncz1n28co", "name": "Update commissions", "flowData": {"nodes": [{"id": "url_node_1739386076426", "type": "mock-api", "position": {"x": 100, "y": 100}, "data": {"label": "Mock API", "apiname": "Update commissions", "path": "/v1/api/dealmaker/admin/update-commissions", "method": "POST", "description": "", "fields": [], "queryFields": [], "responseFields": [], "authType": "none", "outputType": "json", "statusCode": 200}}], "edges": []}}, {"id": "route_1739386087400_xksxvb48n", "name": "Reports", "flowData": {"nodes": [{"id": "url_node_1739386100684", "type": "mock-api", "position": {"x": 100, "y": 100}, "data": {"label": "Mock API", "apiname": "Reports", "path": "/v1/api/dealmaker/admin/reports", "method": "GET", "description": "", "fields": [], "queryFields": [], "responseFields": [], "authType": "none", "outputType": "json", "statusCode": 200}}], "edges": []}}, {"id": "route_1739386110981_e8yyjh0ca", "name": "Admin- Get referral analytics data", "flowData": {"nodes": [{"id": "url_node_1739386122338", "type": "mock-api", "position": {"x": 100, "y": 100}, "data": {"label": "Mock API", "apiname": "Admin- Get referral analytics data", "path": "/v1/api/dealmaker/admin/reports/referrals", "method": "GET", "description": "", "fields": [], "queryFields": [], "responseFields": [], "authType": "none", "outputType": "json", "statusCode": 200}}], "edges": []}}, {"id": "route_1739386129532_uo04fn0zm", "name": "Admin - Analytics for payments", "flowData": {"nodes": [{"id": "url_node_1739386139810", "type": "mock-api", "position": {"x": 100, "y": 100}, "data": {"label": "Mock API", "apiname": "Admin - Analytics for payments", "path": "/v1/api/dealmaker/admin/reports/payments", "method": "GET", "description": "", "fields": [], "queryFields": [], "responseFields": [], "authType": "none", "outputType": "json", "statusCode": 200}}], "edges": []}}, {"id": "route_1739386147741_f7pg9j7oo", "name": "Engagement reports", "flowData": {"nodes": [{"id": "url_node_1739386162611", "type": "mock-api", "position": {"x": 100, "y": 100}, "data": {"label": "Mock API", "apiname": "Engagement reports", "path": "/v1/api/dealmaker/admin/reports/users", "method": "GET", "description": "", "fields": [], "queryFields": [], "responseFields": [], "authType": "none", "outputType": "json", "statusCode": 200}}], "edges": []}}, {"id": "route_1739389842815_mt3kwr8sj", "name": "Get All community", "method": "GET", "url": "/api/community", "flowData": {"nodes": [{"id": "url_node_1739389842815", "type": "url", "position": {"x": 100, "y": 100}, "data": {"label": "Url", "apiname": "Get All community", "path": "/api/community", "method": "GET"}}, {"id": "auth_node_1739389842815", "type": "auth", "position": {"x": 100, "y": 200}, "data": {"label": "<PERSON><PERSON>", "authType": "none"}}, {"id": "db_find_node_1739389842815", "type": "db-find", "position": {"x": 100, "y": 300}, "data": {"label": "Database Find", "model": "community", "operation": "find<PERSON>any", "query": "SELECT * FROM community", "resultVar": "communityResult"}}, {"id": "logic_node_1739389842815", "type": "logic", "position": {"x": 100, "y": 400}, "data": {"label": "Logic", "fields": [], "queryFields": [], "code": "//commented logic"}}, {"id": "output_node_1739389842815", "type": "outputs", "position": {"x": 100, "y": 500}, "data": {"label": "Response", "outputType": "json", "fields": [{"name": "id", "type": "number"}, {"name": "title", "type": "string"}, {"name": "description", "type": "string"}, {"name": "industry_id", "type": "integer"}, {"name": "guidelines", "type": "string"}, {"name": "privacy", "type": "string"}, {"name": "user_id", "type": "integer"}, {"name": "created_at", "type": "datetime"}], "resultVar": "communityResult", "statusCode": 200}}], "edges": [{"id": "url-to-auth_1739389842815", "source": "url_node_1739389842815", "target": "auth_node_1739389842815"}, {"id": "auth-to-db_1739389842815", "source": "auth_node_1739389842815", "target": "db_find_node_1739389842815"}, {"id": "db-to-logic_1739389842815", "source": "db_find_node_1739389842815", "target": "logic_node_1739389842815"}, {"id": "logic-to-output_1739389842815", "source": "logic_node_1739389842815", "target": "output_node_1739389842815"}]}}, {"id": "route_1739389842815_5ija6vxwh", "name": "Get One community", "method": "GET", "url": "/api/community/:id", "flowData": {"nodes": [{"id": "url_node_1739389842815_1", "type": "url", "position": {"x": 100, "y": 100}, "data": {"label": "Url", "apiname": "Get One community", "path": "/api/community/:id", "method": "GET", "queryFields": [{"name": "id", "type": "number", "validation": "required"}]}}, {"id": "auth_node_1739389842815_1", "type": "auth", "position": {"x": 100, "y": 200}, "data": {"label": "<PERSON><PERSON>", "authType": "none"}}, {"id": "db_query_node_1739389842815_1", "type": "db-query", "position": {"x": 100, "y": 300}, "data": {"label": "Database Find", "model": "community", "operation": "findOne", "query": "SELECT * FROM community WHERE id=id", "resultVar": "communityOneResult"}}, {"id": "logic_node_1739389842815_1", "type": "logic", "position": {"x": 100, "y": 400}, "data": {"label": "Logic", "fields": [], "queryFields": [], "code": "//commented logic"}}, {"id": "output_node_1739389842815_1", "type": "outputs", "position": {"x": 100, "y": 500}, "data": {"label": "Response", "outputType": "json", "fields": [{"name": "id", "type": "number"}, {"name": "title", "type": "string"}, {"name": "description", "type": "string"}, {"name": "industry_id", "type": "integer"}, {"name": "guidelines", "type": "string"}, {"name": "privacy", "type": "string"}, {"name": "user_id", "type": "integer"}, {"name": "created_at", "type": "datetime"}, {"name": "error", "type": "boolean"}], "resultVar": "communityOneResult", "statusCode": 200}}], "edges": [{"id": "url-to-auth_1739389842815_1", "source": "url_node_1739389842815_1", "target": "auth_node_1739389842815_1"}, {"id": "auth-to-db_1739389842815_1", "source": "auth_node_1739389842815_1", "target": "db_query_node_1739389842815_1"}, {"id": "db-to-logic_1739389842815_1", "source": "db_query_node_1739389842815_1", "target": "logic_node_1739389842815_1"}, {"id": "logic-to-output_1739389842815_1", "source": "logic_node_1739389842815_1", "target": "output_node_1739389842815_1"}]}}, {"id": "route_1739389842816_zl8uoyq5c", "name": "Create community", "method": "POST", "url": "/api/community", "flowData": {"nodes": [{"id": "url_node_1739389842815_3", "type": "url", "position": {"x": 100, "y": 100}, "data": {"label": "Url", "apiname": "Create community", "path": "/api/community", "method": "POST", "fields": [{"name": "title", "type": "string", "validation": ""}, {"name": "description", "type": "string", "validation": ""}, {"name": "industry_id", "type": "integer", "validation": ""}, {"name": "guidelines", "type": "string", "validation": ""}, {"name": "privacy", "type": "string", "validation": ""}, {"name": "user_id", "type": "integer", "validation": ""}, {"name": "created_at", "type": "datetime", "validation": ""}]}}, {"id": "auth_node_1739389842815_3", "type": "auth", "position": {"x": 100, "y": 200}, "data": {"label": "<PERSON><PERSON>", "authType": "none"}}, {"id": "db_insert_node_1739389842815_3", "type": "db-insert", "position": {"x": 100, "y": 300}, "data": {"label": "Database Insert", "model": "community", "operation": "create", "query": "INSERT INTO community (title, description, industry_id, guidelines, privacy, user_id, created_at)\n                      VALUES (:title, :description, :industry_id, :guidelines, :privacy, :user_id, :created_at)", "resultVar": "communityCreateResult"}}, {"id": "logic_node_1739389842815_3", "type": "logic", "position": {"x": 100, "y": 400}, "data": {"label": "Logic", "fields": [], "queryFields": [], "code": "//commented logic"}}, {"id": "output_node_1739389842815_3", "type": "outputs", "position": {"x": 100, "y": 500}, "data": {"label": "Response", "outputType": "json", "fields": [{"name": "error", "type": "boolean"}, {"name": "id", "type": "number"}], "resultVar": "communityCreateResult", "statusCode": 200}}], "edges": [{"id": "url-to-auth_1739389842815_3", "source": "url_node_1739389842815_3", "target": "auth_node_1739389842815_3"}, {"id": "auth-to-db_1739389842815_3", "source": "auth_node_1739389842815_3", "target": "db_insert_node_1739389842815_3"}, {"id": "db-to-logic_1739389842815_3", "source": "db_insert_node_1739389842815_3", "target": "logic_node_1739389842815_3"}, {"id": "logic-to-output_1739389842815_3", "source": "logic_node_1739389842815_3", "target": "output_node_1739389842815_3"}]}}, {"id": "route_1739389842816_pq7uuowef", "name": "Update community", "method": "PUT", "url": "/api/community/:id", "flowData": {"nodes": [{"id": "url_node_1739389842815_4", "type": "url", "position": {"x": 100, "y": 100}, "data": {"label": "Url", "apiname": "Update community", "path": "/api/community/:id", "method": "PUT", "queryFields": [{"name": "id", "type": "number", "validation": "required"}], "fields": [{"name": "title", "type": "string", "validation": "required"}, {"name": "description", "type": "string", "validation": ""}, {"name": "industry_id", "type": "integer", "validation": "required"}, {"name": "guidelines", "type": "string", "validation": ""}, {"name": "privacy", "type": "string", "validation": "required"}, {"name": "user_id", "type": "integer", "validation": "required"}, {"name": "created_at", "type": "datetime", "validation": "required"}]}}, {"id": "auth_node_1739389842815_4", "type": "auth", "position": {"x": 100, "y": 200}, "data": {"label": "<PERSON><PERSON>", "authType": "none"}}, {"id": "db_update_node_1739389842815_4", "type": "db-update", "position": {"x": 100, "y": 300}, "data": {"label": "Database Update", "model": "community", "operation": "update", "idField": "id", "query": "UPDATE community SET title=:title, description=:description, industry_id=:industry_id, guidelines=:guidelines, privacy=:privacy, user_id=:user_id, created_at=:created_at WHERE id=:id", "resultVar": "communityUpdateResult"}}, {"id": "logic_node_1739389842815_4", "type": "logic", "position": {"x": 100, "y": 400}, "data": {"label": "Logic", "fields": [], "queryFields": [], "code": "//commented logic"}}, {"id": "output_node_1739389842815_4", "type": "outputs", "position": {"x": 100, "y": 500}, "data": {"label": "Response", "outputType": "json", "fields": [{"name": "error", "type": "boolean"}, {"name": "id", "type": "number"}], "resultVar": "communityUpdateResult", "statusCode": 200}}], "edges": [{"id": "url-to-auth_1739389842815_4", "source": "url_node_1739389842815_4", "target": "auth_node_1739389842815_4"}, {"id": "auth-to-db_1739389842815_4", "source": "auth_node_1739389842815_4", "target": "db_update_node_1739389842815_4"}, {"id": "db-to-logic_1739389842815_4", "source": "db_update_node_1739389842815_4", "target": "logic_node_1739389842815_4"}, {"id": "logic-to-output_1739389842815_4", "source": "logic_node_1739389842815_4", "target": "output_node_1739389842815_4"}]}}, {"id": "route_1739389842815_l6g5b36cu", "name": "Delete One community", "method": "DELETE", "url": "/api/community/:id", "flowData": {"nodes": [{"id": "url_node_1739389842815_2", "type": "url", "position": {"x": 100, "y": 100}, "data": {"label": "Url", "apiname": "Delete One community", "path": "/api/community/:id", "method": "DELETE", "queryFields": [{"name": "id", "type": "number", "validation": "required"}]}}, {"id": "auth_node_1739389842815_2", "type": "auth", "position": {"x": 100, "y": 200}, "data": {"label": "<PERSON><PERSON>", "authType": "none"}}, {"id": "db_delete_node_1739389842815_2", "type": "db-delete", "position": {"x": 100, "y": 300}, "data": {"label": "Database Delete", "model": "community", "operation": "delete", "query": "DELETE FROM community WHERE id=id", "resultVar": "communityDeleteResult"}}, {"id": "logic_node_1739389842815_2", "type": "logic", "position": {"x": 100, "y": 400}, "data": {"label": "Logic", "fields": [], "queryFields": [], "code": "//commented logic"}}, {"id": "output_node_1739389842815_2", "type": "outputs", "position": {"x": 100, "y": 500}, "data": {"label": "Response", "outputType": "json", "fields": [{"name": "error", "type": "boolean"}, {"name": "id", "type": "integer"}], "statusCode": 200, "resultVar": "communityDeleteResult"}}], "edges": [{"id": "url-to-auth_1739389842815_2", "source": "url_node_1739389842815_2", "target": "auth_node_1739389842815_2"}, {"id": "auth-to-db_1739389842815_2", "source": "auth_node_1739389842815_2", "target": "db_delete_node_1739389842815_2"}, {"id": "db-to-logic_1739389842815_2", "source": "db_delete_node_1739389842815_2", "target": "logic_node_1739389842815_2"}, {"id": "logic-to-output_1739389842815_2", "source": "logic_node_1739389842815_2", "target": "output_node_1739389842815_2"}]}}, {"id": "route_1739389894984_7haxq3ula", "name": "Get All industry", "method": "GET", "url": "/api/industry", "flowData": {"nodes": [{"id": "url_node_1739389894984", "type": "url", "position": {"x": 100, "y": 100}, "data": {"label": "Url", "apiname": "Get All industry", "path": "/api/industry", "method": "GET"}}, {"id": "auth_node_1739389894984", "type": "auth", "position": {"x": 100, "y": 200}, "data": {"label": "<PERSON><PERSON>", "authType": "none"}}, {"id": "db_find_node_1739389894984", "type": "db-find", "position": {"x": 100, "y": 300}, "data": {"label": "Database Find", "model": "industry", "operation": "find<PERSON>any", "query": "SELECT * FROM industry", "resultVar": "industryResult"}}, {"id": "logic_node_1739389894984", "type": "logic", "position": {"x": 100, "y": 400}, "data": {"label": "Logic", "fields": [], "queryFields": [], "code": "//commented logic"}}, {"id": "output_node_1739389894984", "type": "outputs", "position": {"x": 100, "y": 500}, "data": {"label": "Response", "outputType": "json", "fields": [{"name": "id", "type": "number"}, {"name": "updated_at", "type": "datetime"}, {"name": "created_at", "type": "datetime"}, {"name": "name", "type": "string"}], "resultVar": "industryResult", "statusCode": 200}}], "edges": [{"id": "url-to-auth_1739389894984", "source": "url_node_1739389894984", "target": "auth_node_1739389894984"}, {"id": "auth-to-db_1739389894984", "source": "auth_node_1739389894984", "target": "db_find_node_1739389894984"}, {"id": "db-to-logic_1739389894984", "source": "db_find_node_1739389894984", "target": "logic_node_1739389894984"}, {"id": "logic-to-output_1739389894984", "source": "logic_node_1739389894984", "target": "output_node_1739389894984"}]}}, {"id": "route_1739389894984_bso93cabm", "name": "Get One industry", "method": "GET", "url": "/api/industry/:id", "flowData": {"nodes": [{"id": "url_node_1739389894984_1", "type": "url", "position": {"x": 100, "y": 100}, "data": {"label": "Url", "apiname": "Get One industry", "path": "/api/industry/:id", "method": "GET", "queryFields": [{"name": "id", "type": "number", "validation": "required"}]}}, {"id": "auth_node_1739389894984_1", "type": "auth", "position": {"x": 100, "y": 200}, "data": {"label": "<PERSON><PERSON>", "authType": "none"}}, {"id": "db_query_node_1739389894984_1", "type": "db-query", "position": {"x": 100, "y": 300}, "data": {"label": "Database Find", "model": "industry", "operation": "findOne", "query": "SELECT * FROM industry WHERE id=id", "resultVar": "industryOneResult"}}, {"id": "logic_node_1739389894984_1", "type": "logic", "position": {"x": 100, "y": 400}, "data": {"label": "Logic", "fields": [], "queryFields": [], "code": "//commented logic"}}, {"id": "output_node_1739389894984_1", "type": "outputs", "position": {"x": 100, "y": 500}, "data": {"label": "Response", "outputType": "json", "fields": [{"name": "id", "type": "number"}, {"name": "updated_at", "type": "datetime"}, {"name": "created_at", "type": "datetime"}, {"name": "name", "type": "string"}, {"name": "error", "type": "boolean"}], "resultVar": "industryOneResult", "statusCode": 200}}], "edges": [{"id": "url-to-auth_1739389894984_1", "source": "url_node_1739389894984_1", "target": "auth_node_1739389894984_1"}, {"id": "auth-to-db_1739389894984_1", "source": "auth_node_1739389894984_1", "target": "db_query_node_1739389894984_1"}, {"id": "db-to-logic_1739389894984_1", "source": "db_query_node_1739389894984_1", "target": "logic_node_1739389894984_1"}, {"id": "logic-to-output_1739389894984_1", "source": "logic_node_1739389894984_1", "target": "output_node_1739389894984_1"}]}}, {"id": "route_1739389894984_lbgx68jf6", "name": "Create industry", "method": "POST", "url": "/api/industry", "flowData": {"nodes": [{"id": "url_node_1739389894984_3", "type": "url", "position": {"x": 100, "y": 100}, "data": {"label": "Url", "apiname": "Create industry", "path": "/api/industry", "method": "POST", "fields": [{"name": "updated_at", "type": "datetime", "validation": ""}, {"name": "created_at", "type": "datetime", "validation": ""}, {"name": "name", "type": "string", "validation": ""}]}}, {"id": "auth_node_1739389894984_3", "type": "auth", "position": {"x": 100, "y": 200}, "data": {"label": "<PERSON><PERSON>", "authType": "none"}}, {"id": "db_insert_node_1739389894984_3", "type": "db-insert", "position": {"x": 100, "y": 300}, "data": {"label": "Database Insert", "model": "industry", "operation": "create", "query": "INSERT INTO industry (updated_at, created_at, name)\n                      VALUES (:updated_at, :created_at, :name)", "resultVar": "industryCreateResult"}}, {"id": "logic_node_1739389894984_3", "type": "logic", "position": {"x": 100, "y": 400}, "data": {"label": "Logic", "fields": [], "queryFields": [], "code": "//commented logic"}}, {"id": "output_node_1739389894984_3", "type": "outputs", "position": {"x": 100, "y": 500}, "data": {"label": "Response", "outputType": "json", "fields": [{"name": "error", "type": "boolean"}, {"name": "id", "type": "number"}], "resultVar": "industryCreateResult", "statusCode": 200}}], "edges": [{"id": "url-to-auth_1739389894984_3", "source": "url_node_1739389894984_3", "target": "auth_node_1739389894984_3"}, {"id": "auth-to-db_1739389894984_3", "source": "auth_node_1739389894984_3", "target": "db_insert_node_1739389894984_3"}, {"id": "db-to-logic_1739389894984_3", "source": "db_insert_node_1739389894984_3", "target": "logic_node_1739389894984_3"}, {"id": "logic-to-output_1739389894984_3", "source": "logic_node_1739389894984_3", "target": "output_node_1739389894984_3"}]}}, {"id": "route_1739389894984_0078vapox", "name": "Update industry", "method": "PUT", "url": "/api/industry/:id", "flowData": {"nodes": [{"id": "url_node_1739389894984_4", "type": "url", "position": {"x": 100, "y": 100}, "data": {"label": "Url", "apiname": "Update industry", "path": "/api/industry/:id", "method": "PUT", "queryFields": [{"name": "id", "type": "number", "validation": "required"}], "fields": [{"name": "updated_at", "type": "datetime", "validation": "required"}, {"name": "created_at", "type": "datetime", "validation": "required"}, {"name": "name", "type": "string", "validation": "required"}]}}, {"id": "auth_node_1739389894984_4", "type": "auth", "position": {"x": 100, "y": 200}, "data": {"label": "<PERSON><PERSON>", "authType": "none"}}, {"id": "db_update_node_1739389894984_4", "type": "db-update", "position": {"x": 100, "y": 300}, "data": {"label": "Database Update", "model": "industry", "operation": "update", "idField": "id", "query": "UPDATE industry SET updated_at=:updated_at, created_at=:created_at, name=:name WHERE id=:id", "resultVar": "industryUpdateResult"}}, {"id": "logic_node_1739389894984_4", "type": "logic", "position": {"x": 100, "y": 400}, "data": {"label": "Logic", "fields": [], "queryFields": [], "code": "//commented logic"}}, {"id": "output_node_1739389894984_4", "type": "outputs", "position": {"x": 100, "y": 500}, "data": {"label": "Response", "outputType": "json", "fields": [{"name": "error", "type": "boolean"}, {"name": "id", "type": "number"}], "resultVar": "industryUpdateResult", "statusCode": 200}}], "edges": [{"id": "url-to-auth_1739389894984_4", "source": "url_node_1739389894984_4", "target": "auth_node_1739389894984_4"}, {"id": "auth-to-db_1739389894984_4", "source": "auth_node_1739389894984_4", "target": "db_update_node_1739389894984_4"}, {"id": "db-to-logic_1739389894984_4", "source": "db_update_node_1739389894984_4", "target": "logic_node_1739389894984_4"}, {"id": "logic-to-output_1739389894984_4", "source": "logic_node_1739389894984_4", "target": "output_node_1739389894984_4"}]}}, {"id": "route_1739389894984_gtofr20xa", "name": "Delete One industry", "method": "DELETE", "url": "/api/industry/:id", "flowData": {"nodes": [{"id": "url_node_1739389894984_2", "type": "url", "position": {"x": 100, "y": 100}, "data": {"label": "Url", "apiname": "Delete One industry", "path": "/api/industry/:id", "method": "DELETE", "queryFields": [{"name": "id", "type": "number", "validation": "required"}]}}, {"id": "auth_node_1739389894984_2", "type": "auth", "position": {"x": 100, "y": 200}, "data": {"label": "<PERSON><PERSON>", "authType": "none"}}, {"id": "db_delete_node_1739389894984_2", "type": "db-delete", "position": {"x": 100, "y": 300}, "data": {"label": "Database Delete", "model": "industry", "operation": "delete", "query": "DELETE FROM industry WHERE id=id", "resultVar": "industryDeleteResult"}}, {"id": "logic_node_1739389894984_2", "type": "logic", "position": {"x": 100, "y": 400}, "data": {"label": "Logic", "fields": [], "queryFields": [], "code": "//commented logic"}}, {"id": "output_node_1739389894984_2", "type": "outputs", "position": {"x": 100, "y": 500}, "data": {"label": "Response", "outputType": "json", "fields": [{"name": "error", "type": "boolean"}, {"name": "id", "type": "integer"}], "statusCode": 200, "resultVar": "industryDeleteResult"}}], "edges": [{"id": "url-to-auth_1739389894984_2", "source": "url_node_1739389894984_2", "target": "auth_node_1739389894984_2"}, {"id": "auth-to-db_1739389894984_2", "source": "auth_node_1739389894984_2", "target": "db_delete_node_1739389894984_2"}, {"id": "db-to-logic_1739389894984_2", "source": "db_delete_node_1739389894984_2", "target": "logic_node_1739389894984_2"}, {"id": "logic-to-output_1739389894984_2", "source": "logic_node_1739389894984_2", "target": "output_node_1739389894984_2"}]}}, {"id": "route_1739389997699_t59imijm8", "name": "Get All community_member", "method": "GET", "url": "/api/community_member", "flowData": {"nodes": [{"id": "url_node_1739389997699", "type": "url", "position": {"x": 100, "y": 100}, "data": {"label": "Url", "apiname": "Get All community_member", "path": "/api/community_member", "method": "GET"}}, {"id": "auth_node_1739389997699", "type": "auth", "position": {"x": 100, "y": 200}, "data": {"label": "<PERSON><PERSON>", "authType": "none"}}, {"id": "db_find_node_1739389997699", "type": "db-find", "position": {"x": 100, "y": 300}, "data": {"label": "Database Find", "model": "community_member", "operation": "find<PERSON>any", "query": "SELECT * FROM community_member", "resultVar": "community_memberR<PERSON>ult"}}, {"id": "logic_node_1739389997699", "type": "logic", "position": {"x": 100, "y": 400}, "data": {"label": "Logic", "fields": [], "queryFields": [], "code": "//commented logic"}}, {"id": "output_node_1739389997699", "type": "outputs", "position": {"x": 100, "y": 500}, "data": {"label": "Response", "outputType": "json", "fields": [{"name": "id", "type": "number"}, {"name": "user_id", "type": "integer"}, {"name": "community_id", "type": "integer"}, {"name": "role", "type": "string"}, {"name": "created_at", "type": "datetime"}], "resultVar": "community_memberR<PERSON>ult", "statusCode": 200}}], "edges": [{"id": "url-to-auth_1739389997699", "source": "url_node_1739389997699", "target": "auth_node_1739389997699"}, {"id": "auth-to-db_1739389997699", "source": "auth_node_1739389997699", "target": "db_find_node_1739389997699"}, {"id": "db-to-logic_1739389997699", "source": "db_find_node_1739389997699", "target": "logic_node_1739389997699"}, {"id": "logic-to-output_1739389997699", "source": "logic_node_1739389997699", "target": "output_node_1739389997699"}]}}, {"id": "route_1739389997699_3pavvq1hq", "name": "Get One community_member", "method": "GET", "url": "/api/community_member/:id", "flowData": {"nodes": [{"id": "url_node_1739389997699_1", "type": "url", "position": {"x": 100, "y": 100}, "data": {"label": "Url", "apiname": "Get One community_member", "path": "/api/community_member/:id", "method": "GET", "queryFields": [{"name": "id", "type": "number", "validation": "required"}]}}, {"id": "auth_node_1739389997699_1", "type": "auth", "position": {"x": 100, "y": 200}, "data": {"label": "<PERSON><PERSON>", "authType": "none"}}, {"id": "db_query_node_1739389997699_1", "type": "db-query", "position": {"x": 100, "y": 300}, "data": {"label": "Database Find", "model": "community_member", "operation": "findOne", "query": "SELECT * FROM community_member WHERE id=id", "resultVar": "community_memberOneResult"}}, {"id": "logic_node_1739389997699_1", "type": "logic", "position": {"x": 100, "y": 400}, "data": {"label": "Logic", "fields": [], "queryFields": [], "code": "//commented logic"}}, {"id": "output_node_1739389997699_1", "type": "outputs", "position": {"x": 100, "y": 500}, "data": {"label": "Response", "outputType": "json", "fields": [{"name": "id", "type": "number"}, {"name": "user_id", "type": "integer"}, {"name": "community_id", "type": "integer"}, {"name": "role", "type": "string"}, {"name": "created_at", "type": "datetime"}, {"name": "error", "type": "boolean"}], "resultVar": "community_memberOneResult", "statusCode": 200}}], "edges": [{"id": "url-to-auth_1739389997699_1", "source": "url_node_1739389997699_1", "target": "auth_node_1739389997699_1"}, {"id": "auth-to-db_1739389997699_1", "source": "auth_node_1739389997699_1", "target": "db_query_node_1739389997699_1"}, {"id": "db-to-logic_1739389997699_1", "source": "db_query_node_1739389997699_1", "target": "logic_node_1739389997699_1"}, {"id": "logic-to-output_1739389997699_1", "source": "logic_node_1739389997699_1", "target": "output_node_1739389997699_1"}]}}, {"id": "route_1739389997700_kdup8dzg2", "name": "Create community_member", "method": "POST", "url": "/api/community_member", "flowData": {"nodes": [{"id": "url_node_1739389997699_3", "type": "url", "position": {"x": 100, "y": 100}, "data": {"label": "Url", "apiname": "Create community_member", "path": "/api/community_member", "method": "POST", "fields": [{"name": "user_id", "type": "integer", "validation": ""}, {"name": "community_id", "type": "integer", "validation": ""}, {"name": "role", "type": "string", "validation": ""}, {"name": "created_at", "type": "datetime", "validation": ""}]}}, {"id": "auth_node_1739389997699_3", "type": "auth", "position": {"x": 100, "y": 200}, "data": {"label": "<PERSON><PERSON>", "authType": "none"}}, {"id": "db_insert_node_1739389997699_3", "type": "db-insert", "position": {"x": 100, "y": 300}, "data": {"label": "Database Insert", "model": "community_member", "operation": "create", "query": "INSERT INTO community_member (user_id, community_id, role, created_at)\n                      VALUES (:user_id, :community_id, :role, :created_at)", "resultVar": "community_memberCreateResult"}}, {"id": "logic_node_1739389997699_3", "type": "logic", "position": {"x": 100, "y": 400}, "data": {"label": "Logic", "fields": [], "queryFields": [], "code": "//commented logic"}}, {"id": "output_node_1739389997699_3", "type": "outputs", "position": {"x": 100, "y": 500}, "data": {"label": "Response", "outputType": "json", "fields": [{"name": "error", "type": "boolean"}, {"name": "id", "type": "number"}], "resultVar": "community_memberCreateResult", "statusCode": 200}}], "edges": [{"id": "url-to-auth_1739389997699_3", "source": "url_node_1739389997699_3", "target": "auth_node_1739389997699_3"}, {"id": "auth-to-db_1739389997699_3", "source": "auth_node_1739389997699_3", "target": "db_insert_node_1739389997699_3"}, {"id": "db-to-logic_1739389997699_3", "source": "db_insert_node_1739389997699_3", "target": "logic_node_1739389997699_3"}, {"id": "logic-to-output_1739389997699_3", "source": "logic_node_1739389997699_3", "target": "output_node_1739389997699_3"}]}}, {"id": "route_1739389997700_mej3lbme2", "name": "Update community_member", "method": "PUT", "url": "/api/community_member/:id", "flowData": {"nodes": [{"id": "url_node_1739389997699_4", "type": "url", "position": {"x": 100, "y": 100}, "data": {"label": "Url", "apiname": "Update community_member", "path": "/api/community_member/:id", "method": "PUT", "queryFields": [{"name": "id", "type": "number", "validation": "required"}], "fields": [{"name": "user_id", "type": "integer", "validation": "required"}, {"name": "community_id", "type": "integer", "validation": "required"}, {"name": "role", "type": "string", "validation": "required"}, {"name": "created_at", "type": "datetime", "validation": "required"}]}}, {"id": "auth_node_1739389997699_4", "type": "auth", "position": {"x": 100, "y": 200}, "data": {"label": "<PERSON><PERSON>", "authType": "none"}}, {"id": "db_update_node_1739389997699_4", "type": "db-update", "position": {"x": 100, "y": 300}, "data": {"label": "Database Update", "model": "community_member", "operation": "update", "idField": "id", "query": "UPDATE community_member SET user_id=:user_id, community_id=:community_id, role=:role, created_at=:created_at WHERE id=:id", "resultVar": "community_memberUpdateResult"}}, {"id": "logic_node_1739389997699_4", "type": "logic", "position": {"x": 100, "y": 400}, "data": {"label": "Logic", "fields": [], "queryFields": [], "code": "//commented logic"}}, {"id": "output_node_1739389997699_4", "type": "outputs", "position": {"x": 100, "y": 500}, "data": {"label": "Response", "outputType": "json", "fields": [{"name": "error", "type": "boolean"}, {"name": "id", "type": "number"}], "resultVar": "community_memberUpdateResult", "statusCode": 200}}], "edges": [{"id": "url-to-auth_1739389997699_4", "source": "url_node_1739389997699_4", "target": "auth_node_1739389997699_4"}, {"id": "auth-to-db_1739389997699_4", "source": "auth_node_1739389997699_4", "target": "db_update_node_1739389997699_4"}, {"id": "db-to-logic_1739389997699_4", "source": "db_update_node_1739389997699_4", "target": "logic_node_1739389997699_4"}, {"id": "logic-to-output_1739389997699_4", "source": "logic_node_1739389997699_4", "target": "output_node_1739389997699_4"}]}}, {"id": "route_1739389997700_7wogpjht3", "name": "Delete One community_member", "method": "DELETE", "url": "/api/community_member/:id", "flowData": {"nodes": [{"id": "url_node_1739389997699_2", "type": "url", "position": {"x": 100, "y": 100}, "data": {"label": "Url", "apiname": "Delete One community_member", "path": "/api/community_member/:id", "method": "DELETE", "queryFields": [{"name": "id", "type": "number", "validation": "required"}]}}, {"id": "auth_node_1739389997699_2", "type": "auth", "position": {"x": 100, "y": 200}, "data": {"label": "<PERSON><PERSON>", "authType": "none"}}, {"id": "db_delete_node_1739389997699_2", "type": "db-delete", "position": {"x": 100, "y": 300}, "data": {"label": "Database Delete", "model": "community_member", "operation": "delete", "query": "DELETE FROM community_member WHERE id=id", "resultVar": "community_memberDeleteResult"}}, {"id": "logic_node_1739389997699_2", "type": "logic", "position": {"x": 100, "y": 400}, "data": {"label": "Logic", "fields": [], "queryFields": [], "code": "//commented logic"}}, {"id": "output_node_1739389997699_2", "type": "outputs", "position": {"x": 100, "y": 500}, "data": {"label": "Response", "outputType": "json", "fields": [{"name": "error", "type": "boolean"}, {"name": "id", "type": "integer"}], "statusCode": 200, "resultVar": "community_memberDeleteResult"}}], "edges": [{"id": "url-to-auth_1739389997699_2", "source": "url_node_1739389997699_2", "target": "auth_node_1739389997699_2"}, {"id": "auth-to-db_1739389997699_2", "source": "auth_node_1739389997699_2", "target": "db_delete_node_1739389997699_2"}, {"id": "db-to-logic_1739389997699_2", "source": "db_delete_node_1739389997699_2", "target": "logic_node_1739389997699_2"}, {"id": "logic-to-output_1739389997699_2", "source": "logic_node_1739389997699_2", "target": "output_node_1739389997699_2"}]}}, {"id": "route_1739390112454_vou482ts4", "name": "Get All community_join_request", "method": "GET", "url": "/api/community_join_request", "flowData": {"nodes": [{"id": "url_node_1739390112454", "type": "url", "position": {"x": 100, "y": 100}, "data": {"label": "Url", "apiname": "Get All community_join_request", "path": "/api/community_join_request", "method": "GET"}}, {"id": "auth_node_1739390112454", "type": "auth", "position": {"x": 100, "y": 200}, "data": {"label": "<PERSON><PERSON>", "authType": "none"}}, {"id": "db_find_node_1739390112454", "type": "db-find", "position": {"x": 100, "y": 300}, "data": {"label": "Database Find", "model": "community_join_request", "operation": "find<PERSON>any", "query": "SELECT * FROM community_join_request", "resultVar": "community_join_requestResult"}}, {"id": "logic_node_1739390112454", "type": "logic", "position": {"x": 100, "y": 400}, "data": {"label": "Logic", "fields": [], "queryFields": [], "code": "//commented logic"}}, {"id": "output_node_1739390112454", "type": "outputs", "position": {"x": 100, "y": 500}, "data": {"label": "Response", "outputType": "json", "fields": [{"name": "id", "type": "number"}, {"name": "updated_at", "type": "datetime"}, {"name": "created_at", "type": "datetime"}, {"name": "user_id", "type": "integer"}, {"name": "approver_id", "type": "integer"}], "resultVar": "community_join_requestResult", "statusCode": 200}}], "edges": [{"id": "url-to-auth_1739390112454", "source": "url_node_1739390112454", "target": "auth_node_1739390112454"}, {"id": "auth-to-db_1739390112454", "source": "auth_node_1739390112454", "target": "db_find_node_1739390112454"}, {"id": "db-to-logic_1739390112454", "source": "db_find_node_1739390112454", "target": "logic_node_1739390112454"}, {"id": "logic-to-output_1739390112454", "source": "logic_node_1739390112454", "target": "output_node_1739390112454"}]}}, {"id": "route_1739390112454_gycatc97n", "name": "Get One community_join_request", "method": "GET", "url": "/api/community_join_request/:id", "flowData": {"nodes": [{"id": "url_node_1739390112454_1", "type": "url", "position": {"x": 100, "y": 100}, "data": {"label": "Url", "apiname": "Get One community_join_request", "path": "/api/community_join_request/:id", "method": "GET", "queryFields": [{"name": "id", "type": "number", "validation": "required"}]}}, {"id": "auth_node_1739390112454_1", "type": "auth", "position": {"x": 100, "y": 200}, "data": {"label": "<PERSON><PERSON>", "authType": "none"}}, {"id": "db_query_node_1739390112454_1", "type": "db-query", "position": {"x": 100, "y": 300}, "data": {"label": "Database Find", "model": "community_join_request", "operation": "findOne", "query": "SELECT * FROM community_join_request WHERE id=id", "resultVar": "community_join_requestOneResult"}}, {"id": "logic_node_1739390112454_1", "type": "logic", "position": {"x": 100, "y": 400}, "data": {"label": "Logic", "fields": [], "queryFields": [], "code": "//commented logic"}}, {"id": "output_node_1739390112454_1", "type": "outputs", "position": {"x": 100, "y": 500}, "data": {"label": "Response", "outputType": "json", "fields": [{"name": "id", "type": "number"}, {"name": "updated_at", "type": "datetime"}, {"name": "created_at", "type": "datetime"}, {"name": "user_id", "type": "integer"}, {"name": "approver_id", "type": "integer"}, {"name": "error", "type": "boolean"}], "resultVar": "community_join_requestOneResult", "statusCode": 200}}], "edges": [{"id": "url-to-auth_1739390112454_1", "source": "url_node_1739390112454_1", "target": "auth_node_1739390112454_1"}, {"id": "auth-to-db_1739390112454_1", "source": "auth_node_1739390112454_1", "target": "db_query_node_1739390112454_1"}, {"id": "db-to-logic_1739390112454_1", "source": "db_query_node_1739390112454_1", "target": "logic_node_1739390112454_1"}, {"id": "logic-to-output_1739390112454_1", "source": "logic_node_1739390112454_1", "target": "output_node_1739390112454_1"}]}}, {"id": "route_1739390112455_fxztkauhm", "name": "Create community_join_request", "method": "POST", "url": "/api/community_join_request", "flowData": {"nodes": [{"id": "url_node_1739390112454_3", "type": "url", "position": {"x": 100, "y": 100}, "data": {"label": "Url", "apiname": "Create community_join_request", "path": "/api/community_join_request", "method": "POST", "fields": [{"name": "updated_at", "type": "datetime", "validation": ""}, {"name": "created_at", "type": "datetime", "validation": ""}, {"name": "user_id", "type": "integer", "validation": ""}, {"name": "approver_id", "type": "integer", "validation": ""}]}}, {"id": "auth_node_1739390112454_3", "type": "auth", "position": {"x": 100, "y": 200}, "data": {"label": "<PERSON><PERSON>", "authType": "none"}}, {"id": "db_insert_node_1739390112454_3", "type": "db-insert", "position": {"x": 100, "y": 300}, "data": {"label": "Database Insert", "model": "community_join_request", "operation": "create", "query": "INSERT INTO community_join_request (updated_at, created_at, user_id, approver_id)\n                      VALUES (:updated_at, :created_at, :user_id, :approver_id)", "resultVar": "community_join_requestCreateResult"}}, {"id": "logic_node_1739390112454_3", "type": "logic", "position": {"x": 100, "y": 400}, "data": {"label": "Logic", "fields": [], "queryFields": [], "code": "//commented logic"}}, {"id": "output_node_1739390112454_3", "type": "outputs", "position": {"x": 100, "y": 500}, "data": {"label": "Response", "outputType": "json", "fields": [{"name": "error", "type": "boolean"}, {"name": "id", "type": "number"}], "resultVar": "community_join_requestCreateResult", "statusCode": 200}}], "edges": [{"id": "url-to-auth_1739390112454_3", "source": "url_node_1739390112454_3", "target": "auth_node_1739390112454_3"}, {"id": "auth-to-db_1739390112454_3", "source": "auth_node_1739390112454_3", "target": "db_insert_node_1739390112454_3"}, {"id": "db-to-logic_1739390112454_3", "source": "db_insert_node_1739390112454_3", "target": "logic_node_1739390112454_3"}, {"id": "logic-to-output_1739390112454_3", "source": "logic_node_1739390112454_3", "target": "output_node_1739390112454_3"}]}}, {"id": "route_1739390112455_a1hlb4mw2", "name": "Update community_join_request", "method": "PUT", "url": "/api/community_join_request/:id", "flowData": {"nodes": [{"id": "url_node_1739390112454_4", "type": "url", "position": {"x": 100, "y": 100}, "data": {"label": "Url", "apiname": "Update community_join_request", "path": "/api/community_join_request/:id", "method": "PUT", "queryFields": [{"name": "id", "type": "number", "validation": "required"}], "fields": [{"name": "updated_at", "type": "datetime", "validation": "required"}, {"name": "created_at", "type": "datetime", "validation": "required"}, {"name": "user_id", "type": "integer", "validation": "required"}, {"name": "approver_id", "type": "integer", "validation": "required"}]}}, {"id": "auth_node_1739390112454_4", "type": "auth", "position": {"x": 100, "y": 200}, "data": {"label": "<PERSON><PERSON>", "authType": "none"}}, {"id": "db_update_node_1739390112454_4", "type": "db-update", "position": {"x": 100, "y": 300}, "data": {"label": "Database Update", "model": "community_join_request", "operation": "update", "idField": "id", "query": "UPDATE community_join_request SET updated_at=:updated_at, created_at=:created_at, user_id=:user_id, approver_id=:approver_id WHERE id=:id", "resultVar": "community_join_requestUpdateResult"}}, {"id": "logic_node_1739390112454_4", "type": "logic", "position": {"x": 100, "y": 400}, "data": {"label": "Logic", "fields": [], "queryFields": [], "code": "//commented logic"}}, {"id": "output_node_1739390112454_4", "type": "outputs", "position": {"x": 100, "y": 500}, "data": {"label": "Response", "outputType": "json", "fields": [{"name": "error", "type": "boolean"}, {"name": "id", "type": "number"}], "resultVar": "community_join_requestUpdateResult", "statusCode": 200}}], "edges": [{"id": "url-to-auth_1739390112454_4", "source": "url_node_1739390112454_4", "target": "auth_node_1739390112454_4"}, {"id": "auth-to-db_1739390112454_4", "source": "auth_node_1739390112454_4", "target": "db_update_node_1739390112454_4"}, {"id": "db-to-logic_1739390112454_4", "source": "db_update_node_1739390112454_4", "target": "logic_node_1739390112454_4"}, {"id": "logic-to-output_1739390112454_4", "source": "logic_node_1739390112454_4", "target": "output_node_1739390112454_4"}]}}, {"id": "route_1739390112455_qfbadzwn8", "name": "Delete One community_join_request", "method": "DELETE", "url": "/api/community_join_request/:id", "flowData": {"nodes": [{"id": "url_node_1739390112454_2", "type": "url", "position": {"x": 100, "y": 100}, "data": {"label": "Url", "apiname": "Delete One community_join_request", "path": "/api/community_join_request/:id", "method": "DELETE", "queryFields": [{"name": "id", "type": "number", "validation": "required"}]}}, {"id": "auth_node_1739390112454_2", "type": "auth", "position": {"x": 100, "y": 200}, "data": {"label": "<PERSON><PERSON>", "authType": "none"}}, {"id": "db_delete_node_1739390112454_2", "type": "db-delete", "position": {"x": 100, "y": 300}, "data": {"label": "Database Delete", "model": "community_join_request", "operation": "delete", "query": "DELETE FROM community_join_request WHERE id=id", "resultVar": "community_join_requestDeleteResult"}}, {"id": "logic_node_1739390112454_2", "type": "logic", "position": {"x": 100, "y": 400}, "data": {"label": "Logic", "fields": [], "queryFields": [], "code": "//commented logic"}}, {"id": "output_node_1739390112454_2", "type": "outputs", "position": {"x": 100, "y": 500}, "data": {"label": "Response", "outputType": "json", "fields": [{"name": "error", "type": "boolean"}, {"name": "id", "type": "integer"}], "statusCode": 200, "resultVar": "community_join_requestDeleteResult"}}], "edges": [{"id": "url-to-auth_1739390112454_2", "source": "url_node_1739390112454_2", "target": "auth_node_1739390112454_2"}, {"id": "auth-to-db_1739390112454_2", "source": "auth_node_1739390112454_2", "target": "db_delete_node_1739390112454_2"}, {"id": "db-to-logic_1739390112454_2", "source": "db_delete_node_1739390112454_2", "target": "logic_node_1739390112454_2"}, {"id": "logic-to-output_1739390112454_2", "source": "logic_node_1739390112454_2", "target": "output_node_1739390112454_2"}]}}, {"id": "route_1739390254820_i5fxmaxdo", "name": "Get All community_invite", "method": "GET", "url": "/api/community_invite", "flowData": {"nodes": [{"id": "url_node_1739390254820", "type": "url", "position": {"x": 100, "y": 100}, "data": {"label": "Url", "apiname": "Get All community_invite", "path": "/api/community_invite", "method": "GET"}}, {"id": "auth_node_1739390254820", "type": "auth", "position": {"x": 100, "y": 200}, "data": {"label": "<PERSON><PERSON>", "authType": "none"}}, {"id": "db_find_node_1739390254820", "type": "db-find", "position": {"x": 100, "y": 300}, "data": {"label": "Database Find", "model": "community_invite", "operation": "find<PERSON>any", "query": "SELECT * FROM community_invite", "resultVar": "community_inviteResult"}}, {"id": "logic_node_1739390254820", "type": "logic", "position": {"x": 100, "y": 400}, "data": {"label": "Logic", "fields": [], "queryFields": [], "code": "//commented logic"}}, {"id": "output_node_1739390254820", "type": "outputs", "position": {"x": 100, "y": 500}, "data": {"label": "Response", "outputType": "json", "fields": [{"name": "id", "type": "number"}, {"name": "status", "type": "integer"}, {"name": "created_at", "type": "datetime"}, {"name": "updated_at", "type": "datetime"}, {"name": "user_id", "type": "integer"}, {"name": "community_id", "type": "integer"}], "resultVar": "community_inviteResult", "statusCode": 200}}], "edges": [{"id": "url-to-auth_1739390254820", "source": "url_node_1739390254820", "target": "auth_node_1739390254820"}, {"id": "auth-to-db_1739390254820", "source": "auth_node_1739390254820", "target": "db_find_node_1739390254820"}, {"id": "db-to-logic_1739390254820", "source": "db_find_node_1739390254820", "target": "logic_node_1739390254820"}, {"id": "logic-to-output_1739390254820", "source": "logic_node_1739390254820", "target": "output_node_1739390254820"}]}}, {"id": "route_1739390254820_ec3c85siv", "name": "Get One community_invite", "method": "GET", "url": "/api/community_invite/:id", "flowData": {"nodes": [{"id": "url_node_1739390254820_1", "type": "url", "position": {"x": 100, "y": 100}, "data": {"label": "Url", "apiname": "Get One community_invite", "path": "/api/community_invite/:id", "method": "GET", "queryFields": [{"name": "id", "type": "number", "validation": "required"}]}}, {"id": "auth_node_1739390254820_1", "type": "auth", "position": {"x": 100, "y": 200}, "data": {"label": "<PERSON><PERSON>", "authType": "none"}}, {"id": "db_query_node_1739390254820_1", "type": "db-query", "position": {"x": 100, "y": 300}, "data": {"label": "Database Find", "model": "community_invite", "operation": "findOne", "query": "SELECT * FROM community_invite WHERE id=id", "resultVar": "community_inviteOneResult"}}, {"id": "logic_node_1739390254820_1", "type": "logic", "position": {"x": 100, "y": 400}, "data": {"label": "Logic", "fields": [], "queryFields": [], "code": "//commented logic"}}, {"id": "output_node_1739390254820_1", "type": "outputs", "position": {"x": 100, "y": 500}, "data": {"label": "Response", "outputType": "json", "fields": [{"name": "id", "type": "number"}, {"name": "status", "type": "integer"}, {"name": "created_at", "type": "datetime"}, {"name": "updated_at", "type": "datetime"}, {"name": "user_id", "type": "integer"}, {"name": "community_id", "type": "integer"}, {"name": "error", "type": "boolean"}], "resultVar": "community_inviteOneResult", "statusCode": 200}}], "edges": [{"id": "url-to-auth_1739390254820_1", "source": "url_node_1739390254820_1", "target": "auth_node_1739390254820_1"}, {"id": "auth-to-db_1739390254820_1", "source": "auth_node_1739390254820_1", "target": "db_query_node_1739390254820_1"}, {"id": "db-to-logic_1739390254820_1", "source": "db_query_node_1739390254820_1", "target": "logic_node_1739390254820_1"}, {"id": "logic-to-output_1739390254820_1", "source": "logic_node_1739390254820_1", "target": "output_node_1739390254820_1"}]}}, {"id": "route_1739390254820_bln9s8beq", "name": "Create community_invite", "method": "POST", "url": "/api/community_invite", "flowData": {"nodes": [{"id": "url_node_1739390254820_3", "type": "url", "position": {"x": 100, "y": 100}, "data": {"label": "Url", "apiname": "Create community_invite", "path": "/api/community_invite", "method": "POST", "fields": [{"name": "status", "type": "integer", "validation": ""}, {"name": "created_at", "type": "datetime", "validation": ""}, {"name": "updated_at", "type": "datetime", "validation": ""}, {"name": "user_id", "type": "integer", "validation": ""}, {"name": "community_id", "type": "integer", "validation": ""}]}}, {"id": "auth_node_1739390254820_3", "type": "auth", "position": {"x": 100, "y": 200}, "data": {"label": "<PERSON><PERSON>", "authType": "none"}}, {"id": "db_insert_node_1739390254820_3", "type": "db-insert", "position": {"x": 100, "y": 300}, "data": {"label": "Database Insert", "model": "community_invite", "operation": "create", "query": "INSERT INTO community_invite (status, created_at, updated_at, user_id, community_id)\n                      VALUES (:status, :created_at, :updated_at, :user_id, :community_id)", "resultVar": "community_inviteCreateResult"}}, {"id": "logic_node_1739390254820_3", "type": "logic", "position": {"x": 100, "y": 400}, "data": {"label": "Logic", "fields": [], "queryFields": [], "code": "//commented logic"}}, {"id": "output_node_1739390254820_3", "type": "outputs", "position": {"x": 100, "y": 500}, "data": {"label": "Response", "outputType": "json", "fields": [{"name": "error", "type": "boolean"}, {"name": "id", "type": "number"}], "resultVar": "community_inviteCreateResult", "statusCode": 200}}], "edges": [{"id": "url-to-auth_1739390254820_3", "source": "url_node_1739390254820_3", "target": "auth_node_1739390254820_3"}, {"id": "auth-to-db_1739390254820_3", "source": "auth_node_1739390254820_3", "target": "db_insert_node_1739390254820_3"}, {"id": "db-to-logic_1739390254820_3", "source": "db_insert_node_1739390254820_3", "target": "logic_node_1739390254820_3"}, {"id": "logic-to-output_1739390254820_3", "source": "logic_node_1739390254820_3", "target": "output_node_1739390254820_3"}]}}, {"id": "route_1739390254820_ehtmv06ja", "name": "Update community_invite", "method": "PUT", "url": "/api/community_invite/:id", "flowData": {"nodes": [{"id": "url_node_1739390254820_4", "type": "url", "position": {"x": 100, "y": 100}, "data": {"label": "Url", "apiname": "Update community_invite", "path": "/api/community_invite/:id", "method": "PUT", "queryFields": [{"name": "id", "type": "number", "validation": "required"}], "fields": [{"name": "status", "type": "integer", "validation": "required"}, {"name": "created_at", "type": "datetime", "validation": "required"}, {"name": "updated_at", "type": "datetime", "validation": "required"}, {"name": "user_id", "type": "integer", "validation": "required"}, {"name": "community_id", "type": "integer", "validation": "required"}]}}, {"id": "auth_node_1739390254820_4", "type": "auth", "position": {"x": 100, "y": 200}, "data": {"label": "<PERSON><PERSON>", "authType": "none"}}, {"id": "db_update_node_1739390254820_4", "type": "db-update", "position": {"x": 100, "y": 300}, "data": {"label": "Database Update", "model": "community_invite", "operation": "update", "idField": "id", "query": "UPDATE community_invite SET status=:status, created_at=:created_at, updated_at=:updated_at, user_id=:user_id, community_id=:community_id WHERE id=:id", "resultVar": "community_inviteUpdateResult"}}, {"id": "logic_node_1739390254820_4", "type": "logic", "position": {"x": 100, "y": 400}, "data": {"label": "Logic", "fields": [], "queryFields": [], "code": "//commented logic"}}, {"id": "output_node_1739390254820_4", "type": "outputs", "position": {"x": 100, "y": 500}, "data": {"label": "Response", "outputType": "json", "fields": [{"name": "error", "type": "boolean"}, {"name": "id", "type": "number"}], "resultVar": "community_inviteUpdateResult", "statusCode": 200}}], "edges": [{"id": "url-to-auth_1739390254820_4", "source": "url_node_1739390254820_4", "target": "auth_node_1739390254820_4"}, {"id": "auth-to-db_1739390254820_4", "source": "auth_node_1739390254820_4", "target": "db_update_node_1739390254820_4"}, {"id": "db-to-logic_1739390254820_4", "source": "db_update_node_1739390254820_4", "target": "logic_node_1739390254820_4"}, {"id": "logic-to-output_1739390254820_4", "source": "logic_node_1739390254820_4", "target": "output_node_1739390254820_4"}]}}, {"id": "route_1739390254820_ad0msadck", "name": "Delete One community_invite", "method": "DELETE", "url": "/api/community_invite/:id", "flowData": {"nodes": [{"id": "url_node_1739390254820_2", "type": "url", "position": {"x": 100, "y": 100}, "data": {"label": "Url", "apiname": "Delete One community_invite", "path": "/api/community_invite/:id", "method": "DELETE", "queryFields": [{"name": "id", "type": "number", "validation": "required"}]}}, {"id": "auth_node_1739390254820_2", "type": "auth", "position": {"x": 100, "y": 200}, "data": {"label": "<PERSON><PERSON>", "authType": "none"}}, {"id": "db_delete_node_1739390254820_2", "type": "db-delete", "position": {"x": 100, "y": 300}, "data": {"label": "Database Delete", "model": "community_invite", "operation": "delete", "query": "DELETE FROM community_invite WHERE id=id", "resultVar": "community_inviteDeleteResult"}}, {"id": "logic_node_1739390254820_2", "type": "logic", "position": {"x": 100, "y": 400}, "data": {"label": "Logic", "fields": [], "queryFields": [], "code": "//commented logic"}}, {"id": "output_node_1739390254820_2", "type": "outputs", "position": {"x": 100, "y": 500}, "data": {"label": "Response", "outputType": "json", "fields": [{"name": "error", "type": "boolean"}, {"name": "id", "type": "integer"}], "statusCode": 200, "resultVar": "community_inviteDeleteResult"}}], "edges": [{"id": "url-to-auth_1739390254820_2", "source": "url_node_1739390254820_2", "target": "auth_node_1739390254820_2"}, {"id": "auth-to-db_1739390254820_2", "source": "auth_node_1739390254820_2", "target": "db_delete_node_1739390254820_2"}, {"id": "db-to-logic_1739390254820_2", "source": "db_delete_node_1739390254820_2", "target": "logic_node_1739390254820_2"}, {"id": "logic-to-output_1739390254820_2", "source": "logic_node_1739390254820_2", "target": "output_node_1739390254820_2"}]}}, {"id": "route_1739390429168_x9zdfu4eg", "name": "Get All referral", "method": "GET", "url": "/api/referral", "flowData": {"nodes": [{"id": "url_node_1739390429168", "type": "url", "position": {"x": 100, "y": 100}, "data": {"label": "Url", "apiname": "Get All referral", "path": "/api/referral", "method": "GET"}}, {"id": "auth_node_1739390429168", "type": "auth", "position": {"x": 100, "y": 200}, "data": {"label": "<PERSON><PERSON>", "authType": "none"}}, {"id": "db_find_node_1739390429168", "type": "db-find", "position": {"x": 100, "y": 300}, "data": {"label": "Database Find", "model": "referral", "operation": "find<PERSON>any", "query": "SELECT * FROM referral", "resultVar": "referralResult"}}, {"id": "logic_node_1739390429168", "type": "logic", "position": {"x": 100, "y": 400}, "data": {"label": "Logic", "fields": [], "queryFields": [], "code": "//commented logic"}}, {"id": "output_node_1739390429168", "type": "outputs", "position": {"x": 100, "y": 500}, "data": {"label": "Response", "outputType": "json", "fields": [{"name": "id", "type": "number"}, {"name": "user_id", "type": "integer"}, {"name": "job_title", "type": "string"}, {"name": "description", "type": "string"}, {"name": "industry_id", "type": "integer"}, {"name": "poy", "type": "string"}, {"name": "client_details", "type": "string"}, {"name": "created_at", "type": "datetime"}, {"name": "updated_at", "type": "datetime"}], "resultVar": "referralResult", "statusCode": 200}}], "edges": [{"id": "url-to-auth_1739390429168", "source": "url_node_1739390429168", "target": "auth_node_1739390429168"}, {"id": "auth-to-db_1739390429168", "source": "auth_node_1739390429168", "target": "db_find_node_1739390429168"}, {"id": "db-to-logic_1739390429168", "source": "db_find_node_1739390429168", "target": "logic_node_1739390429168"}, {"id": "logic-to-output_1739390429168", "source": "logic_node_1739390429168", "target": "output_node_1739390429168"}]}}, {"id": "route_1739390429168_gyw1w2wji", "name": "Get One referral", "method": "GET", "url": "/api/referral/:id", "flowData": {"nodes": [{"id": "url_node_1739390429168_1", "type": "url", "position": {"x": 100, "y": 100}, "data": {"label": "Url", "apiname": "Get One referral", "path": "/api/referral/:id", "method": "GET", "queryFields": [{"name": "id", "type": "number", "validation": "required"}]}}, {"id": "auth_node_1739390429168_1", "type": "auth", "position": {"x": 100, "y": 200}, "data": {"label": "<PERSON><PERSON>", "authType": "none"}}, {"id": "db_query_node_1739390429168_1", "type": "db-query", "position": {"x": 100, "y": 300}, "data": {"label": "Database Find", "model": "referral", "operation": "findOne", "query": "SELECT * FROM referral WHERE id=id", "resultVar": "referralOneResult"}}, {"id": "logic_node_1739390429168_1", "type": "logic", "position": {"x": 100, "y": 400}, "data": {"label": "Logic", "fields": [], "queryFields": [], "code": "//commented logic"}}, {"id": "output_node_1739390429168_1", "type": "outputs", "position": {"x": 100, "y": 500}, "data": {"label": "Response", "outputType": "json", "fields": [{"name": "id", "type": "number"}, {"name": "user_id", "type": "integer"}, {"name": "job_title", "type": "string"}, {"name": "description", "type": "string"}, {"name": "industry_id", "type": "integer"}, {"name": "poy", "type": "string"}, {"name": "client_details", "type": "string"}, {"name": "created_at", "type": "datetime"}, {"name": "updated_at", "type": "datetime"}, {"name": "error", "type": "boolean"}], "resultVar": "referralOneResult", "statusCode": 200}}], "edges": [{"id": "url-to-auth_1739390429168_1", "source": "url_node_1739390429168_1", "target": "auth_node_1739390429168_1"}, {"id": "auth-to-db_1739390429168_1", "source": "auth_node_1739390429168_1", "target": "db_query_node_1739390429168_1"}, {"id": "db-to-logic_1739390429168_1", "source": "db_query_node_1739390429168_1", "target": "logic_node_1739390429168_1"}, {"id": "logic-to-output_1739390429168_1", "source": "logic_node_1739390429168_1", "target": "output_node_1739390429168_1"}]}}, {"id": "route_1739390429168_6h1s22tt5", "name": "Create referral", "method": "POST", "url": "/api/referral", "flowData": {"nodes": [{"id": "url_node_1739390429168_3", "type": "url", "position": {"x": 100, "y": 100}, "data": {"label": "Url", "apiname": "Create referral", "path": "/api/referral", "method": "POST", "fields": [{"name": "user_id", "type": "integer", "validation": ""}, {"name": "job_title", "type": "string", "validation": ""}, {"name": "description", "type": "string", "validation": ""}, {"name": "industry_id", "type": "integer", "validation": ""}, {"name": "poy", "type": "string", "validation": ""}, {"name": "client_details", "type": "string", "validation": ""}, {"name": "created_at", "type": "datetime", "validation": ""}, {"name": "updated_at", "type": "datetime", "validation": ""}]}}, {"id": "auth_node_1739390429168_3", "type": "auth", "position": {"x": 100, "y": 200}, "data": {"label": "<PERSON><PERSON>", "authType": "none"}}, {"id": "db_insert_node_1739390429168_3", "type": "db-insert", "position": {"x": 100, "y": 300}, "data": {"label": "Database Insert", "model": "referral", "operation": "create", "query": "INSERT INTO referral (user_id, job_title, description, industry_id, poy, client_details, created_at, updated_at)\n                      VALUES (:user_id, :job_title, :description, :industry_id, :poy, :client_details, :created_at, :updated_at)", "resultVar": "referralCreateResult"}}, {"id": "logic_node_1739390429168_3", "type": "logic", "position": {"x": 100, "y": 400}, "data": {"label": "Logic", "fields": [], "queryFields": [], "code": "//commented logic"}}, {"id": "output_node_1739390429168_3", "type": "outputs", "position": {"x": 100, "y": 500}, "data": {"label": "Response", "outputType": "json", "fields": [{"name": "error", "type": "boolean"}, {"name": "id", "type": "number"}], "resultVar": "referralCreateResult", "statusCode": 200}}], "edges": [{"id": "url-to-auth_1739390429168_3", "source": "url_node_1739390429168_3", "target": "auth_node_1739390429168_3"}, {"id": "auth-to-db_1739390429168_3", "source": "auth_node_1739390429168_3", "target": "db_insert_node_1739390429168_3"}, {"id": "db-to-logic_1739390429168_3", "source": "db_insert_node_1739390429168_3", "target": "logic_node_1739390429168_3"}, {"id": "logic-to-output_1739390429168_3", "source": "logic_node_1739390429168_3", "target": "output_node_1739390429168_3"}]}}, {"id": "route_1739390429168_1gai4hzgg", "name": "Update referral", "method": "PUT", "url": "/api/referral/:id", "flowData": {"nodes": [{"id": "url_node_1739390429168_4", "type": "url", "position": {"x": 100, "y": 100}, "data": {"label": "Url", "apiname": "Update referral", "path": "/api/referral/:id", "method": "PUT", "queryFields": [{"name": "id", "type": "number", "validation": "required"}], "fields": [{"name": "user_id", "type": "integer", "validation": "required"}, {"name": "job_title", "type": "string", "validation": "required"}, {"name": "description", "type": "string", "validation": "required"}, {"name": "industry_id", "type": "integer", "validation": "required"}, {"name": "poy", "type": "string", "validation": "required"}, {"name": "client_details", "type": "string", "validation": "required"}, {"name": "created_at", "type": "datetime", "validation": "required"}, {"name": "updated_at", "type": "datetime", "validation": "required"}]}}, {"id": "auth_node_1739390429168_4", "type": "auth", "position": {"x": 100, "y": 200}, "data": {"label": "<PERSON><PERSON>", "authType": "none"}}, {"id": "db_update_node_1739390429168_4", "type": "db-update", "position": {"x": 100, "y": 300}, "data": {"label": "Database Update", "model": "referral", "operation": "update", "idField": "id", "query": "UPDATE referral SET user_id=:user_id, job_title=:job_title, description=:description, industry_id=:industry_id, poy=:poy, client_details=:client_details, created_at=:created_at, updated_at=:updated_at WHERE id=:id", "resultVar": "referralUpdateResult"}}, {"id": "logic_node_1739390429168_4", "type": "logic", "position": {"x": 100, "y": 400}, "data": {"label": "Logic", "fields": [], "queryFields": [], "code": "//commented logic"}}, {"id": "output_node_1739390429168_4", "type": "outputs", "position": {"x": 100, "y": 500}, "data": {"label": "Response", "outputType": "json", "fields": [{"name": "error", "type": "boolean"}, {"name": "id", "type": "number"}], "resultVar": "referralUpdateResult", "statusCode": 200}}], "edges": [{"id": "url-to-auth_1739390429168_4", "source": "url_node_1739390429168_4", "target": "auth_node_1739390429168_4"}, {"id": "auth-to-db_1739390429168_4", "source": "auth_node_1739390429168_4", "target": "db_update_node_1739390429168_4"}, {"id": "db-to-logic_1739390429168_4", "source": "db_update_node_1739390429168_4", "target": "logic_node_1739390429168_4"}, {"id": "logic-to-output_1739390429168_4", "source": "logic_node_1739390429168_4", "target": "output_node_1739390429168_4"}]}}, {"id": "route_1739390429168_bz994qilu", "name": "Delete One referral", "method": "DELETE", "url": "/api/referral/:id", "flowData": {"nodes": [{"id": "url_node_1739390429168_2", "type": "url", "position": {"x": 100, "y": 100}, "data": {"label": "Url", "apiname": "Delete One referral", "path": "/api/referral/:id", "method": "DELETE", "queryFields": [{"name": "id", "type": "number", "validation": "required"}]}}, {"id": "auth_node_1739390429168_2", "type": "auth", "position": {"x": 100, "y": 200}, "data": {"label": "<PERSON><PERSON>", "authType": "none"}}, {"id": "db_delete_node_1739390429168_2", "type": "db-delete", "position": {"x": 100, "y": 300}, "data": {"label": "Database Delete", "model": "referral", "operation": "delete", "query": "DELETE FROM referral WHERE id=id", "resultVar": "referralDeleteResult"}}, {"id": "logic_node_1739390429168_2", "type": "logic", "position": {"x": 100, "y": 400}, "data": {"label": "Logic", "fields": [], "queryFields": [], "code": "//commented logic"}}, {"id": "output_node_1739390429168_2", "type": "outputs", "position": {"x": 100, "y": 500}, "data": {"label": "Response", "outputType": "json", "fields": [{"name": "error", "type": "boolean"}, {"name": "id", "type": "integer"}], "statusCode": 200, "resultVar": "referralDeleteResult"}}], "edges": [{"id": "url-to-auth_1739390429168_2", "source": "url_node_1739390429168_2", "target": "auth_node_1739390429168_2"}, {"id": "auth-to-db_1739390429168_2", "source": "auth_node_1739390429168_2", "target": "db_delete_node_1739390429168_2"}, {"id": "db-to-logic_1739390429168_2", "source": "db_delete_node_1739390429168_2", "target": "logic_node_1739390429168_2"}, {"id": "logic-to-output_1739390429168_2", "source": "logic_node_1739390429168_2", "target": "output_node_1739390429168_2"}]}}, {"id": "route_1739390513666_wmrn5j678", "name": "Get All referral_communities", "method": "GET", "url": "/api/referral_communities", "flowData": {"nodes": [{"id": "url_node_1739390513666", "type": "url", "position": {"x": 100, "y": 100}, "data": {"label": "Url", "apiname": "Get All referral_communities", "path": "/api/referral_communities", "method": "GET"}}, {"id": "auth_node_1739390513666", "type": "auth", "position": {"x": 100, "y": 200}, "data": {"label": "<PERSON><PERSON>", "authType": "none"}}, {"id": "db_find_node_1739390513666", "type": "db-find", "position": {"x": 100, "y": 300}, "data": {"label": "Database Find", "model": "referral_communities", "operation": "find<PERSON>any", "query": "SELECT * FROM referral_communities", "resultVar": "referral_communitiesResult"}}, {"id": "logic_node_1739390513666", "type": "logic", "position": {"x": 100, "y": 400}, "data": {"label": "Logic", "fields": [], "queryFields": [], "code": "//commented logic"}}, {"id": "output_node_1739390513666", "type": "outputs", "position": {"x": 100, "y": 500}, "data": {"label": "Response", "outputType": "json", "fields": [{"name": "id", "type": "number"}, {"name": "referral_id", "type": "integer"}, {"name": "community_id", "type": "integer"}, {"name": "is_primary", "type": "integer"}, {"name": "created_at", "type": "datetime"}], "resultVar": "referral_communitiesResult", "statusCode": 200}}], "edges": [{"id": "url-to-auth_1739390513666", "source": "url_node_1739390513666", "target": "auth_node_1739390513666"}, {"id": "auth-to-db_1739390513666", "source": "auth_node_1739390513666", "target": "db_find_node_1739390513666"}, {"id": "db-to-logic_1739390513666", "source": "db_find_node_1739390513666", "target": "logic_node_1739390513666"}, {"id": "logic-to-output_1739390513666", "source": "logic_node_1739390513666", "target": "output_node_1739390513666"}]}}, {"id": "route_1739390513667_vcnw2en0b", "name": "Get One referral_communities", "method": "GET", "url": "/api/referral_communities/:id", "flowData": {"nodes": [{"id": "url_node_1739390513666_1", "type": "url", "position": {"x": 100, "y": 100}, "data": {"label": "Url", "apiname": "Get One referral_communities", "path": "/api/referral_communities/:id", "method": "GET", "queryFields": [{"name": "id", "type": "number", "validation": "required"}]}}, {"id": "auth_node_1739390513666_1", "type": "auth", "position": {"x": 100, "y": 200}, "data": {"label": "<PERSON><PERSON>", "authType": "none"}}, {"id": "db_query_node_1739390513666_1", "type": "db-query", "position": {"x": 100, "y": 300}, "data": {"label": "Database Find", "model": "referral_communities", "operation": "findOne", "query": "SELECT * FROM referral_communities WHERE id=id", "resultVar": "referral_communitiesOneResult"}}, {"id": "logic_node_1739390513666_1", "type": "logic", "position": {"x": 100, "y": 400}, "data": {"label": "Logic", "fields": [], "queryFields": [], "code": "//commented logic"}}, {"id": "output_node_1739390513666_1", "type": "outputs", "position": {"x": 100, "y": 500}, "data": {"label": "Response", "outputType": "json", "fields": [{"name": "id", "type": "number"}, {"name": "referral_id", "type": "integer"}, {"name": "community_id", "type": "integer"}, {"name": "is_primary", "type": "integer"}, {"name": "created_at", "type": "datetime"}, {"name": "error", "type": "boolean"}], "resultVar": "referral_communitiesOneResult", "statusCode": 200}}], "edges": [{"id": "url-to-auth_1739390513666_1", "source": "url_node_1739390513666_1", "target": "auth_node_1739390513666_1"}, {"id": "auth-to-db_1739390513666_1", "source": "auth_node_1739390513666_1", "target": "db_query_node_1739390513666_1"}, {"id": "db-to-logic_1739390513666_1", "source": "db_query_node_1739390513666_1", "target": "logic_node_1739390513666_1"}, {"id": "logic-to-output_1739390513666_1", "source": "logic_node_1739390513666_1", "target": "output_node_1739390513666_1"}]}}, {"id": "route_1739390513667_32eamg3s1", "name": "Create referral_communities", "method": "POST", "url": "/api/referral_communities", "flowData": {"nodes": [{"id": "url_node_1739390513666_3", "type": "url", "position": {"x": 100, "y": 100}, "data": {"label": "Url", "apiname": "Create referral_communities", "path": "/api/referral_communities", "method": "POST", "fields": [{"name": "referral_id", "type": "integer", "validation": ""}, {"name": "community_id", "type": "integer", "validation": ""}, {"name": "is_primary", "type": "integer", "validation": ""}, {"name": "created_at", "type": "datetime", "validation": ""}]}}, {"id": "auth_node_1739390513666_3", "type": "auth", "position": {"x": 100, "y": 200}, "data": {"label": "<PERSON><PERSON>", "authType": "none"}}, {"id": "db_insert_node_1739390513666_3", "type": "db-insert", "position": {"x": 100, "y": 300}, "data": {"label": "Database Insert", "model": "referral_communities", "operation": "create", "query": "INSERT INTO referral_communities (referral_id, community_id, is_primary, created_at)\n                      VALUES (:referral_id, :community_id, :is_primary, :created_at)", "resultVar": "referral_communitiesCreateResult"}}, {"id": "logic_node_1739390513666_3", "type": "logic", "position": {"x": 100, "y": 400}, "data": {"label": "Logic", "fields": [], "queryFields": [], "code": "//commented logic"}}, {"id": "output_node_1739390513666_3", "type": "outputs", "position": {"x": 100, "y": 500}, "data": {"label": "Response", "outputType": "json", "fields": [{"name": "error", "type": "boolean"}, {"name": "id", "type": "number"}], "resultVar": "referral_communitiesCreateResult", "statusCode": 200}}], "edges": [{"id": "url-to-auth_1739390513666_3", "source": "url_node_1739390513666_3", "target": "auth_node_1739390513666_3"}, {"id": "auth-to-db_1739390513666_3", "source": "auth_node_1739390513666_3", "target": "db_insert_node_1739390513666_3"}, {"id": "db-to-logic_1739390513666_3", "source": "db_insert_node_1739390513666_3", "target": "logic_node_1739390513666_3"}, {"id": "logic-to-output_1739390513666_3", "source": "logic_node_1739390513666_3", "target": "output_node_1739390513666_3"}]}}, {"id": "route_1739390513667_p53l1p5i0", "name": "Update referral_communities", "method": "PUT", "url": "/api/referral_communities/:id", "flowData": {"nodes": [{"id": "url_node_1739390513666_4", "type": "url", "position": {"x": 100, "y": 100}, "data": {"label": "Url", "apiname": "Update referral_communities", "path": "/api/referral_communities/:id", "method": "PUT", "queryFields": [{"name": "id", "type": "number", "validation": "required"}], "fields": [{"name": "referral_id", "type": "integer", "validation": "required"}, {"name": "community_id", "type": "integer", "validation": "required"}, {"name": "is_primary", "type": "integer", "validation": "required"}, {"name": "created_at", "type": "datetime", "validation": "required"}]}}, {"id": "auth_node_1739390513666_4", "type": "auth", "position": {"x": 100, "y": 200}, "data": {"label": "<PERSON><PERSON>", "authType": "none"}}, {"id": "db_update_node_1739390513666_4", "type": "db-update", "position": {"x": 100, "y": 300}, "data": {"label": "Database Update", "model": "referral_communities", "operation": "update", "idField": "id", "query": "UPDATE referral_communities SET referral_id=:referral_id, community_id=:community_id, is_primary=:is_primary, created_at=:created_at WHERE id=:id", "resultVar": "referral_communitiesUpdateResult"}}, {"id": "logic_node_1739390513666_4", "type": "logic", "position": {"x": 100, "y": 400}, "data": {"label": "Logic", "fields": [], "queryFields": [], "code": "//commented logic"}}, {"id": "output_node_1739390513666_4", "type": "outputs", "position": {"x": 100, "y": 500}, "data": {"label": "Response", "outputType": "json", "fields": [{"name": "error", "type": "boolean"}, {"name": "id", "type": "number"}], "resultVar": "referral_communitiesUpdateResult", "statusCode": 200}}], "edges": [{"id": "url-to-auth_1739390513666_4", "source": "url_node_1739390513666_4", "target": "auth_node_1739390513666_4"}, {"id": "auth-to-db_1739390513666_4", "source": "auth_node_1739390513666_4", "target": "db_update_node_1739390513666_4"}, {"id": "db-to-logic_1739390513666_4", "source": "db_update_node_1739390513666_4", "target": "logic_node_1739390513666_4"}, {"id": "logic-to-output_1739390513666_4", "source": "logic_node_1739390513666_4", "target": "output_node_1739390513666_4"}]}}, {"id": "route_1739390513667_j979md9mh", "name": "Delete One referral_communities", "method": "DELETE", "url": "/api/referral_communities/:id", "flowData": {"nodes": [{"id": "url_node_1739390513666_2", "type": "url", "position": {"x": 100, "y": 100}, "data": {"label": "Url", "apiname": "Delete One referral_communities", "path": "/api/referral_communities/:id", "method": "DELETE", "queryFields": [{"name": "id", "type": "number", "validation": "required"}]}}, {"id": "auth_node_1739390513666_2", "type": "auth", "position": {"x": 100, "y": 200}, "data": {"label": "<PERSON><PERSON>", "authType": "none"}}, {"id": "db_delete_node_1739390513666_2", "type": "db-delete", "position": {"x": 100, "y": 300}, "data": {"label": "Database Delete", "model": "referral_communities", "operation": "delete", "query": "DELETE FROM referral_communities WHERE id=id", "resultVar": "referral_communitiesDeleteResult"}}, {"id": "logic_node_1739390513666_2", "type": "logic", "position": {"x": 100, "y": 400}, "data": {"label": "Logic", "fields": [], "queryFields": [], "code": "//commented logic"}}, {"id": "output_node_1739390513666_2", "type": "outputs", "position": {"x": 100, "y": 500}, "data": {"label": "Response", "outputType": "json", "fields": [{"name": "error", "type": "boolean"}, {"name": "id", "type": "integer"}], "statusCode": 200, "resultVar": "referral_communitiesDeleteResult"}}], "edges": [{"id": "url-to-auth_1739390513666_2", "source": "url_node_1739390513666_2", "target": "auth_node_1739390513666_2"}, {"id": "auth-to-db_1739390513666_2", "source": "auth_node_1739390513666_2", "target": "db_delete_node_1739390513666_2"}, {"id": "db-to-logic_1739390513666_2", "source": "db_delete_node_1739390513666_2", "target": "logic_node_1739390513666_2"}, {"id": "logic-to-output_1739390513666_2", "source": "logic_node_1739390513666_2", "target": "output_node_1739390513666_2"}]}}, {"id": "route_1739390587500_b2d8pyo6g", "name": "Get All activity_feed", "method": "GET", "url": "/api/activity_feed", "flowData": {"nodes": [{"id": "url_node_1739390587500", "type": "url", "position": {"x": 100, "y": 100}, "data": {"label": "Url", "apiname": "Get All activity_feed", "path": "/api/activity_feed", "method": "GET"}}, {"id": "auth_node_1739390587500", "type": "auth", "position": {"x": 100, "y": 200}, "data": {"label": "<PERSON><PERSON>", "authType": "none"}}, {"id": "db_find_node_1739390587500", "type": "db-find", "position": {"x": 100, "y": 300}, "data": {"label": "Database Find", "model": "activity_feed", "operation": "find<PERSON>any", "query": "SELECT * FROM activity_feed", "resultVar": "activity_feedResult"}}, {"id": "logic_node_1739390587500", "type": "logic", "position": {"x": 100, "y": 400}, "data": {"label": "Logic", "fields": [], "queryFields": [], "code": "//commented logic"}}, {"id": "output_node_1739390587500", "type": "outputs", "position": {"x": 100, "y": 500}, "data": {"label": "Response", "outputType": "json", "fields": [{"name": "id", "type": "number"}, {"name": "updated_at", "type": "datetime"}, {"name": "user_id", "type": "integer"}, {"name": "event_details", "type": "string"}, {"name": "event_type", "type": "string"}], "resultVar": "activity_feedResult", "statusCode": 200}}], "edges": [{"id": "url-to-auth_1739390587500", "source": "url_node_1739390587500", "target": "auth_node_1739390587500"}, {"id": "auth-to-db_1739390587500", "source": "auth_node_1739390587500", "target": "db_find_node_1739390587500"}, {"id": "db-to-logic_1739390587500", "source": "db_find_node_1739390587500", "target": "logic_node_1739390587500"}, {"id": "logic-to-output_1739390587500", "source": "logic_node_1739390587500", "target": "output_node_1739390587500"}]}}, {"id": "route_1739390587500_b5r0g4mwm", "name": "Get One activity_feed", "method": "GET", "url": "/api/activity_feed/:id", "flowData": {"nodes": [{"id": "url_node_1739390587500_1", "type": "url", "position": {"x": 100, "y": 100}, "data": {"label": "Url", "apiname": "Get One activity_feed", "path": "/api/activity_feed/:id", "method": "GET", "queryFields": [{"name": "id", "type": "number", "validation": "required"}]}}, {"id": "auth_node_1739390587500_1", "type": "auth", "position": {"x": 100, "y": 200}, "data": {"label": "<PERSON><PERSON>", "authType": "none"}}, {"id": "db_query_node_1739390587500_1", "type": "db-query", "position": {"x": 100, "y": 300}, "data": {"label": "Database Find", "model": "activity_feed", "operation": "findOne", "query": "SELECT * FROM activity_feed WHERE id=id", "resultVar": "activity_feedOneResult"}}, {"id": "logic_node_1739390587500_1", "type": "logic", "position": {"x": 100, "y": 400}, "data": {"label": "Logic", "fields": [], "queryFields": [], "code": "//commented logic"}}, {"id": "output_node_1739390587500_1", "type": "outputs", "position": {"x": 100, "y": 500}, "data": {"label": "Response", "outputType": "json", "fields": [{"name": "id", "type": "number"}, {"name": "updated_at", "type": "datetime"}, {"name": "user_id", "type": "integer"}, {"name": "event_details", "type": "string"}, {"name": "event_type", "type": "string"}, {"name": "error", "type": "boolean"}], "resultVar": "activity_feedOneResult", "statusCode": 200}}], "edges": [{"id": "url-to-auth_1739390587500_1", "source": "url_node_1739390587500_1", "target": "auth_node_1739390587500_1"}, {"id": "auth-to-db_1739390587500_1", "source": "auth_node_1739390587500_1", "target": "db_query_node_1739390587500_1"}, {"id": "db-to-logic_1739390587500_1", "source": "db_query_node_1739390587500_1", "target": "logic_node_1739390587500_1"}, {"id": "logic-to-output_1739390587500_1", "source": "logic_node_1739390587500_1", "target": "output_node_1739390587500_1"}]}}, {"id": "route_1739390587500_61w5by1s6", "name": "Create activity_feed", "method": "POST", "url": "/api/activity_feed", "flowData": {"nodes": [{"id": "url_node_1739390587500_3", "type": "url", "position": {"x": 100, "y": 100}, "data": {"label": "Url", "apiname": "Create activity_feed", "path": "/api/activity_feed", "method": "POST", "fields": [{"name": "updated_at", "type": "datetime", "validation": ""}, {"name": "user_id", "type": "integer", "validation": ""}, {"name": "event_details", "type": "string", "validation": ""}, {"name": "event_type", "type": "string", "validation": ""}]}}, {"id": "auth_node_1739390587500_3", "type": "auth", "position": {"x": 100, "y": 200}, "data": {"label": "<PERSON><PERSON>", "authType": "none"}}, {"id": "db_insert_node_1739390587500_3", "type": "db-insert", "position": {"x": 100, "y": 300}, "data": {"label": "Database Insert", "model": "activity_feed", "operation": "create", "query": "INSERT INTO activity_feed (updated_at, user_id, event_details, event_type)\n                      VALUES (:updated_at, :user_id, :event_details, :event_type)", "resultVar": "activity_feedCreateResult"}}, {"id": "logic_node_1739390587500_3", "type": "logic", "position": {"x": 100, "y": 400}, "data": {"label": "Logic", "fields": [], "queryFields": [], "code": "//commented logic"}}, {"id": "output_node_1739390587500_3", "type": "outputs", "position": {"x": 100, "y": 500}, "data": {"label": "Response", "outputType": "json", "fields": [{"name": "error", "type": "boolean"}, {"name": "id", "type": "number"}], "resultVar": "activity_feedCreateResult", "statusCode": 200}}], "edges": [{"id": "url-to-auth_1739390587500_3", "source": "url_node_1739390587500_3", "target": "auth_node_1739390587500_3"}, {"id": "auth-to-db_1739390587500_3", "source": "auth_node_1739390587500_3", "target": "db_insert_node_1739390587500_3"}, {"id": "db-to-logic_1739390587500_3", "source": "db_insert_node_1739390587500_3", "target": "logic_node_1739390587500_3"}, {"id": "logic-to-output_1739390587500_3", "source": "logic_node_1739390587500_3", "target": "output_node_1739390587500_3"}]}}, {"id": "route_1739390587500_9ylm7auez", "name": "Update activity_feed", "method": "PUT", "url": "/api/activity_feed/:id", "flowData": {"nodes": [{"id": "url_node_1739390587500_4", "type": "url", "position": {"x": 100, "y": 100}, "data": {"label": "Url", "apiname": "Update activity_feed", "path": "/api/activity_feed/:id", "method": "PUT", "queryFields": [{"name": "id", "type": "number", "validation": "required"}], "fields": [{"name": "updated_at", "type": "datetime", "validation": "required"}, {"name": "user_id", "type": "integer", "validation": "required"}, {"name": "event_details", "type": "string", "validation": "required"}, {"name": "event_type", "type": "string", "validation": "required"}]}}, {"id": "auth_node_1739390587500_4", "type": "auth", "position": {"x": 100, "y": 200}, "data": {"label": "<PERSON><PERSON>", "authType": "none"}}, {"id": "db_update_node_1739390587500_4", "type": "db-update", "position": {"x": 100, "y": 300}, "data": {"label": "Database Update", "model": "activity_feed", "operation": "update", "idField": "id", "query": "UPDATE activity_feed SET updated_at=:updated_at, user_id=:user_id, event_details=:event_details, event_type=:event_type WHERE id=:id", "resultVar": "activity_feedUpdateResult"}}, {"id": "logic_node_1739390587500_4", "type": "logic", "position": {"x": 100, "y": 400}, "data": {"label": "Logic", "fields": [], "queryFields": [], "code": "//commented logic"}}, {"id": "output_node_1739390587500_4", "type": "outputs", "position": {"x": 100, "y": 500}, "data": {"label": "Response", "outputType": "json", "fields": [{"name": "error", "type": "boolean"}, {"name": "id", "type": "number"}], "resultVar": "activity_feedUpdateResult", "statusCode": 200}}], "edges": [{"id": "url-to-auth_1739390587500_4", "source": "url_node_1739390587500_4", "target": "auth_node_1739390587500_4"}, {"id": "auth-to-db_1739390587500_4", "source": "auth_node_1739390587500_4", "target": "db_update_node_1739390587500_4"}, {"id": "db-to-logic_1739390587500_4", "source": "db_update_node_1739390587500_4", "target": "logic_node_1739390587500_4"}, {"id": "logic-to-output_1739390587500_4", "source": "logic_node_1739390587500_4", "target": "output_node_1739390587500_4"}]}}, {"id": "route_1739390587500_aqw0l5o0o", "name": "Delete One activity_feed", "method": "DELETE", "url": "/api/activity_feed/:id", "flowData": {"nodes": [{"id": "url_node_1739390587500_2", "type": "url", "position": {"x": 100, "y": 100}, "data": {"label": "Url", "apiname": "Delete One activity_feed", "path": "/api/activity_feed/:id", "method": "DELETE", "queryFields": [{"name": "id", "type": "number", "validation": "required"}]}}, {"id": "auth_node_1739390587500_2", "type": "auth", "position": {"x": 100, "y": 200}, "data": {"label": "<PERSON><PERSON>", "authType": "none"}}, {"id": "db_delete_node_1739390587500_2", "type": "db-delete", "position": {"x": 100, "y": 300}, "data": {"label": "Database Delete", "model": "activity_feed", "operation": "delete", "query": "DELETE FROM activity_feed WHERE id=id", "resultVar": "activity_feedDeleteResult"}}, {"id": "logic_node_1739390587500_2", "type": "logic", "position": {"x": 100, "y": 400}, "data": {"label": "Logic", "fields": [], "queryFields": [], "code": "//commented logic"}}, {"id": "output_node_1739390587500_2", "type": "outputs", "position": {"x": 100, "y": 500}, "data": {"label": "Response", "outputType": "json", "fields": [{"name": "error", "type": "boolean"}, {"name": "id", "type": "integer"}], "statusCode": 200, "resultVar": "activity_feedDeleteResult"}}], "edges": [{"id": "url-to-auth_1739390587500_2", "source": "url_node_1739390587500_2", "target": "auth_node_1739390587500_2"}, {"id": "auth-to-db_1739390587500_2", "source": "auth_node_1739390587500_2", "target": "db_delete_node_1739390587500_2"}, {"id": "db-to-logic_1739390587500_2", "source": "db_delete_node_1739390587500_2", "target": "logic_node_1739390587500_2"}, {"id": "logic-to-output_1739390587500_2", "source": "logic_node_1739390587500_2", "target": "output_node_1739390587500_2"}]}}, {"id": "route_1739390692224_dmquzxnka", "name": "Get All meetings", "method": "GET", "url": "/api/meetings", "flowData": {"nodes": [{"id": "url_node_1739390692224", "type": "url", "position": {"x": 100, "y": 100}, "data": {"label": "Url", "apiname": "Get All meetings", "path": "/api/meetings", "method": "GET"}}, {"id": "auth_node_1739390692224", "type": "auth", "position": {"x": 100, "y": 200}, "data": {"label": "<PERSON><PERSON>", "authType": "none"}}, {"id": "db_find_node_1739390692224", "type": "db-find", "position": {"x": 100, "y": 300}, "data": {"label": "Database Find", "model": "meetings", "operation": "find<PERSON>any", "query": "SELECT * FROM meetings", "resultVar": "meetingsResult"}}, {"id": "logic_node_1739390692224", "type": "logic", "position": {"x": 100, "y": 400}, "data": {"label": "Logic", "fields": [], "queryFields": [], "code": "//commented logic"}}, {"id": "output_node_1739390692224", "type": "outputs", "position": {"x": 100, "y": 500}, "data": {"label": "Response", "outputType": "json", "fields": [{"name": "id", "type": "number"}, {"name": "status", "type": "string"}, {"name": "created_at", "type": "datetime"}, {"name": "updated_at", "type": "datetime"}, {"name": "topic", "type": "string"}, {"name": "description", "type": "string"}, {"name": "meeting_datetime", "type": "datetime"}, {"name": "created_by", "type": "integer"}, {"name": "meeting_link", "type": "string"}], "resultVar": "meetingsResult", "statusCode": 200}}], "edges": [{"id": "url-to-auth_1739390692224", "source": "url_node_1739390692224", "target": "auth_node_1739390692224"}, {"id": "auth-to-db_1739390692224", "source": "auth_node_1739390692224", "target": "db_find_node_1739390692224"}, {"id": "db-to-logic_1739390692224", "source": "db_find_node_1739390692224", "target": "logic_node_1739390692224"}, {"id": "logic-to-output_1739390692224", "source": "logic_node_1739390692224", "target": "output_node_1739390692224"}]}}, {"id": "route_1739390692224_gw51jqaqd", "name": "Get One meetings", "method": "GET", "url": "/api/meetings/:id", "flowData": {"nodes": [{"id": "url_node_1739390692224_1", "type": "url", "position": {"x": 100, "y": 100}, "data": {"label": "Url", "apiname": "Get One meetings", "path": "/api/meetings/:id", "method": "GET", "queryFields": [{"name": "id", "type": "number", "validation": "required"}]}}, {"id": "auth_node_1739390692224_1", "type": "auth", "position": {"x": 100, "y": 200}, "data": {"label": "<PERSON><PERSON>", "authType": "none"}}, {"id": "db_query_node_1739390692224_1", "type": "db-query", "position": {"x": 100, "y": 300}, "data": {"label": "Database Find", "model": "meetings", "operation": "findOne", "query": "SELECT * FROM meetings WHERE id=id", "resultVar": "meetingsOneResult"}}, {"id": "logic_node_1739390692224_1", "type": "logic", "position": {"x": 100, "y": 400}, "data": {"label": "Logic", "fields": [], "queryFields": [], "code": "//commented logic"}}, {"id": "output_node_1739390692224_1", "type": "outputs", "position": {"x": 100, "y": 500}, "data": {"label": "Response", "outputType": "json", "fields": [{"name": "id", "type": "number"}, {"name": "status", "type": "string"}, {"name": "created_at", "type": "datetime"}, {"name": "updated_at", "type": "datetime"}, {"name": "topic", "type": "string"}, {"name": "description", "type": "string"}, {"name": "meeting_datetime", "type": "datetime"}, {"name": "created_by", "type": "integer"}, {"name": "meeting_link", "type": "string"}, {"name": "error", "type": "boolean"}], "resultVar": "meetingsOneResult", "statusCode": 200}}], "edges": [{"id": "url-to-auth_1739390692224_1", "source": "url_node_1739390692224_1", "target": "auth_node_1739390692224_1"}, {"id": "auth-to-db_1739390692224_1", "source": "auth_node_1739390692224_1", "target": "db_query_node_1739390692224_1"}, {"id": "db-to-logic_1739390692224_1", "source": "db_query_node_1739390692224_1", "target": "logic_node_1739390692224_1"}, {"id": "logic-to-output_1739390692224_1", "source": "logic_node_1739390692224_1", "target": "output_node_1739390692224_1"}]}}, {"id": "route_1739390692224_yo95p9n8v", "name": "Create meetings", "method": "POST", "url": "/api/meetings", "flowData": {"nodes": [{"id": "url_node_1739390692224_3", "type": "url", "position": {"x": 100, "y": 100}, "data": {"label": "Url", "apiname": "Create meetings", "path": "/api/meetings", "method": "POST", "fields": [{"name": "status", "type": "string", "validation": ""}, {"name": "created_at", "type": "datetime", "validation": ""}, {"name": "updated_at", "type": "datetime", "validation": ""}, {"name": "topic", "type": "string", "validation": ""}, {"name": "description", "type": "string", "validation": ""}, {"name": "meeting_datetime", "type": "datetime", "validation": ""}, {"name": "created_by", "type": "integer", "validation": ""}, {"name": "meeting_link", "type": "string", "validation": ""}]}}, {"id": "auth_node_1739390692224_3", "type": "auth", "position": {"x": 100, "y": 200}, "data": {"label": "<PERSON><PERSON>", "authType": "none"}}, {"id": "db_insert_node_1739390692224_3", "type": "db-insert", "position": {"x": 100, "y": 300}, "data": {"label": "Database Insert", "model": "meetings", "operation": "create", "query": "INSERT INTO meetings (status, created_at, updated_at, topic, description, meeting_datetime, created_by, meeting_link)\n                      VALUES (:status, :created_at, :updated_at, :topic, :description, :meeting_datetime, :created_by, :meeting_link)", "resultVar": "meetingsCreateResult"}}, {"id": "logic_node_1739390692224_3", "type": "logic", "position": {"x": 100, "y": 400}, "data": {"label": "Logic", "fields": [], "queryFields": [], "code": "//commented logic"}}, {"id": "output_node_1739390692224_3", "type": "outputs", "position": {"x": 100, "y": 500}, "data": {"label": "Response", "outputType": "json", "fields": [{"name": "error", "type": "boolean"}, {"name": "id", "type": "number"}], "resultVar": "meetingsCreateResult", "statusCode": 200}}], "edges": [{"id": "url-to-auth_1739390692224_3", "source": "url_node_1739390692224_3", "target": "auth_node_1739390692224_3"}, {"id": "auth-to-db_1739390692224_3", "source": "auth_node_1739390692224_3", "target": "db_insert_node_1739390692224_3"}, {"id": "db-to-logic_1739390692224_3", "source": "db_insert_node_1739390692224_3", "target": "logic_node_1739390692224_3"}, {"id": "logic-to-output_1739390692224_3", "source": "logic_node_1739390692224_3", "target": "output_node_1739390692224_3"}]}}, {"id": "route_1739390692224_wte11u03r", "name": "Update meetings", "method": "PUT", "url": "/api/meetings/:id", "flowData": {"nodes": [{"id": "url_node_1739390692224_4", "type": "url", "position": {"x": 100, "y": 100}, "data": {"label": "Url", "apiname": "Update meetings", "path": "/api/meetings/:id", "method": "PUT", "queryFields": [{"name": "id", "type": "number", "validation": "required"}], "fields": [{"name": "status", "type": "string", "validation": "required"}, {"name": "created_at", "type": "datetime", "validation": "required"}, {"name": "updated_at", "type": "datetime", "validation": "required"}, {"name": "topic", "type": "string", "validation": "required"}, {"name": "description", "type": "string", "validation": "required"}, {"name": "meeting_datetime", "type": "datetime", "validation": "required"}, {"name": "created_by", "type": "integer", "validation": "required"}, {"name": "meeting_link", "type": "string", "validation": "required"}]}}, {"id": "auth_node_1739390692224_4", "type": "auth", "position": {"x": 100, "y": 200}, "data": {"label": "<PERSON><PERSON>", "authType": "none"}}, {"id": "db_update_node_1739390692224_4", "type": "db-update", "position": {"x": 100, "y": 300}, "data": {"label": "Database Update", "model": "meetings", "operation": "update", "idField": "id", "query": "UPDATE meetings SET status=:status, created_at=:created_at, updated_at=:updated_at, topic=:topic, description=:description, meeting_datetime=:meeting_datetime, created_by=:created_by, meeting_link=:meeting_link WHERE id=:id", "resultVar": "meetingsUpdateResult"}}, {"id": "logic_node_1739390692224_4", "type": "logic", "position": {"x": 100, "y": 400}, "data": {"label": "Logic", "fields": [], "queryFields": [], "code": "//commented logic"}}, {"id": "output_node_1739390692224_4", "type": "outputs", "position": {"x": 100, "y": 500}, "data": {"label": "Response", "outputType": "json", "fields": [{"name": "error", "type": "boolean"}, {"name": "id", "type": "number"}], "resultVar": "meetingsUpdateResult", "statusCode": 200}}], "edges": [{"id": "url-to-auth_1739390692224_4", "source": "url_node_1739390692224_4", "target": "auth_node_1739390692224_4"}, {"id": "auth-to-db_1739390692224_4", "source": "auth_node_1739390692224_4", "target": "db_update_node_1739390692224_4"}, {"id": "db-to-logic_1739390692224_4", "source": "db_update_node_1739390692224_4", "target": "logic_node_1739390692224_4"}, {"id": "logic-to-output_1739390692224_4", "source": "logic_node_1739390692224_4", "target": "output_node_1739390692224_4"}]}}, {"id": "route_1739390692224_z44qsk9fy", "name": "Delete One meetings", "method": "DELETE", "url": "/api/meetings/:id", "flowData": {"nodes": [{"id": "url_node_1739390692224_2", "type": "url", "position": {"x": 100, "y": 100}, "data": {"label": "Url", "apiname": "Delete One meetings", "path": "/api/meetings/:id", "method": "DELETE", "queryFields": [{"name": "id", "type": "number", "validation": "required"}]}}, {"id": "auth_node_1739390692224_2", "type": "auth", "position": {"x": 100, "y": 200}, "data": {"label": "<PERSON><PERSON>", "authType": "none"}}, {"id": "db_delete_node_1739390692224_2", "type": "db-delete", "position": {"x": 100, "y": 300}, "data": {"label": "Database Delete", "model": "meetings", "operation": "delete", "query": "DELETE FROM meetings WHERE id=id", "resultVar": "meetingsDeleteResult"}}, {"id": "logic_node_1739390692224_2", "type": "logic", "position": {"x": 100, "y": 400}, "data": {"label": "Logic", "fields": [], "queryFields": [], "code": "//commented logic"}}, {"id": "output_node_1739390692224_2", "type": "outputs", "position": {"x": 100, "y": 500}, "data": {"label": "Response", "outputType": "json", "fields": [{"name": "error", "type": "boolean"}, {"name": "id", "type": "integer"}], "statusCode": 200, "resultVar": "meetingsDeleteResult"}}], "edges": [{"id": "url-to-auth_1739390692224_2", "source": "url_node_1739390692224_2", "target": "auth_node_1739390692224_2"}, {"id": "auth-to-db_1739390692224_2", "source": "auth_node_1739390692224_2", "target": "db_delete_node_1739390692224_2"}, {"id": "db-to-logic_1739390692224_2", "source": "db_delete_node_1739390692224_2", "target": "logic_node_1739390692224_2"}, {"id": "logic-to-output_1739390692224_2", "source": "logic_node_1739390692224_2", "target": "output_node_1739390692224_2"}]}}, {"id": "route_1739390757676_d1cqoowbc", "name": "Get All meeting_attendee", "method": "GET", "url": "/api/meeting_attendee", "flowData": {"nodes": [{"id": "url_node_1739390757676", "type": "url", "position": {"x": 100, "y": 100}, "data": {"label": "Url", "apiname": "Get All meeting_attendee", "path": "/api/meeting_attendee", "method": "GET"}}, {"id": "auth_node_1739390757676", "type": "auth", "position": {"x": 100, "y": 200}, "data": {"label": "<PERSON><PERSON>", "authType": "none"}}, {"id": "db_find_node_1739390757676", "type": "db-find", "position": {"x": 100, "y": 300}, "data": {"label": "Database Find", "model": "meeting_attendee", "operation": "find<PERSON>any", "query": "SELECT * FROM meeting_attendee", "resultVar": "meeting_attendee<PERSON><PERSON><PERSON>"}}, {"id": "logic_node_1739390757676", "type": "logic", "position": {"x": 100, "y": 400}, "data": {"label": "Logic", "fields": [], "queryFields": [], "code": "//commented logic"}}, {"id": "output_node_1739390757676", "type": "outputs", "position": {"x": 100, "y": 500}, "data": {"label": "Response", "outputType": "json", "fields": [{"name": "id", "type": "number"}, {"name": "meeting_id", "type": "integer"}, {"name": "user_id", "type": "integer"}, {"name": "created_at", "type": "datetime"}, {"name": "updated_at", "type": "datetime"}], "resultVar": "meeting_attendee<PERSON><PERSON><PERSON>", "statusCode": 200}}], "edges": [{"id": "url-to-auth_1739390757676", "source": "url_node_1739390757676", "target": "auth_node_1739390757676"}, {"id": "auth-to-db_1739390757676", "source": "auth_node_1739390757676", "target": "db_find_node_1739390757676"}, {"id": "db-to-logic_1739390757676", "source": "db_find_node_1739390757676", "target": "logic_node_1739390757676"}, {"id": "logic-to-output_1739390757676", "source": "logic_node_1739390757676", "target": "output_node_1739390757676"}]}}, {"id": "route_1739390757676_9eggyixo9", "name": "Get One meeting_attendee", "method": "GET", "url": "/api/meeting_attendee/:id", "flowData": {"nodes": [{"id": "url_node_1739390757676_1", "type": "url", "position": {"x": 100, "y": 100}, "data": {"label": "Url", "apiname": "Get One meeting_attendee", "path": "/api/meeting_attendee/:id", "method": "GET", "queryFields": [{"name": "id", "type": "number", "validation": "required"}]}}, {"id": "auth_node_1739390757676_1", "type": "auth", "position": {"x": 100, "y": 200}, "data": {"label": "<PERSON><PERSON>", "authType": "none"}}, {"id": "db_query_node_1739390757676_1", "type": "db-query", "position": {"x": 100, "y": 300}, "data": {"label": "Database Find", "model": "meeting_attendee", "operation": "findOne", "query": "SELECT * FROM meeting_attendee WHERE id=id", "resultVar": "meeting_attendeeOneResult"}}, {"id": "logic_node_1739390757676_1", "type": "logic", "position": {"x": 100, "y": 400}, "data": {"label": "Logic", "fields": [], "queryFields": [], "code": "//commented logic"}}, {"id": "output_node_1739390757676_1", "type": "outputs", "position": {"x": 100, "y": 500}, "data": {"label": "Response", "outputType": "json", "fields": [{"name": "id", "type": "number"}, {"name": "meeting_id", "type": "integer"}, {"name": "user_id", "type": "integer"}, {"name": "created_at", "type": "datetime"}, {"name": "updated_at", "type": "datetime"}, {"name": "error", "type": "boolean"}], "resultVar": "meeting_attendeeOneResult", "statusCode": 200}}], "edges": [{"id": "url-to-auth_1739390757676_1", "source": "url_node_1739390757676_1", "target": "auth_node_1739390757676_1"}, {"id": "auth-to-db_1739390757676_1", "source": "auth_node_1739390757676_1", "target": "db_query_node_1739390757676_1"}, {"id": "db-to-logic_1739390757676_1", "source": "db_query_node_1739390757676_1", "target": "logic_node_1739390757676_1"}, {"id": "logic-to-output_1739390757676_1", "source": "logic_node_1739390757676_1", "target": "output_node_1739390757676_1"}]}}, {"id": "route_1739390757676_bzamopo5v", "name": "Create meeting_attendee", "method": "POST", "url": "/api/meeting_attendee", "flowData": {"nodes": [{"id": "url_node_1739390757676_3", "type": "url", "position": {"x": 100, "y": 100}, "data": {"label": "Url", "apiname": "Create meeting_attendee", "path": "/api/meeting_attendee", "method": "POST", "fields": [{"name": "meeting_id", "type": "integer", "validation": ""}, {"name": "user_id", "type": "integer", "validation": ""}, {"name": "created_at", "type": "datetime", "validation": ""}, {"name": "updated_at", "type": "datetime", "validation": ""}]}}, {"id": "auth_node_1739390757676_3", "type": "auth", "position": {"x": 100, "y": 200}, "data": {"label": "<PERSON><PERSON>", "authType": "none"}}, {"id": "db_insert_node_1739390757676_3", "type": "db-insert", "position": {"x": 100, "y": 300}, "data": {"label": "Database Insert", "model": "meeting_attendee", "operation": "create", "query": "INSERT INTO meeting_attendee (meeting_id, user_id, created_at, updated_at)\n                      VALUES (:meeting_id, :user_id, :created_at, :updated_at)", "resultVar": "meeting_attendee<PERSON><PERSON><PERSON><PERSON>ult"}}, {"id": "logic_node_1739390757676_3", "type": "logic", "position": {"x": 100, "y": 400}, "data": {"label": "Logic", "fields": [], "queryFields": [], "code": "//commented logic"}}, {"id": "output_node_1739390757676_3", "type": "outputs", "position": {"x": 100, "y": 500}, "data": {"label": "Response", "outputType": "json", "fields": [{"name": "error", "type": "boolean"}, {"name": "id", "type": "number"}], "resultVar": "meeting_attendee<PERSON><PERSON><PERSON><PERSON>ult", "statusCode": 200}}], "edges": [{"id": "url-to-auth_1739390757676_3", "source": "url_node_1739390757676_3", "target": "auth_node_1739390757676_3"}, {"id": "auth-to-db_1739390757676_3", "source": "auth_node_1739390757676_3", "target": "db_insert_node_1739390757676_3"}, {"id": "db-to-logic_1739390757676_3", "source": "db_insert_node_1739390757676_3", "target": "logic_node_1739390757676_3"}, {"id": "logic-to-output_1739390757676_3", "source": "logic_node_1739390757676_3", "target": "output_node_1739390757676_3"}]}}, {"id": "route_1739390757676_d3b0jo661", "name": "Update meeting_attendee", "method": "PUT", "url": "/api/meeting_attendee/:id", "flowData": {"nodes": [{"id": "url_node_1739390757676_4", "type": "url", "position": {"x": 100, "y": 100}, "data": {"label": "Url", "apiname": "Update meeting_attendee", "path": "/api/meeting_attendee/:id", "method": "PUT", "queryFields": [{"name": "id", "type": "number", "validation": "required"}], "fields": [{"name": "meeting_id", "type": "integer", "validation": "required"}, {"name": "user_id", "type": "integer", "validation": "required"}, {"name": "created_at", "type": "datetime", "validation": "required"}, {"name": "updated_at", "type": "datetime", "validation": "required"}]}}, {"id": "auth_node_1739390757676_4", "type": "auth", "position": {"x": 100, "y": 200}, "data": {"label": "<PERSON><PERSON>", "authType": "none"}}, {"id": "db_update_node_1739390757676_4", "type": "db-update", "position": {"x": 100, "y": 300}, "data": {"label": "Database Update", "model": "meeting_attendee", "operation": "update", "idField": "id", "query": "UPDATE meeting_attendee SET meeting_id=:meeting_id, user_id=:user_id, created_at=:created_at, updated_at=:updated_at WHERE id=:id", "resultVar": "meeting_attendeeUpdateR<PERSON>ult"}}, {"id": "logic_node_1739390757676_4", "type": "logic", "position": {"x": 100, "y": 400}, "data": {"label": "Logic", "fields": [], "queryFields": [], "code": "//commented logic"}}, {"id": "output_node_1739390757676_4", "type": "outputs", "position": {"x": 100, "y": 500}, "data": {"label": "Response", "outputType": "json", "fields": [{"name": "error", "type": "boolean"}, {"name": "id", "type": "number"}], "resultVar": "meeting_attendeeUpdateR<PERSON>ult", "statusCode": 200}}], "edges": [{"id": "url-to-auth_1739390757676_4", "source": "url_node_1739390757676_4", "target": "auth_node_1739390757676_4"}, {"id": "auth-to-db_1739390757676_4", "source": "auth_node_1739390757676_4", "target": "db_update_node_1739390757676_4"}, {"id": "db-to-logic_1739390757676_4", "source": "db_update_node_1739390757676_4", "target": "logic_node_1739390757676_4"}, {"id": "logic-to-output_1739390757676_4", "source": "logic_node_1739390757676_4", "target": "output_node_1739390757676_4"}]}}, {"id": "route_1739390757676_5mohpf33q", "name": "Delete One meeting_attendee", "method": "DELETE", "url": "/api/meeting_attendee/:id", "flowData": {"nodes": [{"id": "url_node_1739390757676_2", "type": "url", "position": {"x": 100, "y": 100}, "data": {"label": "Url", "apiname": "Delete One meeting_attendee", "path": "/api/meeting_attendee/:id", "method": "DELETE", "queryFields": [{"name": "id", "type": "number", "validation": "required"}]}}, {"id": "auth_node_1739390757676_2", "type": "auth", "position": {"x": 100, "y": 200}, "data": {"label": "<PERSON><PERSON>", "authType": "none"}}, {"id": "db_delete_node_1739390757676_2", "type": "db-delete", "position": {"x": 100, "y": 300}, "data": {"label": "Database Delete", "model": "meeting_attendee", "operation": "delete", "query": "DELETE FROM meeting_attendee WHERE id=id", "resultVar": "meeting_attendee<PERSON><PERSON><PERSON>R<PERSON>ult"}}, {"id": "logic_node_1739390757676_2", "type": "logic", "position": {"x": 100, "y": 400}, "data": {"label": "Logic", "fields": [], "queryFields": [], "code": "//commented logic"}}, {"id": "output_node_1739390757676_2", "type": "outputs", "position": {"x": 100, "y": 500}, "data": {"label": "Response", "outputType": "json", "fields": [{"name": "error", "type": "boolean"}, {"name": "id", "type": "integer"}], "statusCode": 200, "resultVar": "meeting_attendee<PERSON><PERSON><PERSON>R<PERSON>ult"}}], "edges": [{"id": "url-to-auth_1739390757676_2", "source": "url_node_1739390757676_2", "target": "auth_node_1739390757676_2"}, {"id": "auth-to-db_1739390757676_2", "source": "auth_node_1739390757676_2", "target": "db_delete_node_1739390757676_2"}, {"id": "db-to-logic_1739390757676_2", "source": "db_delete_node_1739390757676_2", "target": "logic_node_1739390757676_2"}, {"id": "logic-to-output_1739390757676_2", "source": "logic_node_1739390757676_2", "target": "output_node_1739390757676_2"}]}}, {"id": "route_1739390836602_sauila1mm", "name": "Get All integration", "method": "GET", "url": "/api/integration", "flowData": {"nodes": [{"id": "url_node_1739390836602", "type": "url", "position": {"x": 100, "y": 100}, "data": {"label": "Url", "apiname": "Get All integration", "path": "/api/integration", "method": "GET"}}, {"id": "auth_node_1739390836602", "type": "auth", "position": {"x": 100, "y": 200}, "data": {"label": "<PERSON><PERSON>", "authType": "none"}}, {"id": "db_find_node_1739390836602", "type": "db-find", "position": {"x": 100, "y": 300}, "data": {"label": "Database Find", "model": "integration", "operation": "find<PERSON>any", "query": "SELECT * FROM integration", "resultVar": "integrationResult"}}, {"id": "logic_node_1739390836602", "type": "logic", "position": {"x": 100, "y": 400}, "data": {"label": "Logic", "fields": [], "queryFields": [], "code": "//commented logic"}}, {"id": "output_node_1739390836602", "type": "outputs", "position": {"x": 100, "y": 500}, "data": {"label": "Response", "outputType": "json", "fields": [{"name": "id", "type": "number"}, {"name": "status", "type": "string"}, {"name": "name", "type": "string"}, {"name": "description", "type": "string"}, {"name": "created_at", "type": "datetime"}], "resultVar": "integrationResult", "statusCode": 200}}], "edges": [{"id": "url-to-auth_1739390836602", "source": "url_node_1739390836602", "target": "auth_node_1739390836602"}, {"id": "auth-to-db_1739390836602", "source": "auth_node_1739390836602", "target": "db_find_node_1739390836602"}, {"id": "db-to-logic_1739390836602", "source": "db_find_node_1739390836602", "target": "logic_node_1739390836602"}, {"id": "logic-to-output_1739390836602", "source": "logic_node_1739390836602", "target": "output_node_1739390836602"}]}}, {"id": "route_1739390836603_wonffcpbs", "name": "Get One integration", "method": "GET", "url": "/api/integration/:id", "flowData": {"nodes": [{"id": "url_node_1739390836602_1", "type": "url", "position": {"x": 100, "y": 100}, "data": {"label": "Url", "apiname": "Get One integration", "path": "/api/integration/:id", "method": "GET", "queryFields": [{"name": "id", "type": "number", "validation": "required"}]}}, {"id": "auth_node_1739390836602_1", "type": "auth", "position": {"x": 100, "y": 200}, "data": {"label": "<PERSON><PERSON>", "authType": "none"}}, {"id": "db_query_node_1739390836602_1", "type": "db-query", "position": {"x": 100, "y": 300}, "data": {"label": "Database Find", "model": "integration", "operation": "findOne", "query": "SELECT * FROM integration WHERE id=id", "resultVar": "integrationOneResult"}}, {"id": "logic_node_1739390836602_1", "type": "logic", "position": {"x": 100, "y": 400}, "data": {"label": "Logic", "fields": [], "queryFields": [], "code": "//commented logic"}}, {"id": "output_node_1739390836602_1", "type": "outputs", "position": {"x": 100, "y": 500}, "data": {"label": "Response", "outputType": "json", "fields": [{"name": "id", "type": "number"}, {"name": "status", "type": "string"}, {"name": "name", "type": "string"}, {"name": "description", "type": "string"}, {"name": "created_at", "type": "datetime"}, {"name": "error", "type": "boolean"}], "resultVar": "integrationOneResult", "statusCode": 200}}], "edges": [{"id": "url-to-auth_1739390836602_1", "source": "url_node_1739390836602_1", "target": "auth_node_1739390836602_1"}, {"id": "auth-to-db_1739390836602_1", "source": "auth_node_1739390836602_1", "target": "db_query_node_1739390836602_1"}, {"id": "db-to-logic_1739390836602_1", "source": "db_query_node_1739390836602_1", "target": "logic_node_1739390836602_1"}, {"id": "logic-to-output_1739390836602_1", "source": "logic_node_1739390836602_1", "target": "output_node_1739390836602_1"}]}}, {"id": "route_1739390836603_pmjkhcd14", "name": "Create integration", "method": "POST", "url": "/api/integration", "flowData": {"nodes": [{"id": "url_node_1739390836602_3", "type": "url", "position": {"x": 100, "y": 100}, "data": {"label": "Url", "apiname": "Create integration", "path": "/api/integration", "method": "POST", "fields": [{"name": "status", "type": "string", "validation": ""}, {"name": "name", "type": "string", "validation": ""}, {"name": "description", "type": "string", "validation": ""}, {"name": "created_at", "type": "datetime", "validation": ""}]}}, {"id": "auth_node_1739390836602_3", "type": "auth", "position": {"x": 100, "y": 200}, "data": {"label": "<PERSON><PERSON>", "authType": "none"}}, {"id": "db_insert_node_1739390836602_3", "type": "db-insert", "position": {"x": 100, "y": 300}, "data": {"label": "Database Insert", "model": "integration", "operation": "create", "query": "INSERT INTO integration (status, name, description, created_at)\n                      VALUES (:status, :name, :description, :created_at)", "resultVar": "integrationCreateResult"}}, {"id": "logic_node_1739390836602_3", "type": "logic", "position": {"x": 100, "y": 400}, "data": {"label": "Logic", "fields": [], "queryFields": [], "code": "//commented logic"}}, {"id": "output_node_1739390836602_3", "type": "outputs", "position": {"x": 100, "y": 500}, "data": {"label": "Response", "outputType": "json", "fields": [{"name": "error", "type": "boolean"}, {"name": "id", "type": "number"}], "resultVar": "integrationCreateResult", "statusCode": 200}}], "edges": [{"id": "url-to-auth_1739390836602_3", "source": "url_node_1739390836602_3", "target": "auth_node_1739390836602_3"}, {"id": "auth-to-db_1739390836602_3", "source": "auth_node_1739390836602_3", "target": "db_insert_node_1739390836602_3"}, {"id": "db-to-logic_1739390836602_3", "source": "db_insert_node_1739390836602_3", "target": "logic_node_1739390836602_3"}, {"id": "logic-to-output_1739390836602_3", "source": "logic_node_1739390836602_3", "target": "output_node_1739390836602_3"}]}}, {"id": "route_1739390836603_jgbsu3cyd", "name": "Update integration", "method": "PUT", "url": "/api/integration/:id", "flowData": {"nodes": [{"id": "url_node_1739390836602_4", "type": "url", "position": {"x": 100, "y": 100}, "data": {"label": "Url", "apiname": "Update integration", "path": "/api/integration/:id", "method": "PUT", "queryFields": [{"name": "id", "type": "number", "validation": "required"}], "fields": [{"name": "status", "type": "string", "validation": "required"}, {"name": "name", "type": "string", "validation": "required"}, {"name": "description", "type": "string", "validation": ""}, {"name": "created_at", "type": "datetime", "validation": "required"}]}}, {"id": "auth_node_1739390836602_4", "type": "auth", "position": {"x": 100, "y": 200}, "data": {"label": "<PERSON><PERSON>", "authType": "none"}}, {"id": "db_update_node_1739390836602_4", "type": "db-update", "position": {"x": 100, "y": 300}, "data": {"label": "Database Update", "model": "integration", "operation": "update", "idField": "id", "query": "UPDATE integration SET status=:status, name=:name, description=:description, created_at=:created_at WHERE id=:id", "resultVar": "integrationUpdateResult"}}, {"id": "logic_node_1739390836602_4", "type": "logic", "position": {"x": 100, "y": 400}, "data": {"label": "Logic", "fields": [], "queryFields": [], "code": "//commented logic"}}, {"id": "output_node_1739390836602_4", "type": "outputs", "position": {"x": 100, "y": 500}, "data": {"label": "Response", "outputType": "json", "fields": [{"name": "error", "type": "boolean"}, {"name": "id", "type": "number"}], "resultVar": "integrationUpdateResult", "statusCode": 200}}], "edges": [{"id": "url-to-auth_1739390836602_4", "source": "url_node_1739390836602_4", "target": "auth_node_1739390836602_4"}, {"id": "auth-to-db_1739390836602_4", "source": "auth_node_1739390836602_4", "target": "db_update_node_1739390836602_4"}, {"id": "db-to-logic_1739390836602_4", "source": "db_update_node_1739390836602_4", "target": "logic_node_1739390836602_4"}, {"id": "logic-to-output_1739390836602_4", "source": "logic_node_1739390836602_4", "target": "output_node_1739390836602_4"}]}}, {"id": "route_1739390836603_i26uxem9w", "name": "Delete One integration", "method": "DELETE", "url": "/api/integration/:id", "flowData": {"nodes": [{"id": "url_node_1739390836602_2", "type": "url", "position": {"x": 100, "y": 100}, "data": {"label": "Url", "apiname": "Delete One integration", "path": "/api/integration/:id", "method": "DELETE", "queryFields": [{"name": "id", "type": "number", "validation": "required"}]}}, {"id": "auth_node_1739390836602_2", "type": "auth", "position": {"x": 100, "y": 200}, "data": {"label": "<PERSON><PERSON>", "authType": "none"}}, {"id": "db_delete_node_1739390836602_2", "type": "db-delete", "position": {"x": 100, "y": 300}, "data": {"label": "Database Delete", "model": "integration", "operation": "delete", "query": "DELETE FROM integration WHERE id=id", "resultVar": "integrationDeleteResult"}}, {"id": "logic_node_1739390836602_2", "type": "logic", "position": {"x": 100, "y": 400}, "data": {"label": "Logic", "fields": [], "queryFields": [], "code": "//commented logic"}}, {"id": "output_node_1739390836602_2", "type": "outputs", "position": {"x": 100, "y": 500}, "data": {"label": "Response", "outputType": "json", "fields": [{"name": "error", "type": "boolean"}, {"name": "id", "type": "integer"}], "statusCode": 200, "resultVar": "integrationDeleteResult"}}], "edges": [{"id": "url-to-auth_1739390836602_2", "source": "url_node_1739390836602_2", "target": "auth_node_1739390836602_2"}, {"id": "auth-to-db_1739390836602_2", "source": "auth_node_1739390836602_2", "target": "db_delete_node_1739390836602_2"}, {"id": "db-to-logic_1739390836602_2", "source": "db_delete_node_1739390836602_2", "target": "logic_node_1739390836602_2"}, {"id": "logic-to-output_1739390836602_2", "source": "logic_node_1739390836602_2", "target": "output_node_1739390836602_2"}]}}, {"id": "route_1739390897460_1ozgsijwq", "name": "Get All analytics_events", "method": "GET", "url": "/api/analytics_events", "flowData": {"nodes": [{"id": "url_node_1739390897460", "type": "url", "position": {"x": 100, "y": 100}, "data": {"label": "Url", "apiname": "Get All analytics_events", "path": "/api/analytics_events", "method": "GET"}}, {"id": "auth_node_1739390897460", "type": "auth", "position": {"x": 100, "y": 200}, "data": {"label": "<PERSON><PERSON>", "authType": "none"}}, {"id": "db_find_node_1739390897460", "type": "db-find", "position": {"x": 100, "y": 300}, "data": {"label": "Database Find", "model": "analytics_events", "operation": "find<PERSON>any", "query": "SELECT * FROM analytics_events", "resultVar": "analytics_eventsResult"}}, {"id": "logic_node_1739390897460", "type": "logic", "position": {"x": 100, "y": 400}, "data": {"label": "Logic", "fields": [], "queryFields": [], "code": "//commented logic"}}, {"id": "output_node_1739390897460", "type": "outputs", "position": {"x": 100, "y": 500}, "data": {"label": "Response", "outputType": "json", "fields": [{"name": "id", "type": "number"}, {"name": "updated_at", "type": "datetime"}, {"name": "created_at", "type": "datetime"}, {"name": "details", "type": "string"}, {"name": "user_id", "type": "integer"}, {"name": "type", "type": "integer"}], "resultVar": "analytics_eventsResult", "statusCode": 200}}], "edges": [{"id": "url-to-auth_1739390897460", "source": "url_node_1739390897460", "target": "auth_node_1739390897460"}, {"id": "auth-to-db_1739390897460", "source": "auth_node_1739390897460", "target": "db_find_node_1739390897460"}, {"id": "db-to-logic_1739390897460", "source": "db_find_node_1739390897460", "target": "logic_node_1739390897460"}, {"id": "logic-to-output_1739390897460", "source": "logic_node_1739390897460", "target": "output_node_1739390897460"}]}}, {"id": "route_1739390897460_0ouaes56e", "name": "Get One analytics_events", "method": "GET", "url": "/api/analytics_events/:id", "flowData": {"nodes": [{"id": "url_node_1739390897460_1", "type": "url", "position": {"x": 100, "y": 100}, "data": {"label": "Url", "apiname": "Get One analytics_events", "path": "/api/analytics_events/:id", "method": "GET", "queryFields": [{"name": "id", "type": "number", "validation": "required"}]}}, {"id": "auth_node_1739390897460_1", "type": "auth", "position": {"x": 100, "y": 200}, "data": {"label": "<PERSON><PERSON>", "authType": "none"}}, {"id": "db_query_node_1739390897460_1", "type": "db-query", "position": {"x": 100, "y": 300}, "data": {"label": "Database Find", "model": "analytics_events", "operation": "findOne", "query": "SELECT * FROM analytics_events WHERE id=id", "resultVar": "analytics_eventsOneResult"}}, {"id": "logic_node_1739390897460_1", "type": "logic", "position": {"x": 100, "y": 400}, "data": {"label": "Logic", "fields": [], "queryFields": [], "code": "//commented logic"}}, {"id": "output_node_1739390897460_1", "type": "outputs", "position": {"x": 100, "y": 500}, "data": {"label": "Response", "outputType": "json", "fields": [{"name": "id", "type": "number"}, {"name": "updated_at", "type": "datetime"}, {"name": "created_at", "type": "datetime"}, {"name": "details", "type": "string"}, {"name": "user_id", "type": "integer"}, {"name": "type", "type": "integer"}, {"name": "error", "type": "boolean"}], "resultVar": "analytics_eventsOneResult", "statusCode": 200}}], "edges": [{"id": "url-to-auth_1739390897460_1", "source": "url_node_1739390897460_1", "target": "auth_node_1739390897460_1"}, {"id": "auth-to-db_1739390897460_1", "source": "auth_node_1739390897460_1", "target": "db_query_node_1739390897460_1"}, {"id": "db-to-logic_1739390897460_1", "source": "db_query_node_1739390897460_1", "target": "logic_node_1739390897460_1"}, {"id": "logic-to-output_1739390897460_1", "source": "logic_node_1739390897460_1", "target": "output_node_1739390897460_1"}]}}, {"id": "route_1739390897460_qyobobujo", "name": "Create analytics_events", "method": "POST", "url": "/api/analytics_events", "flowData": {"nodes": [{"id": "url_node_1739390897460_3", "type": "url", "position": {"x": 100, "y": 100}, "data": {"label": "Url", "apiname": "Create analytics_events", "path": "/api/analytics_events", "method": "POST", "fields": [{"name": "updated_at", "type": "datetime", "validation": ""}, {"name": "created_at", "type": "datetime", "validation": ""}, {"name": "details", "type": "string", "validation": ""}, {"name": "user_id", "type": "integer", "validation": ""}, {"name": "type", "type": "integer", "validation": ""}]}}, {"id": "auth_node_1739390897460_3", "type": "auth", "position": {"x": 100, "y": 200}, "data": {"label": "<PERSON><PERSON>", "authType": "none"}}, {"id": "db_insert_node_1739390897460_3", "type": "db-insert", "position": {"x": 100, "y": 300}, "data": {"label": "Database Insert", "model": "analytics_events", "operation": "create", "query": "INSERT INTO analytics_events (updated_at, created_at, details, user_id, type)\n                      VALUES (:updated_at, :created_at, :details, :user_id, :type)", "resultVar": "analytics_eventsCreateResult"}}, {"id": "logic_node_1739390897460_3", "type": "logic", "position": {"x": 100, "y": 400}, "data": {"label": "Logic", "fields": [], "queryFields": [], "code": "//commented logic"}}, {"id": "output_node_1739390897460_3", "type": "outputs", "position": {"x": 100, "y": 500}, "data": {"label": "Response", "outputType": "json", "fields": [{"name": "error", "type": "boolean"}, {"name": "id", "type": "number"}], "resultVar": "analytics_eventsCreateResult", "statusCode": 200}}], "edges": [{"id": "url-to-auth_1739390897460_3", "source": "url_node_1739390897460_3", "target": "auth_node_1739390897460_3"}, {"id": "auth-to-db_1739390897460_3", "source": "auth_node_1739390897460_3", "target": "db_insert_node_1739390897460_3"}, {"id": "db-to-logic_1739390897460_3", "source": "db_insert_node_1739390897460_3", "target": "logic_node_1739390897460_3"}, {"id": "logic-to-output_1739390897460_3", "source": "logic_node_1739390897460_3", "target": "output_node_1739390897460_3"}]}}, {"id": "route_1739390897460_qih1x9m29", "name": "Update analytics_events", "method": "PUT", "url": "/api/analytics_events/:id", "flowData": {"nodes": [{"id": "url_node_1739390897460_4", "type": "url", "position": {"x": 100, "y": 100}, "data": {"label": "Url", "apiname": "Update analytics_events", "path": "/api/analytics_events/:id", "method": "PUT", "queryFields": [{"name": "id", "type": "number", "validation": "required"}], "fields": [{"name": "updated_at", "type": "datetime", "validation": "required"}, {"name": "created_at", "type": "datetime", "validation": "required"}, {"name": "details", "type": "string", "validation": "required"}, {"name": "user_id", "type": "integer", "validation": "required"}, {"name": "type", "type": "integer", "validation": "required"}]}}, {"id": "auth_node_1739390897460_4", "type": "auth", "position": {"x": 100, "y": 200}, "data": {"label": "<PERSON><PERSON>", "authType": "none"}}, {"id": "db_update_node_1739390897460_4", "type": "db-update", "position": {"x": 100, "y": 300}, "data": {"label": "Database Update", "model": "analytics_events", "operation": "update", "idField": "id", "query": "UPDATE analytics_events SET updated_at=:updated_at, created_at=:created_at, details=:details, user_id=:user_id, type=:type WHERE id=:id", "resultVar": "analytics_eventsUpdateResult"}}, {"id": "logic_node_1739390897460_4", "type": "logic", "position": {"x": 100, "y": 400}, "data": {"label": "Logic", "fields": [], "queryFields": [], "code": "//commented logic"}}, {"id": "output_node_1739390897460_4", "type": "outputs", "position": {"x": 100, "y": 500}, "data": {"label": "Response", "outputType": "json", "fields": [{"name": "error", "type": "boolean"}, {"name": "id", "type": "number"}], "resultVar": "analytics_eventsUpdateResult", "statusCode": 200}}], "edges": [{"id": "url-to-auth_1739390897460_4", "source": "url_node_1739390897460_4", "target": "auth_node_1739390897460_4"}, {"id": "auth-to-db_1739390897460_4", "source": "auth_node_1739390897460_4", "target": "db_update_node_1739390897460_4"}, {"id": "db-to-logic_1739390897460_4", "source": "db_update_node_1739390897460_4", "target": "logic_node_1739390897460_4"}, {"id": "logic-to-output_1739390897460_4", "source": "logic_node_1739390897460_4", "target": "output_node_1739390897460_4"}]}}, {"id": "route_1739390897460_pj1a4mesj", "name": "Delete One analytics_events", "method": "DELETE", "url": "/api/analytics_events/:id", "flowData": {"nodes": [{"id": "url_node_1739390897460_2", "type": "url", "position": {"x": 100, "y": 100}, "data": {"label": "Url", "apiname": "Delete One analytics_events", "path": "/api/analytics_events/:id", "method": "DELETE", "queryFields": [{"name": "id", "type": "number", "validation": "required"}]}}, {"id": "auth_node_1739390897460_2", "type": "auth", "position": {"x": 100, "y": 200}, "data": {"label": "<PERSON><PERSON>", "authType": "none"}}, {"id": "db_delete_node_1739390897460_2", "type": "db-delete", "position": {"x": 100, "y": 300}, "data": {"label": "Database Delete", "model": "analytics_events", "operation": "delete", "query": "DELETE FROM analytics_events WHERE id=id", "resultVar": "analytics_eventsDeleteResult"}}, {"id": "logic_node_1739390897460_2", "type": "logic", "position": {"x": 100, "y": 400}, "data": {"label": "Logic", "fields": [], "queryFields": [], "code": "//commented logic"}}, {"id": "output_node_1739390897460_2", "type": "outputs", "position": {"x": 100, "y": 500}, "data": {"label": "Response", "outputType": "json", "fields": [{"name": "error", "type": "boolean"}, {"name": "id", "type": "integer"}], "statusCode": 200, "resultVar": "analytics_eventsDeleteResult"}}], "edges": [{"id": "url-to-auth_1739390897460_2", "source": "url_node_1739390897460_2", "target": "auth_node_1739390897460_2"}, {"id": "auth-to-db_1739390897460_2", "source": "auth_node_1739390897460_2", "target": "db_delete_node_1739390897460_2"}, {"id": "db-to-logic_1739390897460_2", "source": "db_delete_node_1739390897460_2", "target": "logic_node_1739390897460_2"}, {"id": "logic-to-output_1739390897460_2", "source": "logic_node_1739390897460_2", "target": "output_node_1739390897460_2"}]}}], "roles": [{"id": "role_admin_1739375213616", "name": "Admin", "slug": "super_admin", "permissions": {"routes": ["route_1739386129532_uo04fn0zm", "route_1739386110981_e8yyjh0ca", "route_1739386041212_qj8tyjr7n", "route_1739385952914_23a1ry71i", "route_1739386087400_xksxvb48n", "route_1739386062398_ncz1n28co", "route_1739386147741_f7pg9j7oo"], "canCreateUsers": true, "canEditUsers": true, "canDeleteUsers": true, "canManageRoles": true, "canLogin": true, "canRegister": false, "canForgot": false, "canReset": false, "canGoogleLogin": false, "canAppleLogin": false, "canMicrosoftLogin": false, "canMagicLinkLogin": false, "canTwitterLogin": false, "needs2FA": false, "canSetPermissions": false, "canPreference": true, "canVerifyEmail": true, "canUpload": true, "canStripe": true, "canStripeWebhook": true, "canRealTime": true, "canAI": true, "canUpdateEmail": true, "canUpdatePassword": true, "canUpdateOtherUsers": true, "treeql": {"enabled": true, "models": {"stripe_price": {"allowed": true, "blacklistedFields": [], "operations": {"get": true, "getOne": true, "getAll": true, "post": true, "put": true, "delete": true, "paginate": true, "join": true}}, "stripe_product": {"allowed": true, "blacklistedFields": [], "operations": {"get": true, "getOne": true, "getAll": true, "post": true, "put": true, "delete": true, "paginate": true, "join": true}}, "preference": {"allowed": true, "blacklistedFields": [], "operations": {"get": true, "getOne": true, "getAll": true, "post": true, "put": true, "delete": true, "paginate": true, "join": true}}, "tokens": {"allowed": true, "blacklistedFields": [], "operations": {"get": true, "getOne": true, "getAll": true, "post": true, "put": true, "delete": true, "paginate": true, "join": true}}, "job": {"allowed": true, "blacklistedFields": [], "operations": {"get": true, "getOne": true, "getAll": true, "post": true, "put": true, "delete": true, "paginate": true, "join": true}}, "user": {"allowed": true, "blacklistedFields": [], "operations": {"get": true, "getOne": true, "getAll": true, "post": true, "put": true, "delete": true, "paginate": true, "join": true}}}}}}, {"id": "role_member_1739375213616", "name": "Member", "slug": "member", "permissions": {"routes": ["route_1739375223150_9a4tag659", "route_1739384922583_8gtz5qht9", "route_1739385016991_zb2weo099", "route_1739385057034_u68filmso", "route_1739385093054_6ov0b38lv", "route_1739385185061_bxl49bjjr", "route_1739385216051_vy3pu7t59", "route_1739385261436_pj7faruyo", "route_1739385290678_h38a7id5q", "route_1739385419994_3lhn64zn4", "route_1739385448218_1bq4blwg0", "route_1739385469770_27x1d2y62", "route_1739385500202_y047j4xmn", "route_1739385518101_j0jbsmmqf", "route_1739385538291_fuzo8lpbm", "route_1739385568235_410dr7vhn", "route_1739385664771_udgvrxnmu", "route_1739385691797_z8gxp0c7d", "route_1739385714933_o0z3w5n6a", "route_1739385731307_c7sewh891", "route_1739385753502_oqlewwa7m", "route_1739385788064_6kvf6edu6", "route_1739385804262_qt7978o0f", "route_1739385832215_v05g2e705", "route_1739385850399_9hbft2has", "route_1739385873401_qu56nh2xy", "route_1739385898273_1vlu53txc", "route_1739385952914_23a1ry71i", "route_1739385980711_tgp00ehp1", "route_1739386006565_g5k30cwt9"], "canCreateUsers": true, "canEditUsers": true, "canDeleteUsers": true, "canManageRoles": true, "canLogin": true, "canRegister": true, "canForgot": false, "canReset": false, "canGoogleLogin": true, "canAppleLogin": false, "canMicrosoftLogin": false, "canMagicLinkLogin": false, "canTwitterLogin": false, "needs2FA": false, "canSetPermissions": false, "canPreference": true, "canVerifyEmail": true, "canUpload": true, "canStripe": true, "canStripeWebhook": true, "canRealTime": true, "canAI": false, "canUpdateEmail": true, "canUpdatePassword": true, "canUpdateOtherUsers": false, "treeql": {"enabled": true, "models": {"tokens": {"allowed": true, "blacklistedFields": [], "operations": {"get": true, "getOne": true, "getAll": false, "post": true, "put": true, "delete": false, "paginate": true, "join": true}}, "stripe_price": {"allowed": true, "blacklistedFields": [], "operations": {"get": true, "getOne": true, "getAll": true, "post": false, "put": false, "delete": false, "paginate": true, "join": true}}, "stripe_product": {"allowed": true, "blacklistedFields": [], "operations": {"get": true, "getOne": true, "getAll": true, "post": false, "put": false, "delete": false, "paginate": true, "join": true}}, "preference": {"allowed": true, "blacklistedFields": [], "operations": {"get": true, "getOne": true, "getAll": false, "post": false, "put": false, "delete": false, "paginate": false, "join": true}}, "user": {"allowed": true, "blacklistedFields": ["password", "login_type"], "operations": {"get": true, "getOne": true, "getAll": false, "post": false, "put": false, "delete": false, "paginate": false, "join": true}}}}, "companyScoped": false}}]}