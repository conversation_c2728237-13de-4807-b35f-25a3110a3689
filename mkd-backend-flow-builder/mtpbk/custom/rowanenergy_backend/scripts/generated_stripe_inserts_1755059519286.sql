-- Auto-generated SQL for Hover Lens Stripe products and prices
-- Generated on: 2025-08-13T04:31:59.286Z

-- Insert Stripe Products
INSERT INTO rowanenergy_stripe_products (stripe_product_id, name, description, active, metadata, created_at, updated_at) VALUES
('prod_SrEpzhYCnWlbbu', 'Premium Plan', 'Perfect for growing businesses with unlimited survey bookings and priority support', 1, '{"features":"[\"Unlimited survey bookings\",\"Priority engineer assignment\",\"Advanced reporting & analytics\",\"Priority support\",\"Custom branding on reports\"]","plan_type":"premium","platform":"rowanenergy"}', NOW(), NOW());

INSERT INTO rowanenergy_stripe_products (stripe_product_id, name, description, active, metadata, created_at, updated_at) VALUES
('prod_SrEpo1rlCqhH5F', 'Enterprise Plan', 'For large organizations requiring dedicated support and custom solutions', 1, '{"features":"[\"Everything in Premium\",\"Dedicated account manager\",\"Custom SLA agreements\",\"API access\",\"White-label solution\",\"Multi-location management\"]","plan_type":"enterprise","platform":"rowanenergy"}', NOW(), NOW());

-- Insert Stripe Prices
INSERT INTO rowanenergy_stripe_prices (stripe_price_id, stripe_product_id, unit_amount, currency, recurring_interval, recurring_interval_count, nickname, active, metadata, created_at, updated_at) VALUES
('price_1RvWLyBgOlWo0lDUo3xoCBQT', 'prod_SrEpzhYCnWlbbu', 4900, 'usd', 'month', 1, 'Premium Plan - Monthly', 1, '{"plan_type":"premium","platform":"rowanenergy"}', NOW(), NOW());

INSERT INTO rowanenergy_stripe_prices (stripe_price_id, stripe_product_id, unit_amount, currency, recurring_interval, recurring_interval_count, nickname, active, metadata, created_at, updated_at) VALUES
('price_1RvWLzBgOlWo0lDUnttwvg4e', 'prod_SrEpo1rlCqhH5F', 9900, 'usd', 'month', 1, 'Enterprise Plan - Monthly', 1, '{"plan_type":"enterprise","platform":"rowanenergy"}', NOW(), NOW());

