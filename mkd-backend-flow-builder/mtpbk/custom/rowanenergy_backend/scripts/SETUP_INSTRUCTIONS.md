# Billing System Setup Instructions

## Overview
You need to run the SQL insert statements to populate your database with the Stripe products and prices that were created in Stripe.

## Step 1: Create the Tables (if not already done)
First, make sure you have the Stripe tables created. Run the SQL from `stripe_tables.sql`:

```sql
-- These tables should already exist from the previous setup
-- If not, run the SQL from stripe_tables.sql
```

## Step 2: Insert the Product and Price Data

Run these SQL statements in your database (MySQL/MariaDB):

```sql
-- Insert Stripe Products
INSERT INTO rowanenergy_stripe_products (stripe_product_id, name, description, active, metadata, created_at, updated_at) VALUES
('prod_SrEpzhYCnWlbbu', 'Premium Plan', 'Perfect for growing businesses with unlimited survey bookings and priority support', 1, '{"features":"[\"Unlimited survey bookings\",\"Priority engineer assignment\",\"Advanced reporting & analytics\",\"Priority support\",\"Custom branding on reports\"]","plan_type":"premium","platform":"rowanenergy"}', NOW(), NOW());

INSERT INTO rowanenergy_stripe_products (stripe_product_id, name, description, active, metadata, created_at, updated_at) VALUES
('prod_SrEpo1rlCqhH5F', 'Enterprise Plan', 'For large organizations requiring dedicated support and custom solutions', 1, '{"features":"[\"Everything in Premium\",\"Dedicated account manager\",\"Custom SLA agreements\",\"API access\",\"White-label solution\",\"Multi-location management\"]","plan_type":"enterprise","platform":"rowanenergy"}', NOW(), NOW());

-- Insert Stripe Prices
INSERT INTO rowanenergy_stripe_prices (stripe_price_id, stripe_product_id, unit_amount, currency, recurring_interval, recurring_interval_count, nickname, active, metadata, created_at, updated_at) VALUES
('price_1RvWLyBgOlWo0lDUo3xoCBQT', 'prod_SrEpzhYCnWlbbu', 4900, 'usd', 'month', 1, 'Premium Plan - Monthly', 1, '{"plan_type":"premium","platform":"rowanenergy"}', NOW(), NOW());

INSERT INTO rowanenergy_stripe_prices (stripe_price_id, stripe_product_id, unit_amount, currency, recurring_interval, recurring_interval_count, nickname, active, metadata, created_at, updated_at) VALUES
('price_1RvWLzBgOlWo0lDUnttwvg4e', 'prod_SrEpo1rlCqhH5F', 9900, 'usd', 'month', 1, 'Enterprise Plan - Monthly', 1, '{"plan_type":"enterprise","platform":"rowanenergy"}', NOW(), NOW());
```

## Step 3: Verify the Data

After running the inserts, verify the data was inserted correctly:

```sql
-- Check products
SELECT * FROM rowanenergy_stripe_products;

-- Check prices
SELECT * FROM rowanenergy_stripe_prices;

-- Check the relationship
SELECT 
    p.name as product_name,
    pr.nickname as price_name,
    pr.unit_amount / 100 as amount_usd,
    pr.currency,
    pr.recurring_interval
FROM rowanenergy_stripe_products p
JOIN rowanenergy_stripe_prices pr ON p.stripe_product_id = pr.stripe_product_id
WHERE p.active = 1 AND pr.active = 1;
```

## Step 4: Restart Your Backend Server

After inserting the data, restart your backend server to ensure all changes take effect.

## Step 5: Test the Billing Page

1. Go to your frontend billing page
2. You should now see the Premium Plan ($49/month) and Enterprise Plan ($99/month)
3. The console will show the API responses for debugging

## Expected API Response

After setup, your API should return:

```json
{
  "error": false,
  "model": {
    "plans": [
      {
        "id": "price_1RvWLyBgOlWo0lDUo3xoCBQT",
        "product_id": "prod_SrEpzhYCnWlbbu",
        "name": "Premium Plan - Monthly",
        "description": "Perfect for growing businesses with unlimited survey bookings and priority support",
        "amount": 49,
        "currency": "usd",
        "interval": "month",
        "features": ["Unlimited survey bookings", "Priority engineer assignment", ...]
      },
      {
        "id": "price_1RvWLzBgOlWo0lDUnttwvg4e",
        "product_id": "prod_SrEpo1rlCqhH5F",
        "name": "Enterprise Plan - Monthly",
        "description": "For large organizations requiring dedicated support and custom solutions",
        "amount": 99,
        "currency": "usd",
        "interval": "month",
        "features": ["Everything in Premium", "Dedicated account manager", ...]
      }
    ],
    "free_plan": {
      "id": "free",
      "name": "Free Plan",
      "description": "Basic features for getting started",
      "amount": 0,
      "currency": "usd",
      "interval": "month",
      "features": ["Up to 2 survey bookings per month", "Basic reporting", "Email support"]
    }
  }
}
```

## Troubleshooting

### No Plans Showing
- Check if the SQL inserts ran successfully
- Verify the table names match your schema
- Check the browser console for API errors

### API Errors
- Ensure your backend server is running
- Check the backend logs for error messages
- Verify Stripe API keys are configured

### Subscription Errors
- Ensure you have a valid Stripe account
- Use Stripe test cards for testing
- Check that the price IDs in the database match those in Stripe

## Testing Subscription Flow

Use these Stripe test cards:
- **Success**: `****************`
- **Decline**: `****************`
- **Requires Authentication**: `****************`

All test cards should use:
- Any future expiry date (e.g., 12/34)
- Any 3-digit CVC (e.g., 123)
- Any valid ZIP code (e.g., 12345)
