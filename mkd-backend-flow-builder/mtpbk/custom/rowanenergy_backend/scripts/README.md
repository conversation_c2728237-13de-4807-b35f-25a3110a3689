# Hover Lens Billing Setup Scripts

This directory contains scripts to set up the Stripe billing system for Hover Lens.

## Files

- `setup_billing_system.js` - Complete setup script (creates Stripe products + generates SQL)
- `setup_stripe_products.js` - Stripe-only setup script
- `insert_test_stripe_data.sql` - Manual SQL inserts for testing
- `README.md` - This file

## Quick Setup (Recommended)

1. **Run the complete setup script:**
   ```bash
   cd /path/to/mkd-backend-flow-builder
   node mtpbk/custom/rowanenergy_backend/scripts/setup_billing_system.js
   ```

2. **Execute the generated SQL:**
   ```bash
   mysql -u [username] -p [database] < generated_stripe_inserts_[timestamp].sql
   ```

3. **Test the billing page in your frontend**

## What Gets Created

### Stripe Products
- **Premium Plan** - $49/month
  - Unlimited survey bookings
  - Priority engineer assignment
  - Advanced reporting & analytics
  - Priority support
  - Custom branding on reports

- **Enterprise Plan** - $99/month
  - Everything in Premium
  - Dedicated account manager
  - Custom SLA agreements
  - API access
  - White-label solution
  - Multi-location management

### Database Tables
The scripts work with these tables (created by `stripe_tables.sql`):
- `rowanenergy_stripe_customers`
- `rowanenergy_stripe_products`
- `rowanenergy_stripe_prices`
- `rowanenergy_stripe_subscriptions`

## Manual Setup (Alternative)

If you prefer to set up manually:

1. **Create Stripe products only:**
   ```bash
   node setup_stripe_products.js
   ```

2. **Use manual SQL inserts:**
   ```bash
   mysql -u [username] -p [database] < insert_test_stripe_data.sql
   ```

## Environment Requirements

Make sure your environment has:
- Valid Stripe API keys configured
- Database connection working
- StripeService properly configured

## Testing

After setup, you can test:
1. Go to your frontend billing page
2. Try subscribing to a plan (use Stripe test cards)
3. Check Stripe dashboard for created subscriptions
4. Verify database records are created

## Test Credit Cards

Use these Stripe test cards:
- **Success:** `****************`
- **Decline:** `****************`
- **Auth Required:** `****************`

Use any future date for expiry and any 3-digit CVC.

## Troubleshooting

**Script fails with Stripe error:**
- Check your Stripe API keys
- Ensure you're using the correct Stripe account
- Check if products already exist (use cleanup option)

**Database errors:**
- Ensure tables exist (run `stripe_tables.sql` first)
- Check database connection
- Verify table names match your schema

**Frontend not showing plans:**
- Check browser console for errors
- Verify API endpoints are working
- Test the API directly with Postman

## Cleanup

To remove test products:
```bash
node setup_stripe_products.js --cleanup
```

This will archive (not delete) products in Stripe.
