/**
 * Stripe Products and Prices Setup Script for Hover Lens
 * This script creates products and prices in Stripe and stores them in the database
 * 
 * Usage: node setup_stripe_products.js
 */

const StripeService = require('../../../baas/services/StripeService');

// Initialize Stripe service
const stripeService = new StripeService();

// Products to create
const products = [
  {
    name: 'Premium Plan',
    description: 'Perfect for growing businesses with unlimited survey bookings and priority support',
    metadata: {
      platform: 'rowanenergy',
      plan_type: 'premium',
      features: JSON.stringify([
        'Unlimited survey bookings',
        'Priority engineer assignment',
        'Advanced reporting & analytics',
        'Priority support',
        'Custom branding on reports'
      ])
    },
    prices: [
      {
        amount: 4900, // $49.00 in cents
        currency: 'usd',
        interval: 'month',
        nickname: 'Premium Plan - Monthly'
      }
    ]
  },
  {
    name: 'Enterprise Plan',
    description: 'For large organizations requiring dedicated support and custom solutions',
    metadata: {
      platform: 'rowanenergy',
      plan_type: 'enterprise',
      features: JSON.stringify([
        'Everything in Premium',
        'Dedicated account manager',
        'Custom SLA agreements',
        'API access',
        'White-label solution',
        'Multi-location management'
      ])
    },
    prices: [
      {
        amount: 9900, // $99.00 in cents
        currency: 'usd',
        interval: 'month',
        nickname: 'Enterprise Plan - Monthly'
      }
    ]
  }
];

async function createProducts() {
  console.log('🚀 Starting Stripe products and prices setup...\n');
  
  const results = [];
  
  for (const productData of products) {
    try {
      console.log(`📦 Creating product: ${productData.name}`);
      
      // Create product in Stripe
      const product = await stripeService.createStripeProduct({
        name: productData.name,
        description: productData.description,
        metadata: productData.metadata
      });
      
      if (product.error) {
        console.error(`❌ Error creating product ${productData.name}:`, product.message);
        continue;
      }
      
      console.log(`✅ Product created: ${product.id}`);
      
      // Create prices for this product
      const productPrices = [];
      
      for (const priceData of productData.prices) {
        console.log(`💰 Creating price: ${priceData.nickname}`);
        
        const price = await stripeService.createStripeRecurringPrice({
          productId: product.id,
          name: priceData.nickname,
          amount: priceData.amount / 100, // Convert from cents to dollars for StripeService
          currency: priceData.currency,
          interval: priceData.interval,
          interval_count: 1,
          trial_days: 0, // No trial period
          metadata: {
            platform: 'rowanenergy',
            plan_type: productData.metadata.plan_type
          }
        });
        
        if (price.error) {
          console.error(`❌ Error creating price ${priceData.nickname}:`, price.message);
          continue;
        }
        
        console.log(`✅ Price created: ${price.id} ($${priceData.amount / 100}/${priceData.interval})`);
        productPrices.push(price);
      }
      
      results.push({
        product,
        prices: productPrices
      });
      
      console.log(''); // Empty line for readability
      
    } catch (error) {
      console.error(`❌ Error processing product ${productData.name}:`, error.message);
    }
  }
  
  return results;
}

async function displayResults(results) {
  console.log('📊 Setup Complete! Here are your products and prices:\n');
  console.log('=' * 60);
  
  results.forEach(({ product, prices }) => {
    console.log(`\n🏷️  Product: ${product.name}`);
    console.log(`   ID: ${product.id}`);
    console.log(`   Description: ${product.description}`);
    
    prices.forEach(price => {
      console.log(`\n   💰 Price: ${price.nickname}`);
      console.log(`      ID: ${price.id}`);
      console.log(`      Amount: $${price.unit_amount / 100}/${price.recurring.interval}`);
      console.log(`      Currency: ${price.currency.toUpperCase()}`);
    });
    
    console.log('\n' + '-' * 40);
  });
  
  console.log('\n🎉 All products and prices have been created successfully!');
  console.log('\n📋 Next Steps:');
  console.log('1. These products are now available in your Stripe dashboard');
  console.log('2. The frontend will automatically fetch these via the API');
  console.log('3. Users can now subscribe to these plans');
  console.log('\n🔗 Stripe Dashboard: https://dashboard.stripe.com/products');
}

async function main() {
  try {
    const results = await createProducts();
    
    if (results.length === 0) {
      console.log('❌ No products were created successfully.');
      process.exit(1);
    }
    
    await displayResults(results);
    console.log('\n✨ Script completed successfully!');
    
  } catch (error) {
    console.error('💥 Script failed:', error.message);
    console.error(error.stack);
    process.exit(1);
  }
}

// Script for deleting all products (use with caution)
async function cleanupProducts() {
  console.log('🧹 Cleaning up existing Hover Lens products...\n');
  
  try {
    // List all products
    const productsResult = await stripeService.retrieveStripeProducts({
      limit: 100
    });
    
    if (productsResult.error) {
      console.error('❌ Error fetching products:', productsResult.message);
      return;
    }
    
    // Filter Hover Lens products
    const hoverLensProducts = productsResult.data.filter(
      product => product.metadata?.platform === 'rowanenergy'
    );
    
    if (hoverLensProducts.length === 0) {
      console.log('ℹ️  No Hover Lens products found to delete.');
      return;
    }
    
    console.log(`Found ${hoverLensProducts.length} Hover Lens products to delete:`);
    
    for (const product of hoverLensProducts) {
      console.log(`🗑️  Deleting product: ${product.name} (${product.id})`);
      
      // Archive the product (Stripe doesn't allow deletion, only archiving)
      const result = await stripeService.updateStripeProduct(product.id, {
        active: false
      });
      
      if (result.error) {
        console.error(`❌ Error archiving product ${product.id}:`, result.message);
      } else {
        console.log(`✅ Product archived: ${product.id}`);
      }
    }
    
    console.log('\n🧹 Cleanup completed!');
    
  } catch (error) {
    console.error('💥 Cleanup failed:', error.message);
  }
}

// Check command line arguments
const args = process.argv.slice(2);

if (args.includes('--cleanup') || args.includes('-c')) {
  cleanupProducts();
} else if (args.includes('--help') || args.includes('-h')) {
  console.log('Hover Lens Stripe Setup Script\n');
  console.log('Usage:');
  console.log('  node setup_stripe_products.js          # Create products and prices');
  console.log('  node setup_stripe_products.js -c       # Cleanup existing products');
  console.log('  node setup_stripe_products.js --help   # Show this help');
} else {
  main();
}
