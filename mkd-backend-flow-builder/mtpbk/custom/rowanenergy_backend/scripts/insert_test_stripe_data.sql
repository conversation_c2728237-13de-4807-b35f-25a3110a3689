-- Insert test Stripe products and prices data for Hover Lens
-- This script populates the database with sample Stripe data for testing

-- Note: These are example records. In production, actual Stripe IDs would be used
-- Run this after setting up actual products in Stripe using the setup_stripe_products.js script

-- Insert test Stripe customers (this would normally be created when users sign up)
INSERT INTO `rowanenergy_stripe_customers` (`user_id`, `stripe_customer_id`, `email`, `name`, `metadata`, `created_at`, `updated_at`) VALUES
(1, 'cus_test_customer_001', '<EMAIL>', '<PERSON>', '{"platform": "rowanenergy", "test": true}', NOW(), NOW()),
(2, 'cus_test_customer_002', '<EMAIL>', '<PERSON>', '{"platform": "rowanenergy", "test": true}', NOW(), NOW());

-- Insert test Stripe products
INSERT INTO `rowanenergy_stripe_products` (`stripe_product_id`, `name`, `description`, `active`, `metadata`, `created_at`, `updated_at`) VALUES
('prod_test_premium_001', 'Premium Plan', 'Perfect for growing businesses with unlimited survey bookings and priority support', 1, '{"platform": "rowanenergy", "plan_type": "premium", "features": "[\"Unlimited survey bookings\", \"Priority engineer assignment\", \"Advanced reporting & analytics\", \"Priority support\", \"Custom branding on reports\"]"}', NOW(), NOW()),
('prod_test_enterprise_001', 'Enterprise Plan', 'For large organizations requiring dedicated support and custom solutions', 1, '{"platform": "rowanenergy", "plan_type": "enterprise", "features": "[\"Everything in Premium\", \"Dedicated account manager\", \"Custom SLA agreements\", \"API access\", \"White-label solution\", \"Multi-location management\"]"}', NOW(), NOW());

-- Insert test Stripe prices
INSERT INTO `rowanenergy_stripe_prices` (`stripe_price_id`, `stripe_product_id`, `unit_amount`, `currency`, `recurring_interval`, `recurring_interval_count`, `nickname`, `active`, `metadata`, `created_at`, `updated_at`) VALUES
('price_test_premium_monthly_001', 'prod_test_premium_001', 4900, 'usd', 'month', 1, 'Premium Plan - Monthly', 1, '{"platform": "rowanenergy", "plan_type": "premium"}', NOW(), NOW()),
('price_test_enterprise_monthly_001', 'prod_test_enterprise_001', 9900, 'usd', 'month', 1, 'Enterprise Plan - Monthly', 1, '{"platform": "rowanenergy", "plan_type": "enterprise"}', NOW(), NOW());

-- Insert test Stripe subscriptions (optional - for testing existing subscriptions)
INSERT INTO `rowanenergy_stripe_subscriptions` (`stripe_subscription_id`, `stripe_customer_id`, `stripe_price_id`, `status`, `current_period_start`, `current_period_end`, `metadata`, `created_at`, `updated_at`) VALUES
('sub_test_subscription_001', 'cus_test_customer_001', 'price_test_premium_monthly_001', 'active', DATE_SUB(NOW(), INTERVAL 15 DAY), DATE_ADD(NOW(), INTERVAL 15 DAY), '{"platform": "rowanenergy", "user_id": "1"}', NOW(), NOW());

-- Verification queries to check the data
-- Uncomment these to verify the data was inserted correctly

-- SELECT 'Stripe Customers:' as table_name;
-- SELECT * FROM rowanenergy_stripe_customers;

-- SELECT 'Stripe Products:' as table_name;
-- SELECT * FROM rowanenergy_stripe_products;

-- SELECT 'Stripe Prices:' as table_name;
-- SELECT * FROM rowanenergy_stripe_prices;

-- SELECT 'Stripe Subscriptions:' as table_name;
-- SELECT * FROM rowanenergy_stripe_subscriptions;

-- Query to see customer with their subscription details
-- SELECT 
--     c.email,
--     c.name,
--     p.name as product_name,
--     pr.unit_amount / 100 as amount_usd,
--     pr.currency,
--     pr.recurring_interval,
--     s.status as subscription_status,
--     s.current_period_start,
--     s.current_period_end
-- FROM rowanenergy_stripe_customers c
-- JOIN rowanenergy_stripe_subscriptions s ON c.stripe_customer_id = s.stripe_customer_id
-- JOIN rowanenergy_stripe_prices pr ON s.stripe_price_id = pr.stripe_price_id
-- JOIN rowanenergy_stripe_products p ON pr.stripe_product_id = p.stripe_product_id;
