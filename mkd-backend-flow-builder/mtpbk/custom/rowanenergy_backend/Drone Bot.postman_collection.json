{"info": {"_postman_id": "2f71c773-152c-4bcb-a7bc-dbc724c7932f", "name": "<PERSON><PERSON>", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "40500216"}, "item": [{"name": "chat booking assistant", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "drbot-nhLybL86VBiUTtyngshsdj9efmc", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n  \"query\": \"hi, start immediatlety\",\n  \"history\": []\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:5120/api/v1/chat/booking-assistant", "protocol": "http", "host": ["localhost"], "port": "5120", "path": ["api", "v1", "chat", "booking-assistant"]}}, "response": []}, {"name": "flight assessment assistant", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "drbot-nhLybL86VBiUTtyngshsdj9efmc", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\"job_id\":\"x\"}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:5120/api/v1/chat/analyse-survey", "protocol": "http", "host": ["localhost"], "port": "5120", "path": ["api", "v1", "chat", "analyse-survey"]}}, "response": []}]}