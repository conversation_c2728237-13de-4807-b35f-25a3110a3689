

    
import { AdminWrapper } from "Components/AdminWrapper";

import { 

    AdminListJobPage,
    AdminAddJobPage,
    AdminEditJobPage,
    AdminViewJobPage,

    AdminListUploadsPage,
    AdminAddUploadsPage,
    AdminEditUploadsPage,
    AdminViewUploadsPage,

    AdminListTokensPage,
    AdminAddTokensPage,
    AdminEditTokensPage,
    AdminViewTokensPage,

    AdminListPreferencePage,
    AdminAddPreferencePage,
    AdminEditPreferencePage,
    AdminViewPreferencePage,

    AdminListUserPage,
    AdminAddUserPage,
    AdminEditUserPage,
    AdminViewUserPage,

    AdminListStripe_productPage,
    AdminAddStripe_productPage,
    AdminEditStripe_productPage,
    AdminViewStripe_productPage,

    AdminListStripe_pricePage,
    AdminAddStripe_pricePage,
    AdminEditStripe_pricePage,
    AdminViewStripe_pricePage,

    AdminListStripe_subscriptionPage,
    AdminAddStripe_subscriptionPage,
    AdminEditStripe_subscriptionPage,
    AdminViewStripe_subscriptionPage,

    AdminListStripe_checkoutPage,
    AdminAddStripe_checkoutPage,
    AdminEditStripe_checkoutPage,
    AdminViewStripe_checkoutPage,

    AdminListStripe_webhookPage,
    AdminAddStripe_webhookPage,
    AdminEditStripe_webhookPage,
    AdminViewStripe_webhookPage,

    AdminListStripe_settingPage,
    AdminAddStripe_settingPage,
    AdminEditStripe_settingPage,
    AdminViewStripe_settingPage,

    AdminListStripe_orderPage,
    AdminAddStripe_orderPage,
    AdminEditStripe_orderPage,
    AdminViewStripe_orderPage,

    AdminListStripe_invoicePage,
    AdminAddStripe_invoicePage,
    AdminEditStripe_invoicePage,
    AdminViewStripe_invoicePage,

    AdminListStripe_refundPage,
    AdminAddStripe_refundPage,
    AdminEditStripe_refundPage,
    AdminViewStripe_refundPage,

    AdminListStripe_disputePage,
    AdminAddStripe_disputePage,
    AdminEditStripe_disputePage,
    AdminViewStripe_disputePage
} from "./LazyLoad";


<Route
  exact
  path="/admin/job"
  element={
    <PrivateRoute
      access="admin"
      path="/admin/job"
      element={
        <AdminWrapper>
          <AdminJobListPage />
        </AdminWrapper>
      }
    />
  }
/>

<Route
  exact
  path="/admin/add-job"
  element={
    <PrivateRoute
      access="admin"
      path="/admin/add-job"
      element={
        <AdminWrapper>
          <AdminAddJobPage />
        </AdminWrapper>
      }
    />
  }
/>

<Route
  exact
  path="/admin/edit-job/:id"
  element={
    <PrivateRoute
      access="admin"
      path="/admin/edit-job/:id"
      element={
        <AdminWrapper>
          <AdminEditJobPage />
        </AdminWrapper>
      }
    />
  }
/>

<Route
  exact
  path="/admin/view-job/:id"
  element={
    <PrivateRoute
      access="admin"
      path="/admin/view-job/:id"
      element={
        <AdminWrapper>
          <AdminViewJobPage />
        </AdminWrapper>
      }
    />
  }
/>

<Route
  exact
  path="/admin/uploads"
  element={
    <PrivateRoute
      access="admin"
      path="/admin/uploads"
      element={
        <AdminWrapper>
          <AdminUploadsListPage />
        </AdminWrapper>
      }
    />
  }
/>

<Route
  exact
  path="/admin/add-uploads"
  element={
    <PrivateRoute
      access="admin"
      path="/admin/add-uploads"
      element={
        <AdminWrapper>
          <AdminAddUploadsPage />
        </AdminWrapper>
      }
    />
  }
/>

<Route
  exact
  path="/admin/edit-uploads/:id"
  element={
    <PrivateRoute
      access="admin"
      path="/admin/edit-uploads/:id"
      element={
        <AdminWrapper>
          <AdminEditUploadsPage />
        </AdminWrapper>
      }
    />
  }
/>

<Route
  exact
  path="/admin/view-uploads/:id"
  element={
    <PrivateRoute
      access="admin"
      path="/admin/view-uploads/:id"
      element={
        <AdminWrapper>
          <AdminViewUploadsPage />
        </AdminWrapper>
      }
    />
  }
/>

<Route
  exact
  path="/admin/tokens"
  element={
    <PrivateRoute
      access="admin"
      path="/admin/tokens"
      element={
        <AdminWrapper>
          <AdminTokensListPage />
        </AdminWrapper>
      }
    />
  }
/>

<Route
  exact
  path="/admin/add-tokens"
  element={
    <PrivateRoute
      access="admin"
      path="/admin/add-tokens"
      element={
        <AdminWrapper>
          <AdminAddTokensPage />
        </AdminWrapper>
      }
    />
  }
/>

<Route
  exact
  path="/admin/edit-tokens/:id"
  element={
    <PrivateRoute
      access="admin"
      path="/admin/edit-tokens/:id"
      element={
        <AdminWrapper>
          <AdminEditTokensPage />
        </AdminWrapper>
      }
    />
  }
/>

<Route
  exact
  path="/admin/view-tokens/:id"
  element={
    <PrivateRoute
      access="admin"
      path="/admin/view-tokens/:id"
      element={
        <AdminWrapper>
          <AdminViewTokensPage />
        </AdminWrapper>
      }
    />
  }
/>

<Route
  exact
  path="/admin/preference"
  element={
    <PrivateRoute
      access="admin"
      path="/admin/preference"
      element={
        <AdminWrapper>
          <AdminPreferenceListPage />
        </AdminWrapper>
      }
    />
  }
/>

<Route
  exact
  path="/admin/add-preference"
  element={
    <PrivateRoute
      access="admin"
      path="/admin/add-preference"
      element={
        <AdminWrapper>
          <AdminAddPreferencePage />
        </AdminWrapper>
      }
    />
  }
/>

<Route
  exact
  path="/admin/edit-preference/:id"
  element={
    <PrivateRoute
      access="admin"
      path="/admin/edit-preference/:id"
      element={
        <AdminWrapper>
          <AdminEditPreferencePage />
        </AdminWrapper>
      }
    />
  }
/>

<Route
  exact
  path="/admin/view-preference/:id"
  element={
    <PrivateRoute
      access="admin"
      path="/admin/view-preference/:id"
      element={
        <AdminWrapper>
          <AdminViewPreferencePage />
        </AdminWrapper>
      }
    />
  }
/>

<Route
  exact
  path="/admin/user"
  element={
    <PrivateRoute
      access="admin"
      path="/admin/user"
      element={
        <AdminWrapper>
          <AdminUserListPage />
        </AdminWrapper>
      }
    />
  }
/>

<Route
  exact
  path="/admin/add-user"
  element={
    <PrivateRoute
      access="admin"
      path="/admin/add-user"
      element={
        <AdminWrapper>
          <AdminAddUserPage />
        </AdminWrapper>
      }
    />
  }
/>

<Route
  exact
  path="/admin/edit-user/:id"
  element={
    <PrivateRoute
      access="admin"
      path="/admin/edit-user/:id"
      element={
        <AdminWrapper>
          <AdminEditUserPage />
        </AdminWrapper>
      }
    />
  }
/>

<Route
  exact
  path="/admin/view-user/:id"
  element={
    <PrivateRoute
      access="admin"
      path="/admin/view-user/:id"
      element={
        <AdminWrapper>
          <AdminViewUserPage />
        </AdminWrapper>
      }
    />
  }
/>

<Route
  exact
  path="/admin/stripe_product"
  element={
    <PrivateRoute
      access="admin"
      path="/admin/stripe_product"
      element={
        <AdminWrapper>
          <AdminStripe_productListPage />
        </AdminWrapper>
      }
    />
  }
/>

<Route
  exact
  path="/admin/add-stripe_product"
  element={
    <PrivateRoute
      access="admin"
      path="/admin/add-stripe_product"
      element={
        <AdminWrapper>
          <AdminAddStripe_productPage />
        </AdminWrapper>
      }
    />
  }
/>

<Route
  exact
  path="/admin/edit-stripe_product/:id"
  element={
    <PrivateRoute
      access="admin"
      path="/admin/edit-stripe_product/:id"
      element={
        <AdminWrapper>
          <AdminEditStripe_productPage />
        </AdminWrapper>
      }
    />
  }
/>

<Route
  exact
  path="/admin/view-stripe_product/:id"
  element={
    <PrivateRoute
      access="admin"
      path="/admin/view-stripe_product/:id"
      element={
        <AdminWrapper>
          <AdminViewStripe_productPage />
        </AdminWrapper>
      }
    />
  }
/>

<Route
  exact
  path="/admin/stripe_price"
  element={
    <PrivateRoute
      access="admin"
      path="/admin/stripe_price"
      element={
        <AdminWrapper>
          <AdminStripe_priceListPage />
        </AdminWrapper>
      }
    />
  }
/>

<Route
  exact
  path="/admin/add-stripe_price"
  element={
    <PrivateRoute
      access="admin"
      path="/admin/add-stripe_price"
      element={
        <AdminWrapper>
          <AdminAddStripe_pricePage />
        </AdminWrapper>
      }
    />
  }
/>

<Route
  exact
  path="/admin/edit-stripe_price/:id"
  element={
    <PrivateRoute
      access="admin"
      path="/admin/edit-stripe_price/:id"
      element={
        <AdminWrapper>
          <AdminEditStripe_pricePage />
        </AdminWrapper>
      }
    />
  }
/>

<Route
  exact
  path="/admin/view-stripe_price/:id"
  element={
    <PrivateRoute
      access="admin"
      path="/admin/view-stripe_price/:id"
      element={
        <AdminWrapper>
          <AdminViewStripe_pricePage />
        </AdminWrapper>
      }
    />
  }
/>

<Route
  exact
  path="/admin/stripe_subscription"
  element={
    <PrivateRoute
      access="admin"
      path="/admin/stripe_subscription"
      element={
        <AdminWrapper>
          <AdminStripe_subscriptionListPage />
        </AdminWrapper>
      }
    />
  }
/>

<Route
  exact
  path="/admin/add-stripe_subscription"
  element={
    <PrivateRoute
      access="admin"
      path="/admin/add-stripe_subscription"
      element={
        <AdminWrapper>
          <AdminAddStripe_subscriptionPage />
        </AdminWrapper>
      }
    />
  }
/>

<Route
  exact
  path="/admin/edit-stripe_subscription/:id"
  element={
    <PrivateRoute
      access="admin"
      path="/admin/edit-stripe_subscription/:id"
      element={
        <AdminWrapper>
          <AdminEditStripe_subscriptionPage />
        </AdminWrapper>
      }
    />
  }
/>

<Route
  exact
  path="/admin/view-stripe_subscription/:id"
  element={
    <PrivateRoute
      access="admin"
      path="/admin/view-stripe_subscription/:id"
      element={
        <AdminWrapper>
          <AdminViewStripe_subscriptionPage />
        </AdminWrapper>
      }
    />
  }
/>

<Route
  exact
  path="/admin/stripe_checkout"
  element={
    <PrivateRoute
      access="admin"
      path="/admin/stripe_checkout"
      element={
        <AdminWrapper>
          <AdminStripe_checkoutListPage />
        </AdminWrapper>
      }
    />
  }
/>

<Route
  exact
  path="/admin/add-stripe_checkout"
  element={
    <PrivateRoute
      access="admin"
      path="/admin/add-stripe_checkout"
      element={
        <AdminWrapper>
          <AdminAddStripe_checkoutPage />
        </AdminWrapper>
      }
    />
  }
/>

<Route
  exact
  path="/admin/edit-stripe_checkout/:id"
  element={
    <PrivateRoute
      access="admin"
      path="/admin/edit-stripe_checkout/:id"
      element={
        <AdminWrapper>
          <AdminEditStripe_checkoutPage />
        </AdminWrapper>
      }
    />
  }
/>

<Route
  exact
  path="/admin/view-stripe_checkout/:id"
  element={
    <PrivateRoute
      access="admin"
      path="/admin/view-stripe_checkout/:id"
      element={
        <AdminWrapper>
          <AdminViewStripe_checkoutPage />
        </AdminWrapper>
      }
    />
  }
/>

<Route
  exact
  path="/admin/stripe_webhook"
  element={
    <PrivateRoute
      access="admin"
      path="/admin/stripe_webhook"
      element={
        <AdminWrapper>
          <AdminStripe_webhookListPage />
        </AdminWrapper>
      }
    />
  }
/>

<Route
  exact
  path="/admin/add-stripe_webhook"
  element={
    <PrivateRoute
      access="admin"
      path="/admin/add-stripe_webhook"
      element={
        <AdminWrapper>
          <AdminAddStripe_webhookPage />
        </AdminWrapper>
      }
    />
  }
/>

<Route
  exact
  path="/admin/edit-stripe_webhook/:id"
  element={
    <PrivateRoute
      access="admin"
      path="/admin/edit-stripe_webhook/:id"
      element={
        <AdminWrapper>
          <AdminEditStripe_webhookPage />
        </AdminWrapper>
      }
    />
  }
/>

<Route
  exact
  path="/admin/view-stripe_webhook/:id"
  element={
    <PrivateRoute
      access="admin"
      path="/admin/view-stripe_webhook/:id"
      element={
        <AdminWrapper>
          <AdminViewStripe_webhookPage />
        </AdminWrapper>
      }
    />
  }
/>

<Route
  exact
  path="/admin/stripe_setting"
  element={
    <PrivateRoute
      access="admin"
      path="/admin/stripe_setting"
      element={
        <AdminWrapper>
          <AdminStripe_settingListPage />
        </AdminWrapper>
      }
    />
  }
/>

<Route
  exact
  path="/admin/add-stripe_setting"
  element={
    <PrivateRoute
      access="admin"
      path="/admin/add-stripe_setting"
      element={
        <AdminWrapper>
          <AdminAddStripe_settingPage />
        </AdminWrapper>
      }
    />
  }
/>

<Route
  exact
  path="/admin/edit-stripe_setting/:id"
  element={
    <PrivateRoute
      access="admin"
      path="/admin/edit-stripe_setting/:id"
      element={
        <AdminWrapper>
          <AdminEditStripe_settingPage />
        </AdminWrapper>
      }
    />
  }
/>

<Route
  exact
  path="/admin/view-stripe_setting/:id"
  element={
    <PrivateRoute
      access="admin"
      path="/admin/view-stripe_setting/:id"
      element={
        <AdminWrapper>
          <AdminViewStripe_settingPage />
        </AdminWrapper>
      }
    />
  }
/>

<Route
  exact
  path="/admin/stripe_order"
  element={
    <PrivateRoute
      access="admin"
      path="/admin/stripe_order"
      element={
        <AdminWrapper>
          <AdminStripe_orderListPage />
        </AdminWrapper>
      }
    />
  }
/>

<Route
  exact
  path="/admin/add-stripe_order"
  element={
    <PrivateRoute
      access="admin"
      path="/admin/add-stripe_order"
      element={
        <AdminWrapper>
          <AdminAddStripe_orderPage />
        </AdminWrapper>
      }
    />
  }
/>

<Route
  exact
  path="/admin/edit-stripe_order/:id"
  element={
    <PrivateRoute
      access="admin"
      path="/admin/edit-stripe_order/:id"
      element={
        <AdminWrapper>
          <AdminEditStripe_orderPage />
        </AdminWrapper>
      }
    />
  }
/>

<Route
  exact
  path="/admin/view-stripe_order/:id"
  element={
    <PrivateRoute
      access="admin"
      path="/admin/view-stripe_order/:id"
      element={
        <AdminWrapper>
          <AdminViewStripe_orderPage />
        </AdminWrapper>
      }
    />
  }
/>

<Route
  exact
  path="/admin/stripe_invoice"
  element={
    <PrivateRoute
      access="admin"
      path="/admin/stripe_invoice"
      element={
        <AdminWrapper>
          <AdminStripe_invoiceListPage />
        </AdminWrapper>
      }
    />
  }
/>

<Route
  exact
  path="/admin/add-stripe_invoice"
  element={
    <PrivateRoute
      access="admin"
      path="/admin/add-stripe_invoice"
      element={
        <AdminWrapper>
          <AdminAddStripe_invoicePage />
        </AdminWrapper>
      }
    />
  }
/>

<Route
  exact
  path="/admin/edit-stripe_invoice/:id"
  element={
    <PrivateRoute
      access="admin"
      path="/admin/edit-stripe_invoice/:id"
      element={
        <AdminWrapper>
          <AdminEditStripe_invoicePage />
        </AdminWrapper>
      }
    />
  }
/>

<Route
  exact
  path="/admin/view-stripe_invoice/:id"
  element={
    <PrivateRoute
      access="admin"
      path="/admin/view-stripe_invoice/:id"
      element={
        <AdminWrapper>
          <AdminViewStripe_invoicePage />
        </AdminWrapper>
      }
    />
  }
/>

<Route
  exact
  path="/admin/stripe_refund"
  element={
    <PrivateRoute
      access="admin"
      path="/admin/stripe_refund"
      element={
        <AdminWrapper>
          <AdminStripe_refundListPage />
        </AdminWrapper>
      }
    />
  }
/>

<Route
  exact
  path="/admin/add-stripe_refund"
  element={
    <PrivateRoute
      access="admin"
      path="/admin/add-stripe_refund"
      element={
        <AdminWrapper>
          <AdminAddStripe_refundPage />
        </AdminWrapper>
      }
    />
  }
/>

<Route
  exact
  path="/admin/edit-stripe_refund/:id"
  element={
    <PrivateRoute
      access="admin"
      path="/admin/edit-stripe_refund/:id"
      element={
        <AdminWrapper>
          <AdminEditStripe_refundPage />
        </AdminWrapper>
      }
    />
  }
/>

<Route
  exact
  path="/admin/view-stripe_refund/:id"
  element={
    <PrivateRoute
      access="admin"
      path="/admin/view-stripe_refund/:id"
      element={
        <AdminWrapper>
          <AdminViewStripe_refundPage />
        </AdminWrapper>
      }
    />
  }
/>

<Route
  exact
  path="/admin/stripe_dispute"
  element={
    <PrivateRoute
      access="admin"
      path="/admin/stripe_dispute"
      element={
        <AdminWrapper>
          <AdminStripe_disputeListPage />
        </AdminWrapper>
      }
    />
  }
/>

<Route
  exact
  path="/admin/add-stripe_dispute"
  element={
    <PrivateRoute
      access="admin"
      path="/admin/add-stripe_dispute"
      element={
        <AdminWrapper>
          <AdminAddStripe_disputePage />
        </AdminWrapper>
      }
    />
  }
/>

<Route
  exact
  path="/admin/edit-stripe_dispute/:id"
  element={
    <PrivateRoute
      access="admin"
      path="/admin/edit-stripe_dispute/:id"
      element={
        <AdminWrapper>
          <AdminEditStripe_disputePage />
        </AdminWrapper>
      }
    />
  }
/>

<Route
  exact
  path="/admin/view-stripe_dispute/:id"
  element={
    <PrivateRoute
      access="admin"
      path="/admin/view-stripe_dispute/:id"
      element={
        <AdminWrapper>
          <AdminViewStripe_disputePage />
        </AdminWrapper>
      }
    />
  }
/>
    