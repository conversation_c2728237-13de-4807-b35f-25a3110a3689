
import { lazy } from 'react';

export const AdminListJobPage = lazy(() => {
  const __import = import("@/pages/Admin/List/AdminListJobPage");
  __import.finally(() => {});
  return __import;
});

export const AdminAddJobPage = lazy(() => {
  const __import = import("@/pages/Admin/Add/AdminAddJobPage");
  __import.finally(() => {});
  return __import;
});

export const AdminEditJobPage = lazy(() => {
  const __import = import("@/pages/Admin/Edit/AdminEditJobPage");
  __import.finally(() => {});
  return __import;
});

export const AdminViewJobPage = lazy(() => {
  const __import = import("@/pages/Admin/View/AdminViewJobPage");
  __import.finally(() => {});
  return __import;
});


export const AdminListUploadsPage = lazy(() => {
  const __import = import("@/pages/Admin/List/AdminListUploadsPage");
  __import.finally(() => {});
  return __import;
});

export const AdminAddUploadsPage = lazy(() => {
  const __import = import("@/pages/Admin/Add/AdminAddUploadsPage");
  __import.finally(() => {});
  return __import;
});

export const AdminEditUploadsPage = lazy(() => {
  const __import = import("@/pages/Admin/Edit/AdminEditUploadsPage");
  __import.finally(() => {});
  return __import;
});

export const AdminViewUploadsPage = lazy(() => {
  const __import = import("@/pages/Admin/View/AdminViewUploadsPage");
  __import.finally(() => {});
  return __import;
});


export const AdminListTokensPage = lazy(() => {
  const __import = import("@/pages/Admin/List/AdminListTokensPage");
  __import.finally(() => {});
  return __import;
});

export const AdminAddTokensPage = lazy(() => {
  const __import = import("@/pages/Admin/Add/AdminAddTokensPage");
  __import.finally(() => {});
  return __import;
});

export const AdminEditTokensPage = lazy(() => {
  const __import = import("@/pages/Admin/Edit/AdminEditTokensPage");
  __import.finally(() => {});
  return __import;
});

export const AdminViewTokensPage = lazy(() => {
  const __import = import("@/pages/Admin/View/AdminViewTokensPage");
  __import.finally(() => {});
  return __import;
});


export const AdminListPreferencePage = lazy(() => {
  const __import = import("@/pages/Admin/List/AdminListPreferencePage");
  __import.finally(() => {});
  return __import;
});

export const AdminAddPreferencePage = lazy(() => {
  const __import = import("@/pages/Admin/Add/AdminAddPreferencePage");
  __import.finally(() => {});
  return __import;
});

export const AdminEditPreferencePage = lazy(() => {
  const __import = import("@/pages/Admin/Edit/AdminEditPreferencePage");
  __import.finally(() => {});
  return __import;
});

export const AdminViewPreferencePage = lazy(() => {
  const __import = import("@/pages/Admin/View/AdminViewPreferencePage");
  __import.finally(() => {});
  return __import;
});


export const AdminListUserPage = lazy(() => {
  const __import = import("@/pages/Admin/List/AdminListUserPage");
  __import.finally(() => {});
  return __import;
});

export const AdminAddUserPage = lazy(() => {
  const __import = import("@/pages/Admin/Add/AdminAddUserPage");
  __import.finally(() => {});
  return __import;
});

export const AdminEditUserPage = lazy(() => {
  const __import = import("@/pages/Admin/Edit/AdminEditUserPage");
  __import.finally(() => {});
  return __import;
});

export const AdminViewUserPage = lazy(() => {
  const __import = import("@/pages/Admin/View/AdminViewUserPage");
  __import.finally(() => {});
  return __import;
});


export const AdminListStripe_productPage = lazy(() => {
  const __import = import("@/pages/Admin/List/AdminListStripe_productPage");
  __import.finally(() => {});
  return __import;
});

export const AdminAddStripe_productPage = lazy(() => {
  const __import = import("@/pages/Admin/Add/AdminAddStripe_productPage");
  __import.finally(() => {});
  return __import;
});

export const AdminEditStripe_productPage = lazy(() => {
  const __import = import("@/pages/Admin/Edit/AdminEditStripe_productPage");
  __import.finally(() => {});
  return __import;
});

export const AdminViewStripe_productPage = lazy(() => {
  const __import = import("@/pages/Admin/View/AdminViewStripe_productPage");
  __import.finally(() => {});
  return __import;
});


export const AdminListStripe_pricePage = lazy(() => {
  const __import = import("@/pages/Admin/List/AdminListStripe_pricePage");
  __import.finally(() => {});
  return __import;
});

export const AdminAddStripe_pricePage = lazy(() => {
  const __import = import("@/pages/Admin/Add/AdminAddStripe_pricePage");
  __import.finally(() => {});
  return __import;
});

export const AdminEditStripe_pricePage = lazy(() => {
  const __import = import("@/pages/Admin/Edit/AdminEditStripe_pricePage");
  __import.finally(() => {});
  return __import;
});

export const AdminViewStripe_pricePage = lazy(() => {
  const __import = import("@/pages/Admin/View/AdminViewStripe_pricePage");
  __import.finally(() => {});
  return __import;
});


export const AdminListStripe_subscriptionPage = lazy(() => {
  const __import = import("@/pages/Admin/List/AdminListStripe_subscriptionPage");
  __import.finally(() => {});
  return __import;
});

export const AdminAddStripe_subscriptionPage = lazy(() => {
  const __import = import("@/pages/Admin/Add/AdminAddStripe_subscriptionPage");
  __import.finally(() => {});
  return __import;
});

export const AdminEditStripe_subscriptionPage = lazy(() => {
  const __import = import("@/pages/Admin/Edit/AdminEditStripe_subscriptionPage");
  __import.finally(() => {});
  return __import;
});

export const AdminViewStripe_subscriptionPage = lazy(() => {
  const __import = import("@/pages/Admin/View/AdminViewStripe_subscriptionPage");
  __import.finally(() => {});
  return __import;
});


export const AdminListStripe_checkoutPage = lazy(() => {
  const __import = import("@/pages/Admin/List/AdminListStripe_checkoutPage");
  __import.finally(() => {});
  return __import;
});

export const AdminAddStripe_checkoutPage = lazy(() => {
  const __import = import("@/pages/Admin/Add/AdminAddStripe_checkoutPage");
  __import.finally(() => {});
  return __import;
});

export const AdminEditStripe_checkoutPage = lazy(() => {
  const __import = import("@/pages/Admin/Edit/AdminEditStripe_checkoutPage");
  __import.finally(() => {});
  return __import;
});

export const AdminViewStripe_checkoutPage = lazy(() => {
  const __import = import("@/pages/Admin/View/AdminViewStripe_checkoutPage");
  __import.finally(() => {});
  return __import;
});


export const AdminListStripe_webhookPage = lazy(() => {
  const __import = import("@/pages/Admin/List/AdminListStripe_webhookPage");
  __import.finally(() => {});
  return __import;
});

export const AdminAddStripe_webhookPage = lazy(() => {
  const __import = import("@/pages/Admin/Add/AdminAddStripe_webhookPage");
  __import.finally(() => {});
  return __import;
});

export const AdminEditStripe_webhookPage = lazy(() => {
  const __import = import("@/pages/Admin/Edit/AdminEditStripe_webhookPage");
  __import.finally(() => {});
  return __import;
});

export const AdminViewStripe_webhookPage = lazy(() => {
  const __import = import("@/pages/Admin/View/AdminViewStripe_webhookPage");
  __import.finally(() => {});
  return __import;
});


export const AdminListStripe_settingPage = lazy(() => {
  const __import = import("@/pages/Admin/List/AdminListStripe_settingPage");
  __import.finally(() => {});
  return __import;
});

export const AdminAddStripe_settingPage = lazy(() => {
  const __import = import("@/pages/Admin/Add/AdminAddStripe_settingPage");
  __import.finally(() => {});
  return __import;
});

export const AdminEditStripe_settingPage = lazy(() => {
  const __import = import("@/pages/Admin/Edit/AdminEditStripe_settingPage");
  __import.finally(() => {});
  return __import;
});

export const AdminViewStripe_settingPage = lazy(() => {
  const __import = import("@/pages/Admin/View/AdminViewStripe_settingPage");
  __import.finally(() => {});
  return __import;
});


export const AdminListStripe_orderPage = lazy(() => {
  const __import = import("@/pages/Admin/List/AdminListStripe_orderPage");
  __import.finally(() => {});
  return __import;
});

export const AdminAddStripe_orderPage = lazy(() => {
  const __import = import("@/pages/Admin/Add/AdminAddStripe_orderPage");
  __import.finally(() => {});
  return __import;
});

export const AdminEditStripe_orderPage = lazy(() => {
  const __import = import("@/pages/Admin/Edit/AdminEditStripe_orderPage");
  __import.finally(() => {});
  return __import;
});

export const AdminViewStripe_orderPage = lazy(() => {
  const __import = import("@/pages/Admin/View/AdminViewStripe_orderPage");
  __import.finally(() => {});
  return __import;
});


export const AdminListStripe_invoicePage = lazy(() => {
  const __import = import("@/pages/Admin/List/AdminListStripe_invoicePage");
  __import.finally(() => {});
  return __import;
});

export const AdminAddStripe_invoicePage = lazy(() => {
  const __import = import("@/pages/Admin/Add/AdminAddStripe_invoicePage");
  __import.finally(() => {});
  return __import;
});

export const AdminEditStripe_invoicePage = lazy(() => {
  const __import = import("@/pages/Admin/Edit/AdminEditStripe_invoicePage");
  __import.finally(() => {});
  return __import;
});

export const AdminViewStripe_invoicePage = lazy(() => {
  const __import = import("@/pages/Admin/View/AdminViewStripe_invoicePage");
  __import.finally(() => {});
  return __import;
});


export const AdminListStripe_refundPage = lazy(() => {
  const __import = import("@/pages/Admin/List/AdminListStripe_refundPage");
  __import.finally(() => {});
  return __import;
});

export const AdminAddStripe_refundPage = lazy(() => {
  const __import = import("@/pages/Admin/Add/AdminAddStripe_refundPage");
  __import.finally(() => {});
  return __import;
});

export const AdminEditStripe_refundPage = lazy(() => {
  const __import = import("@/pages/Admin/Edit/AdminEditStripe_refundPage");
  __import.finally(() => {});
  return __import;
});

export const AdminViewStripe_refundPage = lazy(() => {
  const __import = import("@/pages/Admin/View/AdminViewStripe_refundPage");
  __import.finally(() => {});
  return __import;
});


export const AdminListStripe_disputePage = lazy(() => {
  const __import = import("@/pages/Admin/List/AdminListStripe_disputePage");
  __import.finally(() => {});
  return __import;
});

export const AdminAddStripe_disputePage = lazy(() => {
  const __import = import("@/pages/Admin/Add/AdminAddStripe_disputePage");
  __import.finally(() => {});
  return __import;
});

export const AdminEditStripe_disputePage = lazy(() => {
  const __import = import("@/pages/Admin/Edit/AdminEditStripe_disputePage");
  __import.finally(() => {});
  return __import;
});

export const AdminViewStripe_disputePage = lazy(() => {
  const __import = import("@/pages/Admin/View/AdminViewStripe_disputePage");
  __import.finally(() => {});
  return __import;
});
    