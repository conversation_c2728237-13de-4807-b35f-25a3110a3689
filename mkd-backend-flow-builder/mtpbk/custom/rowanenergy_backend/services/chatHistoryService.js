const { sqlDateTimeFormat } = require('../../../baas/services/UtilService');

class ChatHistoryService {
  constructor() {
    this.sdk = null;
  }

  setSDK(sdk) {
    this.sdk = sdk;
    this.sdk.setTable('chat_history');
  }

  async saveMessage(userId, sessionId, chatType, role, content, metadata = null) {
    try {
      const messageData = {
        user_id: userId || null,
        session_id: sessionId,
        chat_type: chatType,
        role: role, // 'user' or 'assistant'
        content: content,
        metadata: metadata ? JSON.stringify(metadata) : null,
        created_at: sqlDateTimeFormat(new Date()),
        updated_at: sqlDateTimeFormat(new Date())
      };

      const result = await this.sdk.create('chat_history', messageData);
      return result;
    } catch (error) {
      console.error('Error saving chat message:', error);
      throw error;
    }
  }

  async getChatHistory(userId, sessionId, chatType, limit = 50) {
    try {
      const whereClause = {
        chat_type: chatType
      };

      if (userId) {
        whereClause.user_id = userId;
        whereClause.session_id = sessionId;
      } else if (sessionId) {
        whereClause.session_id = sessionId;
        whereClause.user_id = null; // Ensure we get anonymous messages
      }

      const history = await this.sdk.find('chat_history', whereClause, {
        orderBy: 'created_at',
        direction: 'asc',
        limit: limit
      });

      return history.map(msg => ({
        role: msg.role,
        content: msg.content,
        metadata: msg.metadata ? JSON.parse(msg.metadata) : null,
        created_at: msg.created_at
      }));
    } catch (error) {
      console.error('Error fetching chat history:', error);
      return [];
    }
  }

  async clearChatHistory(userId, sessionId, chatType) {
    try {
      const whereClause = {
        chat_type: chatType
      };

      if (userId) {
        whereClause.user_id = userId;
      } else if (sessionId) {
        whereClause.session_id = sessionId;
        whereClause.user_id = null;
      }

      await this.sdk.deleteWhere('chat_history', whereClause);
      return true;
    } catch (error) {
      console.error('Error clearing chat history:', error);
      throw error;
    }
  }

  async transferAnonymousHistory(sessionId, userId) {
    try {
      // Transfer anonymous chat history to logged-in user
      await this.sdk.updateWhere('chat_history', 
        { session_id: sessionId, user_id: null },
        { user_id: userId, updated_at: sqlDateTimeFormat(new Date()) }
      );
      return true;
    } catch (error) {
      console.error('Error transferring anonymous history:', error);
      throw error;
    }
  }
}

module.exports = ChatHistoryService;
