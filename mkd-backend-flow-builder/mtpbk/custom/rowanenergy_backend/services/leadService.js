class LeadService {
  constructor() {
    this._sdk = null;
  }

  setSDK(sdk) {
    this._sdk = sdk;
    return this;
  }

  async createLead(payload) {
    return await this._sdk.create('lead', {
      name: payload.name,
      email: payload.email,
      phone: payload.phone,
      site_address: payload.site_address,
      asset_type: payload.asset_type,
      site_details: payload.site_details,
      source: payload.source || 'form',
      status: payload.status || 'new',
      region: payload.region || null,
      follow_up_needed: payload.follow_up_needed ?? 1,
    });
  }

  async listLeads(filters = {}, options = {}) {
    const query = { ...filters };
    if (filters.q) {
      query.$or = [
        { name: { $like: `%${filters.q}%` } },
        { email: { $like: `%${filters.q}%` } },
        { phone: { $like: `%${filters.q}%` } },
      ];
    }
    return await this._sdk.find('lead', query, options);
  }

  async updateLead(id, payload) {
    return await this._sdk.update('lead', { id }, payload);
  }
}

module.exports = new LeadService();


