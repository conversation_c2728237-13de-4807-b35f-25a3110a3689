const { sqlDateTimeFormat } = require("../../../baas/services/UtilService");

class EngineerService {
  constructor() {
    this._sdk = null;
  }

  setSDK(sdk) {
    this._sdk = sdk;
    return this;
  }

  async getProfile(userId) {
    return await this._sdk.findOne('engineer_profile', { user_id: userId });
  }

  async upsertProfile(userId, payload) {
    const existing = await this._sdk.findOne('engineer_profile', { user_id: userId });
    if (existing) {
      return await this._sdk.update('engineer_profile', { id: existing.id }, payload);
    }
    return await this._sdk.create('engineer_profile', { user_id: userId, ...payload });
  }

  async getAvailability(userId) {
    // Get both user profile and preferences
    const [user, preference] = await Promise.all([
      this._sdk.findOne('user', { id: userId }),
      this._sdk.findOne('preference', { user_id: userId })
    ]);

    const defaultAvailability = {
      Monday: { morning: true, afternoon: true, evening: false },
      Tuesday: { morning: true, afternoon: true, evening: false },
      Wednesday: { morning: true, afternoon: true, evening: false },
      Thursday: { morning: true, afternoon: true, evening: false },
      Friday: { morning: true, afternoon: true, evening: false },
      Saturday: { morning: false, afternoon: false, evening: false },
      Sunday: { morning: false, afternoon: false, evening: false }
    };

    const defaultRegions = {
      'North England': true,
      'South England': true,
      'East England': false,
      'West England': false,
      'Wales': false,
      'Scotland': false
    };

    // If no preference exists, create one with defaults
    if (!preference) {
      try {
        await this._sdk.create('preference', {
          user_id: userId,
          first_name: user?.data ? JSON.parse(user.data).first_name || 'Engineer' : 'Engineer',
          last_name: user?.data ? JSON.parse(user.data).last_name || '' : '',
          weekly_availability: JSON.stringify(defaultAvailability),
          region_preferences: JSON.stringify(defaultRegions),
          workload_limit: 'Limit to 1 job per day maximum',
          created_at: sqlDateTimeFormat(new Date()),
          updated_at: sqlDateTimeFormat(new Date())
        });

        // Fetch the newly created preference
        const newPreference = await this._sdk.findOne('preference', { user_id: userId });
        return this.buildAvailabilityResponse(user, newPreference, defaultAvailability, defaultRegions);
      } catch (error) {
        console.error('Error creating preference:', error);
        // Return defaults even if creation fails
        return this.buildAvailabilityResponse(user, null, defaultAvailability, defaultRegions);
      }
    }

    return this.buildAvailabilityResponse(user, preference, defaultAvailability, defaultRegions);
  }

  buildAvailabilityResponse(user, preference, defaultAvailability, defaultRegions) {
    const availabilityData = {
      user: {
        first_name: preference?.first_name || (user?.data ? JSON.parse(user.data).first_name || 'Engineer' : 'Engineer'),
        last_name: preference?.last_name || (user?.data ? JSON.parse(user.data).last_name || '' : ''),
        email: user?.email || ''
      },
      weekly_availability: preference?.weekly_availability 
        ? (typeof preference.weekly_availability === 'string' 
            ? JSON.parse(preference.weekly_availability) 
            : preference.weekly_availability)
        : defaultAvailability,
      region_preferences: preference?.region_preferences 
        ? (typeof preference.region_preferences === 'string' 
            ? JSON.parse(preference.region_preferences) 
            : preference.region_preferences)
        : defaultRegions,
      workload_limit: preference?.workload_limit || 'Limit to 1 job per day maximum',
      description: 'Configure when and where you\'re available to work. This helps our scheduling system assign jobs that match your preferences.'
    };

    return availabilityData;
  }

  async updateAvailability(userId, payload) {
    const existing = await this._sdk.findOne('preference', { user_id: userId });
    const engineer = await this._sdk.findOne('engineer_profile', { user_id: userId });
    const availabilityData = {
      weekly_availability: JSON.stringify(payload.weekly_availability),
      region_preferences: JSON.stringify(payload.region_preferences),
      workload_limit: payload.workload_limit,
      updated_at: sqlDateTimeFormat(new Date())
    };
    let result = null

    if (existing) {
      result = await this._sdk.updateById('preference', existing.id, availabilityData);
    } else {
      result = await this._sdk.create('preference', { 
        user_id: userId, 
        ...availabilityData,
        created_at: sqlDateTimeFormat(new Date())
      });
    }

    // map{"Monday": {"morning": true, "afternoon": true, "evening": false}, "Tuesday": {"morning": true, "afternoon": true, "evening": false}, "Wednesday": {"morning": true, "afternoon": true, "evening": false}, "Thursday": {"morning": true, "afternoon": true, "evening": false}, "Friday": {"morning": true, "afternoon": true, "evening": false}, "Saturday": {"morning": false, "afternoon": false, "evening": false}, "Sunday": {"morning": false, "afternoon": false, "evening": false}}
    // {"Monday": ["08:00-17:00"], "Tuesday": ["08:00-17:00"], "Wednesday": ["08:00-17:00"], "Thursday": ["08:00-17:00"], "Friday": ["08:00-17:00"], "Saturday": ["09:00-15:00"]}
    if (engineer) {
      await this._sdk.updateById('engineer_profile', engineer.id, {
        regions: Object.keys(payload.region_preferences),
        updated_at: sqlDateTimeFormat(new Date())
      });
    } else {
      await this._sdk.create('engineer_profile', {
        user_id: userId,
        regions: Object.keys(payload.region_preferences),
        created_at: sqlDateTimeFormat(new Date()),
        updated_at: sqlDateTimeFormat(new Date())
      });
    }

    return result;
  }
}

module.exports = new EngineerService();


