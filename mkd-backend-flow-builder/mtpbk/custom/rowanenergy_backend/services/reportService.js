class ReportService {
  constructor() {
    this._sdk = null;
  }

  setSDK(sdk) {
    this._sdk = sdk;
    return this;
  }

  async createReport(jobId, payload) {
    try {
      // First, create the report record
      const reportData = {
        job_id: jobId,
        thermal_images: JSON.stringify(payload.thermal_images || []),
        rgb_photos: JSON.stringify(payload.rgb_photos || []),
        video_footage: JSON.stringify(payload.video_footage || []),
        flight_logs: JSON.stringify(payload.flight_logs || []),
        ai_analysis: payload.ai_analysis || null,
        compliance_docs: payload.compliance_docs || null,
        internal_notes: payload.internal_notes || null,
        customer_summary: payload.customer_summary || null,
        uploaded_at: new Date(),
        status: 'completed'
      };

      const report = await this._sdk.create('report', reportData);

      // Also update the job_survey_notes table for tracking
      const surveyNotesData = {
        job_id: jobId,
        job_status: payload.status || 'complete',
        internal_notes: payload.internal_notes,
        customer_summary: payload.customer_summary,
        files_uploaded_count: payload.files_uploaded_count || 0,
        thermal_images_count: payload.thermal_images_count || 0,
        rgb_photos_count: payload.rgb_photos_count || 0,
        video_files_count: payload.video_files_count || 0,
        dji_logs_count: payload.dji_logs_count || 0,
        completed_at: new Date()
      };

      // Check if survey notes already exist
      const existingNotes = await this._sdk.find('job_survey_notes', { job_id: jobId });

      if (existingNotes && existingNotes.length > 0) {
        // Update existing record
        await this._sdk.update('job_survey_notes', { job_id: jobId }, surveyNotesData);
      } else {
        // Create new record
        await this._sdk.create('job_survey_notes', surveyNotesData);
      }

      return report;
    } catch (error) {
      console.error('Error creating report:', error);
      throw error;
    }
  }

  async listReports(filter = {}, options = {}) {
    return await this._sdk.find('report', filter, options);
  }

  async getReport(id) {
    return await this._sdk.findOne('report', { id });
  }
}

module.exports = new ReportService();


