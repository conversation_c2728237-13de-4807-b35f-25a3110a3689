const { sqlDateTimeFormat, filterEmptyFields } = require("../../../baas/services/UtilService");

class JobService {
  constructor() {
    this._sdk = null;
  }

  setSDK(sdk) {
    this._sdk = sdk;
    return this;
  }

  async createJob(payload) {
    // Normalize incoming payload keys from frontend
    const normalized = {
      asset_type: payload.asset_type,
      site_name: payload.site_name,
      system_size: payload.system_size,
      location: payload.location,
      region: payload.region,
      latitude: payload?.latitude || null,
      longitude: payload?.longitude || null,
      scheduled_time: payload.scheduled_time || null,
      duration_minutes: payload.duration_minutes != null ? payload.duration_minutes : null,
      is_flexible: payload.is_flexible === true || payload.is_flexible === 1 ? 1 : 0,
      contact_name: payload.contact_name,
      contact_phone: payload.contact_phone,
      access_instructions: payload.access_instructions,
      survey_purpose: payload.survey_purpose,
      additional_notes: payload.additional_notes,
      status: payload.status || 'pending',
      engineer_id: payload.engineer_id || null,
      source: payload.source || 'form' // Default to 'form' if not specified
    };

    return await this._sdk.create('job_survey', filterEmptyFields({
      customer_id: payload.customer_id,
      engineer_id: normalized.engineer_id,
      site_name: normalized.site_name,
      location: normalized.location,
      region: normalized.region || null,
      latitude: normalized.latitude || null,
      longitude: normalized.longitude || null,
      asset_type: normalized.asset_type || 'solar',
      scheduled_time: normalized.scheduled_time,
      duration_minutes: normalized.duration_minutes,
      is_flexible: normalized.is_flexible,
      status: normalized.status,
      source: normalized.source,
      system_size: normalized.system_size || null,
      access_instructions: normalized.access_instructions || null,
      contact_name: normalized.contact_name || null,
      contact_phone: normalized.contact_phone || null,
      survey_purpose: normalized.survey_purpose || null,
      additional_notes: normalized.additional_notes || null,
    }));
  }

  async listJobs(filter = {}, options = {}) {
    return await this._sdk.find('job_survey', filter, options);
  }

  async getJob(id) {
    return await this._sdk.findOne('job_survey', { id });
  }

  async updateJob(id, payload) {
    return await this._sdk.update('job_survey', { id }, {
      ...payload,
      updated_at: sqlDateTimeFormat(new Date()),
    });
  }
}

module.exports = new JobService();


