const { sqlDateTimeFormat } = require("../../../baas/services/UtilService");

class ChecklistService {
  constructor() {
    this._sdk = null;
  }

  setSDK(sdk) {
    this._sdk = sdk;
    return this;
  }

  async upsert(jobId, payload) {
    const existing = await this._sdk.findOne('job_checklist', { job_id: jobId });
    if (existing) {
      return await this._sdk.update('job_checklist', { id: existing.id }, {
        ...payload,
        equipment_checklist: JSON.stringify(payload.equipment_checks),
        updated_at: sqlDateTimeFormat(new Date()),
        last_updated: sqlDateTimeFormat(new Date()),
      });
    }
    return await this._sdk.create('job_checklist', {
      job_id: jobId,
      ...payload,
      equipment_checklist: JSON.stringify(payload.equipment_checks),
      created_at: sqlDateTimeFormat(new Date()),
      updated_at: sqlDateTimeFormat(new Date()),
      last_updated: sqlDateTimeFormat(new Date()),
    });
  }

  async get(jobId) {
    return await this._sdk.findOne('checklist', { job_id: jobId });
  }
}

module.exports = new ChecklistService();


