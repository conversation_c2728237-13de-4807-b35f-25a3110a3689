CREATE TABLE IF NOT EXISTS rowanenergy_job (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `task` VARCHAR(512) NOT NULL,
  `arguments` TEXT,
  `time_interval` VARCHAR(512) DEFAULT 'once',
  `retries` INT DEFAULT '1',
  `status` INT DEFAULT 0,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS rowanenergy_uploads (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `url` VARCHAR(512) NOT NULL,
  `caption` VARCHAR(512),
  `user_id` INT,
  `width` INT,
  `height` INT,
  `type` INT DEFAULT 0 NOT NULL,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS rowanenergy_tokens (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `user_id` INT NOT NULL,
  `token` VARCHAR(512) NOT NULL,
  `code` VARCHAR(512) NOT NULL,
  `type` INT DEFAULT 0 NOT NULL,
  `data` TEXT,
  `status` INT DEFAULT 1 NOT NULL,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `expired_at` TIMESTAMP
);

CREATE TABLE IF NOT EXISTS rowanenergy_preference (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `first_name` VARCHAR(512),
  `last_name` VARCHAR(512),
  `phone` VARCHAR(512),
  `photo` VARCHAR(512),
  `user_id` INT NOT NULL,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS rowanenergy_user (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `email` VARCHAR(512) NOT NULL,
  `password` VARCHAR(100) NOT NULL,
  `login_type` INT DEFAULT 0 NOT NULL,
  `role_id` VARCHAR(512),
  `data` TEXT,
  `status` INT DEFAULT 0 NOT NULL,
  `verify` BOOLEAN DEFAULT '0' NOT NULL,
  `two_factor_authentication` BOOLEAN DEFAULT '0',
  `company_id` INT DEFAULT '0',
  `stripe_uid` VARCHAR(512),
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS rowanenergy_stripe_product (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `created_at` DATE,
  `updated_at` DATETIME,
  `name` VARCHAR(512),
  `product_id` VARCHAR(512),
  `stripe_id` VARCHAR(512),
  `object` LONGTEXT,
  `status` INT
);

CREATE TABLE IF NOT EXISTS rowanenergy_stripe_price (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `created_at` DATE,
  `updated_at` DATETIME,
  `name` VARCHAR(512),
  `product_id` VARCHAR(512),
  `stripe_id` VARCHAR(512),
  `is_usage_metered` INT,
  `usage_limit` INT,
  `object` MEDIUMTEXT,
  `amount` FLOAT,
  `trial_days` INT,
  `type` VARCHAR(512),
  `status` INT
);

CREATE TABLE IF NOT EXISTS rowanenergy_stripe_subscription (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `stripe_id` VARCHAR(512),
  `price_id` VARCHAR(512),
  `user_id` INT,
  `object` TEXT,
  `status` VARCHAR(512),
  `is_lifetime` BOOLEAN,
  `created_at` DATE,
  `updated_at` DATETIME
);

CREATE TABLE IF NOT EXISTS rowanenergy_stripe_checkout (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `user_id` INT,
  `stripe_id` VARCHAR(512),
  `object` TEXT,
  `created_at` DATE,
  `updated_at` DATETIME
);

CREATE TABLE IF NOT EXISTS rowanenergy_stripe_webhook (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `stripe_id` VARCHAR(512),
  `idempotency_key` VARCHAR(512),
  `description` VARCHAR(512),
  `event_type` VARCHAR(512),
  `resource_type` VARCHAR(512),
  `object` TEXT,
  `is_handled` BOOLEAN,
  `created_at` DATE,
  `updated_at` DATETIME
);

CREATE TABLE IF NOT EXISTS rowanenergy_stripe_setting (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `key` VARCHAR(512),
  `value` MEDIUMTEXT,
  `created_at` DATE,
  `updated_at` DATETIME
);

CREATE TABLE IF NOT EXISTS rowanenergy_stripe_order (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `user_id` INT,
  `price_id` VARCHAR(512),
  `stripe_id` VARCHAR(512),
  `object` TEXT,
  `created_at` DATE,
  `updated_at` DATETIME
);

CREATE TABLE IF NOT EXISTS rowanenergy_stripe_invoice (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `user_id` INT,
  `stripe_id` VARCHAR(512),
  `object` TEXT,
  `created_at` DATE,
  `updated_at` DATETIME
);

CREATE TABLE IF NOT EXISTS rowanenergy_stripe_refund (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `user_id` INT,
  `stripe_id` VARCHAR(512),
  `object` TEXT,
  `charge_id` VARCHAR(512),
  `subscription_id` VARCHAR(512),
  `amount` VARCHAR(512),
  `currency` VARCHAR(512),
  `reason` VARCHAR(512),
  `status` VARCHAR(512),
  `created_at` DATE,
  `updated_at` DATETIME
);

CREATE TABLE IF NOT EXISTS rowanenergy_stripe_dispute (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `user_id` INT,
  `stripe_id` VARCHAR(512),
  `subscription_id` VARCHAR(512),
  `object` TEXT,
  `amount` VARCHAR(512),
  `reason` VARCHAR(512),
  `reason_description` VARCHAR(512),
  `status` VARCHAR(512),
  `created_at` DATE,
  `updated_at` DATETIME
);

-- Hover Lens: New Core Tables
CREATE TABLE IF NOT EXISTS rowanenergy_lead (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `name` VARCHAR(512) NOT NULL,
  `email` VARCHAR(512) NOT NULL,
  `phone` VARCHAR(128),
  `site_address` VARCHAR(1024),
  `asset_type` VARCHAR(128),
  `site_details` TEXT,
  `source` VARCHAR(128) DEFAULT 'form',
  `status` VARCHAR(128) DEFAULT 'new',
  `region` VARCHAR(256),
  `job_id` INT,
  `follow_up_needed` BOOLEAN DEFAULT 1,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS rowanenergy_job_survey (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `customer_id` INT NOT NULL,
  `engineer_id` INT,
  `site_name` VARCHAR(512) NOT NULL,
  `location` VARCHAR(1024) NOT NULL,
  `asset_type` VARCHAR(128) DEFAULT 'solar',
  `scheduled_time` DATETIME,
  `status` VARCHAR(128) DEFAULT 'pending',
  `system_size` VARCHAR(256),
  `access_instructions` TEXT,
  `contact_name` VARCHAR(512),
  `contact_phone` VARCHAR(128),
  `survey_purpose` TEXT,
  `additional_notes` TEXT,
  `internal_notes` TEXT,
  `customer_summary` TEXT,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS rowanenergy_report (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `job_id` INT NOT NULL,
  `thermal_images` TEXT,
  `rgb_photos` TEXT,
  `video_footage` TEXT,
  `flight_logs` TEXT,
  `ai_analysis` TEXT,
  `compliance_docs` TEXT,
  `internal_notes` TEXT,
  `customer_summary` TEXT,
  `uploaded_at` DATETIME,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS rowanenergy_engineer_profile (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `user_id` INT NOT NULL,
  `availability` TEXT,
  `regions` TEXT,
  `workload_limit` INT,
  `status` VARCHAR(64) DEFAULT 'active',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS rowanenergy_checklist (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `job_id` INT NOT NULL,
  `equipment_checklist` TEXT,
  `risk_assessment` TEXT,
  `risk_mitigation_plan` TEXT,
  `required_uploads` TEXT,
  `status` VARCHAR(64) DEFAULT 'pending',
  `last_updated` DATETIME,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Chat History Table
CREATE TABLE IF NOT EXISTS rowanenergy_chat_history (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `user_id` INT NULL,
  `session_id` VARCHAR(255) NOT NULL,
  `chat_type` ENUM('booking_assistant', 'survey_analysis') NOT NULL,
  `role` ENUM('user', 'assistant') NOT NULL,
  `content` TEXT NOT NULL,
  `metadata` JSON NULL,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Indexes for chat history
CREATE INDEX IF NOT EXISTS idx_rowanenergy_chat_history_user_id ON rowanenergy_chat_history(`user_id`);
CREATE INDEX IF NOT EXISTS idx_rowanenergy_chat_history_session_id ON rowanenergy_chat_history(`session_id`);
CREATE INDEX IF NOT EXISTS idx_rowanenergy_chat_history_chat_type ON rowanenergy_chat_history(`chat_type`);
CREATE INDEX IF NOT EXISTS idx_rowanenergy_chat_history_created_at ON rowanenergy_chat_history(`created_at`);

