
const AuthService = require('../../baas/services/AuthService');
const TokenMiddleware = require('../../baas/middleware/TokenMiddleware');
const { filterEmptyFields, sqlDateTimeFormat } = require('../../baas/services/UtilService');
const fetch = require('node-fetch');
const axios = require('axios');

const LeadService = require('./services/leadService');
const JobService = require('./services/jobService');
const ReportService = require('./services/reportService');
const ChecklistService = require('./services/checklistService');
const EngineerService = require('./services/engineerService');
const ChatHistoryService = require('./services/chatHistoryService');
const {generateAvailableSlots} = require('./utils/util');
const moment = require('moment-timezone');
const ds_url = 'http://104.225.217.215:5340';

  // Weather API Configuration
  const WEATHER_API_KEY = process.env.OPENWEATHER_API_KEY || '********************************';
  const WEATHER_BASE_URL = 'https://api.openweathermap.org/data/2.5';
  
  // Maps API Configuration
  const GOOGLE_MAPS_API_KEY = process.env.GOOGLE_MAPS_API_KEY || 'AIzaSyAb3HuumPrd8iDPFGDnsQP_y57DyRXKJzE';
  const GOOGLE_MAPS_BASE_URL = 'https://maps.googleapis.com/maps/api';

  // Weather Service Functions
  const WeatherService = {
    async getCurrentWeather(lat, lon) {
      try {
        const response = await axios.get(`${WEATHER_BASE_URL}/weather`, {
          params: {
            lat,
            lon,
            appid: WEATHER_API_KEY,
            units: 'metric'
          }
        });
        
        return {
          temperature: Math.round(response.data.main.temp),
          condition: response.data.weather[0].main,
          description: response.data.weather[0].description,
          humidity: response.data.main.humidity,
          windSpeed: response.data.wind.speed,
          visibility: response.data.visibility / 1000, // Convert to km
          icon: response.data.weather[0].icon
        };
      } catch (error) {
        console.error('Weather API error:', error);
        // Return fallback weather data
        return {
          temperature: 22,
          condition: 'Clear',
          description: 'clear sky',
          humidity: 65,
          windSpeed: 3.2,
          visibility: 10,
          icon: '01d'
        };
      }
    },

    async getWeatherForecast(lat, lon, days = 5) {
      try {
        const response = await axios.get(`${WEATHER_BASE_URL}/forecast`, {
          params: {
            lat,
            lon,
            appid: WEATHER_API_KEY,
            units: 'metric',
            cnt: days * 8 // 8 forecasts per day (3-hour intervals)
          }
        });

        // Group forecasts by day
        const dailyForecasts = {};
        response.data.list.forEach(forecast => {
          const date = forecast.dt_txt.split(' ')[0];
          if (!dailyForecasts[date]) {
            dailyForecasts[date] = {
              date,
              temp_min: forecast.main.temp_min,
              temp_max: forecast.main.temp_max,
              condition: forecast.weather[0].main,
              description: forecast.weather[0].description,
              icon: forecast.weather[0].icon,
              humidity: forecast.main.humidity,
              windSpeed: forecast.wind.speed
            };
          } else {
            // Update min/max temperatures
            dailyForecasts[date].temp_min = Math.min(dailyForecasts[date].temp_min, forecast.main.temp_min);
            dailyForecasts[date].temp_max = Math.max(dailyForecasts[date].temp_max, forecast.main.temp_max);
          }
        });

        return Object.values(dailyForecasts).map(day => ({
          ...day,
          temp_min: Math.round(day.temp_min),
          temp_max: Math.round(day.temp_max)
        }));
      } catch (error) {
        console.error('Weather forecast API error:', error);
        // Return fallback forecast data
        return Array.from({ length: days }, (_, i) => ({
          date: new Date(Date.now() + i * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
          temp_min: 18 + Math.floor(Math.random() * 5),
          temp_max: 25 + Math.floor(Math.random() * 8),
          condition: ['Clear', 'Clouds', 'Rain'][Math.floor(Math.random() * 3)],
          description: 'Variable conditions',
          icon: '02d',
          humidity: 60 + Math.floor(Math.random() * 20),
          windSpeed: 2 + Math.random() * 4
        }));
      }
    },

    // Get coordinates from location string (using OpenWeather Geocoding API)
    async getCoordinatesFromLocation(location) {
      try {
        const response = await axios.get(`http://api.openweathermap.org/geo/1.0/direct`, {
          params: {
            q: location,
            limit: 1,
            appid: WEATHER_API_KEY
          }
        });

        if (response.data.length > 0) {
          return {
            lat: response.data[0].lat,
            lon: response.data[0].lon,
            name: response.data[0].name,
            country: response.data[0].country
          };
        }
        return null;
      } catch (error) {
        console.error('Geocoding API error:', error);
        return null;
      }
    }
  };

  // Maps Service Functions
  const MapsService = {
    // Geocode an address to get coordinates
    async geocodeAddress(address) {
      try {
        const response = await axios.get(`${GOOGLE_MAPS_BASE_URL}/geocode/json`, {
          params: {
            address,
            key: GOOGLE_MAPS_API_KEY
          }
        });

        if (response.data.status === 'OK' && response.data.results.length > 0) {
          const result = response.data.results[0];
          return {
            lat: result.geometry.location.lat,
            lng: result.geometry.location.lng,
            formatted_address: result.formatted_address,
            place_id: result.place_id
          };
        }
        return null;
      } catch (error) {
        console.error('Geocoding error:', error);
        return null;
      }
    },

    // Get distance and travel time between two points
    async getDistanceMatrix(origins, destinations) {
      try {
        const response = await axios.get(`${GOOGLE_MAPS_BASE_URL}/distancematrix/json`, {
          params: {
            origins: Array.isArray(origins) ? origins.join('|') : origins,
            destinations: Array.isArray(destinations) ? destinations.join('|') : destinations,
            key: GOOGLE_MAPS_API_KEY,
            units: 'metric'
          }
        });

        if (response.data.status === 'OK') {
          return response.data.rows.map(row => 
            row.elements.map(element => ({
              distance: element.distance,
              duration: element.duration,
              status: element.status
            }))
          );
        }
        return null;
      } catch (error) {
        console.error('Distance Matrix error:', error);
        return null;
      }
    },

    // Get directions between multiple waypoints
    async getDirections(origin, destination, waypoints = []) {
      try {
        const params = {
          origin,
          destination,
          key: GOOGLE_MAPS_API_KEY
        };

        if (waypoints.length > 0) {
          params.waypoints = waypoints.join('|');
          params.optimize = 'true'; // Optimize waypoint order
        }

        const response = await axios.get(`${GOOGLE_MAPS_BASE_URL}/directions/json`, {
          params
        });

        if (response.data.status === 'OK') {
          const route = response.data.routes[0];
          return {
            distance: route.legs.reduce((acc, leg) => acc + leg.distance.value, 0),
            duration: route.legs.reduce((acc, leg) => acc + leg.duration.value, 0),
            polyline: route.overview_polyline.points,
            waypoint_order: response.data.routes[0].waypoint_order || [],
            legs: route.legs.map(leg => ({
              distance: leg.distance,
              duration: leg.duration,
              start_address: leg.start_address,
              end_address: leg.end_address
            }))
          };
        }
        return null;
      } catch (error) {
        console.error('Directions error:', error);
        return null;
      }
    },

    // Generate static map URL
    generateStaticMapUrl(options) {
      const {
        center,
        zoom = 15,
        size = '400x300',
        markers = [],
        path = null
      } = options;

      const params = new URLSearchParams({
        center,
        zoom,
        size,
        key: GOOGLE_MAPS_API_KEY
      });

      // Add markers
      markers.forEach(marker => {
        const markerStr = `${marker.color || 'red'}|${marker.lat},${marker.lng}`;
        params.append('markers', markerStr);
      });

      // Add path if provided
      if (path) {
        params.append('path', `color:0x0000ff|weight:5|${path}`);
      }

      return `${GOOGLE_MAPS_BASE_URL}/staticmap?${params.toString()}`;
    },

    // Get nearby places
    async getNearbyPlaces(lat, lng, type = 'point_of_interest', radius = 5000) {
      try {
        const response = await axios.get(`${GOOGLE_MAPS_BASE_URL}/place/nearbysearch/json`, {
          params: {
            location: `${lat},${lng}`,
            radius,
            type,
            key: GOOGLE_MAPS_API_KEY
          }
        });

        if (response.data.status === 'OK') {
          return response.data.results.map(place => ({
            name: place.name,
            place_id: place.place_id,
            location: place.geometry.location,
            rating: place.rating,
            types: place.types
          }));
        }
        return [];
      } catch (error) {
        console.error('Nearby places error:', error);
        return [];
      }
    }
  };

module.exports = function(app) {

  // Helper function for admin-only access
  const AdminMiddleware = () => {
    return [
      TokenMiddleware(),
      (req, res, next) => {
        if (req.role !== 'admin' && req.role !== 'super_admin') {
          return res.status(403).json({ error: true, message: 'Admin access required' });
        }
        next();
      }
    ];
  };
  // Load lambda functions
  const memberRegisterLambda = require('./lambda/member_register');
  const memberLoginLambda = require('./lambda/member_login');
  const droneEngineerRegisterLambda = require('./lambda/drone-engineer_register');
  const droneEngineerLoginLambda = require('./lambda/drone-engineer_login');

  // Initialize lambda functions
  if (typeof memberRegisterLambda === 'function') memberRegisterLambda(app);
  if (typeof memberLoginLambda === 'function') memberLoginLambda(app);
  if (typeof droneEngineerRegisterLambda === 'function') droneEngineerRegisterLambda(app);
  if (typeof droneEngineerLoginLambda === 'function') droneEngineerLoginLambda(app);

  // CUSTOMER
  app.get('/v1/api/rowanenergy/member/customer/dashboard', [TokenMiddleware()], async (req, res) => {
    try {
      const sdk = app.get('sdk');
      sdk.setProjectId('rowanenergy');

      // Get user data
      const user = await sdk.findOne('user', { id: req.user_id });

      // Get next scheduled job
      const nextJob = await sdk.findOne('job_survey', { customer_id: req.user_id, status: 'scheduled' }, { orderBy: 'scheduled_time', direction: 'asc' });

      // Get booking statistics
      const bookingStats = await sdk.rawQuery(`
        SELECT
          COUNT(CASE WHEN status = 'completed' OR status = 'survey_completed' THEN 1 END) as completed_bookings,
          COUNT(CASE WHEN status = 'scheduled' THEN 1 END) as pending_bookings,
          COUNT(CASE WHEN status = 'in_progress' THEN 1 END) as in_progress_bookings
        FROM job_survey
        WHERE customer_id = ?
      `, [req.user_id]);

      // Get recent reports (last 3)
      const recentReports = await sdk.rawQuery(`
        SELECT r.*, js.site_name, js.location, js.asset_type, js.scheduled_time,
               ep.first_name as engineer_first_name, ep.last_name as engineer_last_name
        FROM rowanenergy_report r
        JOIN rowanenergy_job_survey js ON r.job_id = js.id
        LEFT JOIN rowanenergy_preference ep ON js.engineer_id = ep.user_id
        WHERE js.customer_id = ?
        ORDER BY r.uploaded_at DESC
        LIMIT 3
      `, [req.user_id]);

      console.log('Dashboard - Recent reports found:', recentReports.length, 'for customer:', req.user_id);
      console.log('Dashboard - Recent reports data:', recentReports);

      // Debug: Check if there are any reports at all for this customer
      const allReportsForCustomer = await sdk.rawQuery(`
        SELECT COUNT(*) as total_reports
        FROM rowanenergy_report r
        JOIN rowanenergy_job_survey js ON r.job_id = js.id
        WHERE js.customer_id = ?
      `, [req.user_id]);
      console.log('Dashboard - Total reports for customer:', allReportsForCustomer[0]?.total_reports || 0);

      // Debug: Check if there are any jobs for this customer
      const allJobsForCustomer = await sdk.rawQuery(`
        SELECT COUNT(*) as total_jobs
        FROM rowanenergy_job_survey js
        WHERE js.customer_id = ?
      `, [req.user_id]);
      console.log('Dashboard - Total jobs for customer:', allJobsForCustomer[0]?.total_jobs || 0);

      // Get all bookings for the customer
      const allBookings = await sdk.find('job_survey', { customer_id: req.user_id }, { orderBy: 'scheduled_time', direction: 'desc' });

      const dashboardData = {
        user: {
          first_name: user?.first_name || '',
          last_name: user?.last_name || '',
          email: user?.email || ''
        },
        booking_stats: {
          completed_bookings: bookingStats[0]?.completed_bookings || 0,
          pending_bookings: bookingStats[0]?.pending_bookings || 0,
          in_progress_bookings: bookingStats[0]?.in_progress_bookings || 0
        },
        next_survey: nextJob || null,
        recent_reports: recentReports.map(report => ({
          id: report.id,
          job_id: report.job_id,
          site_name: report.site_name,
          location: report.location,
          asset_type: report.asset_type,
          engineer_name: `${report.engineer_first_name || ''} ${report.engineer_last_name || ''}`.trim(),
          customer_summary: report.customer_summary,
          uploaded_at: report.uploaded_at,
          scheduled_time: report.scheduled_time
        })),
        all_bookings: allBookings || []
      };

      res.status(200).json({ error: false, model: dashboardData });
    } catch (error) {
      console.error('Customer dashboard error:', error);
      res.status(500).json({ error: true, message: error.message });
    }
  });

  app.post('/v1/api/rowanenergy/member/customer/booking', [TokenMiddleware()], async (req, res) => {
    try {
      const sdk = app.get('sdk');
      sdk.setProjectId('rowanenergy');
      JobService.setSDK(sdk);
      const job = await JobService.createJob({ customer_id: req.user_id, ...req.body });
      res.status(200).json({ error: false, model: job });
    } catch (error) { res.status(500).json({ error: true, message: error.message }); }
  });

  app.get('/v1/api/rowanenergy/member/customer/bookings', [TokenMiddleware()], async (req, res) => {
    try {
      const sdk = app.get('sdk');
      sdk.setProjectId('rowanenergy');
      JobService.setSDK(sdk);
      
      // Extract pagination and filter parameters
      const { page = 1, limit = 10, start_date, end_date, status } = req.query;
      const offset = (parseInt(page) - 1) * parseInt(limit);
      
      // Build base where conditions
      let whereConditions = ['customer_id = ?'];
      let whereParams = [req.user_id];
      
      // Add date filters
      if (start_date) {
        whereConditions.push('DATE(scheduled_time) >= ?');
        whereParams.push(start_date);
      }
      
      if (end_date) {
        whereConditions.push('DATE(scheduled_time) <= ?');
        whereParams.push(end_date);
      }
      
      // Add status filter
      if (status && status !== 'all') {
        whereConditions.push('status = ?');
        whereParams.push(status);
      }
      
      const whereClause = whereConditions.join(' AND ');
      
      // Get total count for pagination
      const countQuery = `SELECT COUNT(*) as total FROM rowanenergy_job_survey WHERE ${whereClause}`;
      const countResult = await sdk.rawQuery(countQuery, whereParams);
      const total = countResult[0]?.total || 0;
      
      // Get paginated results
      const dataQuery = `
        SELECT * FROM rowanenergy_job_survey 
        WHERE ${whereClause} 
        ORDER BY created_at DESC 
        LIMIT ? OFFSET ?
      `;
      const list = await sdk.rawQuery(dataQuery, [...whereParams, parseInt(limit), offset]);
      
      const totalPages = Math.ceil(total / parseInt(limit));
      
      res.status(200).json({ 
        error: false, 
        list,
        total,
        totalPages,
        currentPage: parseInt(page),
        limit: parseInt(limit)
      });
    } catch (error) { res.status(500).json({ error: true, message: error.message }); }
  });

  app.get('/v1/api/rowanenergy/member/customer/booking/:id', [TokenMiddleware()], async (req, res) => {
    try {
      const sdk = app.get('sdk');
      sdk.setProjectId('rowanenergy');
      JobService.setSDK(sdk);
      const job = await JobService.getJob(req.params.id);
      
      // Verify job ownership
      if (!job || job.customer_id !== req.user_id) {
        return res.status(403).json({ error: true, message: 'Forbidden' });
      }
      
      // Get engineer details if job has an assigned engineer
      let engineerDetails = null;
      if (job && job.engineer_id) {
        try {
          // Get engineer user data
          const engineerUser = await sdk.findOne('user', { id: job.engineer_id });
          // Get engineer preference data (for first_name, last_name, etc.)
          const engineerPreference = await sdk.findOne('preference', { user_id: job.engineer_id });
          
          if (engineerUser) {
            const userData = JSON.parse(engineerUser.data || '{}');
            engineerDetails = {
              id: engineerUser.id,
              email: engineerUser.email,
              first_name: engineerPreference?.first_name || userData.first_name || '',
              last_name: engineerPreference?.last_name || userData.last_name || '',
              full_name: engineerPreference ? 
                `${engineerPreference.first_name} ${engineerPreference.last_name}`.trim() :
                userData.full_name || `${userData.first_name || ''} ${userData.last_name || ''}`.trim(),
              phone: engineerPreference?.phone || userData.phone || '',
              photo: engineerPreference?.photo || userData.photo || '',
              experience: userData.experience || '',
              certifications: userData.certifications || []
            };
          }
        } catch (e) {
          // Silently fail if engineer details can't be fetched
          console.error('Error fetching engineer details:', e);
        }
      }
      
      // If job is completed, get survey notes for customer summary
      let surveyNotes = null;
      if (job && job.status === 'completed') {
        try {
          surveyNotes = await sdk.findOne('job_survey_notes', { job_id: job.id });
        } catch (e) {
          // Silently fail if survey notes don't exist
        }
      }
      
      // Get checklist progress if available
      let checklistProgress = null;
      if (job) {
        try {
          const checklist = await sdk.findOne('checklist', { job_id: job.id });
          if (checklist) {
            checklistProgress = {
              status: checklist.status || 'pending',
              last_updated: checklist.last_updated,
              equipment_completed: false,
              risk_assessment_completed: false
            };
            
            // Check equipment checklist completion
            try {
              const equipmentChecklist = JSON.parse(checklist.equipment_checklist || '{}');
              checklistProgress.equipment_completed = Object.values(equipmentChecklist).every(Boolean);
            } catch (e) {}
            
            // Check risk assessment completion
            try {
              const riskAssessment = JSON.parse(checklist.risk_assessment || '{}');
              checklistProgress.risk_assessment_completed = 
                riskAssessment.public_safety_assessed && 
                riskAssessment.wildlife_check_complete && 
                riskAssessment.airspace_clearance_obtained && 
                riskAssessment.permits_verified;
            } catch (e) {}
          }
        } catch (e) {
          // Silently fail if checklist can't be fetched
        }
      }
      
      // Include comprehensive job data for customer
      const model = {
        ...job,
        engineer_name: engineerDetails?.full_name || 'Not assigned',
        engineer_email: engineerDetails?.email || null,
        engineer_phone: engineerDetails?.phone || null,
        engineer_photo: engineerDetails?.photo || null,
        engineer_experience: engineerDetails?.experience || null,
        engineer_certifications: engineerDetails?.certifications || [],
        checklist_progress: checklistProgress,
        survey_summary: surveyNotes?.customer_summary || null,
        survey_completed_at: surveyNotes?.completed_at || null,
        // Status timeline information
        timeline: {
          booked_at: job.created_at,
          scheduled_at: job.scheduled_time,
          started_at: job.status === 'in_progress' || job.status === 'completed' ? job.updated_at : null,
          completed_at: job.status === 'completed' ? surveyNotes?.completed_at || job.updated_at : null
        }
      };
      
      res.status(200).json({ error: false, model });
    } catch (error) { res.status(500).json({ error: true, message: error.message }); }
  });

  app.get('/v1/api/rowanenergy/member/customer/reports', [TokenMiddleware()], async (req, res) => {
    try {
      const sdk = app.get('sdk');
      sdk.setProjectId('rowanenergy');
      ReportService.setSDK(sdk);
      
      // Enhanced query to get reports with job and engineer details
      const query = `
        SELECT 
          r.*,
          j.site_name,
          j.location,
          j.scheduled_time,
          j.engineer_id,
          j.asset_type,
          j.survey_purpose,
          j.status as job_status
        FROM rowanenergy_report r
        INNER JOIN rowanenergy_job_survey j ON r.job_id = j.id
        WHERE j.customer_id = ?
        ORDER BY r.created_at DESC
      `;
      const reports = await sdk.rawQuery(query, [req.user_id]);
      
      // Enhance reports with additional metadata and proper formatting
      const enhancedReports = await Promise.all(reports.map(async (report) => {
        // Get engineer details if available
        let engineerName = 'Unknown Engineer';
        if (report.engineer_id) {
          try {
            const engineerUser = await sdk.findOne('user', { id: report.engineer_id });
            const engineerPreference = await sdk.findOne('preference', { user_id: report.engineer_id });
            
            if (engineerUser) {
              const userData = JSON.parse(engineerUser.data || '{}');
              engineerName = engineerPreference ? 
                `${engineerPreference.first_name} ${engineerPreference.last_name}`.trim() :
                userData.full_name || `${userData.first_name || ''} ${userData.last_name || ''}`.trim() || 
                'Assigned Engineer';
            }
          } catch (e) {
            // Silently fail
          }
        }
        
        // Determine report type and format based on file name/type
        const fileName = report.file_name || report.title || 'Survey Report';
        const fileExtension = fileName.split('.').pop()?.toUpperCase() || 'PDF';
        const fileSize = report.file_size || `${Math.floor(Math.random() * 3) + 1}.${Math.floor(Math.random() * 9) + 1} MB`;
        
        // Generate appropriate report title based on survey type
        let reportTitle = fileName;
        if (report.asset_type) {
          const assetTypeMap = {
            'solar': 'Solar Farm Survey Report',
            'wind': 'Wind Turbine Inspection Report',
            'building': 'Building Condition Report',
            'infrastructure': 'Infrastructure Assessment Report'
          };
          reportTitle = assetTypeMap[report.asset_type.toLowerCase()] || `${report.asset_type} Survey Report`;
        }
        
        // Set appropriate colors based on file type
        let typeColor = 'text-red-500';
        let typeBgColor = 'bg-red-50';
        
        if (fileExtension === 'CSV') {
          typeColor = 'text-green-500';
          typeBgColor = 'bg-green-50';
        } else if (fileExtension === 'ZIP') {
          typeColor = 'text-yellow-500';
          typeBgColor = 'bg-yellow-50';
        }
        
        return {
          id: report.id,
          title: reportTitle,
          location: report.site_name || report.location || 'Unknown Location',
          date: report.scheduled_time ? 
            new Date(report.scheduled_time).toLocaleDateString('en-US', { 
              year: 'numeric', 
              month: 'short', 
              day: 'numeric' 
            }) : 
            new Date(report.created_at).toLocaleDateString('en-US', { 
              year: 'numeric', 
              month: 'short', 
              day: 'numeric' 
            }),
          type: fileExtension,
          size: fileSize,
          color: typeColor,
          bgColor: typeBgColor,
          engineer_name: engineerName,
          job_id: report.job_id,
          file_url: report.file_url || report.download_url,
          survey_purpose: report.survey_purpose || 'General inspection',
          job_status: report.job_status || 'completed',
          upload_date: new Date(report.created_at).toLocaleDateString('en-US', { 
            year: 'numeric', 
            month: 'short', 
            day: 'numeric' 
          }),
          // Additional metadata for filtering and searching
          asset_type: report.asset_type,
          created_at: report.created_at,
          updated_at: report.updated_at
        };
      }));
      
      res.status(200).json({ error: false, list: enhancedReports });
    } catch (error) { 
      console.error('Reports fetch error:', error);
      res.status(500).json({ error: true, message: error.message }); 
    }
  });

  // secure report download
  app.get('/v1/api/rowanenergy/member/reports/download/:id', [TokenMiddleware()], async (req, res) => {
    try {
      const sdk = app.get('sdk');
      sdk.setProjectId('rowanenergy');
      const report = await sdk.findOne('report', { id: req.params.id });
      if (!report) return res.status(404).json({ error: true, message: 'Report not found' });
      const job = await sdk.findOne('job_survey', { id: report.job_id });
      if (!job || job.customer_id !== req.user_id) {
        return res.status(403).json({ error: true, message: 'Forbidden' });
      }
      // In a real impl, generate signed URLs for files. Placeholder returns metadata.
      res.status(200).json({ error: false, model: report });
    } catch (error) { res.status(500).json({ error: true, message: error.message }); }
  });

  app.get('/v1/api/rowanenergy/member/customer/profile', [TokenMiddleware()], async (req, res) => {
    try {
      const sdk = app.get('sdk');
      sdk.setProjectId('rowanenergy');
      
      // Get user data
      const user = await sdk.findOne('user', { id: req.user_id });
      if (!user) {
        return res.status(404).json({ error: true, message: 'User not found' });
      }
      
      // Get preference data
      const preference = await sdk.findOne('preference', { user_id: req.user_id });
      
      // Merge user data with preference data
      const userData = JSON.parse(user.data || '{}');
      const profileData = {
        ...user,
        ...userData,
        // Preference data takes precedence
        first_name: preference?.first_name || userData.first_name || '',
        last_name: preference?.last_name || userData.last_name || '',
        phone: preference?.phone || userData.phone || '',
        photo: preference?.photo || userData.photo || ''
      };
      
      res.status(200).json({ error: false, model: profileData });
    } catch (error) { 
      res.status(500).json({ error: true, message: error.message }); 
    }
  });

  app.put('/v1/api/rowanenergy/member/customer/profile', [TokenMiddleware()], async (req, res) => {
    try {
      const sdk = app.get('sdk');
      sdk.setProjectId('rowanenergy');
      
      // Update user data field
      const user = await sdk.findOne('user', { id: req.user_id });
      const currentData = JSON.parse(user.data || '{}');
      const updatedData = { 
        ...currentData, 
        first_name: req.body.first_name || currentData.first_name,
        last_name: req.body.last_name || currentData.last_name,
        phone: req.body.phone || currentData.phone,
        photo: req.body.photo || currentData.photo,
        company: req.body.company || currentData.company
      };
      
      await sdk.update('user', { id: req.user_id }, { 
        data: JSON.stringify(updatedData), 
        updated_at: sqlDateTimeFormat(new Date()) 
      });
      
      // Update or create preference record
      const preference = await sdk.findOne('preference', { user_id: req.user_id });
      const preferenceData = {
        user_id: req.user_id,
        first_name: req.body.first_name || '',
        last_name: req.body.last_name || '',
        phone: req.body.phone || '',
        photo: req.body.photo || '',
        updated_at: sqlDateTimeFormat(new Date())
      };
      
      if (preference) {
        await sdk.update('preference', { user_id: req.user_id }, preferenceData);
      } else {
        preferenceData.created_at = sqlDateTimeFormat(new Date());
        await sdk.create('preference', preferenceData);
      }
      
      res.status(200).json({ error: false, message: 'Profile updated' });
    } catch (error) { 
      res.status(500).json({ error: true, message: error.message }); 
    }
  });

  // ADMIN LOGIN
  app.post('/v1/api/rowanenergy/super_admin/auth/login', async (req, res) => {
    try {
      const { email, password, is_refresh = true } = req.body;
      
      if (!email || !password) {
        return res.status(400).json({ 
          error: true, 
          message: 'Email and password are required' 
        });
      }

      const sdk = app.get('sdk');
      sdk.setProjectId('rowanenergy');
      
      // Check if user exists and validate password
      const user = await sdk.findOne('user', { email: email });
      if (!user) {
        return res.status(403).json({ 
          error: true, 
          message: 'Invalid credentials' 
        });
      }

      // Verify password
      const PasswordService = require('../../baas/services/PasswordService');
      const isValidPassword = await PasswordService.compareHash(password, user.password);
      if (!isValidPassword) {
        return res.status(403).json({ 
          error: true, 
          message: 'Invalid credentials' 
        });
      }

      // Check if user is admin (you can adjust this logic based on your admin setup)
      if (user.role_id !== 'super_admin' && user.role_id !== 'admin') {
        return res.status(403).json({ 
          error: true, 
          message: 'Admin access required' 
        });
      }

      // Generate tokens
      const JwtService = require('../../baas/services/JwtService');
      const config = req.app.get('configuration');
      
      const tokenPayload = {
        user_id: user.id,
        role: user.role_id
      };

      let response = {
        error: false,
        role: user.role_id,
        token: JwtService.createAccessToken(
          tokenPayload,
          config.access_jwt_expire,
          config.jwt_key
        ),
        expire_at: config.access_jwt_expire,
        user_id: user.id
      };

      // Handle refresh token if needed
      if (is_refresh) {
        const refreshToken = JwtService.createAccessToken(
          tokenPayload,
          config.refresh_jwt_expire,
          config.jwt_key
        );

        let expireDate = new Date();
        expireDate.setSeconds(expireDate.getSeconds() + config.refresh_jwt_expire);

        // Save refresh token
        await sdk.create('tokens', {
          user_id: user.id,
          token: refreshToken,
          code: refreshToken,
          type: 1,
          data: "{}",
          expired_at: expireDate,
          updated_at: new Date(),
          created_at: new Date()
        });

        response.refresh_token = refreshToken;
      }

      res.status(200).json(response);
    } catch (error) {
      console.error('Admin login error:', error);
      res.status(500).json({ 
        error: true, 
        message: 'Login failed' 
      });
    }
  });

  // LEADS (Public + Admin)
  app.post('/v1/api/rowanenergy/member/leads', async (req, res) => {
    try {
      const sdk = app.get('sdk');
      sdk.setProjectId('rowanenergy');
      LeadService.setSDK(sdk);
      const lead = await LeadService.createLead(req.body);
      res.status(200).json({ error: false, model: lead });
    } catch (error) { res.status(500).json({ error: true, message: error.message }); }
  });

  // LEADS MANAGEMENT  
  app.get('/v1/api/rowanenergy/super_admin/leads', AdminMiddleware(), async (req, res) => {
    try {
      const sdk = app.get('sdk');
      sdk.setProjectId('rowanenergy');
      LeadService.setSDK(sdk);
      
      const { page = 1, limit = 20, search, status, source, start_date, end_date } = req.query;
      const offset = (parseInt(page) - 1) * parseInt(limit);
      
      // Build where conditions
      let whereConditions = [];
      let queryParams = [];
      
      if (search) {
        whereConditions.push('(l.name LIKE ? OR l.email LIKE ? OR l.phone LIKE ? OR l.site_address LIKE ?)');
        const searchPattern = `%${search}%`;
        queryParams.push(searchPattern, searchPattern, searchPattern, searchPattern);
      }
      
      if (status && status !== 'all') {
        whereConditions.push('l.status = ?');
        queryParams.push(status);
      }
      
      if (source && source !== 'all') {
        whereConditions.push('l.source = ?');
        queryParams.push(source);
      }
      
      if (start_date) {
        whereConditions.push('DATE(l.created_at) >= ?');
        queryParams.push(start_date);
      }
      
      if (end_date) {
        whereConditions.push('DATE(l.created_at) <= ?');
        queryParams.push(end_date);
      }
      
      const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';
      
      // Get total count
      const countQuery = `SELECT COUNT(*) as total FROM rowanenergy_lead l ${whereClause}`;
      const countResult = await sdk.rawQuery(countQuery, queryParams);
      const total = countResult[0]?.total || 0;
      
      // Get leads (potential customers who submitted inquiries)
      const leadsQuery = `
        SELECT 
          l.*,
          l.created_at as date_joined
        FROM rowanenergy_lead l
        ${whereClause}
        ORDER BY l.created_at DESC
        LIMIT ? OFFSET ?
      `;
      const leads = await sdk.rawQuery(leadsQuery, [...queryParams, parseInt(limit), offset]);
      
      // Format leads for frontend display
      const formattedLeads = leads.map(lead => ({
        ...lead,
        full_name: `${lead.name || 'Unknown Lead'}`,
        contact_info: `${lead.email || ''} ${lead.phone ? '• ' + lead.phone : ''}`.trim(),
        region: lead.region || 'Unknown Region',
        follow_up: lead.follow_up_needed ? 'Yes' : 'No',
        conversion_status: lead.status || 'new',
        date_received: new Date(lead.created_at).toLocaleDateString('en-US', { 
          year: 'numeric', 
          month: 'short', 
          day: 'numeric' 
        }),
        job_reference: null // Leads don't directly convert to jobs in this system
      }));
      
      const totalPages = Math.ceil(total / parseInt(limit));
      
      res.status(200).json({ 
        error: false, 
        list: formattedLeads,
        pagination: {
          total,
          totalPages,
          currentPage: parseInt(page),
          limit: parseInt(limit)
        }
      });
    } catch (error) { 
      console.error('Leads management error:', error);
      res.status(500).json({ error: true, message: error.message }); 
    }
  });

  // Bulk lead actions
  app.post('/v1/api/rowanenergy/super_admin/leads/bulk-call', AdminMiddleware(), async (req, res) => {
    try {
      const { lead_ids, status_filter } = req.body;
      const sdk = app.get('sdk');
      sdk.setProjectId('rowanenergy');
      
      let leads = [];
      if (lead_ids && lead_ids.length > 0) {
        const placeholders = lead_ids.map(() => '?').join(',');
        leads = await sdk.rawQuery(`SELECT * FROM rowanenergy_lead WHERE id IN (${placeholders})`, lead_ids);
      } else if (status_filter) {
        leads = await sdk.rawQuery('SELECT * FROM rowanenergy_lead WHERE status = ?', [status_filter]);
      }
      
      // Here you would integrate with your calling system
      // For now, just return the leads that would be called
      res.status(200).json({ 
        error: false, 
        message: `Initiated calling sequence for ${leads.length} leads`,
        leads_to_call: leads.length
      });
    } catch (error) { 
      console.error('Bulk call error:', error);
      res.status(500).json({ error: true, message: error.message }); 
    }
  });

  // Update lead status
  app.put('/v1/api/rowanenergy/super_admin/leads/:id', AdminMiddleware(), async (req, res) => {
    try {
      const { id } = req.params;
      const sdk = app.get('sdk');
      sdk.setProjectId('rowanenergy');
      
      const updateData = {
        ...req.body,
        updated_at: sqlDateTimeFormat(new Date())
      };
      
      await sdk.updateById('lead', id, updateData);
      res.status(200).json({ error: false, message: 'Lead updated successfully' });
    } catch (error) { 
      console.error('Lead update error:', error);
      res.status(500).json({ error: true, message: error.message }); 
    }
  });

  // ALL SURVEY JOBS MANAGEMENT
  app.get('/v1/api/rowanenergy/super_admin/jobs', AdminMiddleware(), async (req, res) => {
    try {
      const sdk = app.get('sdk');
      sdk.setProjectId('rowanenergy');
      
      const { page = 1, limit = 20, search, status, region, engineer_id, start_date, end_date } = req.query;
      const offset = (parseInt(page) - 1) * parseInt(limit);
      
      // Build where conditions
      let whereConditions = [];
      let queryParams = [];
      
      if (search) {
        whereConditions.push('(j.site_name LIKE ? OR j.location LIKE ? OR CONCAT(cp.first_name, " ", cp.last_name) LIKE ? OR CONCAT(p.first_name, " ", p.last_name) LIKE ?)');
        const searchPattern = `%${search}%`;
        queryParams.push(searchPattern, searchPattern, searchPattern, searchPattern);
      }
      
      if (status && status !== 'all') {
        whereConditions.push('j.status = ?');
        queryParams.push(status);
      }
      
      if (region && region !== 'all') {
        whereConditions.push('j.location LIKE ?');
        queryParams.push(`%${region}%`);
      }
      
      if (engineer_id && engineer_id !== 'all') {
        whereConditions.push('j.engineer_id = ?');
        queryParams.push(engineer_id);
      }
      
      if (start_date) {
        whereConditions.push('DATE(j.scheduled_time) >= ?');
        queryParams.push(start_date);
      }
      
      if (end_date) {
        whereConditions.push('DATE(j.scheduled_time) <= ?');
        queryParams.push(end_date);
      }
      
      const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';
      
      // Get total count
      const countQuery = `
        SELECT COUNT(*) as total 
        FROM rowanenergy_job_survey j
        LEFT JOIN rowanenergy_preference cp ON j.customer_id = cp.user_id
        LEFT JOIN rowanenergy_preference p ON j.engineer_id = p.user_id
        ${whereClause}
      `;
      const countResult = await sdk.rawQuery(countQuery, queryParams);
      const total = countResult[0]?.total || 0;
      
      // Get jobs with details
      const jobsQuery = `
        SELECT 
          j.*,
          CONCAT(cp.first_name, ' ', cp.last_name) as customer_name,
          cu.email as customer_email,
          cp.photo as customer_photo,
          CONCAT(p.first_name, ' ', p.last_name) as engineer_name,
          p.photo as engineer_photo,
          r.id as report_id,
          CASE WHEN r.id IS NOT NULL THEN 'uploaded' ELSE 'pending' END as upload_status
        FROM rowanenergy_job_survey j
        LEFT JOIN rowanenergy_preference cp ON j.customer_id = cp.user_id
        LEFT JOIN rowanenergy_user cu ON j.customer_id = cu.id
        LEFT JOIN rowanenergy_preference p ON j.engineer_id = p.user_id
        LEFT JOIN rowanenergy_report r ON j.id = r.job_id
        ${whereClause}
        ORDER BY j.scheduled_time DESC
        LIMIT ? OFFSET ?
      `;
      const jobs = await sdk.rawQuery(jobsQuery, [...queryParams, parseInt(limit), offset]);
      
      const totalPages = Math.ceil(total / parseInt(limit));
      
      res.status(200).json({ 
        error: false, 
        list: jobs,
        pagination: {
          total,
          totalPages,
          currentPage: parseInt(page),
          limit: parseInt(limit)
        }
      });
    } catch (error) { 
      console.error('Jobs management error:', error);
      res.status(500).json({ error: true, message: error.message }); 
    }
  });

  // Get single job details
  app.get('/v1/api/rowanenergy/super_admin/jobs/:id', AdminMiddleware(), async (req, res) => {
    try {
      const { id } = req.params;
      const sdk = app.get('sdk');
      sdk.setProjectId('rowanenergy');

      // Get job details with customer and engineer information
      const jobQuery = `
        SELECT js.*,
               up.first_name as customer_first_name, up.last_name as customer_last_name,
               c.email as customer_email, up.phone as customer_phone,
               e.first_name as engineer_first_name, e.last_name as engineer_last_name
        FROM rowanenergy_job_survey js
        LEFT JOIN rowanenergy_user c ON js.customer_id = c.id
        LEFT JOIN rowanenergy_preference up ON js.customer_id = up.user_id
        LEFT JOIN rowanenergy_preference e ON js.engineer_id = e.user_id
        WHERE js.id = ?
      `;

      const jobs = await sdk.rawQuery(jobQuery, [id]);

      if (jobs.length === 0) {
        return res.status(404).json({ error: true, message: 'Job not found' });
      }

      const job = jobs[0];

      // Format the response
      const formattedJob = {
        id: job.id,
        site_name: job.site_name,
        location: job.location,
        region: job.region,
        latitude: job.latitude,
        longitude: job.longitude,
        asset_type: job.asset_type,
        system_size: job.system_size,
        scheduled_time: job.scheduled_time,
        duration_minutes: job.duration_minutes,
        status: job.status,
        contact_name: job.contact_name,
        contact_phone: job.contact_phone,
        access_instructions: job.access_instructions,
        survey_purpose: job.survey_purpose,
        additional_notes: job.additional_notes,
        engineer_id: job.engineer_id,
        engineer_name: job.engineer_first_name && job.engineer_last_name ?
                      `${job.engineer_first_name} ${job.engineer_last_name}` : null,
        customer_name: job.customer_first_name && job.customer_last_name ?
                      `${job.customer_first_name} ${job.customer_last_name}` : null,
        customer_email: job.customer_email,
        customer_phone: job.customer_phone,
        created_at: job.created_at
      };

      res.status(200).json({ error: false, model: formattedJob });
    } catch (error) {
      console.error('Job details error:', error);
      res.status(500).json({ error: true, message: error.message });
    }
  });

  // Job assignment/reassignment
  app.put('/v1/api/rowanenergy/super_admin/jobs/:id/assign-engineer', AdminMiddleware(), async (req, res) => {
    try {
      const { id } = req.params;
      const { engineer_id, reason } = req.body;
      const sdk = app.get('sdk');
      sdk.setProjectId('rowanenergy');
      
      await sdk.updateById('job_survey', id, {
        engineer_id,
        updated_at: sqlDateTimeFormat(new Date())
      });
      
      // Log the assignment change if reason provided
      if (reason) {
        await sdk.create('job_assignment_log', {
          job_id: id,
          engineer_id,
          assigned_by: req.user_id,
          reason,
          created_at: sqlDateTimeFormat(new Date())
        });
      }
      
      res.status(200).json({ error: false, message: 'Engineer assigned successfully' });
    } catch (error) { 
      console.error('Job assignment error:', error);
      res.status(500).json({ error: true, message: error.message }); 
    }
  });

  // Update job status
  app.put('/v1/api/rowanenergy/super_admin/jobs/:id/status', AdminMiddleware(), async (req, res) => {
    try {
      const { id } = req.params;
      const { status, reason } = req.body;
      const sdk = app.get('sdk');
      sdk.setProjectId('rowanenergy');
      
      await sdk.updateById('job_survey', id, {
        status,
        updated_at: sqlDateTimeFormat(new Date())
      });
      
      res.status(200).json({ error: false, message: 'Job status updated successfully' });
    } catch (error) { 
      console.error('Job status update error:', error);
      res.status(500).json({ error: true, message: error.message }); 
    }
  });

  // WEEKLY CALENDAR VIEW
  app.get('/v1/api/rowanenergy/super_admin/calendar', AdminMiddleware(), async (req, res) => {
    try {
      const sdk = app.get('sdk');
      sdk.setProjectId('rowanenergy');
      
      const { week_start, view = 'weekly', region, status } = req.query;
      const startDate = week_start || new Date().toISOString().slice(0, 10);
      const endDate = view === 'weekly' ? 
        new Date(new Date(startDate).getTime() + 6 * 24 * 60 * 60 * 1000).toISOString().slice(0, 10) :
        startDate;
      
      // Build where conditions
      let whereConditions = ['DATE(j.scheduled_time) BETWEEN ? AND ?'];
      let queryParams = [startDate, endDate];
      
      if (region && region !== 'all' && region !== 'All Regions' && region !== 'undefined') {
        whereConditions.push('j.location LIKE ?');
        queryParams.push(region);
      }
      
      if (status && status !== 'all' && status !== 'All Status' && status !== 'undefined') {
        whereConditions.push('j.status = ?');
        queryParams.push(status);
      }
      
      const whereClause = whereConditions.join(' AND ');
      
      // Get scheduled jobs
      const jobsQuery = `
        SELECT 
          j.*,
          CONCAT(p.first_name, ' ', p.last_name) as engineer_name,
          p.photo as engineer_photo,
          CONCAT(cp.first_name, ' ', cp.last_name) as customer_name
        FROM rowanenergy_job_survey j
        LEFT JOIN rowanenergy_preference p ON j.engineer_id = p.user_id
        LEFT JOIN rowanenergy_preference cp ON j.customer_id = cp.user_id
        WHERE ${whereClause}
        ORDER BY j.scheduled_time ASC
      `;
      const jobs = await sdk.rawQuery(jobsQuery, queryParams);
      
      // Get unassigned jobs
      const unassignedQuery = `
        SELECT j.*, CONCAT(cp.first_name, ' ', cp.last_name) as customer_name
        FROM rowanenergy_job_survey j
        LEFT JOIN rowanenergy_preference cp ON j.customer_id = cp.user_id
        WHERE DATE(j.scheduled_time) BETWEEN ? AND ? AND j.engineer_id IS NULL
      `;
      const unassignedJobs = await sdk.rawQuery(unassignedQuery, [startDate, endDate]);
      
      // Get engineer workload summary
      const workloadQuery = `
        SELECT 
          p.first_name,
          p.last_name,
          COUNT(j.id) as job_count
        FROM rowanenergy_preference p
        INNER JOIN rowanenergy_user u ON p.user_id = u.id
        INNER JOIN rowanenergy_engineer_profile ep ON u.id = ep.user_id
        LEFT JOIN rowanenergy_job_survey j ON p.user_id = j.engineer_id 
          AND DATE(j.scheduled_time) BETWEEN ? AND ?
        WHERE u.status = 1
        GROUP BY p.user_id, p.first_name, p.last_name
      `;
      const workload = await sdk.rawQuery(workloadQuery, [startDate, endDate]);
      
      res.status(200).json({ 
        error: false, 
        model: {
          jobs,
          unassigned_jobs: unassignedJobs,
          workload_summary: workload,
          date_range: { start: startDate, end: endDate }
        }
      });
    } catch (error) { 
      console.error('Calendar view error:', error);
      res.status(500).json({ error: true, message: error.message }); 
    }
  });

  // CREATE NEW ENGINEER
  app.post('/v1/api/rowanenergy/super_admin/engineers', AdminMiddleware(), async (req, res) => {
    try {
      const sdk = app.get('sdk');
      sdk.setProjectId('rowanenergy');
      
      const { 
        first_name, 
        last_name, 
        email, 
        password, 
        phone, 
        regions, 
        certifications, 
        experience 
      } = req.body;

      // Create user account
      const hashedPassword = await PasswordService.hash(password);
      const userData = {
        email,
        password: hashedPassword,
        role_id: 'drone-engineer',
        status: 1,
        data: JSON.stringify({ certifications, experience })
      };

      const userResponse = await sdk.callRestAPI(userData, 'POST', 'rowanenergy_user');
      if (userResponse.error) {
        return res.status(400).json({ error: true, message: 'Failed to create user account' });
      }

      const userId = userResponse.id;

      // Create engineer profile
      const engineerProfileData = {
        user_id: userId,
        regions: regions,
        created_at: sqlDateTimeFormat(new Date())
      };

      await sdk.callRestAPI(engineerProfileData, 'POST', 'rowanenergy_engineer_profile');

      // Create preference record
      const preferenceData = {
        user_id: userId,
        first_name,
        last_name,
        phone,
        weekly_availability: JSON.stringify({
          monday: { available: true, start: '09:00', end: '17:00' },
          tuesday: { available: true, start: '09:00', end: '17:00' },
          wednesday: { available: true, start: '09:00', end: '17:00' },
          thursday: { available: true, start: '09:00', end: '17:00' },
          friday: { available: true, start: '09:00', end: '17:00' },
          saturday: { available: false, start: '09:00', end: '17:00' },
          sunday: { available: false, start: '09:00', end: '17:00' }
        }),
        region_preferences: JSON.stringify([regions]),
        workload_limit: '3-4 jobs per week',
        created_at: sqlDateTimeFormat(new Date())
      };

      await sdk.callRestAPI(preferenceData, 'POST', 'rowanenergy_preference');

      res.status(200).json({ 
        error: false, 
        message: 'Engineer created successfully',
        model: { id: userId, email, first_name, last_name }
      });
    } catch (error) {
      console.error('Create engineer error:', error);
      res.status(500).json({ error: true, message: error.message });
    }
  });

  // ENGINEERS DIRECTORY
  app.get('/v1/api/rowanenergy/super_admin/engineers', AdminMiddleware(), async (req, res) => {
    try {
      const sdk = app.get('sdk');
      sdk.setProjectId('rowanenergy');
      
      const { search, availability, region, workload } = req.query;
      
      // Build where conditions - check if user has engineer profile
      let whereConditions = ['ep.user_id IS NOT NULL'];
      let queryParams = [];
      
      if (search) {
        whereConditions.push('(p.first_name LIKE ? OR p.last_name LIKE ? OR u.email LIKE ?)');
        const searchPattern = `%${search}%`;
        queryParams.push(searchPattern, searchPattern, searchPattern);
      }
      
      if (availability && availability !== 'all') {
        whereConditions.push('u.status = ?');
        queryParams.push(availability === 'active' ? 'active' : 'inactive');
      }
      
      const whereClause = whereConditions.join(' AND ');
      
      // Get engineers with job counts
      const engineersQuery = `
        SELECT 
          u.id,
          u.email,
          u.status,
          p.first_name,
          p.last_name,
          p.phone,
          p.photo,
          u.data as certifications,
          ep.regions,
          COUNT(j.id) as jobs_this_week,
          COUNT(CASE WHEN j.status = 'completed' THEN 1 END) as completed_jobs,
          COUNT(CASE WHEN DATE(j.scheduled_time) = CURDATE() THEN 1 END) as jobs_today
        FROM rowanenergy_user u
        LEFT JOIN rowanenergy_preference p ON u.id = p.user_id
        LEFT JOIN rowanenergy_engineer_profile ep ON u.id = ep.user_id
        LEFT JOIN rowanenergy_job_survey j ON u.id = j.engineer_id 
          AND DATE(j.scheduled_time) BETWEEN DATE(NOW()) AND DATE(NOW() + INTERVAL 6 DAY)
        WHERE ${whereClause}
        GROUP BY u.id, u.email, u.status, p.first_name, p.last_name, p.phone, p.photo, u.data, ep.regions
        ORDER BY p.first_name, p.last_name
      `;
      const engineers = await sdk.rawQuery(engineersQuery, queryParams);
      
      res.status(200).json({ 
        error: false, 
        list: engineers
      });
    } catch (error) { 
      console.error('Engineers directory error:', error);
      res.status(500).json({ error: true, message: error.message }); 
    }
  });

  // Update engineer status
  app.put('/v1/api/rowanenergy/super_admin/engineers/:id/status', AdminMiddleware(), async (req, res) => {
    try {
      const { id } = req.params;
      const { status } = req.body;
      const sdk = app.get('sdk');
      sdk.setProjectId('rowanenergy');
      
      await sdk.updateById('user', id, {
        status,
        updated_at: sqlDateTimeFormat(new Date())
      });
      
      res.status(200).json({ error: false, message: 'Engineer status updated successfully' });
    } catch (error) { 
      console.error('Engineer status update error:', error);
      res.status(500).json({ error: true, message: error.message }); 
    }
  });

  // ROUTE PLANNER
  app.get('/v1/api/rowanenergy/super_admin/route-planner', AdminMiddleware(), async (req, res) => {
    try {
      const sdk = app.get('sdk');
      sdk.setProjectId('rowanenergy');
      
      const { date, engineer_id, region } = req.query;
      const targetDate = date || new Date().toISOString().slice(0, 10);
      
      // Build where conditions
      let whereConditions = ['DATE(j.scheduled_time) = ?'];
      let queryParams = [targetDate];
      
      if (engineer_id && engineer_id !== 'all') {
        whereConditions.push('j.engineer_id = ?');
        queryParams.push(engineer_id);
      }
      
      if (region && region !== 'all') {
        whereConditions.push('j.location LIKE ?');
        queryParams.push(`%${region}%`);
      }
      
      const whereClause = whereConditions.join(' AND ');
      
      // Get jobs for route planning
      const jobsQuery = `
        SELECT 
          j.*,
          CONCAT(p.first_name, ' ', p.last_name) as engineer_name
        FROM rowanenergy_job_survey j
        LEFT JOIN rowanenergy_preference p ON j.engineer_id = p.user_id
        WHERE ${whereClause}
        ORDER BY j.scheduled_time ASC
      `;
      const jobs = await sdk.rawQuery(jobsQuery, queryParams);
      
      // Get engineer details if specific engineer selected
      let engineerDetails = null;
      if (engineer_id && engineer_id !== 'all') {
        const engineerQuery = `
          SELECT 
            u.id,
            p.first_name,
            p.last_name,
            ep.regions
          FROM rowanenergy_user u
          LEFT JOIN rowanenergy_preference p ON u.id = p.user_id
          LEFT JOIN rowanenergy_engineer_profile ep ON u.id = ep.user_id
          WHERE u.id = ?
        `;
        const engineerResult = await sdk.rawQuery(engineerQuery, [engineer_id]);
        engineerDetails = engineerResult[0] || null;
      }
      
      res.status(200).json({ 
        error: false, 
        model: {
          jobs,
          engineer: engineerDetails,
          date: targetDate,
          total_jobs: jobs.length,
          estimated_drive_time: Math.floor(Math.random() * 180) + 60, // Placeholder
          total_distance: Math.floor(Math.random() * 200) + 50 // Placeholder
        }
      });
    } catch (error) { 
      console.error('Route planner error:', error);
      res.status(500).json({ error: true, message: error.message }); 
    }
  });

  // Optimize route
  app.post('/v1/api/rowanenergy/super_admin/route-planner/optimize', AdminMiddleware(), async (req, res) => {
    try {
      const { job_ids } = req.body;
      const sdk = app.get('sdk');
      sdk.setProjectId('rowanenergy');
      
      // Get jobs
      const placeholders = job_ids.map(() => '?').join(',');
      const jobsQuery = `SELECT * FROM rowanenergy_job_survey WHERE id IN (${placeholders})`;
      const jobs = await sdk.rawQuery(jobsQuery, job_ids);
      
      // Simple optimization - in production you'd use Google Maps API
      const optimizedJobs = jobs.map((job, index) => ({
        ...job,
        order: index + 1,
        estimated_travel_time: Math.floor(Math.random() * 30) + 10
      }));
      
      res.status(200).json({ 
        error: false, 
        model: {
          optimized_route: optimizedJobs,
          total_distance: Math.floor(Math.random() * 200) + 100,
          total_time: optimizedJobs.reduce((acc, job) => acc + job.estimated_travel_time, 0),
          time_saved: Math.floor(Math.random() * 60) + 15
        }
      });
    } catch (error) { 
      console.error('Route optimization error:', error);
      res.status(500).json({ error: true, message: error.message }); 
    }
  });

  // ADMIN LEADS CSV UPLOAD ENDPOINT
  app.post('/v1/api/rowanenergy/super_admin/leads/upload', AdminMiddleware(), async (req, res) => {
    try {
      const { leads } = req.body;
      const sdk = app.get('sdk');
      sdk.setProjectId('rowanenergy');

      if (!leads || !Array.isArray(leads)) {
        return res.status(400).json({ error: true, message: 'Leads array is required' });
      }

      // Required fields validation
      const requiredFields = ['name', 'first_name', 'email', 'phone'];
      const validationErrors = [];
      const validLeads = [];

      for (let i = 0; i < leads.length; i++) {
        const lead = leads[i];
        const errors = [];

        // Check required fields
        requiredFields.forEach(field => {
          if (!lead[field] || lead[field].toString().trim() === '') {
            errors.push(`Row ${i + 1}: Missing required field '${field}'`);
          }
        });

        // Validate email format
        if (lead.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(lead.email)) {
          errors.push(`Row ${i + 1}: Invalid email format`);
        }

        // Validate phone format (basic check)
        if (lead.phone && !/^[\d\s\-\+\(\)]+$/.test(lead.phone)) {
          errors.push(`Row ${i + 1}: Invalid phone format`);
        }

        // Validate priority enum
        if (lead.priority && !['low', 'medium', 'high'].includes(lead.priority.toLowerCase())) {
          errors.push(`Row ${i + 1}: Priority must be 'low', 'medium', or 'high'`);
        }

        if (errors.length > 0) {
          validationErrors.push(...errors);
        } else {
          validLeads.push(lead);
        }
      }

      if (validationErrors.length > 0) {
        return res.status(400).json({
          error: true,
          message: 'Validation errors found',
          validation_errors: validationErrors
        });
      }

      // Save leads to database
      const savedLeads = [];
      const failedLeads = [];

      for (const lead of validLeads) {
        try {

          // check if already exists
          const existingLead = await sdk.findOne('lead', {
            email: lead.email.trim()
          });
          if (existingLead) {
            failedLeads.push({ lead, error: 'Lead already exists' });
            continue;
          }
          const leadData = {
            name: lead.name.trim(),
            first_name: lead.first_name.trim(),
            last_name: lead.last_name ? lead.last_name.trim() : null,
            email: lead.email.trim(),
            phone: lead.phone.trim(),
            site_address: lead.site_address ? lead.site_address.trim() : null,
            asset_type: lead.asset_type ? lead.asset_type.trim() : null,
            site_details: lead.site_details ? lead.site_details.trim() : null,
            source: lead.source ? lead.source.trim() : 'upload',
            status: lead.status ? lead.status.trim() : 'new',
            priority: lead.priority ? lead.priority.toLowerCase() : 'medium',
            lead_score: lead.lead_score ? parseInt(lead.lead_score) : 0,
            region: lead.region ? lead.region.trim() : null,
            job_id: lead.job_id ? parseInt(lead.job_id) : null,
            follow_up_needed: lead.follow_up_needed !== undefined ? (lead.follow_up_needed === 'true' || lead.follow_up_needed === '1' || lead.follow_up_needed === true) : true,
            follow_up_required: lead.follow_up_required !== undefined ? (lead.follow_up_required === 'true' || lead.follow_up_required === '1' || lead.follow_up_required === true) : true,
            last_contacted_at: lead.last_contacted_at ? new Date(lead.last_contacted_at).toISOString() : null,
            next_follow_up_date: lead.next_follow_up_date ? new Date(lead.next_follow_up_date).toISOString().split('T')[0] : null,
            notes: lead.notes ? lead.notes.trim() : null,
            converted_to_customer: lead.converted_to_customer !== undefined ? (lead.converted_to_customer === 'true' || lead.converted_to_customer === '1' || lead.converted_to_customer === true) : false,
            converted_at: lead.converted_at ? new Date(lead.converted_at).toISOString() : null,
            conversion_value: lead.conversion_value ? parseFloat(lead.conversion_value) : 0.00
          };

          const saved = await sdk.create('lead', leadData);
          savedLeads.push(saved);
        } catch (error) {
          console.error('Error saving lead:', error);
          failedLeads.push({ lead, error: error.message });
        }
      }

      res.status(200).json({
        error: false,
        message: `Successfully saved ${savedLeads.length} leads`,
        model: {
          saved_count: savedLeads.length,
          failed_count: failedLeads.length,
          total_count: leads.length,
          failed_leads: failedLeads
        }
      });
    } catch (error) {
      console.error('Save leads error:', error);
      res.status(500).json({ error: true, message: error.message });
    }
  });

  // ADMIN LEADS CSV TEMPLATE DOWNLOAD
  app.get('/v1/api/rowanenergy/super_admin/leads/template', AdminMiddleware(), async (req, res) => {
    try {
      // CSV template with all fields and sample data
      const csvTemplate = `name,first_name,last_name,email,phone,site_address,asset_type,site_details,source,status,priority,lead_score,region,job_id,follow_up_needed,follow_up_required,last_contacted_at,next_follow_up_date,notes,converted_to_customer,converted_at,conversion_value
John Smith Solar Project,John,Smith,<EMAIL>,+1234567890,123 Main St Solar Farm,solar,Large commercial solar installation,website,new,high,85,North,,"true","true",2024-01-15T10:30:00Z,2024-02-01,Initial contact made - very interested,false,,0.00
Jane Doe Wind Farm,Jane,Doe,<EMAIL>,+1987654321,456 Wind Valley Rd,wind,Wind turbine maintenance required,referral,contacted,medium,70,South,,"true","true",,2024-01-20,Follow up on maintenance quote,false,,0.00`;

      res.setHeader('Content-Type', 'text/csv');
      res.setHeader('Content-Disposition', 'attachment; filename="leads_template.csv"');
      res.status(200).send(csvTemplate);
    } catch (error) {
      console.error('Template download error:', error);
      res.status(500).json({ error: true, message: error.message });
    }
  });

  // ADMIN SETTINGS - Optimized single row approach
  app.get('/v1/api/rowanenergy/super_admin/settings', AdminMiddleware(), async (req, res) => {
    try {
      const sdk = app.get('sdk');
      sdk.setProjectId('rowanenergy');

      // Get the single settings row from the new optimized table
      const settingsResult = await sdk.rawQuery(
        'SELECT * FROM rowanenergy_admin_settings_config WHERE id = 1'
      );

      // Default settings structure if no row exists
      const defaultSettings = {
        inbound_campaign: {
          name: 'Solar Survey Inquiry',
          phone_number: '+44 7700 900123'
        },
        outbound_campaign: {
          name: '',
          phone_number: '+44 7700 900456',
          status: 'inactive',
          voice_template: 'Solar Quote Reminder',
          start_date: null,
          call_schedule: 'immediately',
          time_window: { start: '09:00', end: '17:00' }
        },
        email_integration: {
          connected: false,
          google_account: null,
          sender_email: '<EMAIL>',
          calendar_used: 'Primary Calendar',
          send_confirmation: true,
          cc_engineer: true,
          add_calendar_invite: true
        },
        ai_behavior: {
          interrupt_sensitivity: 'low',
          response_speed: 'auto',
          initial_message_delay: 0,
          caller_id_name: 'Bespoke Ins',
          creativity_level: 'professional',
          double_call_retry: false,
          vm_detection: false
        },
        llm_config: {
          provider: 'Groq',
          model: 'llama-3.3-70b-versatile (Fast)',
          max_tokens: 250,
          temperature: 0.01,
          top_p: 0.9,
          system_prompt: 'You are an AI assistant for HoverLens, a drone survey company. You help customers book solar and wind farm inspections. Be professional, helpful, and efficient. Always confirm availability using ${availability} context and capture customer details using ${details}.'
        },
        speech_settings: {
          recognition: {
            provider: 'Deepgram',
            model: 'nova-3 (Very Fast)'
          },
          tts: {
            provider: 'ElevenLabs',
            model: 'eleven_flash_v2'
          }
        },
        company_hours: {
          monday: { enabled: true, timeSlots: [{ start: '09:00', end: '17:00', enabled: true }] },
          tuesday: { enabled: true, timeSlots: [{ start: '09:00', end: '17:00', enabled: true }] },
          wednesday: { enabled: true, timeSlots: [{ start: '09:00', end: '17:00', enabled: true }] },
          thursday: { enabled: true, timeSlots: [{ start: '09:00', end: '17:00', enabled: true }] },
          friday: { enabled: true, timeSlots: [{ start: '09:00', end: '17:00', enabled: true }] },
          saturday: { enabled: false, timeSlots: [{ start: '09:00', end: '17:00', enabled: false }] },
          sunday: { enabled: false, timeSlots: [{ start: '09:00', end: '17:00', enabled: false }] }
        },
        google_calendar_tokens: null,
        admin_tokens: null
      };

      let settings = defaultSettings;

      // If settings exist, parse and merge with defaults
      if (settingsResult.length > 0) {
        const dbSettings = settingsResult[0];

        // Parse JSON fields safely
        const parseJsonField = (field, fallback) => {
          try {
            return field ? JSON.parse(field) : fallback;
          } catch (e) {
            console.error(`Error parsing ${field}:`, e);
            return fallback;
          }
        };

        settings = {
          inbound_campaign: parseJsonField(dbSettings.inbound_campaign, defaultSettings.inbound_campaign),
          outbound_campaign: parseJsonField(dbSettings.outbound_campaign, defaultSettings.outbound_campaign),
          email_integration: parseJsonField(dbSettings.email_integration, defaultSettings.email_integration),
          ai_behavior: parseJsonField(dbSettings.ai_behavior, defaultSettings.ai_behavior),
          llm_config: parseJsonField(dbSettings.llm_config, defaultSettings.llm_config),
          speech_settings: parseJsonField(dbSettings.speech_settings, defaultSettings.speech_settings),
          company_hours: parseJsonField(dbSettings.company_hours, defaultSettings.company_hours),
          google_calendar_tokens: parseJsonField(dbSettings.google_calendar_tokens, null),
          admin_tokens: parseJsonField(dbSettings.admin_tokens, null)
        };
      }

      res.status(200).json({ error: false, model: settings });
    } catch (error) {
      console.error('Settings fetch error:', error);
      res.status(500).json({ error: true, message: error.message });
    }
  });

  // Update voice assistant system prompt
  app.put('/v1/api/rowanenergy/super_admin/voice-assistant/prompt', AdminMiddleware(), async (req, res) => {
    try {
      const { system_prompt } = req.body;
      const sdk = app.get('sdk');
      sdk.setProjectId('rowanenergy');

      if (!system_prompt) {
        return res.status(400).json({ error: true, message: 'System prompt is required' });
      }

      // Get API key from settings
      const settingsResult = await sdk.rawQuery(
        'SELECT admin_tokens FROM rowanenergy_admin_settings_config WHERE id = 1'
      );

      let apiKey = null;
      if (settingsResult.length > 0 && settingsResult[0].admin_tokens) {
        try {
          const adminTokens = JSON.parse(settingsResult[0].admin_tokens);
          apiKey = adminTokens.voice_api_key;
        } catch (error) {
          console.error('Error parsing admin tokens:', error);
        }
      }

      if (!apiKey) {
        return res.status(400).json({ error: true, message: 'Voice API key not configured' });
      }

      // Update voice assistant with new system prompt
      const axios = require('axios');
      const voiceApiResponse = await axios.post(
        'https://voiceapi.automateintel.ai/v3/api/custom/voiceoutreach/user/update_assistant/214',
        {
          script: system_prompt
        },
        {
          headers: {
            'x-api-key': apiKey,
            'Content-Type': 'application/json'
          }
        }
      );

      if (voiceApiResponse.data) {
        // Also update in our local settings
        const updateData = {
          llm_config: JSON.stringify({
            ...JSON.parse(settingsResult[0]?.llm_config || '{}'),
            system_prompt: system_prompt
          })
        };

        const existingSettings = await sdk.rawQuery(
          'SELECT id FROM rowanenergy_admin_settings_config WHERE id = 1'
        );

        if (existingSettings.length > 0) {
          await sdk.rawQuery(
            'UPDATE rowanenergy_admin_settings_config SET llm_config = ?, updated_at = CURRENT_TIMESTAMP WHERE id = 1',
            [updateData.llm_config]
          );
        }

        res.status(200).json({
          error: false,
          message: 'Voice assistant system prompt updated successfully',
          voice_api_response: voiceApiResponse.data
        });
      } else {
        res.status(500).json({
          error: true,
          message: 'Failed to update voice assistant'
        });
      }
    } catch (error) {
      console.error('Voice assistant update error:', error);
      res.status(500).json({ error: true, message: error.message });
    }
  });

  // Update admin settings - Optimized single row approach
  app.put('/v1/api/rowanenergy/super_admin/settings', AdminMiddleware(), async (req, res) => {
    try {
      const sdk = app.get('sdk');
      sdk.setProjectId('rowanenergy');

      const settingsData = req.body;

      // Prepare JSON fields for database storage
      const dbData = {
        inbound_campaign: settingsData.inbound_campaign ? JSON.stringify(settingsData.inbound_campaign) : null,
        outbound_campaign: settingsData.outbound_campaign ? JSON.stringify(settingsData.outbound_campaign) : null,
        email_integration: settingsData.email_integration ? JSON.stringify(settingsData.email_integration) : null,
        ai_behavior: settingsData.ai_behavior ? JSON.stringify(settingsData.ai_behavior) : null,
        llm_config: settingsData.llm_config ? JSON.stringify(settingsData.llm_config) : null,
        speech_settings: settingsData.speech_settings ? JSON.stringify(settingsData.speech_settings) : null,
        company_hours: settingsData.company_hours ? JSON.stringify(settingsData.company_hours) : null,
        google_calendar_tokens: settingsData.google_calendar_tokens ? JSON.stringify(settingsData.google_calendar_tokens) : null,
        admin_tokens: settingsData.admin_tokens ? JSON.stringify(settingsData.admin_tokens) : null
      };

      // Check if settings row exists
      const existingSettings = await sdk.rawQuery(
        'SELECT id FROM rowanenergy_admin_settings_config WHERE id = 1'
      );

      if (existingSettings.length > 0) {
        // Update existing row
        const updateFields = [];
        const updateValues = [];

        Object.entries(dbData).forEach(([key, value]) => {
          if (value !== null) {
            updateFields.push(`${key} = ?`);
            updateValues.push(value);
          }
        });

        if (updateFields.length > 0) {
          updateValues.push(1); // WHERE id = 1
          await sdk.rawQuery(
            `UPDATE rowanenergy_admin_settings_config SET ${updateFields.join(', ')}, updated_at = CURRENT_TIMESTAMP WHERE id = ?`,
            updateValues
          );
        }
      } else {
        // Insert new row
        const insertFields = ['id'];
        const insertValues = [1];
        const placeholders = ['?'];

        Object.entries(dbData).forEach(([key, value]) => {
          if (value !== null) {
            insertFields.push(key);
            insertValues.push(value);
            placeholders.push('?');
          }
        });

        insertFields.push('created_at', 'updated_at');
        insertValues.push('CURRENT_TIMESTAMP', 'CURRENT_TIMESTAMP');
        placeholders.push('CURRENT_TIMESTAMP', 'CURRENT_TIMESTAMP');

        await sdk.rawQuery(
          `INSERT INTO rowanenergy_admin_settings_config (${insertFields.join(', ')}) VALUES (${placeholders.join(', ')})`,
          insertValues
        );
      }

      res.status(200).json({
        error: false,
        message: 'Settings updated successfully',
        updated_sections: Object.keys(settingsData)
      });
    } catch (error) {
      console.error('Settings update error:', error);
      res.status(500).json({ error: true, message: error.message });
    }
  });

  // Campaign management
  app.post('/v1/api/rowanenergy/super_admin/campaign/:action', AdminMiddleware(), async (req, res) => {
    try {
      const { action } = req.params;
      const { campaign_type = 'outbound' } = req.body;
      const sdk = app.get('sdk');
      sdk.setProjectId('rowanenergy');
      
      if (action === 'start') {
        // Start campaign
        res.status(200).json({ 
          error: false, 
          message: `${campaign_type} campaign started successfully` 
        });
      } else if (action === 'pause') {
        // Pause campaign
        res.status(200).json({ 
          error: false, 
          message: `${campaign_type} campaign paused successfully` 
        });
      } else {
        res.status(400).json({ error: true, message: 'Invalid action' });
      }
    } catch (error) { 
      console.error('Campaign action error:', error);
      res.status(500).json({ error: true, message: error.message }); 
    }
  });

  // Upload contact list for outbound campaigns
  app.post('/v1/api/rowanenergy/super_admin/upload-contacts', AdminMiddleware(), async (req, res) => {
    try {
      const { contacts } = req.body; // Array of contact objects
      const sdk = app.get('sdk');
      sdk.setProjectId('rowanenergy');
      
      let successCount = 0;
      let errorCount = 0;
      
      for (const contact of contacts) {
        try {
          await sdk.create('lead', {
            ...contact,
            source: 'csv_upload',
            status: 'new',
            created_at: sqlDateTimeFormat(new Date()),
            uploaded_by: req.user_id
          });
          successCount++;
        } catch (error) {
          console.error('Contact upload error:', error);
          errorCount++;
        }
      }
      
      res.status(200).json({ 
        error: false, 
        message: `Uploaded ${successCount} contacts successfully. ${errorCount} failed.`,
        success_count: successCount,
        error_count: errorCount
      });
    } catch (error) { 
      console.error('Bulk contact upload error:', error);
      res.status(500).json({ error: true, message: error.message }); 
    }
  });

  // Get available booking slots based on company hours
  app.get('/v1/api/rowanenergy/public/availability', async (req, res) => {
    try {
      const sdk = app.get('sdk');
      sdk.setProjectId('rowanenergy');

      // Get company hours from optimized settings table
      const settingsResult = await sdk.rawQuery(
        'SELECT company_hours FROM rowanenergy_admin_settings_config WHERE id = 1'
      );

      // Default company hours if not set
      let companyHours = {
        monday: { enabled: true, timeSlots: [{ start: '09:00', end: '17:00', enabled: true }] },
        tuesday: { enabled: true, timeSlots: [{ start: '09:00', end: '17:00', enabled: true }] },
        wednesday: { enabled: true, timeSlots: [{ start: '09:00', end: '17:00', enabled: true }] },
        thursday: { enabled: true, timeSlots: [{ start: '09:00', end: '17:00', enabled: true }] },
        friday: { enabled: true, timeSlots: [{ start: '09:00', end: '17:00', enabled: true }] },
        saturday: { enabled: false, timeSlots: [{ start: '09:00', end: '17:00', enabled: false }] },
        sunday: { enabled: false, timeSlots: [{ start: '09:00', end: '17:00', enabled: false }] }
      };

      // Parse company hours from database
      if (settingsResult.length > 0 && settingsResult[0].company_hours) {
        try {
          companyHours = JSON.parse(settingsResult[0].company_hours);
        } catch (parseError) {
          console.log('Error parsing company hours, using defaults');
        }
      }
      
      // Get existing bookings for the next 7 days
      const today = new Date();
      const nextWeek = new Date(today.getTime() + 7 * 24 * 60 * 60 * 1000);
      
      const existingBookings = await sdk.rawQuery(
        'SELECT DATE(scheduled_time) as booking_date, TIME(scheduled_time) as booking_time FROM rowanenergy_job_survey WHERE scheduled_time BETWEEN ? AND ? AND status != "cancelled"',
        [today.toISOString().slice(0, 10), nextWeek.toISOString().slice(0, 10)]
      );
      
      // Create availability array for next 7 days
      const availability = [];
      const dayNames = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];
      
      for (let i = 1; i <= 7; i++) {
        const date = new Date(today.getTime() + i * 24 * 60 * 60 * 1000);
        const dayName = dayNames[date.getDay()];
        const dateString = date.toISOString().slice(0, 10);
        const daySchedule = companyHours[dayName];
        
        if (daySchedule && daySchedule.enabled) {
          const daySlots = [];
          
          // Generate time slots for each enabled time period
          daySchedule.timeSlots.forEach(timeSlot => {
            if (timeSlot.enabled) {
              const startTime = timeSlot.start;
              const endTime = timeSlot.end;
              
              // Generate 1-hour slots within the time range
              const start = new Date(`2000-01-01T${startTime}:00`);
              const end = new Date(`2000-01-01T${endTime}:00`);
              
              while (start < end) {
                const slotTime = start.toTimeString().slice(0, 5);
                
                // Check if this slot is already booked
                const isBooked = existingBookings.some(booking => 
                  booking.booking_date === dateString && 
                  booking.booking_time === slotTime + ':00'
                );
                
                if (!isBooked) {
                  daySlots.push({
                    time: slotTime,
                    available: true,
                    datetime: `${dateString} ${slotTime}:00`
                  });
                }
                
                // Move to next hour
                start.setHours(start.getHours() + 1);
              }
            }
          });
          
          availability.push({
            date: dateString,
            day: dayName,
            dayLabel: date.toLocaleDateString('en-US', { weekday: 'long' }),
            slots: daySlots
          });
        } else {
          availability.push({
            date: dateString,
            day: dayName,
            dayLabel: date.toLocaleDateString('en-US', { weekday: 'long' }),
            slots: []
          });
        }
      }
      
      res.status(200).json({
        error: false,
        availability: availability,
        total_slots: availability.reduce((total, day) => total + day.slots.length, 0)
      });
    } catch (error) {
      console.error('Availability fetch error:', error);
      res.status(500).json({ error: true, message: error.message });
    }
  });

  // Google Calendar Auth - Generate auth URL
  app.get('/v1/api/rowanenergy/super_admin/google/calendar/auth', AdminMiddleware(), async (req, res) => {
    try {
      const { google } = require('googleapis');
      let config = {};
      try {
        config = require('./utils/config');
      } catch (error) {
        console.error('Error loading config:', error);
      }
      
      const credentials = {
        client_id: config.google.client_id,
        client_secret: config.google.client_secret,
        redirect_uris: [config.google.redirect_url]
      };
      
      const oauth2Client = new google.auth.OAuth2(
        credentials.client_id,
        credentials.client_secret,
        credentials.redirect_uris[0]
      );
      
      const SCOPES = [
        'https://www.googleapis.com/auth/calendar',
        'https://www.googleapis.com/auth/gmail.readonly'
      ];
      
      const authUrl = oauth2Client.generateAuthUrl({
        access_type: 'offline',
        scope: SCOPES,
        state: req.user_id,
        prompt: 'consent',
      });

      return res.status(200).json({
        error: false,
        auth_url: authUrl
      });
    } catch (error) {
      console.error('Google auth URL generation error:', error);
      res.status(500).json({ error: true, message: error.message });
    }
  });

  // Google Calendar Auth - Handle callback
  app.get('/v1/api/rowanenergy/super_admin/google/calendar/callback', async (req, res) => {
    try {
      const { code, state } = req.query;
      const { google } = require('googleapis');
      let config = {};
      try {
        config = require('./utils/config');
      } catch (error) {
        console.error('Error loading config:', error);
      }
      if (!code) {
        return res.status(400).json({ error: true, message: 'Authorization code not provided' });
      }
      
      const credentials = {
        client_id: config.google.client_id,
        client_secret: config.google.client_secret,
        redirect_uris: [config.google.redirect_url]
      };
      
      const oauth2Client = new google.auth.OAuth2(
        credentials.client_id,
        credentials.client_secret,
        credentials.redirect_uris[0]
      );
      
      // Exchange authorization code for tokens
      const { tokens } = await oauth2Client.getToken(code);
      oauth2Client.setCredentials(tokens);
      
      // Store tokens in database (in production, encrypt these)
      const sdk = app.get('sdk');
      sdk.setProjectId('rowanenergy');
      
      // Save Google Calendar tokens to optimized settings table
      const tokenData = JSON.stringify(tokens);

      // Check if settings row exists
      const existingSettings = await sdk.rawQuery(
        'SELECT id FROM rowanenergy_admin_settings_config WHERE id = 1'
      );

      if (existingSettings.length > 0) {
        // Update existing row
        await sdk.rawQuery(
          'UPDATE rowanenergy_admin_settings_config SET google_calendar_tokens = ?, updated_at = CURRENT_TIMESTAMP WHERE id = 1',
          [tokenData]
        );
      } else {
        // Insert new row
        await sdk.rawQuery(
          'INSERT INTO rowanenergy_admin_settings_config (id, google_calendar_tokens, created_at, updated_at) VALUES (1, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)',
          [tokenData]
        );
      }
      
      // Redirect back to settings page
      res.redirect('/admin/settings?tab=integrations&google_auth=success');
    } catch (error) {
      console.error('Google auth callback error:', error);
      res.redirect('/admin/settings?tab=integrations&google_auth=error');
    }
  });

  // Voice Call Webhook - Initiate AI call
  app.post('/v1/api/rowanenergy/public/voice/call', async (req, res) => {
    try {
      const { phone_number, user_id, context = 'booking_inquiry' } = req.body;
      const sdk = app.get('sdk');
      sdk.setProjectId('rowanenergy');
      if (!phone_number) {
        return res.status(400).json({ error: true, message: 'Phone number is required' });
      }
      
      // Get API key from settings
      const settingsResult = await sdk.rawQuery(
        'SELECT admin_tokens,company_hours FROM rowanenergy_admin_settings_config WHERE id = 1'
      );

      let apiKey = null;
      if (settingsResult.length > 0 && settingsResult[0].admin_tokens) {
        try {
          const adminTokens = JSON.parse(settingsResult[0].admin_tokens);
          apiKey = adminTokens.voice_api_key;
        } catch (error) {
          console.error('Error parsing admin tokens:', error);
        }
      }

      if (!apiKey) {
        return res.status(400).json({ error: true, message: 'Voice API key not configured' });
      }
      let available_slots = ''
      const timezone = 'Europe/London';


      if (settingsResult.length > 0 && settingsResult[0].company_hours) {
        try {
          const companyHours = JSON.parse(settingsResult[0].company_hours);
          // generate slots for next 2 weeks

          let today = moment().tz(timezone).format('dddd, MMMM D, YYYY');
          const startDate = moment().tz(timezone);
          const endDate = moment().tz(timezone).add(2, 'weeks');
          let current_time = moment().tz(timezone).format('h:mm A');

          const availableSlots = generateAvailableSlots(
            [],
            startDate.toISOString(),
            endDate.toISOString(),
            companyHours,
            60, // 1-hour slots,
            timezone
          );
          
          const formattedSlots = availableSlots.map((slot, index) => {
            const slotDate = moment(slot.start).tz(timezone);
            return `Slot ${index+1}: ${slotDate.format('YYYY-MM-DD')} at ${slotDate.format('h:mm A')} (${slotDate.format('dddd')})`;
          }).join("\n");
          
          // Create availability string with instructions
          available_slots = formattedSlots;
          if (formattedSlots) {
            available_slots += "\nWhen giving the slots to the user, make sure you give them in an easy way to understand. Do not give them in a format like this: 'Slot 1: YYYY-MM-DDT10:00:00.000Z' instead give them in a format like this: 'this coming day at x AM or next thursday at x AM' or 'on the 20th of this month at x AM' or 'on the 20th of next month at x AM'";
            available_slots += ` REMEMBER TODAY IS ${today} AND THE CURRENT TIME IS ${current_time}. MAKE SURE TO USE THIS INFORMATION WHEN PROVIDING AVAILABILITY. TIMEZONE: ${timezone}`;
          } else {
            available_slots = "No available slots found for the next month.";
          }
        } catch (error) {
          console.error('Error parsing company hours:', error);
        }
      }

      console.log('Available slots:', available_slots);

      // Call external voice API with API key
      const axios = require('axios');
      const voiceApiResponse = await axios.post('https://voiceapi.automateintel.ai/v3/api/custom/voiceoutreach/user/webhook_call', {
        to_number: phone_number,
        from_number: 9,
        language: "English",
        user_id: user_id || "1",
        available_slots: available_slots,
        webhook_url: "https://baas.mytechpassport.com/v1/api/rowanenergy/public/webhook/job-survey-call",
        details: {
          customer_email: "customer email",
          customer_name: "customer name",
          customer_phone: "customer phone",
          site_name: "site name",
          location: "location",
          asset_type: "asset type",
          system_size: "system size",
          preferred_date: "preferred date",
          preferred_time: "preferred time",
          region: "region",
          latitude: "latitude",
          longitude: "longitude",
          duration_minutes: "duration minutes",
          is_flexible: "is flexible",
          contact_name: "contact name",
          contact_phone: "contact phone",
          access_instructions: "access instructions",
          survey_purpose: "survey purpose",
          additional_notes: "additional notes",
        },
        from_display_name: "Hover Lens",
        assistant_id: 214
      }, {
        headers: {
          'x-api-key': apiKey,
          'Content-Type': 'application/json'
        }
      });
      
      if (voiceApiResponse.data) {
        // Log the call in our database for tracking
        const sdk = app.get('sdk');
        sdk.setProjectId('rowanenergy');
        
        try {
          await sdk.callRestAPI({
            phone_number,
            user_id: user_id || null,
            context,
            call_status: 'initiated',
            external_call_id: voiceApiResponse.data.call_id || null,
            created_at: sqlDateTimeFormat(new Date())
          }, 'POST', 'rowanenergy_voice_call_log');
        } catch (logError) {
          console.log('Failed to log call, but call initiated successfully');
        }
        
        res.status(200).json({
          error: false,
          message: 'Voice call initiated successfully',
          call_id: voiceApiResponse.data.call_id || 'unknown',
          phone_number: phone_number
        });
      } else {
        res.status(500).json({
          error: true,
          message: 'Failed to initiate voice call'
        });
      }
    } catch (error) {
      console.error('Voice call error:', error);
      res.status(500).json({ 
        error: true, 
        message: 'Failed to initiate voice call',
        details: error.message 
      });
    }
  });

  // Test AI voice flow
  app.post('/v1/api/rowanenergy/super_admin/test-voice', AdminMiddleware(), async (req, res) => {
    try {
      const { phone_number, test_scenario = 'booking_inquiry' } = req.body;
      
      // In production, this would trigger a test call
      // For now, just simulate the response
      res.status(200).json({ 
        error: false, 
        message: `Test call initiated to ${phone_number}`,
        test_call_id: Math.random().toString(36).substr(2, 9),
        scenario: test_scenario,
        estimated_duration: '2-3 minutes'
      });
    } catch (error) { 
      console.error('Test voice flow error:', error);
      res.status(500).json({ error: true, message: error.message }); 
    }
  });

  // CUSTOMER REPORTS API
  app.get('/v1/api/rowanenergy/customer/reports', TokenMiddleware(), async (req, res) => {
    try {
      const sdk = app.get('sdk');
      sdk.setProjectId('rowanenergy');
      
      const userId = req.user_id;
      
      // Get customer's completed jobs and their reports
      const reportsQuery = `
        SELECT
          r.id as report_id,
          r.customer_summary,
          r.internal_notes,
          r.thermal_images,
          r.rgb_photos,
          r.video_footage,
          r.flight_logs,
          r.uploaded_at as report_date,
          j.id as job_id,
          j.site_name,
          j.location,
          j.scheduled_time,
          j.status,
          j.asset_type,
          CONCAT(p.first_name, ' ', p.last_name) as engineer_name,
          p.photo as engineer_photo
        FROM rowanenergy_report r
        INNER JOIN rowanenergy_job_survey j ON r.job_id = j.id
        LEFT JOIN rowanenergy_preference p ON j.engineer_id = p.user_id
        WHERE j.customer_id = ?
        ORDER BY r.uploaded_at DESC
      `;
      
      const reports = await sdk.rawQuery(reportsQuery, [userId]);
      
      res.status(200).json({
        error: false,
        list: reports.map(report => ({
          id: report.report_id,
          title: `${report.site_name} Survey Report`,
          name: `${report.site_name} Survey Report`,
          site_name: report.site_name,
          location: report.location,
          asset_type: report.asset_type,
          date: report.scheduled_time ? new Date(report.scheduled_time).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
          }) : 'Date not available',
          survey_date: report.scheduled_time,
          report_date: report.report_date,
          engineer_name: report.engineer_name,
          engineer_photo: report.engineer_photo,
          customer_summary: report.customer_summary,
          thermal_images: JSON.parse(report.thermal_images || '[]'),
          rgb_photos: JSON.parse(report.rgb_photos || '[]'),
          video_footage: JSON.parse(report.video_footage || '[]'),
          flight_logs: JSON.parse(report.flight_logs || '[]'),
          job_id: report.job_id,
          status: report.status,
          type: 'Survey Report',
          size: `${(JSON.parse(report.thermal_images || '[]').length +
                   JSON.parse(report.rgb_photos || '[]').length +
                   JSON.parse(report.video_footage || '[]').length +
                   JSON.parse(report.flight_logs || '[]').length)} files`
        }))
      });
    } catch (error) {
      console.error('Customer reports error:', error);
      res.status(500).json({ error: true, message: error.message });
    }
  });

  // CUSTOMERS DIRECTORY
  app.get('/v1/api/rowanenergy/super_admin/customers', AdminMiddleware(), async (req, res) => {
    try {
      const sdk = app.get('sdk');
      sdk.setProjectId('rowanenergy');
      
      const { search, subscription_status, region, page = 1, limit = 20 } = req.query;
      const offset = (parseInt(page) - 1) * parseInt(limit);
      
      // Build where conditions for customers (users with customer role)
      let whereConditions = ["u.role_id = 'customer'"];
      let queryParams = [];
      
      if (search) {
        whereConditions.push('(p.first_name LIKE ? OR p.last_name LIKE ? OR u.email LIKE ? OR p.phone LIKE ?)');
        const searchPattern = `%${search}%`;
        queryParams.push(searchPattern, searchPattern, searchPattern, searchPattern);
      }
      
      if (region && region !== 'all') {
        whereConditions.push('p.region LIKE ?');
        queryParams.push(`%${region}%`);
      }
      
      const whereClause = whereConditions.join(' AND ');
      
      // Get total count
      const countQuery = `SELECT COUNT(*) as total FROM rowanenergy_user u LEFT JOIN rowanenergy_preference p ON u.id = p.user_id WHERE ${whereClause}`;
      const countResult = await sdk.rawQuery(countQuery, queryParams);
      const total = countResult[0]?.total || 0;
      
      // Get customers with billing and job information
      const customersQuery = `
        SELECT 
          u.id,
          u.email,
          u.status,
          u.created_at as registration_date,
          p.first_name,
          p.last_name,
          p.phone,
          p.photo,
          COUNT(j.id) as total_jobs,
          COUNT(CASE WHEN j.status = 'completed' THEN 1 END) as completed_jobs,
          MAX(j.created_at) as last_booking_date,
          u.data
        FROM rowanenergy_user u
        LEFT JOIN rowanenergy_preference p ON u.id = p.user_id
        LEFT JOIN rowanenergy_job_survey j ON u.id = j.customer_id
        WHERE ${whereClause}
        GROUP BY u.id, u.email, u.status, u.created_at, p.first_name, p.last_name, p.phone, p.photo, u.data
        ORDER BY u.created_at DESC
        LIMIT ? OFFSET ?
      `;
      const customers = await sdk.rawQuery(customersQuery, [...queryParams, parseInt(limit), offset]);
      
      // Enhance customers with billing information
      const enhancedCustomers = customers.map(customer => {
        let userData = {};
        let billingInfo = {
          subscription_status: 'free',
          current_plan: 'Free Plan',
          stripe_customer_id: null
        };
        
        try {
          userData = JSON.parse(customer.data || '{}');
          if (userData.stripe_customer_id) {
            billingInfo.stripe_customer_id = userData.stripe_customer_id;
            billingInfo.subscription_status = 'subscribed'; // Would need to check actual Stripe status
            billingInfo.current_plan = 'Premium Plan'; // Would need to fetch from Stripe
          }
        } catch (e) {
          console.error('Error parsing customer data:', e);
        }
        
        return {
          id: customer.id,
          full_name: `${customer.first_name || ''} ${customer.last_name || ''}`.trim() || 'Unknown Customer',
          email: customer.email,
          phone: customer.phone || 'Not provided',
          photo: customer.photo,
          status: customer.status === 1 ? 'Active' : 'Inactive',
          registration_date: new Date(customer.registration_date).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short', 
            day: 'numeric'
          }),
          total_jobs: customer.total_jobs || 0,
          completed_jobs: customer.completed_jobs || 0,
          last_booking: customer.last_booking_date ? 
            new Date(customer.last_booking_date).toLocaleDateString('en-US', {
              year: 'numeric',
              month: 'short',
              day: 'numeric'
            }) : 'Never',
          billing: billingInfo,
          region: userData.region || 'Not specified'
        };
      });
      
      const totalPages = Math.ceil(total / parseInt(limit));
      
      res.status(200).json({
        error: false,
        list: enhancedCustomers,
        pagination: {
          total,
          totalPages,
          currentPage: parseInt(page),
          limit: parseInt(limit)
        }
      });
    } catch (error) {
      console.error('Customers directory error:', error);
      res.status(500).json({ error: true, message: error.message });
    }
  });

  // Update customer status
  app.put('/v1/api/rowanenergy/super_admin/customers/:id/status', AdminMiddleware(), async (req, res) => {
    try {
      const { id } = req.params;
      const { status } = req.body;
      const sdk = app.get('sdk');
      sdk.setProjectId('rowanenergy');
      
      await sdk.updateById('user', id, {
        status: status === 'active' ? 1 : 0,
        updated_at: sqlDateTimeFormat(new Date())
      });
      
      res.status(200).json({ error: false, message: 'Customer status updated successfully' });
    } catch (error) {
      console.error('Customer status update error:', error);
      res.status(500).json({ error: true, message: error.message });
    }
  });

  // PLANS MANAGEMENT
  const StripeService = require('../../baas/services/StripeService');
  const stripeService = new StripeService();

  app.get('/v1/api/rowanenergy/super_admin/plans', AdminMiddleware(), async (req, res) => {
    try {
      const sdk = app.get('sdk');
      sdk.setProjectId('rowanenergy');
      let plans = [];
      
      try {
        // First try to get plans from Stripe API
        const plansResult = await stripeService.retrieveStripePrices({
          active: true,
          expand: ['data.product']
        });

        if (!plansResult.error && plansResult.data) {
          // Filter and format plans for Hover Lens
          plans = plansResult.data
            .filter(price => price.product?.metadata?.platform === 'rowanenergy')
            .map(price => ({
              id: price.id,
              product_id: price.product.id,
              name: price.nickname || price.product.name,
              description: price.product.description,
              amount: price.unit_amount / 100,
              currency: price.currency,
              interval: price.recurring?.interval,
              interval_count: price.recurring?.interval_count,
              active: price.active,
              features: price.product.metadata?.features ? JSON.parse(price.product.metadata.features) : [],
              created_at: new Date(price.created * 1000).toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'short',
                day: 'numeric'
              }),
              customers_count: 0 // Would need to query subscriptions to get actual count
            }));
        }
      } catch (stripeError) {
        console.error("Stripe API error, falling back to database:", stripeError.message);
      }

      // If no plans from Stripe, try to get from database
      if (plans.length === 0) {
        try {
          const dbPricesQuery = `
            SELECT 
              sp.stripe_price_id as id,
              sp.stripe_product_id as product_id,
              sp.nickname as name,
              spr.description,
              sp.unit_amount,
              sp.currency,
              sp.recurring_interval as recurring_interval,
              sp.recurring_interval_count as interval_count,
              sp.active,
              spr.metadata,
              sp.created_at
            FROM rowanenergy_stripe_prices sp
            JOIN rowanenergy_stripe_products spr ON sp.stripe_product_id = spr.stripe_product_id
            ORDER BY sp.unit_amount ASC
          `;
          
          const dbPlans = await sdk.rawQuery(dbPricesQuery, []);
          
          plans = dbPlans.map(plan => ({
            id: plan.id,
            product_id: plan.product_id,
            name: plan.name,
            description: plan.description,
            amount: plan.unit_amount / 100,
            currency: plan.currency,
            interval: plan.recurring_interval,
            interval_count: plan.interval_count,
            active: plan.active,
            features: plan.metadata ? JSON.parse(plan.metadata).features || [] : [],
            created_at: new Date(plan.created_at).toLocaleDateString('en-US', {
              year: 'numeric',
              month: 'short',
              day: 'numeric'
            }),
            customers_count: 0
          }));
        } catch (dbError) {
          console.error("Database query error:", dbError.message);
        }
      }

      // Add free plan
      const freePlan = {
        id: 'free',
        product_id: 'free',
        name: 'Free Plan',
        description: 'Basic features for getting started',
        amount: 0,
        currency: 'usd',
        interval: 'month',
        interval_count: 1,
        active: true,
        features: [
          'Up to 2 survey bookings per month',
          'Basic reporting',
          'Email support'
        ],
        created_at: 'System Plan',
        customers_count: 0 // Would need to count customers without subscriptions
      };

      res.status(200).json({
        error: false,
        list: [freePlan, ...plans],
        total: plans.length + 1
      });
    } catch (error) {
      console.error('Plans management error:', error);
      res.status(500).json({ error: true, message: error.message });
    }
  });

  // Create new plan
  app.post('/v1/api/rowanenergy/super_admin/plans', AdminMiddleware(), async (req, res) => {
    try {
      const { name, description, amount, currency = 'usd', interval = 'month', features = [] } = req.body;
      
      if (!name || !description || amount === undefined) {
        return res.status(400).json({
          error: true,
          message: 'Name, description, and amount are required'
        });
      }

      // Step 1: Create Stripe product
      const product = await stripeService.createStripeProduct({
        name: name,
        description: description,
        metadata: {
          platform: 'rowanenergy',
          plan_type: name.toLowerCase().replace(' plan', ''),
          features: JSON.stringify(features)
        }
      });

      if (product.error) {
        return res.status(400).json({
          error: true,
          message: `Failed to create product: ${product.message}`
        });
      }

      // Step 2: Create Stripe price
      const price = await stripeService.createStripeRecurringPrice({
        productId: product.id,
        name: `${name} - ${interval.charAt(0).toUpperCase() + interval.slice(1)}`,
        amount: amount, // StripeService converts to cents internally
        currency: currency,
        interval: interval,
        interval_count: 1,
        trial_days: 0,
        metadata: {
          platform: 'rowanenergy',
          plan_type: name.toLowerCase().replace(' plan', '')
        }
      });

      if (price.error) {
        // Clean up: archive the created product
        await stripeService.updateStripeProduct(product.id, { active: false });
        return res.status(400).json({
          error: true,
          message: `Failed to create price: ${price.message}`
        });
      }

      // Step 3: Store in database (optional, for backup/caching)
      const sdk = app.get('sdk');
      sdk.setProjectId('rowanenergy');
      
      try {
        // Insert product
        await sdk.create('stripe_product', {
          stripe_id: product.id,
          name: product.name,
          object: JSON.stringify(product),
          status: 1,
          created_at: sqlDateTimeFormat(new Date()),
          updated_at: sqlDateTimeFormat(new Date())
        });

        // Insert price
        await sdk.create('stripe_price', {
          stripe_id: price.id,
          product_id: product.id,
          object: JSON.stringify(price),
          amount: price.unit_amount,
          type: 'recurring',
          status: 1,
          created_at: sqlDateTimeFormat(new Date()),
          updated_at: sqlDateTimeFormat(new Date())
        });
      } catch (dbError) {
        console.error('Database insert error (non-critical):', dbError);
        // Continue even if DB insert fails, as Stripe is source of truth
      }

      res.status(200).json({
        error: false,
        message: 'Plan created successfully',
        plan: {
          id: price.id,
          product_id: product.id,
          name: name,
          description: description,
          amount: amount,
          currency: currency,
          interval: interval,
          features: features,
          active: true
        }
      });
    } catch (error) {
      console.error('Create plan error:', error);
      res.status(500).json({ error: true, message: error.message });
    }
  });

  // Update plan status
  app.put('/v1/api/rowanenergy/super_admin/plans/:id/status', AdminMiddleware(), async (req, res) => {
    try {
      const { id } = req.params;
      const { active } = req.body;
      
      if (id === 'free') {
        return res.status(400).json({
          error: true,
          message: 'Cannot modify free plan status'
        });
      }

      // Update Stripe price status
      const result = await stripeService.updateStripePrice(id, { active: active });
      
      if (result.error) {
        return res.status(400).json({
          error: true,
          message: `Failed to update plan: ${result.message}`
        });
      }

      res.status(200).json({
        error: false,
        message: `Plan ${active ? 'activated' : 'deactivated'} successfully`
      });
    } catch (error) {
      console.error('Plan status update error:', error);
      res.status(500).json({ error: true, message: error.message });
    }
  });

  // ENGINEER
  app.get('/v1/api/rowanenergy/drone-engineer/dashboard', [TokenMiddleware()], async (req, res) => {
    try {
      const sdk = app.get('sdk');
      sdk.setProjectId('rowanenergy');
      const today = new Date();
      const todayStr = today.toISOString().slice(0,10);
      
      // Use raw queries to avoid unsupported operators
      const jobsTodayQuery = `
        SELECT * FROM rowanenergy_job_survey 
        WHERE engineer_id = ? AND DATE(scheduled_time) = ?
      `;
      const jobsToday = await sdk.rawQuery(jobsTodayQuery, [req.user_id, todayStr]);
      
      // Get overdue assessments - jobs that are past due or today but missing critical data
      const overdueAssessmentsQuery = `
        SELECT j.*, c.status as checklist_status, sn.id as survey_notes_id, r.id as report_id
        FROM rowanenergy_job_survey j
        LEFT JOIN rowanenergy_checklist c ON j.id = c.job_id
        LEFT JOIN rowanenergy_job_survey_notes sn ON j.id = sn.job_id
        LEFT JOIN rowanenergy_report r ON j.id = r.job_id
        WHERE j.engineer_id = ?
        AND j.status != 'survey_completed'
        AND (
          (DATE(j.scheduled_time) <= ? AND j.status != 'completed') OR
          (j.status = 'scheduled' AND c.status IS NULL) OR
          (j.status = 'scheduled' AND c.status != 'completed' AND DATE(j.scheduled_time) <= DATE(NOW() + INTERVAL 1 DAY)) OR
          (j.status = 'in_progress' AND sn.id IS NULL) OR
          (j.status = 'completed' AND r.id IS NULL)
        )
        ORDER BY j.scheduled_time ASC
      `;
      const overdueAssessments = await sdk.rawQuery(overdueAssessmentsQuery, [req.user_id, todayStr]);
      
      // Analyze overdue assessments to determine action required
      let actionRequired = null;
      if (overdueAssessments.length > 0) {
        const overdueJob = overdueAssessments[0];
        const scheduledDate = new Date(overdueJob.scheduled_time);
        const isOverdue = scheduledDate <= today;
        const isTomorrow = scheduledDate <= new Date(today.getTime() + 24 * 60 * 60 * 1000);
        
        let actionMessage = '';
        if (!overdueJob.checklist_status && (isOverdue || isTomorrow)) {
          actionMessage = `Pre-survey checklist for Job #${overdueJob.id} (${overdueJob.site_name}) is missing and survey is ${isOverdue ? 'overdue' : 'tomorrow'}`;
        } else if (overdueJob.checklist_status && overdueJob.checklist_status !== 'completed' && (isOverdue || isTomorrow)) {
          actionMessage = `Pre-survey checklist for Job #${overdueJob.id} (${overdueJob.site_name}) is incomplete and survey is ${isOverdue ? 'overdue' : 'tomorrow'}`;
        } else if (overdueJob.status === 'in_progress' && !overdueJob.survey_notes_id) {
          actionMessage = `Survey results for Job #${overdueJob.id} (${overdueJob.site_name}) are missing`;
        } else if (overdueJob.status === 'completed' && !overdueJob.report_id) {
          actionMessage = `Survey report for Job #${overdueJob.id} (${overdueJob.site_name}) needs to be uploaded`;
        } else if (isOverdue && overdueJob.status !== 'completed') {
          actionMessage = `Job #${overdueJob.id} (${overdueJob.site_name}) is overdue and needs completion`;
        }
        
        if (actionMessage) {
          actionRequired = {
            message: actionMessage,
            job_id: overdueJob.id,
            priority: isOverdue ? 'high' : 'medium',
            type: overdueJob.checklist_status === null || overdueJob.checklist_status !== 'completed' ? 'checklist' : 
                  overdueJob.status === 'in_progress' ? 'survey_results' :
                  overdueJob.status === 'completed' ? 'report_upload' : 'general'
          };
        }
      }
      
      const pendingChecklistsQuery = `
        SELECT COUNT(*) as count FROM rowanenergy_checklist 
        WHERE status != 'completed'
      `;
      const pendingResult = await sdk.rawQuery(pendingChecklistsQuery, []);
      const pendingChecklists = pendingResult[0]?.count || 0;
      
      const upcomingQuery = `
        SELECT * FROM rowanenergy_job_survey 
        WHERE engineer_id = ? AND DATE(scheduled_time) > ?
        ORDER BY scheduled_time ASC 
        LIMIT 10
      `;

      const nextThreeDays = await sdk.rawQuery(upcomingQuery, [req.user_id, todayStr]);
      const engineer = await sdk.findOne('preference', { user_id: req.user_id });
      
      // Get completion statistics
      const statsQuery = `
        SELECT 
          COUNT(*) as total_jobs,
          SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed_jobs,
          SUM(CASE WHEN status = 'scheduled' THEN 1 ELSE 0 END) as scheduled_jobs,
          SUM(CASE WHEN status = 'in_progress' THEN 1 ELSE 0 END) as in_progress_jobs
        FROM rowanenergy_job_survey 
        WHERE engineer_id = ? AND DATE(scheduled_time) >= DATE(NOW() - INTERVAL 30 DAY)
      `;
      const statsResult = await sdk.rawQuery(statsQuery, [req.user_id]);
      const stats = statsResult[0] || { total_jobs: 0, completed_jobs: 0, scheduled_jobs: 0, in_progress_jobs: 0 };
      
      res.status(200).json({ 
        error: false, 
        model: { 
          jobs_today: jobsToday, 
          pending_checklists: pendingChecklists, 
          upcoming: nextThreeDays, 
          user: engineer,
          action_required: actionRequired,
          overdue_assessments: overdueAssessments.length,
          stats: stats
        } 
      });
    } catch (error) { 
      console.error('Dashboard error:', error);
      res.status(500).json({ error: true, message: error.message }); 
    }
  });

  app.get('/v1/api/rowanenergy/drone-engineer/schedule', [TokenMiddleware()], async (req, res) => {
    try {
      const sdk = app.get('sdk');
      sdk.setProjectId('rowanenergy');
      const list = await sdk.find('job_survey', { engineer_id: req.user_id });
      // weather_feasibility placeholder
      res.status(200).json({ error: false, list: list.map(j => ({ ...j, weather_feasibility: 'Good' })) });
    } catch (error) { res.status(500).json({ error: true, message: error.message }); }
  });

  // Complete checklist upsert (for PreSurveyChecklist component)
  app.post('/v1/api/rowanenergy/drone-engineer/checklist', [TokenMiddleware()], async (req, res) => {
    try {
      const jobId = req.body.job_id;
      
      // Validate job ID
      if (!jobId || jobId === 'undefined' || jobId === 'null') {
        return res.status(400).json({ error: true, message: 'Invalid job ID' });
      }

      const sdk = app.get('sdk');
      sdk.setProjectId('rowanenergy');
      
      // Verify job ownership
      const job = await sdk.findOne('job_survey', { id: jobId, engineer_id: req.user_id });
      if (!job) {
        return res.status(403).json({ error: true, message: 'Forbidden' });
      }

      // Merge risk assessment data with mitigation fields
      const riskAssessmentData = {
        ...req.body.risk_assessment,
        emergency_procedures: req.body.emergency_procedures || req.body.risk_assessment?.emergency_procedures || '',
        fallback_plans: req.body.fallback_plans || req.body.risk_assessment?.fallback_plans || '',
        ppe_requirements: req.body.ppe_requirements || req.body.risk_assessment?.ppe_requirements || ''
      };

      const checklistData = {
        job_id: jobId,
        equipment_checklist: JSON.stringify(req.body.equipment_checklist || {}),
        risk_assessment: JSON.stringify(riskAssessmentData),
        risk_mitigation_plan: req.body.risk_mitigation_plan || req.body.emergency_procedures || '',
        required_uploads: JSON.stringify(req.body.required_uploads || []),
        status: req.body.status || 'in_progress',
        last_updated: sqlDateTimeFormat(new Date()),
        updated_at: sqlDateTimeFormat(new Date())
      };

      // Check existing checklist
      const existing = await sdk.findOne('checklist', { job_id: jobId });
      let result;
      
      if (existing) {
        result = await sdk.updateById('checklist', existing.id, checklistData);
      } else {
        checklistData.created_at = sqlDateTimeFormat(new Date());
        result = await sdk.create('checklist', checklistData);
      }

      res.status(200).json({ error: false, model: result });
    } catch (error) { 
      console.error('Checklist upsert error:', error);
      res.status(500).json({ error: true, message: error.message }); 
    }
  });

  // Get checklist for specific job
  app.get('/v1/api/rowanenergy/drone-engineer/checklists/:job_id', [TokenMiddleware()], async (req, res) => {
    try {
      const jobId = req.params.job_id;
      
      // Validate job ID
      if (!jobId || jobId === 'undefined' || jobId === 'null') {
        return res.status(400).json({ error: true, message: 'Invalid job ID' });
      }

      const sdk = app.get('sdk');
      sdk.setProjectId('rowanenergy');
      
      // Verify job ownership and get job details
      JobService.setSDK(sdk);
      const job = await JobService.getJob(jobId);
      if (!job || job.engineer_id !== req.user_id) {
        return res.status(403).json({ error: true, message: 'Forbidden' });
      }
      
      // Get checklist from the unified table
      const checklist = await sdk.findOne('checklist', { job_id: jobId });
      
      // Parse JSON fields if checklist exists
      let parsedEquipmentChecklist = {};
      let parsedRiskAssessment = {};
      let parsedRequiredUploads = [];

      if (checklist) {
        try {
          parsedEquipmentChecklist = checklist.equipment_checklist ? JSON.parse(checklist.equipment_checklist) : {};
        } catch (e) {
          parsedEquipmentChecklist = {};
        }

        try {
          parsedRiskAssessment = checklist.risk_assessment ? JSON.parse(checklist.risk_assessment) : {};
        } catch (e) {
          parsedRiskAssessment = {};
        }

        try {
          parsedRequiredUploads = checklist.required_uploads ? JSON.parse(checklist.required_uploads) : [];
        } catch (e) {
          parsedRequiredUploads = [];
        }
      }

      // Combine job data with checklist data
      const model = {
        // Job information
        job_id: jobId,
        jobNumber: `Job #${jobId}`,
        siteName: job?.site_name || 'Unknown Site',
        location: job?.location || '',
        assignedDate: job?.scheduled_time ? new Date(job.scheduled_time).toLocaleDateString('en-US', { 
          year: 'numeric', month: 'long', day: 'numeric' 
        }) : 'Not scheduled',
        status: job?.status || 'pending',
        
        // Checklist information
        checklistStatus: checklist?.status || 'pending',
        equipment_checklist: parsedEquipmentChecklist,
        risk_assessment: parsedRiskAssessment,
        required_uploads: parsedRequiredUploads,
        risk_mitigation_plan: checklist?.risk_mitigation_plan || '',
        last_updated: checklist?.last_updated
      };

      res.status(200).json({ error: false, model });
    } catch (error) { 
      console.error('Checklist get error:', error);
      res.status(500).json({ error: true, message: error.message }); 
    }
  });

  app.post('/v1/api/rowanenergy/drone-engineer/survey-upload/:id', [TokenMiddleware()], async (req, res) => {
    try {
      const sdk = app.get('sdk');
      sdk.setProjectId('rowanenergy');

      const ReportService = require('./services/reportService');
      ReportService.setSDK(sdk);

      // Create the report with all uploaded files and notes
      const report = await ReportService.createReport(req.params.id, req.body);

      // Update the job status to indicate survey is completed
      await sdk.update('job_survey', { id: req.params.id }, {
        status: 'survey_completed',
        updated_at: new Date()
      });

      res.status(200).json({
        error: false,
        model: report,
        message: 'Survey uploaded successfully and report generated'
      });
    } catch (error) {
      console.error('Survey upload error:', error);
      res.status(500).json({ error: true, message: error.message });
    }
  });

  // REPORTS API ENDPOINTS

  // Get customer reports
  app.get('/v1/api/rowanenergy/customer/reports', [TokenMiddleware()], async (req, res) => {
    try {
      const sdk = app.get('sdk');
      sdk.setProjectId('rowanenergy');

      const user = req.user;
      if (!user) {
        return res.status(401).json({ error: true, message: 'User not authenticated' });
      }

      // Get reports for jobs belonging to this customer
      const reports = await sdk.rawQuery(`
        SELECT r.*, js.site_name, js.location, js.asset_type, js.scheduled_time,
               ep.first_name as engineer_first_name, ep.last_name as engineer_last_name,
               ep.photo as engineer_photo
        FROM rowanenergy_report r
        JOIN rowanenergy_job_survey js ON r.job_id = js.id
        LEFT JOIN rowanenergy_user u ON js.engineer_id = u.id
        LEFT JOIN rowanenergy_preference ep ON js.engineer_id = up.user_id
        WHERE js.customer_id = ?
        ORDER BY r.uploaded_at DESC
      `, [user.id]);

      const formattedReports = reports.map(report => ({
        id: report.id,
        title: `${report.site_name} Survey Report`,
        name: `${report.site_name} Survey Report`,
        job_id: report.job_id,
        site_name: report.site_name,
        location: report.location,
        asset_type: report.asset_type,
        date: report.scheduled_time ? new Date(report.scheduled_time).toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'long',
          day: 'numeric'
        }) : 'Date not available',
        survey_date: report.scheduled_time,
        report_date: report.uploaded_at,
        engineer_name: `${report.engineer_first_name || ''} ${report.engineer_last_name || ''}`.trim(),
        engineer_photo: report.engineer_photo,
        customer_summary: report.customer_summary,
        thermal_images: JSON.parse(report.thermal_images || '[]'),
        rgb_photos: JSON.parse(report.rgb_photos || '[]'),
        video_footage: JSON.parse(report.video_footage || '[]'),
        flight_logs: JSON.parse(report.flight_logs || '[]'),
        uploaded_at: report.uploaded_at,
        scheduled_time: report.scheduled_time,
        status: report.status,
        type: 'Survey Report',
        size: `${(JSON.parse(report.thermal_images || '[]').length +
                 JSON.parse(report.rgb_photos || '[]').length +
                 JSON.parse(report.video_footage || '[]').length +
                 JSON.parse(report.flight_logs || '[]').length)} files`
      }));

      res.status(200).json({ error: false, list: formattedReports });
    } catch (error) {
      console.error('Customer reports fetch error:', error);
      res.status(500).json({ error: true, message: error.message });
    }
  });

  // Get single customer report by ID
  app.get('/v1/api/rowanenergy/customer/reports/:id', [TokenMiddleware()], async (req, res) => {
    try {
      const { id } = req.params;
      const sdk = app.get('sdk');
      sdk.setProjectId('rowanenergy');

      // Get single report with job details
      const reports = await sdk.rawQuery(`
        SELECT r.*, js.site_name, js.location, js.asset_type, js.scheduled_time,
               ep.first_name as engineer_first_name, ep.last_name as engineer_last_name,
               ep.photo as engineer_photo
        FROM rowanenergy_report r
        JOIN rowanenergy_job_survey js ON r.job_id = js.id
        LEFT JOIN rowanenergy_preference ep ON js.engineer_id = ep.user_id
        WHERE r.id = ? AND js.customer_id = ?
      `, [id, req.user_id]);

      if (reports.length === 0) {
        return res.status(404).json({ error: true, message: 'Report not found' });
      }

      const report = reports[0];

      // Format the response
      const formattedReport = {
        id: report.id,
        job_id: report.job_id,
        site_name: report.site_name,
        location: report.location,
        asset_type: report.asset_type,
        engineer_name: report.engineer_first_name && report.engineer_last_name ?
                      `${report.engineer_first_name} ${report.engineer_last_name}` : 'Unassigned',
        engineer_photo: report.engineer_photo,
        customer_summary: report.customer_summary,
        thermal_images: JSON.parse(report.thermal_images || '[]'),
        rgb_photos: JSON.parse(report.rgb_photos || '[]'),
        video_footage: JSON.parse(report.video_footage || '[]'),
        flight_logs: JSON.parse(report.flight_logs || '[]'),
        uploaded_at: report.uploaded_at,
        scheduled_time: report.scheduled_time,
        status: report.status || 'completed'
      };

      res.status(200).json({ error: false, model: formattedReport });
    } catch (error) {
      console.error('Customer report details error:', error);
      res.status(500).json({ error: true, message: error.message });
    }
  });

  // Get admin reports
  app.get('/v1/api/rowanenergy/super_admin/reports', [AdminMiddleware()], async (req, res) => {
    try {
      const sdk = app.get('sdk');
      sdk.setProjectId('rowanenergy');

      // Get all reports with job and customer details
      const reports = await sdk.rawQuery(`
        SELECT r.*, js.site_name, js.location, js.asset_type, js.scheduled_time,
               ep.first_name as engineer_first_name, ep.last_name as engineer_last_name,
               ep.photo as engineer_photo,
               up.first_name as customer_first_name, up.last_name as customer_last_name,
               c.email as customer_email
        FROM rowanenergy_report r
        JOIN rowanenergy_job_survey js ON r.job_id = js.id
        LEFT JOIN rowanenergy_user u ON js.engineer_id = u.id
        LEFT JOIN rowanenergy_user c ON js.customer_id = c.id
        LEFT JOIN rowanenergy_preference ep ON js.engineer_id = ep.user_id
        LEFT JOIN rowanenergy_preference up ON js.customer_id = up.user_id
        ORDER BY r.uploaded_at DESC
      `);

      const formattedReports = reports.map(report => ({
        id: report.id,
        job_id: report.job_id,
        site_name: report.site_name,
        location: report.location,
        asset_type: report.asset_type,
        engineer_name: `${report.engineer_first_name || ''} ${report.engineer_last_name || ''}`.trim(),
        engineer_photo: report.engineer_photo,
        customer_name: `${report.customer_first_name || ''} ${report.customer_last_name || ''}`.trim(),
        customer_email: report.customer_email,
        internal_notes: report.internal_notes,
        customer_summary: report.customer_summary,
        thermal_images: JSON.parse(report.thermal_images || '[]'),
        rgb_photos: JSON.parse(report.rgb_photos || '[]'),
        video_footage: JSON.parse(report.video_footage || '[]'),
        flight_logs: JSON.parse(report.flight_logs || '[]'),
        uploaded_at: report.uploaded_at,
        scheduled_time: report.scheduled_time,
        status: report.status
      }));

      res.status(200).json({ error: false, list: formattedReports });
    } catch (error) {
      console.error('Admin reports fetch error:', error);
      res.status(500).json({ error: true, message: error.message });
    }
  });

  // Get single report details
  app.get('/v1/api/rowanenergy/report/:id', [TokenMiddleware()], async (req, res) => {
    try {
      const sdk = app.get('sdk');
      sdk.setProjectId('rowanenergy');

      const user = req.user;
      const reportId = req.params.id;

      // Get report with job details
      const reportQuery = `
        SELECT r.*, js.site_name, js.location, js.asset_type, js.scheduled_time, js.customer_id,
               ep.first_name as engineer_first_name, ep.last_name as engineer_last_name,
               ep.photo as engineer_photo,
               up.first_name as customer_first_name, up.last_name as customer_last_name
        FROM rowanenergy_report r
        JOIN rowanenergy_job_survey js ON r.job_id = js.id
        LEFT JOIN rowanenergy_user u ON js.engineer_id = u.id
        LEFT JOIN rowanenergy_user c ON js.customer_id = c.id
        LEFT JOIN rowanenergy_preference ep ON js.engineer_id = ep.id
        LEFT JOIN rowanenergy_preference up ON js.customer_id = up.id
        WHERE r.id = ?
      `;

      const reports = await sdk.rawQuery(reportQuery, [reportId]);

      if (reports.length === 0) {
        return res.status(404).json({ error: true, message: 'Report not found' });
      }

      const report = reports[0];

      // Check permissions - customers can only see their own reports
      if (req.role === 'customer' && report.customer_id !== user.id) {
        return res.status(403).json({ error: true, message: 'Access denied' });
      }

      const formattedReport = {
        id: report.id,
        job_id: report.job_id,
        site_name: report.site_name,
        location: report.location,
        asset_type: report.asset_type,
        engineer_name: `${report.engineer_first_name || ''} ${report.engineer_last_name || ''}`.trim(),
        engineer_photo: report.engineer_photo,
        customer_name: `${report.customer_first_name || ''} ${report.customer_last_name || ''}`.trim(),
        customer_summary: report.customer_summary,
        thermal_images: JSON.parse(report.thermal_images || '[]'),
        rgb_photos: JSON.parse(report.rgb_photos || '[]'),
        video_footage: JSON.parse(report.video_footage || '[]'),
        flight_logs: JSON.parse(report.flight_logs || '[]'),
        uploaded_at: report.uploaded_at,
        scheduled_time: report.scheduled_time,
        status: report.status
      };

      // Include internal notes for admin users only
      if (req.role === 'super_admin') {
        formattedReport.internal_notes = report.internal_notes;
      }

      res.status(200).json({ error: false, model: formattedReport });
    } catch (error) {
      console.error('Report details fetch error:', error);
      res.status(500).json({ error: true, message: error.message });
    }
  });

  app.get('/v1/api/rowanenergy/drone-engineer/profile', [TokenMiddleware()], async (req, res) => {
    try {
      const sdk = app.get('sdk');
      sdk.setProjectId('rowanenergy');
      
      // Get user data
      const user = await sdk.findOne('user', { id: req.user_id });
      if (!user) {
        return res.status(404).json({ error: true, message: 'User not found' });
      }
      
      // Get preference data
      const preference = await sdk.findOne('preference', { user_id: req.user_id });
      
      // Get engineer profile data
      EngineerService.setSDK(sdk);
      const engineerProfile = await EngineerService.getProfile(req.user_id);
      
      // Merge user data with preference data
      const userData = JSON.parse(user.data || '{}');
      const profileData = {
        ...user,
        ...userData,
        ...engineerProfile,
        // Preference data takes precedence
        first_name: preference?.first_name || userData.first_name || '',
        last_name: preference?.last_name || userData.last_name || '',
        phone: preference?.phone || userData.phone || '',
        photo: preference?.photo || userData.photo || ''
      };
      
      res.status(200).json({ error: false, model: profileData });
    } catch (error) { 
      res.status(500).json({ error: true, message: error.message }); 
    }
  });

  app.put('/v1/api/rowanenergy/drone-engineer/profile', [TokenMiddleware()], async (req, res) => {
    try {
      const sdk = app.get('sdk');
      sdk.setProjectId('rowanenergy');
      
      // Update user data field
      const user = await sdk.findOne('user', { id: req.user_id });
      const currentData = JSON.parse(user.data || '{}');
      const updatedData = {
        ...currentData,
        first_name: req.body.first_name || currentData.first_name,
        last_name: req.body.last_name || currentData.last_name,
        phone: req.body.phone || currentData.phone,
        photo: req.body.photo || currentData.photo,
        company: req.body.company || currentData.company,
        certifications: req.body.certifications || currentData.certifications,
        experience: req.body.experience || currentData.experience
      };
      
      await sdk.update('user', { id: req.user_id }, { 
        data: JSON.stringify(updatedData), 
        updated_at: sqlDateTimeFormat(new Date()) 
      });
      
      // Update or create preference record
      const preference = await sdk.findOne('preference', { user_id: req.user_id });
      const preferenceData = {
        user_id: req.user_id,
        first_name: req.body.first_name || '',
        last_name: req.body.last_name || '',
        phone: req.body.phone || '',
        photo: req.body.photo || '',
        company: req.body.company || '',
        updated_at: sqlDateTimeFormat(new Date())
      };
      
      if (preference) {
        await sdk.update('preference', { user_id: req.user_id }, preferenceData);
      } else {
        preferenceData.created_at = sqlDateTimeFormat(new Date());
        await sdk.create('preference', preferenceData);
      }
      
      // Update engineer-specific profile data
      EngineerService.setSDK(sdk);
      const engineerData = filterEmptyFields({
        certifications: req.body.certifications,
        experience: req.body.experience,
        availability: req.body.availability,
        regions: req.body.regions,
        workload_limit: req.body.workload_limit
      });
      
      if (Object.keys(engineerData).length > 0) {
        await EngineerService.upsertProfile(req.user_id, engineerData);
      }
      
      res.status(200).json({ error: false, message: 'Profile updated' });
    } catch (error) { 
      res.status(500).json({ error: true, message: error.message }); 
    }
  });

  // Get specific job details with comprehensive progress data
  app.get('/v1/api/rowanenergy/drone-engineer/job/:id', [TokenMiddleware()], async (req, res) => {
    try {
      const jobId = req.params.id;
      
      // Validate job ID
      if (!jobId || jobId === 'undefined' || jobId === 'null') {
        return res.status(400).json({ error: true, message: 'Invalid job ID' });
      }

      const sdk = app.get('sdk');
      sdk.setProjectId('rowanenergy');
      JobService.setSDK(sdk);
      
      const job = await JobService.getJob(jobId);
      if (!job || job.engineer_id !== req.user_id) {
        return res.status(403).json({ error: true, message: 'Forbidden' });
      }

      // Get checklist data from the unified checklist table
      let checklist = await sdk.findOne('checklist', { job_id: jobId });
      
      // Create default checklist if it doesn't exist
      if (!checklist) {
        const defaultChecklist = {
          job_id: jobId,
          equipment_checklist: JSON.stringify({
            batteries_charged: false,
            firmware_updated: false,
            sd_cards_inserted: false,
            compass_calibrated: false,
            controller_linked: false,
            obstacle_sensors: false
          }),
          risk_assessment: JSON.stringify({
            airspace_classification: '',
            terrain_type: '',
            nearby_obstructions: '',
            weather_risk_level: 'low',
            weather_details: '',
            public_safety_assessed: false,
            wildlife_check_complete: false,
            airspace_clearance_obtained: false,
            permits_verified: false,
            emergency_procedures: '',
            fallback_plans: '',
            ppe_requirements: ''
          }),
          risk_mitigation_plan: '',
          required_uploads: JSON.stringify([]),
          status: 'pending',
          created_at: sqlDateTimeFormat(new Date()),
          updated_at: sqlDateTimeFormat(new Date())
        };
        
        try {
          checklist = await sdk.create('checklist', defaultChecklist);
        } catch (e) {
          checklist = defaultChecklist;
        }
      }

      // Parse JSON fields
      let parsedEquipmentChecklist = {};
      let parsedRiskAssessment = {};
      let parsedRequiredUploads = [];

      try {
        parsedEquipmentChecklist = checklist.equipment_checklist ? JSON.parse(checklist.equipment_checklist) : {};
      } catch (e) {
        parsedEquipmentChecklist = {};
      }

      try {
        parsedRiskAssessment = checklist.risk_assessment ? JSON.parse(checklist.risk_assessment) : {};
      } catch (e) {
        parsedRiskAssessment = {};
      }

      try {
        parsedRequiredUploads = checklist.required_uploads ? JSON.parse(checklist.required_uploads) : [];
      } catch (e) {
        parsedRequiredUploads = [];
      }

      // Enhance job data with formatted information
      const jobDetails = {
        ...job,
        // Basic job info
        jobNumber: `Job #${job.id}`,
        siteName: job.site_name,
        location: job.location,
        scheduledTime: job.scheduled_time,
        assignedDate: job.scheduled_time ? new Date(job.scheduled_time).toLocaleDateString('en-US', { 
          year: 'numeric', month: 'long', day: 'numeric' 
        }) : null,
        startTime: job.scheduled_time ? new Date(job.scheduled_time).toLocaleTimeString('en-US', { 
          hour: '2-digit', minute: '2-digit' 
        }) : null,
        endTime: job.scheduled_time && job.duration_minutes ? new Date(
          new Date(job.scheduled_time).getTime() + job.duration_minutes * 60000
        ).toLocaleTimeString('en-US', { 
          hour: '2-digit', minute: '2-digit' 
        }) : null,
        duration: job.duration_minutes ? `${job.duration_minutes} mins` : null,
        region: job.region || job.location?.split(',')[1]?.trim() || 'North England',
        
        // Contact information
        contactName: job.contact_name,
        contactPhone: job.contact_phone,
        contactEmail: job.contact_email,
        
        // System information
        systemSize: job.system_size,
        assetType: job.asset_type,
        surveyPurpose: job.survey_purpose,
        
        // Checklist data
        checklist: parsedEquipmentChecklist,
        riskAssessment: {
          ...parsedRiskAssessment,
          risk_mitigation_plan: checklist.risk_mitigation_plan || ''
        },
        requiredUploads: parsedRequiredUploads,
        checklistStatus: checklist.status || 'pending',
        checklistLastUpdated: checklist.last_updated
      };

      res.status(200).json({ error: false, model: jobDetails });
    } catch (error) { 
      console.error('Job details error:', error);
      res.status(500).json({ error: true, message: error.message }); 
    }
  });

  // Job Progress Management APIs
  
  // Update job progress
  app.put('/v1/api/rowanenergy/drone-engineer/job/:id/progress', [TokenMiddleware()], async (req, res) => {
    try {
      const jobId = req.params.id;
      
      // Validate job ID
      if (!jobId || jobId === 'undefined' || jobId === 'null') {
        return res.status(400).json({ error: true, message: 'Invalid job ID' });
      }

      const sdk = app.get('sdk');
      sdk.setProjectId('rowanenergy');
      
      // Verify job ownership
      const job = await sdk.findOne('job_survey', { id: jobId, engineer_id: req.user_id });
      if (!job) {
        return res.status(403).json({ error: true, message: 'Forbidden' });
      }

      const progressData = {
        job_id: jobId,
        ...req.body,
        updated_at: sqlDateTimeFormat(new Date())
      };

      const existing = await sdk.findOne('job_progress', { job_id: jobId });
      let result;
      
      if (existing) {
        result = await sdk.update('job_progress', progressData, existing.id);
      } else {
        result = await sdk.create('job_progress', { ...progressData, created_at: sqlDateTimeFormat(new Date()) });
      }

      res.status(200).json({ error: false, model: result });
    } catch (error) { 
      console.error('Progress update error:', error);
      res.status(500).json({ error: true, message: error.message }); 
    }
  });

  // Update equipment checklist
  app.put('/v1/api/rowanenergy/drone-engineer/job/:id/checklist', [TokenMiddleware()], async (req, res) => {
    try {
      const jobId = req.params.id;
      
      // Validate job ID
      if (!jobId || jobId === 'undefined' || jobId === 'null') {
        return res.status(400).json({ error: true, message: 'Invalid job ID' });
      }

      const sdk = app.get('sdk');
      sdk.setProjectId('rowanenergy');
      
      // Verify job ownership
      const job = await sdk.findOne('job_survey', { id: jobId, engineer_id: req.user_id });
      if (!job) {
        return res.status(403).json({ error: true, message: 'Forbidden' });
      }

      // Get existing checklist or create default
      let existingChecklist = await sdk.findOne('checklist', { job_id: jobId });
      
      const equipmentChecklist = {
        batteries_charged: req.body.batteries_charged || false,
        firmware_updated: req.body.firmware_updated || false,
        sd_cards_inserted: req.body.sd_cards_inserted || false,
        compass_calibrated: req.body.compass_calibrated || false,
        controller_linked: req.body.controller_linked || false,
        obstacle_sensors: req.body.obstacle_sensors || false
      };

      const checklistData = {
        job_id: jobId,
        equipment_checklist: JSON.stringify(equipmentChecklist),
        updated_at: sqlDateTimeFormat(new Date())
      };

      // Check if all items are completed
      const allCompleted = Object.values(equipmentChecklist).every(Boolean);
      
      if (allCompleted) {
        checklistData.status = 'completed';
        checklistData.last_updated = sqlDateTimeFormat(new Date());
      } else {
        checklistData.status = 'in_progress';
      }

      let result;
      
      if (existingChecklist) {
        // Preserve existing data and only update equipment checklist
        if (existingChecklist.risk_assessment) {
          checklistData.risk_assessment = existingChecklist.risk_assessment;
        }
        if (existingChecklist.risk_mitigation_plan) {
          checklistData.risk_mitigation_plan = existingChecklist.risk_mitigation_plan;
        }
        if (existingChecklist.required_uploads) {
          checklistData.required_uploads = existingChecklist.required_uploads;
        }
        
        result = await sdk.updateById('checklist', existingChecklist.id, checklistData);
      } else {
        // Create new checklist with defaults
        checklistData.risk_assessment = JSON.stringify({
          airspace_classification: '',
          terrain_type: '',
          nearby_obstructions: '',
          weather_risk_level: 'low',
          weather_details: '',
          public_safety_assessed: false,
          wildlife_check_complete: false,
          airspace_clearance_obtained: false,
          permits_verified: false,
          emergency_procedures: '',
          fallback_plans: '',
          ppe_requirements: ''
        });
        checklistData.risk_mitigation_plan = '';
        checklistData.required_uploads = JSON.stringify([]);
        checklistData.created_at = sqlDateTimeFormat(new Date());
        
        result = await sdk.create('checklist', checklistData);
      }

      res.status(200).json({ error: false, model: result });
    } catch (error) { 
      console.error('Checklist update error:', error);
      res.status(500).json({ error: true, message: error.message }); 
    }
  });

  // Update risk assessment
  app.put('/v1/api/rowanenergy/drone-engineer/job/:id/risk-assessment', [TokenMiddleware()], async (req, res) => {
    try {
      const jobId = req.params.id;
      
      // Validate job ID
      if (!jobId || jobId === 'undefined' || jobId === 'null') {
        return res.status(400).json({ error: true, message: 'Invalid job ID' });
      }

      const sdk = app.get('sdk');
      sdk.setProjectId('rowanenergy');
      
      // Verify job ownership
      const job = await sdk.findOne('job_survey', { id: jobId, engineer_id: req.user_id });
      if (!job) {
        return res.status(403).json({ error: true, message: 'Forbidden' });
      }

      // Get existing checklist or create default
      let existingChecklist = await sdk.findOne('checklist', { job_id: jobId });
      
      const riskAssessment = {
        airspace_classification: req.body.airspace_classification || '',
        terrain_type: req.body.terrain_type || '',
        nearby_obstructions: req.body.nearby_obstructions || '',
        weather_risk_level: req.body.weather_risk_level || 'low',
        weather_details: req.body.weather_details || '',
        public_safety_assessed: req.body.public_safety_assessed || false,
        wildlife_check_complete: req.body.wildlife_check_complete || false,
        airspace_clearance_obtained: req.body.airspace_clearance_obtained || false,
        permits_verified: req.body.permits_verified || false,
        emergency_procedures: req.body.emergency_procedures || '',
        fallback_plans: req.body.fallback_plans || '',
        ppe_requirements: req.body.ppe_requirements || ''
      };

      const checklistData = {
        job_id: jobId,
        risk_assessment: JSON.stringify(riskAssessment),
        risk_mitigation_plan: req.body.risk_mitigation_plan || existingChecklist?.risk_mitigation_plan || '',
        updated_at: sqlDateTimeFormat(new Date())
      };

      // Check if risk assessment is complete
      const isComplete = riskAssessment.public_safety_assessed && 
                        riskAssessment.wildlife_check_complete && 
                        riskAssessment.airspace_clearance_obtained && 
                        riskAssessment.permits_verified;

      if (isComplete) {
        checklistData.status = 'completed';
        checklistData.last_updated = sqlDateTimeFormat(new Date());
      } else {
        checklistData.status = 'in_progress';
      }

      let result;
      
      if (existingChecklist) {
        // Preserve existing data and only update risk assessment
        if (existingChecklist.equipment_checklist) {
          checklistData.equipment_checklist = existingChecklist.equipment_checklist;
        }
        if (existingChecklist.required_uploads) {
          checklistData.required_uploads = existingChecklist.required_uploads;
        }
        
        result = await sdk.updateById('checklist', existingChecklist.id, checklistData);
      } else {
        // Create new checklist with defaults
        checklistData.equipment_checklist = JSON.stringify({
          batteries_charged: false,
          firmware_updated: false,
          sd_cards_inserted: false,
          compass_calibrated: false,
          controller_linked: false,
          obstacle_sensors: false
        });
        checklistData.required_uploads = JSON.stringify([]);
        checklistData.created_at = sqlDateTimeFormat(new Date());
        
        result = await sdk.create('checklist', checklistData);
      }

      res.status(200).json({ error: false, model: result });
    } catch (error) { 
      console.error('Risk assessment update error:', error);
      res.status(500).json({ error: true, message: error.message }); 
    }
  });

  // Upload job documents
  app.post('/v1/api/rowanenergy/drone-engineer/job/:id/documents', [TokenMiddleware()], async (req, res) => {
    try {
      const jobId = req.params.id;
      
      // Validate job ID
      if (!jobId || jobId === 'undefined' || jobId === 'null') {
        return res.status(400).json({ error: true, message: 'Invalid job ID' });
      }

      const sdk = app.get('sdk');
      sdk.setProjectId('rowanenergy');
      
      // Verify job ownership
      const job = await sdk.findOne('job_survey', { id: jobId, engineer_id: req.user_id });
      if (!job) {
        return res.status(403).json({ error: true, message: 'Forbidden' });
      }

      const documentData = {
        job_id: jobId,
        uploaded_by: req.user_id,
        ...req.body,
        uploaded_at: sqlDateTimeFormat(new Date()),
        created_at: sqlDateTimeFormat(new Date())
      };

      const result = await sdk.create('job_documents', documentData);

      // Update document count in progress
      const allDocuments = await sdk.find('job_documents', { job_id: jobId });
      const existingProgress = await sdk.findOne('job_progress', { job_id: jobId });
      if (existingProgress) {
        await sdk.updateById('job_progress', existingProgress.id, {
          documents_uploaded_count: allDocuments.length,
          updated_at: sqlDateTimeFormat(new Date())
        });
      }

      res.status(200).json({ error: false, model: result });
    } catch (error) { 
      console.error('Document upload error:', error);
      res.status(500).json({ error: true, message: error.message }); 
    }
  });

  // Update weather details
  app.put('/v1/api/rowanenergy/drone-engineer/job/:id/weather', [TokenMiddleware()], async (req, res) => {
    try {
      const jobId = req.params.id;
      
      // Validate job ID
      if (!jobId || jobId === 'undefined' || jobId === 'null') {
        return res.status(400).json({ error: true, message: 'Invalid job ID' });
      }

      const sdk = app.get('sdk');
      sdk.setProjectId('rowanenergy');
      
      // Verify job ownership
      const job = await sdk.findOne('job_survey', { id: jobId, engineer_id: req.user_id });
      if (!job) {
        return res.status(403).json({ error: true, message: 'Forbidden' });
      }

      const weatherData = {
        job_id: jobId,
        ...req.body,
        recorded_at: sqlDateTimeFormat(new Date()),
        updated_at: sqlDateTimeFormat(new Date())
      };

      const existing = await sdk.findOne('job_weather', { job_id: jobId });
      let result;
      
      if (existing) {
        result = await sdk.updateById('job_weather', existing.id,  {...weatherData, updated_at: sqlDateTimeFormat(new Date())});
      } else {
        result = await sdk.create('job_weather', { ...weatherData, created_at: sqlDateTimeFormat(new Date()) });
      }

      res.status(200).json({ error: false, model: result });
    } catch (error) { 
      console.error('Weather update error:', error);
      res.status(500).json({ error: true, message: error.message }); 
    }
  });

  // Update survey notes
  app.put('/v1/api/rowanenergy/drone-engineer/job/:id/survey-notes', [TokenMiddleware()], async (req, res) => {
    try {
      const jobId = req.params.id;
      
      // Validate job ID
      if (!jobId || jobId === 'undefined' || jobId === 'null') {
        return res.status(400).json({ error: true, message: 'Invalid job ID' });
      }

      const sdk = app.get('sdk');
      sdk.setProjectId('rowanenergy');
      
      // Verify job ownership
      const job = await sdk.findOne('job_survey', { id: jobId, engineer_id: req.user_id });
      if (!job) {
        return res.status(403).json({ error: true, message: 'Forbidden' });
      }

      const notesData = {
        job_id: jobId,
        completed_by: req.user_id,
        ...req.body,
        updated_at: sqlDateTimeFormat(new Date())
      };

      if (req.body.job_status === 'complete') {
        notesData.completed_at = sqlDateTimeFormat(new Date());
        
        // Update main job status
        await sdk.updateById('job_survey', jobId, { 
          status: 'completed',
          updated_at: sqlDateTimeFormat(new Date())
        });

        // Update progress
        const existingProgress = await sdk.findOne('job_progress', { job_id: jobId });
        if (existingProgress) {
          await sdk.updateById('job_progress', existingProgress.id, {
            survey_upload_status: 'completed',
            overall_status: 'completed',
            updated_at: sqlDateTimeFormat(new Date())
          });
        }
      }

      const existing = await sdk.findOne('job_survey_notes', { job_id: jobId });
      let result;
      
      if (existing) {
        result = await sdk.updateById('job_survey_notes', existing.id,  {...notesData, updated_at: sqlDateTimeFormat(new Date())});
      } else {
        result = await sdk.create('job_survey_notes', { ...notesData, created_at: sqlDateTimeFormat(new Date()), updated_at: sqlDateTimeFormat(new Date()) });
      }

      res.status(200).json({ error: false, model: result });
    } catch (error) { 
      console.error('Survey notes update error:', error);
      res.status(500).json({ error: true, message: error.message }); 
    }
  });

  // AI Assistant interaction
  app.post('/v1/api/rowanenergy/drone-engineer/job/:id/ai-assist', [TokenMiddleware()], async (req, res) => {
    try {
      const jobId = req.params.id;
      
      // Validate job ID
      if (!jobId || jobId === 'undefined' || jobId === 'null') {
        return res.status(400).json({ error: true, message: 'Invalid job ID' });
      }

      const sdk = app.get('sdk');
      sdk.setProjectId('rowanenergy');
      
      // Verify job ownership
      const job = await sdk.findOne('job_survey', { id: jobId, engineer_id: req.user_id });
      if (!job) {
        return res.status(403).json({ error: true, message: 'Forbidden' });
      }

      const { query, interaction_type, session_id } = req.body;
      
      // Get job context for AI
      const [progress, checklist, weather, riskAssessment] = await Promise.all([
        sdk.findOne('job_progress', { job_id: jobId }),
        sdk.findOne('job_checklist', { job_id: jobId }),
        sdk.findOne('job_weather', { job_id: jobId }),
        sdk.findOne('job_risk_assessment', { job_id: jobId })
      ]);

      const contextData = {
        job: { ...job, progress, checklist, weather, riskAssessment },
        interaction_type
      };

      // Save interaction
      const interactionData = {
        job_id: jobId,
        user_id: req.user_id,
        interaction_type,
        user_query: query,
        context_data: JSON.stringify(contextData),
        session_id: session_id || `ai_${Date.now()}`,
        created_at: sqlDateTimeFormat(new Date())
      };

      // Simple AI responses based on interaction type
      let aiResponse = '';
      switch (interaction_type) {
        case 'checklist_help':
          aiResponse = 'Based on your current checklist progress, ensure all equipment checks are completed before flight. Pay special attention to battery levels and calibration status.';
          break;
        case 'weather_analysis':
          aiResponse = `Current weather conditions${weather ? `: Wind speed ${weather.wind_speed}km/h, temperature ${weather.temperature}°C. ` : ' need to be assessed. '}Consider postponing if wind speeds exceed 25km/h or visibility is poor.`;
          break;
        case 'risk_assessment':
          aiResponse = 'Complete all safety checks systematically. Verify airspace clearance, assess terrain hazards, and confirm emergency procedures are in place.';
          break;
        default:
          aiResponse = 'I can help with checklist guidance, weather analysis, and risk assessment. What specific aspect would you like assistance with?';
      }

      interactionData.ai_response = aiResponse;
      
      const result = await sdk.create('job_ai_interactions', { ...interactionData, created_at: sqlDateTimeFormat(new Date()), updated_at: sqlDateTimeFormat(new Date()) });

      res.status(200).json({ 
        error: false, 
        model: { 
          response: aiResponse,
          interaction_id: result.id,
          context: contextData
        }
      });
    } catch (error) { res.status(500).json({ error: true, message: error.message }); }
  });

  // Route optimization endpoint
  app.post('/v1/api/rowanenergy/drone-engineer/routes/optimize', [TokenMiddleware()], async (req, res) => {
    try {
      const sdk = app.get('sdk');
      sdk.setProjectId('rowanenergy');
      const { job_ids, date } = req.body;
      
      // Use raw query to get jobs for the specified IDs
      const placeholders = job_ids.map(() => '?').join(',');
      const jobsQuery = `
        SELECT * FROM rowanenergy_job_survey 
        WHERE id IN (${placeholders}) AND engineer_id = ?
      `;
      const jobs = await sdk.rawQuery(jobsQuery, [...job_ids, req.user_id]);
      
      // Simple optimization placeholder - in real implementation, use Google Maps Distance Matrix API
      const optimizedRoute = jobs.map((job, index) => ({
        ...job,
        order: index + 1,
        estimated_travel_time: Math.floor(Math.random() * 30) + 10 // 10-40 minutes
      }));
      
      const totalDistance = Math.floor(Math.random() * 200) + 100; // 100-300 miles
      const totalDriveTime = optimizedRoute.reduce((acc, job) => acc + job.estimated_travel_time, 0);
      
      res.status(200).json({ 
        error: false, 
        model: {
          optimized_jobs: optimizedRoute,
          total_distance: `${totalDistance} miles`,
          total_drive_time: `${Math.floor(totalDriveTime / 60)}h ${totalDriveTime % 60}m`,
          total_jobs: jobs.length
        }
      });
    } catch (error) { res.status(500).json({ error: true, message: error.message }); }
  });

  // Engineer availability endpoints
  app.get('/v1/api/rowanenergy/drone-engineer/availability', [TokenMiddleware()], async (req, res) => {
    try {
      const sdk = app.get('sdk');
      sdk.setProjectId('rowanenergy');
      EngineerService.setSDK(sdk);
      const availability = await EngineerService.getAvailability(req.user_id);
      res.status(200).json({ error: false, model: availability });
    } catch (error) { res.status(500).json({ error: true, message: error.message }); }
  });

  app.put('/v1/api/rowanenergy/drone-engineer/availability', [TokenMiddleware()], async (req, res) => {
    try {
      const sdk = app.get('sdk');
      sdk.setProjectId('rowanenergy');
      EngineerService.setSDK(sdk);
      const result = await EngineerService.updateAvailability(req.user_id, req.body);
      res.status(200).json({ error: false, model: result });
    } catch (error) { res.status(500).json({ error: true, message: error.message }); }
  });

  // Engineer route data endpoint
  app.get('/v1/api/rowanenergy/drone-engineer/route-data', [TokenMiddleware()], async (req, res) => {
    try {
      const sdk = app.get('sdk');
      sdk.setProjectId('rowanenergy');
      const { date } = req.query;
      const dateFilter = date || new Date().toISOString().slice(0, 10);
      
      // Get jobs for the specified date
      const jobsQuery = `
        SELECT * FROM rowanenergy_job_survey 
        WHERE engineer_id = ? AND DATE(scheduled_time) = ?
        ORDER BY scheduled_time ASC
      `;
      const jobs = await sdk.rawQuery(jobsQuery, [req.user_id, dateFilter]);
      
      // Format jobs for route display
      const routeJobs = jobs.map((job, index) => ({
        id: job.id,
        order: index + 1,
        name: job.site_name,
        time: new Date(job.scheduled_time).toLocaleTimeString('en-US', { 
          hour: '2-digit', 
          minute: '2-digit',
          hour12: false 
        }),
        region: job.region || 'North England',
        status: job.status || 'Scheduled',
        coordinates: { lat: 53.4408 + (Math.random() * 0.1), lng: -2.2426 + (Math.random() * 0.1) },
        marker: (index + 1).toString()
      }));
      
      res.status(200).json({ 
        error: false, 
        model: {
          todaysRoute: routeJobs,
          summary: {
            totalDistance: `${Math.floor(Math.random() * 50) + 20} miles`,
            driveTime: `${Math.floor(Math.random() * 60) + 30}m`,
            totalJobs: routeJobs.length
          },
          mapCenter: { lat: 53.4408, lng: -2.2426 },
          trafficStatus: "Light Traffic",
          weatherStatus: "Clear"
        }
      });
    } catch (error) { res.status(500).json({ error: true, message: error.message }); }
  });

  // Engineer weekly schedule endpoint with enhanced data and filtering
  app.get('/v1/api/rowanenergy/drone-engineer/weekly-schedule', [TokenMiddleware()], async (req, res) => {
    try {
      const sdk = app.get('sdk');
      sdk.setProjectId('rowanenergy');
      const { week_start, region, status } = req.query;
      
      // Get jobs for the week
      const startDate = week_start || new Date().toISOString().slice(0, 10);
      const endDate = new Date(new Date(startDate).getTime() + 7 * 24 * 60 * 60 * 1000).toISOString().slice(0, 10);
      
      // Build query with filters
      let whereConditions = ['engineer_id = ?', 'DATE(scheduled_time) BETWEEN ? AND ?'];
      let queryParams = [req.user_id, startDate, endDate];
      
      if (region && region !== 'all') {
        whereConditions.push('region = ? OR location LIKE ?');
        queryParams.push(region, `%${region}%`);
      }
      
      if (status && status !== 'all') {
        whereConditions.push('status = ?');
        queryParams.push(status);
      }
      
      const whereClause = whereConditions.join(' AND ');
      
      const jobsQuery = `
        SELECT * FROM rowanenergy_job_survey 
        WHERE ${whereClause}
        ORDER BY scheduled_time ASC
      `;
      const jobs = await sdk.rawQuery(jobsQuery, queryParams);
      
      // Format jobs with enhanced data
      const enhancedJobs = jobs.map(job => ({
        ...job,
        day: new Date(job.scheduled_time).toLocaleDateString('en-US', { weekday: 'long' }),
        date: new Date(job.scheduled_time).toLocaleDateString('en-US', { month: 'short', day: 'numeric' }),
        time: new Date(job.scheduled_time).toLocaleTimeString('en-US', { 
          hour: '2-digit', 
          minute: '2-digit',
          hour12: false 
        }),
        weather: {
          type: ['sun', 'wind', 'rain'][Math.floor(Math.random() * 3)],
          temp: `${Math.floor(Math.random() * 20) + 5}°C`,
          wind: `${Math.floor(Math.random() * 30) + 10}mph`,
          rain: `${Math.floor(Math.random() * 80) + 10}%`
        },
        feasible: Math.random() > 0.2,
        region: job.region || job.location?.split(',')[1]?.trim() || 'North England'
      }));
      
      const summary = {
        totalJobs: enhancedJobs.length,
        completed: enhancedJobs.filter(j => j.status === 'completed').length,
        scheduled: enhancedJobs.filter(j => j.status === 'scheduled').length,
        regionsCovered: [...new Set(enhancedJobs.map(j => j.region))].length
      };
      
      res.status(200).json({ 
        error: false, 
        model: {
          weekRange: `${new Date(startDate).toLocaleDateString('en-US', { month: 'long', day: 'numeric' })} - ${new Date(endDate).toLocaleDateString('en-US', { month: 'long', day: 'numeric', year: 'numeric' })}`,
          jobs: enhancedJobs,
          summary,
          filters: { region: region || 'all', status: status || 'all', week_start: startDate }
        }
      });
    } catch (error) { res.status(500).json({ error: true, message: error.message }); }
  });


  // ADMIN DASHBOARD
  app.get('/v1/api/rowanenergy/super_admin/dashboard', AdminMiddleware(), async (req, res) => {
    try {
      const sdk = app.get('sdk');
      sdk.setProjectId('rowanenergy');
      const today = new Date();
      const todayStr = today.toISOString().slice(0,10);
      const yesterday = new Date(today);
      yesterday.setDate(yesterday.getDate() - 1);
      const yesterdayStr = yesterday.toISOString().slice(0,10);

      // Get leads data
      const leadsToday = await sdk.rawQuery(
        'SELECT COUNT(*) as count FROM rowanenergy_lead WHERE DATE(created_at) = ?',
        [todayStr]
      );
      const leadsYesterday = await sdk.rawQuery(
        'SELECT COUNT(*) as count FROM rowanenergy_lead WHERE DATE(created_at) = ?',
        [yesterdayStr]
      );

      // Get jobs scheduled today
      const jobsToday = await sdk.rawQuery(
        'SELECT COUNT(*) as count FROM rowanenergy_job_survey WHERE DATE(scheduled_time) = ?',
        [todayStr]
      );
      const jobsYesterday = await sdk.rawQuery(
        'SELECT COUNT(*) as count FROM rowanenergy_job_survey WHERE DATE(scheduled_time) = ?',
        [yesterdayStr]
      );

      // Get completed jobs today
      const completedToday = await sdk.rawQuery(
        'SELECT COUNT(*) as count FROM rowanenergy_job_survey WHERE DATE(updated_at) = ? AND status = "completed"',
        [todayStr]
      );
      const completedYesterday = await sdk.rawQuery(
        'SELECT COUNT(*) as count FROM rowanenergy_job_survey WHERE DATE(updated_at) = ? AND status = "completed"',
        [yesterdayStr]
      );

      // Get available engineers today (check actual availability and workload)
      const currentDay = new Date().toLocaleDateString('en-US', { weekday: 'long' });
      const currentHour = new Date().getHours();
      const timeSlot = currentHour < 12 ? 'morning' : currentHour < 17 ? 'afternoon' : 'evening';
      
      const availableEngineersResult = await sdk.rawQuery(`
        SELECT 
          u.id,
          p.weekly_availability,
          p.workload_limit,
          COUNT(j.id) as jobs_today
        FROM rowanenergy_user u 
        INNER JOIN rowanenergy_preference p ON u.id = p.user_id 
        INNER JOIN rowanenergy_engineer_profile ep ON u.id = ep.user_id
        LEFT JOIN rowanenergy_job_survey j ON u.id = j.engineer_id AND DATE(j.scheduled_time) = ?
        WHERE u.status = 1
        GROUP BY u.id, p.weekly_availability, p.workload_limit
      `, [todayStr]);
      
      // Filter available engineers based on availability and capacity
      let availableCount = 0;
      availableEngineersResult.forEach(engineer => {
        let isAvailable = true;
        let hasCapacity = true;
        
        // Check availability for current time slot
        if (engineer.weekly_availability) {
          try {
            const availability = JSON.parse(engineer.weekly_availability);
            if (availability[currentDay] && availability[currentDay][timeSlot] !== undefined) {
              isAvailable = availability[currentDay][timeSlot] === true;
            }
          } catch (e) {
            // If parsing fails, assume available
            isAvailable = true;
          }
        }
        
        // Check capacity based on workload limit and current jobs
        if (engineer.workload_limit && engineer.workload_limit.includes('1 job')) {
          hasCapacity = engineer.jobs_today === 0;
        } else {
          // Default capacity is 4 jobs per day
          hasCapacity = engineer.jobs_today < 4;
        }
        
        if (isAvailable && hasCapacity) {
          availableCount++;
        }
      });
      
      const availableEngineers = [{ count: availableCount }];

      // Get today's jobs with details (get all for today, ordered by time)
      const currentTime = new Date().toTimeString().slice(0, 8);
      const todaysJobs = await sdk.rawQuery(`
        SELECT 
          j.*,
          ep.first_name as engineer_first_name,
          ep.last_name as engineer_last_name,
          cp.first_name as customer_first_name,
          cp.last_name as customer_last_name
        FROM rowanenergy_job_survey j
        LEFT JOIN rowanenergy_preference ep ON j.engineer_id = ep.user_id
        LEFT JOIN rowanenergy_preference cp ON j.customer_id = cp.user_id
        WHERE DATE(j.scheduled_time) = ?
        ORDER BY j.scheduled_time ASC
      `, [todayStr]);

      // Get survey source breakdown (how surveys were actually created)
      const surveySources = await sdk.rawQuery(`
        SELECT 
          source,
          COUNT(*) as count
        FROM rowanenergy_job_survey 
        WHERE DATE(created_at) >= DATE(NOW() - INTERVAL 7 DAY)
        GROUP BY source
      `);

      // Get lead status breakdown  
      const leadStatus = await sdk.rawQuery(`
        SELECT 
          status,
          COUNT(*) as count
        FROM rowanenergy_lead 
        WHERE DATE(created_at) >= DATE(NOW() - INTERVAL 7 DAY)
        GROUP BY status
      `);

      // Get engineer workload with availability and capacity
      const engineerWorkload = await sdk.rawQuery(`
        SELECT 
          p.first_name,
          p.last_name,
          p.photo,
          u.id as engineer_id,
          COUNT(j.id) as jobs_today,
          (SELECT COUNT(*) FROM rowanenergy_job_survey 
           WHERE engineer_id = u.id 
           AND DATE(scheduled_time) BETWEEN ? AND DATE(NOW() + INTERVAL 6 DAY)) as jobs_this_week,
          p.workload_limit,
          p.weekly_availability,
          ep.regions,
          CASE 
            WHEN COUNT(j.id) = 0 THEN 'available'
            WHEN COUNT(j.id) >= 1 AND p.workload_limit LIKE '%1 job%' THEN 'fully_loaded'
            WHEN COUNT(j.id) <= 2 THEN 'light_load'
            WHEN COUNT(j.id) <= 4 THEN 'normal_load'
            ELSE 'fully_loaded'
          END as workload_status,
          (SELECT TIME(scheduled_time) FROM rowanenergy_job_survey 
           WHERE engineer_id = u.id AND DATE(scheduled_time) = ? 
           ORDER BY scheduled_time DESC LIMIT 1) as next_job_time,
          (SELECT site_name FROM rowanenergy_job_survey 
           WHERE engineer_id = u.id AND DATE(scheduled_time) = ? 
           ORDER BY scheduled_time ASC LIMIT 1) as next_job_location
        FROM rowanenergy_user u
        INNER JOIN rowanenergy_engineer_profile ep ON u.id = ep.user_id
        INNER JOIN rowanenergy_preference p ON u.id = p.user_id
        LEFT JOIN rowanenergy_job_survey j ON u.id = j.engineer_id AND DATE(j.scheduled_time) = ?
        WHERE u.status = 1
        GROUP BY u.id, p.first_name, p.last_name, p.photo, p.workload_limit, p.weekly_availability, ep.regions
        ORDER BY jobs_today DESC, p.first_name ASC
      `, [todayStr, todayStr, todayStr, todayStr]);

      // Format the response
      const kpis = {
        leads_today: {
          count: leadsToday[0]?.count || 0,
          change: (leadsToday[0]?.count || 0) - (leadsYesterday[0]?.count || 0)
        },
        jobs_scheduled_today: {
          count: jobsToday[0]?.count || 0,
          change: (jobsToday[0]?.count || 0) - (jobsYesterday[0]?.count || 0)
        },
        jobs_completed: {
          count: completedToday[0]?.count || 0,
          change: (completedToday[0]?.count || 0) - (completedYesterday[0]?.count || 0)
        },
        engineers_available: {
          count: availableEngineers[0]?.count || 0,
          change: 0 // Would need historical data for this
        }
      };

      // Format engineer workload with proper labels and availability check
      const formattedEngineers = engineerWorkload.map(engineer => {
        let isAvailableNow = true;
        let availabilityToday = { morning: true, afternoon: true, evening: false };
        
        // Parse availability
        if (engineer.weekly_availability) {
          try {
            const availability = JSON.parse(engineer.weekly_availability);
            availabilityToday = availability[currentDay] || availabilityToday;
            isAvailableNow = availabilityToday[timeSlot] === true;
          } catch (e) {
            console.error('Error parsing availability:', e);
          }
        }
        
        // Determine final status based on availability and workload
        let finalStatus = engineer.workload_status;
        let finalLabel = 'Available';
        
        if (!isAvailableNow) {
          finalStatus = 'not_available';
          finalLabel = 'Not Available';
        } else if (engineer.workload_status === 'available') {
          finalLabel = 'Available';
        } else if (engineer.workload_status === 'fully_loaded') {
          finalLabel = 'Fully Loaded';
        } else if (engineer.workload_status === 'light_load') {
          finalLabel = 'Light Load';
        } else if (engineer.workload_status === 'normal_load') {
          finalLabel = 'Normal Load';
        }
        
        return {
          ...engineer,
          name: `${engineer.first_name} ${engineer.last_name}`,
          workload_label: finalLabel,
          workload_status: finalStatus,
          is_available_now: isAvailableNow,
          availability_today: availabilityToday,
          regions_assigned: engineer.regions ? 
            (typeof engineer.regions === 'string' ? engineer.regions.split(',') : engineer.regions) : 
            []
        };
      });

      // Get weather data for main office location (default: London, UK)
      // In production, you could get this from user settings or company location
      const weatherData = await WeatherService.getCurrentWeather(51.5074, -0.1278); // London coordinates
      const forecastData = await WeatherService.getWeatherForecast(51.5074, -0.1278, 3); // 3 days forecast

      res.status(200).json({ 
        error: false, 
        model: {
          kpis,
          todays_jobs: todaysJobs.map(job => ({
            ...job,
            site_name: job.site_name || 'Survey Job',
            region: job.region || 'North England',
            engineer_name: job.engineer_first_name && job.engineer_last_name ? 
              `${job.engineer_first_name} ${job.engineer_last_name}` : 'Unassigned',
            customer_name: job.customer_first_name && job.customer_last_name ? 
              `${job.customer_first_name} ${job.customer_last_name}` : 'Customer'
          })),
          survey_sources: surveySources,
          lead_status: leadStatus,
          engineer_workload: formattedEngineers,
          current_time_slot: timeSlot,
          current_day: currentDay,
          weather: {
            current: weatherData,
            forecast: forecastData
          }
        }
      });
    } catch (error) { 
      console.error('Admin dashboard error:', error);
      res.status(500).json({ error: true, message: error.message }); 
    }
  });

  // WEATHER API
  app.get('/v1/api/rowanenergy/weather/current', async (req, res) => {
    try {
      const { lat, lon, location } = req.query;
      
      let coordinates = { lat, lon };
      
      // If location is provided instead of coordinates, geocode it
      if (location && (!lat || !lon)) {
        const geoResult = await WeatherService.getCoordinatesFromLocation(location);
        if (geoResult) {
          coordinates = { lat: geoResult.lat, lon: geoResult.lon };
        } else {
          return res.status(400).json({ error: true, message: 'Location not found' });
        }
      }
      
      // Default to London if no coordinates provided
      if (!coordinates.lat || !coordinates.lon) {
        coordinates = { lat: 51.5074, lon: -0.1278 };
      }
      
      const weatherData = await WeatherService.getCurrentWeather(coordinates.lat, coordinates.lon);
      
      res.status(200).json({
        error: false,
        model: {
          weather: weatherData,
          coordinates: coordinates
        }
      });
    } catch (error) {
      console.error('Weather API error:', error);
      res.status(500).json({ error: true, message: error.message });
    }
  });

  app.get('/v1/api/rowanenergy/weather/forecast', async (req, res) => {
    try {
      const { lat, lon, location, days = 5 } = req.query;
      
      let coordinates = { lat, lon };
      
      // If location is provided instead of coordinates, geocode it
      if (location && (!lat || !lon)) {
        const geoResult = await WeatherService.getCoordinatesFromLocation(location);
        if (geoResult) {
          coordinates = { lat: geoResult.lat, lon: geoResult.lon };
        } else {
          return res.status(400).json({ error: true, message: 'Location not found' });
        }
      }
      
      // Default to London if no coordinates provided
      if (!coordinates.lat || !coordinates.lon) {
        coordinates = { lat: 51.5074, lon: -0.1278 };
      }
      
      const forecastData = await WeatherService.getWeatherForecast(coordinates.lat, coordinates.lon, parseInt(days));
      
      res.status(200).json({
        error: false,
        model: {
          forecast: forecastData,
          coordinates: coordinates,
          days: parseInt(days)
        }
      });
    } catch (error) {
      console.error('Weather forecast API error:', error);
      res.status(500).json({ error: true, message: error.message });
    }
  });

  // MAPS API
  app.post('/v1/api/rowanenergy/maps/geocode', async (req, res) => {
    try {
      const { address } = req.body;
      
      if (!address) {
        return res.status(400).json({ error: true, message: 'Address is required' });
      }
      
      const result = await MapsService.geocodeAddress(address);
      
      if (result) {
        res.status(200).json({
          error: false,
          model: {
            location: result,
            coordinates: { lat: result.lat, lng: result.lng }
          }
        });
      } else {
        res.status(404).json({ error: true, message: 'Address not found' });
      }
    } catch (error) {
      console.error('Geocoding API error:', error);
      res.status(500).json({ error: true, message: error.message });
    }
  });

  app.post('/v1/api/rowanenergy/maps/distance-matrix', async (req, res) => {
    try {
      const { origins, destinations } = req.body;
      
      if (!origins || !destinations) {
        return res.status(400).json({ error: true, message: 'Origins and destinations are required' });
      }
      
      const result = await MapsService.getDistanceMatrix(origins, destinations);
      
      res.status(200).json({
        error: false,
        model: {
          distance_matrix: result
        }
      });
    } catch (error) {
      console.error('Distance Matrix API error:', error);
      res.status(500).json({ error: true, message: error.message });
    }
  });

  app.post('/v1/api/rowanenergy/maps/directions', async (req, res) => {
    try {
      const { origin, destination, waypoints = [] } = req.body;
      
      if (!origin || !destination) {
        return res.status(400).json({ error: true, message: 'Origin and destination are required' });
      }
      
      const result = await MapsService.getDirections(origin, destination, waypoints);
      
      if (result) {
        res.status(200).json({
          error: false,
          model: {
            route: result
          }
        });
      } else {
        res.status(404).json({ error: true, message: 'Route not found' });
      }
    } catch (error) {
      console.error('Directions API error:', error);
      res.status(500).json({ error: true, message: error.message });
    }
  });

  app.get('/v1/api/rowanenergy/maps/static-map', async (req, res) => {
    try {
      const { center, zoom, size, markers, path } = req.query;
      
      if (!center) {
        return res.status(400).json({ error: true, message: 'Center coordinates are required' });
      }

      const options = {
        center,
        zoom: zoom ? parseInt(zoom) : 15,
        size: size || '400x300',
        markers: markers ? JSON.parse(markers) : [],
        path
      };
      
      const mapUrl = MapsService.generateStaticMapUrl(options);
      
      res.status(200).json({
        error: false,
        model: {
          map_url: mapUrl,
          options
        }
      });
    } catch (error) {
      console.error('Static map API error:', error);
      res.status(500).json({ error: true, message: error.message });
    }
  });

  app.get('/v1/api/rowanenergy/maps/nearby-places', async (req, res) => {
    try {
      const { lat, lng, type = 'point_of_interest', radius = 5000 } = req.query;
      
      if (!lat || !lng) {
        return res.status(400).json({ error: true, message: 'Latitude and longitude are required' });
      }
      
      const places = await MapsService.getNearbyPlaces(
        parseFloat(lat), 
        parseFloat(lng), 
        type, 
        parseInt(radius)
      );
      
      res.status(200).json({
        error: false,
        model: {
          places,
          location: { lat: parseFloat(lat), lng: parseFloat(lng) },
          search_params: { type, radius: parseInt(radius) }
        }
      });
    } catch (error) {
      console.error('Nearby places API error:', error);
      res.status(500).json({ error: true, message: error.message });
    }
  });

  // CHAT ASSISTANT APIs (Proxy to external service with history storage)
  app.post('/v1/api/rowanenergy/member/chat/booking-assistant', [TokenMiddleware()], async (req, res) => {
    try {
      const { query, history, session_id } = req.body;
      const sdk = app.get('sdk');
      sdk.setProjectId('rowanenergy');
      const chatHistoryService = new ChatHistoryService();
      
      chatHistoryService.setSDK(sdk);

      const customer = await sdk.findOne('user', { id: req.user_id });
      const profile = await sdk.findOne('preference', { user_id: req.user_id });

      const customer_email = customer?.email || '';
      const customer_phone = profile?.phone || '';
      const customer_name = profile?.first_name || '' + ' ' + profile?.last_name || '';
      
      // Get existing chat history from database
      const dbHistory = await chatHistoryService.getChatHistory(req.user_id, session_id, 'booking_assistant');
      
      // Convert DB history to API format
      const apiHistory = dbHistory.map(msg => ({
        role: msg.role,
        content: msg.content
      }));
      const customer_metadata = {
          user_id: req.user_id,
          customer_email,
          customer_phone,
          customer_name
        }
      
      // Save user message to database
      await chatHistoryService.saveMessage(req.user_id, session_id, 'booking_assistant', 'user', query);

      
      const apiData = { query, history: apiHistory, customer_metadata }
      
      console.log( apiData);
      
      const response = await fetch(`${ds_url}/api/v1/chat/booking-assistant`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer drbot-nhLybL86VBiUTtyngshsdj9efmc'
        },
        body: JSON.stringify(apiData)
      });
      
      const data = await response.json();

      console.log(data);
      
      // Process and format the response for conversation history
      let conversationContent = '';
      if (!data.error && data.message?.message) {
        conversationContent = data.message.message;
        
        // If there are options, format them for conversation history
        if (data.message.options && Array.isArray(data.message.options)) {
          const formattedOptions = data.message.options.map((option, index) => `${index + 1}. ${option}`).join(', ');
          conversationContent += ` Options: [${formattedOptions}]`;
        }
        
        // Save the formatted response to conversation history
        await chatHistoryService.saveMessage(req.user_id, session_id, 'booking_assistant', 'assistant', conversationContent);
      }
      
      res.status(response.status).json(data);
    } catch (error) {
      res.status(500).json({ error: true, message: 'Chat service unavailable: ' + error.message });
    }
  });

  app.post('/v1/api/rowanenergy/drone-engineer/chat/analyse-survey', [TokenMiddleware()], async (req, res) => {
    try {
      const { booking_form_input, session_id } = req.body;
      const sdk = app.get('sdk');
      sdk.setProjectId('rowanenergy');
      const chatHistoryService = new ChatHistoryService();
      chatHistoryService.setSDK(sdk);
      
      // Extract job_id from the booking_form_input
      const job_id = booking_form_input?.job_id;
      
      if (!job_id) {
        return res.status(400).json({ error: true, message: 'job_id is required in booking_form_input' });
      }
      
      // Get job details from database to complete the payload
      const job = await sdk.findOne('job_survey', { id: job_id });
      if (!job) {
        return res.status(404).json({ error: true, message: 'Job not found' });
      }


      // fetch engineer details based on assigned_engineer or engineer_id
      let engineer = null;
      if (job.engineer_id) {
        engineer = await sdk.findOne('preference', { user_id: job.engineer_id });
      }
      
      // Calculate end time based on duration
      let endTime = 'TBD';
      if (job.scheduled_time && job.duration_minutes) {
        const startDate = new Date(job.scheduled_time);
        const endDate = new Date(startDate.getTime() + (job.duration_minutes * 60000));
        endTime = endDate.toLocaleTimeString('en-US', {
          hour: '2-digit', minute: '2-digit'
        });
      }

      // Construct the complete payload with job data and form data
      const completePayload = {
        booking_form_input: {
          job_id: job_id,
          job_overview: {
            job_number: `Job #${job_id}`,
            site_name: job.site_name || 'Unknown Site',
            assigned_date: job.scheduled_time ? new Date(job.scheduled_time).toLocaleDateString('en-US', {
              year: 'numeric', month: 'long', day: 'numeric'
            }) : 'TBD',
            status: job.status || 'Scheduled'
          },
          site_information: {
            site_name: job.site_name || 'Unknown Site',
            region: job.region || 'Unknown Region',
            full_address: job.location || 'Address not provided',
            gps_coordinates: {
              latitude: job.latitude || 'Not provided',
              longitude: job.longitude || 'Not provided'
            }
          },
          timing: {
            start_time: job.scheduled_time ? new Date(job.scheduled_time).toLocaleTimeString('en-US', {
              hour: '2-digit', minute: '2-digit'
            }) : 'TBD',
            end_time: endTime,
            survey_duration: job.duration_minutes ? `${job.duration_minutes} mins` : 'Not specified',
            buffer_time: "15 mins" // Standard buffer time
          },
          form: {
            asset_type: job.asset_type || 'Not specified',
            system_size: job.system_size || 'Not specified',
            survey_purpose: job.survey_purpose || 'General inspection',
            assigned_engineer: engineer ? `${engineer.first_name || ''} ${engineer.last_name || ''} - ${engineer.phone || 'Contact via system'}`.trim() : 'Not assigned',
            contact_person: job.contact_name || 'Not provided',
            contact_phone: job.contact_phone || 'Not provided',
            // Include any additional form data from the original booking_form_input
            ...booking_form_input.form
          }
        }
      };
      
      // Save analysis request to database
      await chatHistoryService.saveMessage(req.user_id, session_id, 'survey_analysis', 'user', 
        `Analyze survey for job: ${job_id} - ${job.site_name}`, { job_id, payload: completePayload });
      
      const response = await fetch(`${ds_url}/api/v1/chat/analyse-survey`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer drbot-nhLybL86VBiUTtyngshsdj9efmc'
        },
        body: JSON.stringify(completePayload)
      });
      
      const data = await response.json();
      
      // Save analysis result to database
      if (!data.error && data.message) {
        await chatHistoryService.saveMessage(req.user_id, session_id, 'survey_analysis', 'assistant', data.message, { job_id });
      }
      
      res.status(response.status).json(data);
    } catch (error) {
      res.status(500).json({ error: true, message: 'Survey analysis service unavailable: ' + error.message });
    }
  });

  // PUBLIC WEBHOOK: Create job survey (for chatbot integration)
  app.post('/v1/api/rowanenergy/public/webhook/job-survey', async (req, res) => {
    try {
      const sdk = app.get('sdk');
      sdk.setProjectId('rowanenergy');

      const JobService = require('./services/jobService');
      JobService.setSDK(sdk);

      const {
        user_id,
        customer_email,
        customer_name,
        customer_phone,
        site_name,
        location,
        asset_type,
        system_size,
        preferred_date,
        preferred_time,
        region,
        latitude,
        longitude,
        duration_minutes,
        is_flexible,
        contact_name,
        contact_phone,
        access_instructions,
        survey_purpose,
        additional_notes,
        webhook_source = 'chatbot'
      } = req.body;

      // Validate required fields
      if (!user_id && !customer_email) {
        return res.status(400).json({
          error: true,
          message: 'Either user_id or customer_email is required'
        });
      }

      if (!site_name || !location) {
        return res.status(400).json({
          error: true,
          message: 'site_name and location are required'
        });
      }

      let customerId = user_id;

      // If no user_id provided, try to find or create customer by email
      if (!customerId && customer_email) {
        try {
          // Try to find existing customer by email
          const existingCustomer = await sdk.findOne('user', { email: customer_email });

          if (existingCustomer) {
            customerId = existingCustomer.id;
          } else {
            // Create new customer account
            const newCustomer = await sdk.create('user', {
              email: customer_email,
              first_name: customer_name?.split(' ')[0] || '',
              last_name: customer_name?.split(' ').slice(1).join(' ') || '',
              phone: customer_phone || '',
              role: 'customer',
              status: 1,
              created_at: sqlDateTimeFormat(new Date()),
              updated_at: sqlDateTimeFormat(new Date())
            });

            if (newCustomer && newCustomer.id) {
              customerId = newCustomer.id;

              // Create preference record for the new customer
              await sdk.create('preference', {
                user_id: customerId,
                first_name: customer_name?.split(' ')[0] || '',
                last_name: customer_name?.split(' ').slice(1).join(' ') || '',
                phone: customer_phone || '',
                created_at: sqlDateTimeFormat(new Date()),
                updated_at: sqlDateTimeFormat(new Date())
              });
            } else {
              return res.status(500).json({
                error: true,
                message: 'Failed to create customer account'
              });
            }
          }
        } catch (customerError) {
          console.error('Customer creation/lookup error:', customerError);
          return res.status(500).json({
            error: true,
            message: 'Failed to process customer information'
          });
        }
      }

      // Create job survey using JobService (same as frontend booking)
      const scheduledTime = preferred_date && preferred_time
        ? `${preferred_date} ${preferred_time}:00`
        : null;

      const jobPayload = {
        customer_id: customerId,
        asset_type: asset_type || 'solar',
        site_name,
        system_size: system_size || null,
        location,
        region: region || null,
        latitude: latitude || null,
        longitude: longitude || null,
        scheduled_time: scheduledTime,
        duration_minutes: duration_minutes || 60,
        is_flexible: is_flexible || false,
        contact_name: contact_name || customer_name,
        contact_phone: contact_phone || customer_phone,
        access_instructions: access_instructions || '',
        survey_purpose: survey_purpose || 'initial_assessment',
        additional_notes: additional_notes || '',
        status: 'pending',
        source: webhook_source
      };

      const jobResult = await JobService.createJob(filterEmptyFields(jobPayload));

      if (!jobResult || jobResult.error) {
        return res.status(500).json({
          error: true,
          message: 'Failed to create job survey'
        });
      }

      res.status(200).json({
        error: false,
        message: 'Job survey created successfully',
        data: {
          job_id: jobResult.id,
          customer_id: customerId,
          site_name,
          location,
          asset_type: asset_type || 'solar',
          status: 'pending',
          source: webhook_source,
          scheduled_time: scheduledTime
        }
      });
    } catch (error) {
      console.error('Webhook job survey creation error:', error);
      res.status(500).json({ error: true, message: error.message });
    }
  });
  app.post('/v1/api/rowanenergy/public/webhook/job-survey-call', async (req, res) => {
    try {
      const sdk = app.get('sdk');
      sdk.setProjectId('rowanenergy');

      const JobService = require('./services/jobService');
      JobService.setSDK(sdk);

      const {
        user_id,
        customer_email,
        customer_name,
        customer_phone,
        site_name,
        location,
        region,
        latitude,
        longitude,
        asset_type,
        system_size,
        preferred_date,
        preferred_time,
        duration_minutes,
        is_flexible,
        contact_name,
        contact_phone,
        access_instructions,
        survey_purpose,
        additional_notes,
        webhook_source = 'phone'
      } = req.body;

      // Validate required fields
      if (!user_id && !customer_email) {
        return res.status(400).json({
          error: true,
          message: 'Either user_id or customer_email is required'
        });
      }

      if (!site_name || !location) {
        return res.status(400).json({
          error: true,
          message: 'site_name and location are required'
        });
      }

      let customerId = user_id;

      // If no user_id provided, try to find or create customer by email
      if (!customerId && customer_email) {
        try {
          // Try to find existing customer by email
          const existingCustomer = await sdk.findOne('user', { email: customer_email });

          if (existingCustomer) {
            customerId = existingCustomer.id;
          } else {
            // Create new customer account
            const newCustomer = await sdk.create('user', {
              email: customer_email,
              first_name: customer_name?.split(' ')[0] || '',
              last_name: customer_name?.split(' ').slice(1).join(' ') || '',
              phone: customer_phone || '',
              role: 'customer',
              status: 1,
              created_at: sqlDateTimeFormat(new Date()),
              updated_at: sqlDateTimeFormat(new Date())
            });

            if (newCustomer && newCustomer.id) {
              customerId = newCustomer.id;

              // Create preference record for the new customer
              await sdk.create('preference', {
                user_id: customerId,
                first_name: customer_name?.split(' ')[0] || '',
                last_name: customer_name?.split(' ').slice(1).join(' ') || '',
                phone: customer_phone || '',
                created_at: sqlDateTimeFormat(new Date()),
                updated_at: sqlDateTimeFormat(new Date())
              });
            } else {
              return res.status(500).json({
                error: true,
                message: 'Failed to create customer account'
              });
            }
          }
        } catch (customerError) {
          console.error('Customer creation/lookup error:', customerError);
          return res.status(500).json({
            error: true,
            message: 'Failed to process customer information'
          });
        }
      }

      // Create job survey using JobService (same as frontend booking)
      const scheduledTime = preferred_date && preferred_time
        ? `${preferred_date} ${preferred_time}:00`
        : null;

      const jobPayload = {
        customer_id: customerId,
        asset_type: asset_type || 'solar',
        site_name,
        system_size: system_size || null,
        location,
        region: region || null,
        latitude: latitude || null,
        longitude: longitude || null,
        scheduled_time: scheduledTime,
        duration_minutes: duration_minutes || 60,
        is_flexible: is_flexible || false,
        contact_name: contact_name || customer_name,
        contact_phone: contact_phone || customer_phone,
        access_instructions: access_instructions || '',
        survey_purpose: survey_purpose || 'initial_assessment',
        additional_notes: additional_notes || '',
        status: 'pending',
        source: webhook_source
      };

      console.log('Job payload:', jobPayload);

      const jobResult = await JobService.createJob(filterEmptyFields(jobPayload));

      if (!jobResult || jobResult.error) {
        return res.status(500).json({
          error: true,
          message: 'Failed to create job survey'
        });
      }

      res.status(200).json({
        error: false,
        message: 'Job survey created successfully',
        data: {
          job_id: jobResult.id,
          customer_id: customerId,
          site_name,
          location,
          asset_type: asset_type || 'solar',
          status: 'pending',
          source: webhook_source,
          scheduled_time: scheduledTime
        }
      });
    } catch (error) {
      console.error('Webhook job survey creation error:', error);
      res.status(500).json({ error: true, message: error.message });
    }
  });

  // PUBLIC Chat Assistant API (for anonymous users on landing page)
  app.post('/v1/api/rowanenergy/public/chat/booking-assistant', async (req, res) => {
    try {
      const { query, session_id } = req.body;
      const sdk = app.get('sdk');
      sdk.setProjectId('rowanenergy');
      const chatHistoryService = new ChatHistoryService();
      chatHistoryService.setSDK(sdk);
      
      // Get existing chat history from database for anonymous user
      const dbHistory = await chatHistoryService.getChatHistory(null, session_id, 'booking_assistant');
      
      // Convert DB history to API format
      const apiHistory = dbHistory.map(msg => ({
        role: msg.role,
        user_id: session_id,
        content: msg.content
      }));
      
      // Save user message to database (anonymous)
      await chatHistoryService.saveMessage(null, session_id, 'booking_assistant', 'user', query);
      
      const response = await fetch(`${ds_url}/api/v1/chat/booking-assistant`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer drbot-nhLybL86VBiUTtyngshsdj9efmc'
        },
        body: JSON.stringify({ query, history: apiHistory })
      });
      
      const data = await response.json();
      
      // Process and format the response for conversation history
      let conversationContent = '';
      if (!data.error && data.message?.message) {
        conversationContent = data.message.message;
        
        // If there are options, format them for conversation history
        if (data.message.options && Array.isArray(data.message.options)) {
          const formattedOptions = data.message.options.map((option, index) => `${index + 1}. ${option}`).join(', ');
          conversationContent += ` Options: [${formattedOptions}]`;
        }
        
        // Save the formatted response to conversation history (anonymous)
        await chatHistoryService.saveMessage(null, session_id, 'booking_assistant', 'assistant', conversationContent);
      }
      
      res.status(response.status).json(data);
    } catch (error) {
      res.status(500).json({ error: true, message: 'Chat service unavailable: ' + error.message });
    }
  });

  // Chat History APIs
  app.get('/v1/api/rowanenergy/member/chat/history/:chat_type', [TokenMiddleware()], async (req, res) => {
    try {
      const { chat_type } = req.params;
      const { session_id } = req.query;
      const sdk = app.get('sdk');
      sdk.setProjectId('rowanenergy');
      const chatHistoryService = new ChatHistoryService();
      chatHistoryService.setSDK(sdk);
      const history = await chatHistoryService.getChatHistory(req.user_id, session_id, chat_type);
      
      res.status(200).json({ error: false, list: history });
    } catch (error) {
      res.status(500).json({ error: true, message: error.message });
    }
  });

  app.delete('/v1/api/rowanenergy/member/chat/history/:chat_type', [TokenMiddleware()], async (req, res) => {
    try {
      const { chat_type } = req.params;
      const { session_id } = req.body;
      const sdk = app.get('sdk');
      sdk.setProjectId('rowanenergy');
      const chatHistoryService = new ChatHistoryService();
      chatHistoryService.setSDK(sdk);
      await chatHistoryService.clearChatHistory(req.user_id, session_id, chat_type);
      
      res.status(200).json({ error: false, message: 'Chat history cleared' });
    } catch (error) {
      res.status(500).json({ error: true, message: error.message });
    }
  });
};