{"settings": {"globalKey": "key_1754942273315_03lybk2o3", "databaseType": "mysql", "authType": "session", "timezone": "UTC", "dbHost": "localhost", "dbPort": "3306", "dbUser": "root", "dbPassword": "root", "dbName": "database_2025-08-11", "id": "rowanenergy", "isPWA": false, "isMultiTenant": false, "model_namespace": "namespace_1754942273316_9wcrjltza", "payment_option": "one_time_payment"}, "models": [{"id": "model_1754942289750_9fsath376", "name": "job", "fields": [{"name": "id", "type": "primary key", "defaultValue": "", "validation": ""}, {"name": "task", "type": "string", "defaultValue": "", "validation": "required"}, {"name": "arguments", "type": "json", "defaultValue": "", "validation": ""}, {"name": "time_interval", "type": "string", "defaultValue": "once", "validation": ""}, {"name": "retries", "type": "integer", "defaultValue": "1", "validation": ""}, {"name": "status", "type": "mapping", "mapping": "0:Pending,1:Failed,2:Processing,3:Completed", "defaultValue": "0", "validation": ""}, {"name": "created_at", "type": "timestamp", "defaultValue": "CURRENT_TIMESTAMP", "validation": "date"}, {"name": "updated_at", "type": "timestamp", "defaultValue": "CURRENT_TIMESTAMP", "validation": "date"}]}, {"id": "model_1754942289750_zn3ng1632", "name": "uploads", "fields": [{"name": "id", "type": "primary key", "defaultValue": "", "validation": ""}, {"name": "url", "type": "string", "defaultValue": "", "validation": "required"}, {"name": "caption", "type": "string", "defaultValue": "", "validation": ""}, {"name": "user_id", "type": "foreign key", "defaultValue": "", "validation": ""}, {"name": "width", "type": "integer", "defaultValue": "", "validation": ""}, {"name": "height", "type": "integer", "defaultValue": "", "validation": ""}, {"name": "type", "type": "mapping", "mapping": "0:Image,1:s3,2:Video,3:base64", "defaultValue": "0", "validation": "required,enum:0,1,2,3"}, {"name": "created_at", "type": "timestamp", "defaultValue": "CURRENT_TIMESTAMP", "validation": "date"}, {"name": "updated_at", "type": "timestamp", "defaultValue": "CURRENT_TIMESTAMP", "validation": "date"}]}, {"id": "model_1754942289750_5p2jh44e3", "name": "tokens", "fields": [{"name": "id", "type": "primary key", "defaultValue": "", "validation": ""}, {"name": "user_id", "type": "foreign key", "defaultValue": "", "validation": "required"}, {"name": "token", "type": "string", "defaultValue": "", "validation": "required"}, {"name": "code", "type": "string", "defaultValue": "", "validation": "required"}, {"name": "type", "type": "mapping", "mapping": "0:Access,1:<PERSON><PERSON><PERSON>,2:<PERSON><PERSON>,3:<PERSON><PERSON><PERSON>,4:<PERSON>", "defaultValue": "0", "validation": "required,enum:0,1,2,3,4"}, {"name": "data", "type": "json", "defaultValue": "", "validation": ""}, {"name": "status", "type": "mapping", "mapping": "0:Inactive,1:Active", "defaultValue": "1", "validation": "required,enum:0,1"}, {"name": "created_at", "type": "timestamp", "defaultValue": "CURRENT_TIMESTAMP", "validation": "date"}, {"name": "updated_at", "type": "timestamp", "defaultValue": "CURRENT_TIMESTAMP", "validation": "date"}, {"name": "expired_at", "type": "timestamp", "defaultValue": "", "validation": "date"}]}, {"id": "model_1754942289750_lb73hhk1r", "name": "preference", "fields": [{"name": "id", "type": "primary key", "defaultValue": "", "validation": ""}, {"name": "first_name", "type": "string", "defaultValue": "", "validation": ""}, {"name": "last_name", "type": "string", "defaultValue": "", "validation": ""}, {"name": "phone", "type": "string", "defaultValue": "", "validation": ""}, {"name": "photo", "type": "string", "defaultValue": "", "validation": ""}, {"name": "user_id", "type": "integer", "defaultValue": "", "validation": "required"}]}, {"id": "model_1754942289750_93nz5peec", "name": "user", "fields": [{"name": "id", "type": "primary key", "defaultValue": "", "validation": ""}, {"name": "email", "type": "string", "defaultValue": "", "validation": "required,email"}, {"name": "password", "type": "password", "defaultValue": "", "validation": "required"}, {"name": "login_type", "type": "mapping", "mapping": "0:<PERSON>,1:Google,2:Microsoft,3:Apple,4:Twitter,5:Facebook", "defaultValue": "0", "validation": "required,enum:0,1,2,3,4,5"}, {"name": "role_id", "type": "string", "defaultValue": "", "validation": ""}, {"name": "data", "type": "json", "defaultValue": "", "validation": ""}, {"name": "status", "type": "mapping", "mapping": "0:Active,1:Inactive,2:Suspend", "defaultValue": "0", "validation": "required,enum:0,1,2"}, {"name": "verify", "type": "boolean", "defaultValue": "0", "validation": "required"}, {"name": "two_factor_authentication", "type": "boolean", "defaultValue": "0", "validation": ""}, {"name": "company_id", "type": "integer", "defaultValue": "0", "validation": ""}, {"name": "stripe_uid", "type": "string", "defaultValue": "", "validation": ""}, {"name": "created_at", "type": "timestamp", "defaultValue": "CURRENT_TIMESTAMP", "validation": "date"}]}, {"id": "model_1754942503113_rnx27e8ks", "name": "stripe_product", "fields": [{"name": "id", "type": "primary key", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "created_at", "type": "date", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "updated_at", "type": "datetime", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "name", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "product_id", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "stripe_id", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "object", "type": "long text", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "status", "type": "integer", "defaultValue": "", "validation": "", "validationOptions": {}}]}, {"id": "model_1754942503113_ow3ukm5g8", "name": "stripe_price", "fields": [{"name": "id", "type": "primary key", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "created_at", "type": "date", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "updated_at", "type": "datetime", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "name", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "product_id", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "stripe_id", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "is_usage_metered", "type": "integer", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "usage_limit", "type": "integer", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "object", "type": "medium text", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "amount", "type": "float", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "trial_days", "type": "integer", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "type", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "status", "type": "integer", "defaultValue": "", "validation": "", "validationOptions": {}}]}, {"id": "model_1754942503113_nq9l2szv6", "name": "stripe_subscription", "fields": [{"name": "id", "type": "primary key", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "stripe_id", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "price_id", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "user_id", "type": "integer", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "object", "type": "json", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "status", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "is_lifetime", "type": "boolean", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "created_at", "type": "date", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "updated_at", "type": "datetime", "defaultValue": "", "validation": "", "validationOptions": {}}]}, {"id": "model_1754942503113_qtx28ojgn", "name": "stripe_checkout", "fields": [{"name": "id", "type": "primary key", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "user_id", "type": "integer", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "stripe_id", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "object", "type": "json", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "created_at", "type": "date", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "updated_at", "type": "datetime", "defaultValue": "", "validation": "", "validationOptions": {}}]}, {"id": "model_1754942503113_ahdm3hq2s", "name": "stripe_webhook", "fields": [{"name": "id", "type": "primary key", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "stripe_id", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "idempotency_key", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "description", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "event_type", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "resource_type", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "object", "type": "json", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "is_handled", "type": "boolean", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "created_at", "type": "date", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "updated_at", "type": "datetime", "defaultValue": "", "validation": "", "validationOptions": {}}]}, {"id": "model_1754942503113_hpmiqqwtp", "name": "stripe_setting", "fields": [{"name": "id", "type": "primary key", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "key", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "value", "type": "medium text", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "created_at", "type": "date", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "updated_at", "type": "datetime", "defaultValue": "", "validation": "", "validationOptions": {}}]}, {"id": "model_1754942503113_zlbmi5g7n", "name": "stripe_order", "fields": [{"name": "id", "type": "primary key", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "user_id", "type": "integer", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "price_id", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "stripe_id", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "object", "type": "json", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "created_at", "type": "date", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "updated_at", "type": "datetime", "defaultValue": "", "validation": "", "validationOptions": {}}]}, {"id": "model_1754942503113_15ow1qq8u", "name": "stripe_invoice", "fields": [{"name": "id", "type": "primary key", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "user_id", "type": "integer", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "stripe_id", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "object", "type": "json", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "created_at", "type": "date", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "updated_at", "type": "datetime", "defaultValue": "", "validation": "", "validationOptions": {}}]}, {"id": "model_1754942503113_ls45unexz", "name": "stripe_refund", "fields": [{"name": "id", "type": "primary key", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "user_id", "type": "integer", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "stripe_id", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "object", "type": "json", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "charge_id", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "subscription_id", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "amount", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "currency", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "reason", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "status", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "created_at", "type": "date", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "updated_at", "type": "datetime", "defaultValue": "", "validation": "", "validationOptions": {}}]}, {"id": "model_1754942503113_y49zvt3sk", "name": "stripe_dispute", "fields": [{"name": "id", "type": "primary key", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "user_id", "type": "integer", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "stripe_id", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "subscription_id", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "object", "type": "json", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "amount", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "reason", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "reason_description", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "status", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "created_at", "type": "date", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "updated_at", "type": "datetime", "defaultValue": "", "validation": "", "validationOptions": {}}]}], "roles": [{"id": "role_admin_1754942289750", "name": "Super Admin", "slug": "super_admin", "permissions": {"routes": [], "canCreateUsers": true, "canEditUsers": true, "canDeleteUsers": true, "canManageRoles": true, "canLogin": true, "canRegister": false, "canForgot": false, "canReset": false, "canGoogleLogin": false, "canAppleLogin": false, "canMicrosoftLogin": false, "canMagicLinkLogin": false, "canTwitterLogin": false, "needs2FA": false, "canSetPermissions": false, "canPreference": true, "canVerifyEmail": false, "canUpload": true, "canStripe": true, "canStripeWebhook": true, "canRealTime": true, "canAI": false, "canUpdateEmail": true, "canUpdatePassword": true, "canUpdateOtherUsers": true, "treeql": {"enabled": true, "models": {"job": {"allowed": true, "blacklistedFields": [], "operations": {"get": true, "getOne": true, "getAll": true, "post": true, "put": true, "delete": true, "paginate": true, "join": true}}, "uploads": {"allowed": true, "blacklistedFields": [], "operations": {"get": true, "getOne": true, "getAll": true, "post": true, "put": true, "delete": true, "paginate": true, "join": true}}, "tokens": {"allowed": true, "blacklistedFields": [], "operations": {"get": true, "getOne": true, "getAll": true, "post": true, "put": true, "delete": true, "paginate": true, "join": true}}, "preference": {"allowed": true, "blacklistedFields": [], "operations": {"get": true, "getOne": true, "getAll": true, "post": true, "put": true, "delete": true, "paginate": true, "join": true}}, "user": {"allowed": true, "blacklistedFields": [], "operations": {"get": true, "getOne": true, "getAll": true, "post": true, "put": true, "delete": true, "paginate": true, "join": true}}}}}}, {"id": "role_member_1754942289750", "name": "Member", "slug": "member", "permissions": {"routes": [], "canCreateUsers": false, "canEditUsers": false, "canDeleteUsers": false, "canManageRoles": false, "canLogin": true, "canRegister": true, "canForgot": false, "canReset": false, "canGoogleLogin": true, "canAppleLogin": true, "canMicrosoftLogin": true, "canMagicLinkLogin": false, "canTwitterLogin": false, "needs2FA": true, "canSetPermissions": true, "canPreference": false, "canVerifyEmail": true, "canUpload": true, "canStripe": true, "canStripeWebhook": true, "canRealTime": true, "canAI": true, "canUpdateEmail": true, "canUpdatePassword": true, "canUpdateOtherUsers": false, "treeql": {"enabled": true, "models": {"preference": {"allowed": true, "blacklistedFields": [], "operations": {"get": true, "getOne": true, "getAll": false, "post": true, "put": true, "delete": true, "paginate": true, "join": true}}}}, "companyScoped": false}}, {"id": "role_1754942296768_yf6d1ezwu", "name": "drone_engineer", "slug": "drone-engineer", "permissions": {"routes": [], "canCreateUsers": false, "canEditUsers": false, "canDeleteUsers": false, "canManageRoles": false, "canLogin": true, "canRegister": true, "canForgot": false, "canReset": false, "canGoogleLogin": true, "canAppleLogin": true, "canMicrosoftLogin": true, "canMagicLinkLogin": false, "canTwitterLogin": false, "needs2FA": false, "canSetPermissions": false, "canPreference": false, "canVerifyEmail": true, "canUpload": true, "canStripe": true, "canStripeWebhook": true, "canRealTime": true, "canAI": true, "canUpdateEmail": true, "canUpdatePassword": true, "canUpdateOtherUsers": false, "treeql": {"enabled": true, "models": {"user": {"allowed": true, "blacklistedFields": [], "operations": {"get": true, "getOne": true, "getAll": true, "post": false, "put": true, "delete": true, "paginate": false, "join": true}}}}}}], "routes": []}