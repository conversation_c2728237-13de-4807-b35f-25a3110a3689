const moment = require('moment-timezone');

const generateAvailableSlots = (events, startDate, endDate, businessHours, slotDuration = 60) => {
    const slots = [];
    const currentDate = new Date(startDate);
    const endDateObj = new Date(endDate);
    console.log(`Generating slots from ${currentDate.toISOString()} to ${endDateObj.toISOString()}`);

    console.log(`Generating slots from ${currentDate.toISOString()} to ${endDateObj.toISOString()}`);

    while (currentDate <= endDateObj) {
        const dayOfWeek = currentDate.getDay(); // 0 = Sunday, 1 = Monday, etc.
        const dayName = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'][dayOfWeek];

        console.log(`Checking day: ${dayName}`,businessHours[dayName]);
        
        // Check if this day has business hours
        if (businessHours[dayName] && businessHours[dayName].enabled) {
        console.log(`Generating slots for ${dayName} (${currentDate.toISOString().split('T')[0]})`);
        const hours = businessHours[dayName]?.timeSlots ? businessHours[dayName].timeSlots[0] : null;
        const daySlots = generateDaySlots(
            new Date(currentDate), 
            hours, 
            events, 
            slotDuration
        );
        console.log(`Generated ${daySlots.length} slots for ${dayName}`);
        slots.push(...daySlots);
        } else {
        console.log(`Skipping ${dayName} - not enabled in business hours`);
        }
        
        // Move to next day
        currentDate.setDate(currentDate.getDate() + 1);
    }

    return slots;
};


const generateDaySlots = (date, dayHours, events, slotDuration = 60, timezone = 'America/New_York') => {
  const slots = [];
  
  // Create a new date object for the current date to avoid modifying the original
  const dateStr = new Date(date).toISOString().split('T')[0];
  
  // Set business hours for the day
  const [startHour, startMinute] = dayHours.start.split(':').map(Number);
  const [endHour, endMinute] = dayHours.end.split(':').map(Number);
  
  // Create proper start and end time objects for this specific date
  const startTime = new Date(`${dateStr}T${String(startHour).padStart(2, '0')}:${String(startMinute).padStart(2, '0')}:00`);
  const endTime = new Date(`${dateStr}T${String(endHour).padStart(2, '0')}:${String(endMinute).padStart(2, '0')}:00`);
  
  console.log(`Generating slots for ${dateStr} from ${startHour}:${startMinute} to ${endHour}:${endMinute}`);
  console.log(`Start time: ${startTime.toISOString()}, End time: ${endTime.toISOString()}`);
  
  // Generate slots for the day
  const current = new Date(startTime);
  
  // Debug the starting point
  console.log(`Starting slot generation at: ${current.toISOString()}`);
  
  while (current < endTime) {
    const slotEnd = new Date(current);
    slotEnd.setMinutes(slotEnd.getMinutes() + slotDuration);
    
    // Ensure we don't go past business hours
    if (slotEnd > endTime) {
      console.log(`Slot would end after business hours (${slotEnd.toISOString()} > ${endTime.toISOString()}), skipping`);
      break;
    }
    
    // Check if this slot conflicts with existing events
    const hasConflict = events.some(event => {
      // Extract event dates, ensuring they're proper Date objects
      let eventStart, eventEnd;
      
      try {
        eventStart = new Date(event.start.dateTime || event.start.date);
        eventEnd = new Date(event.end.dateTime || event.end.date);
      } catch (e) {
        console.error("Error parsing event dates:", e);
        return false;
      }
      
      // Get the date part only (without time) for comparison
      const eventDate = eventStart.toISOString().split('T')[0];
      const slotDate = current.toISOString().split('T')[0];
      
      // Only check events on the same day as the slot
      if (eventDate !== slotDate) {
        return false;
      }
      
      // Debug log for events on the same day
      console.log(`Checking same-day event: ${eventStart.toISOString()} - ${eventEnd.toISOString()}`);
      console.log(`Against slot: ${current.toISOString()} - ${slotEnd.toISOString()}`);
      
      // A conflict exists if:
      // 1. Event starts during the slot (event start is between slot start and end)
      // 2. Event ends during the slot (event end is between slot start and end)
      // 3. Event completely encompasses the slot (event start ≤ slot start AND event end ≥ slot end)
      const eventStartsDuringSlot = eventStart >= current && eventStart < slotEnd;
      const eventEndsDuringSlot = eventEnd > current && eventEnd <= slotEnd;
      const eventEncompassesSlot = eventStart <= current && eventEnd >= slotEnd;
      
      const conflict = eventStartsDuringSlot || eventEndsDuringSlot || eventEncompassesSlot;
      if (conflict) {
        console.log(`Conflict found with event from ${eventStart.toISOString()} to ${eventEnd.toISOString()}`);
      }
      
      return conflict;
    });
    
    if (!hasConflict) {
      slots.push({
        start: current.toISOString(),
        end: slotEnd.toISOString(),
        available: true,
        date: current.toISOString().split('T')[0],
        time: moment(current).tz(timezone).format('h:mm A')
      });
    }
    
    // Move to the next slot
    current.setMinutes(current.getMinutes() + slotDuration);
  }
  
  console.log(`Generated ${slots.length} available slots for ${dateStr}`);
  return slots;
};

module.exports = {
    generateAvailableSlots
}