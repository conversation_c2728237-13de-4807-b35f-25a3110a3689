const BaseModel = require('../../../baas/core/BaseModel');

class engineer_profile extends BaseModel {
  static schema() {
    return [
      { name: 'id', type: 'primary key', validation: [], defaultValue: null, mapping: null },
      { name: 'user_id', type: 'integer', validation: 'required', defaultValue: null, mapping: null },
      { name: 'availability', type: 'json', validation: [], defaultValue: null, mapping: null },
      { name: 'regions', type: 'json', validation: [], defaultValue: null, mapping: null },
      { name: 'workload_limit', type: 'integer', validation: [], defaultValue: null, mapping: null },
      { name: 'status', type: 'string', validation: 'enum:active,inactive', defaultValue: 'active', mapping: null },
      { name: 'created_at', type: 'timestamp', validation: 'date', defaultValue: 'CURRENT_TIMESTAMP', mapping: null },
      { name: 'updated_at', type: 'timestamp', validation: 'date', defaultValue: 'CURRENT_TIMESTAMP', mapping: null },
    ];
  }
}

module.exports = engineer_profile;


