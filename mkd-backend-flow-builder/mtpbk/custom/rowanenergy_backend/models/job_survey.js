const BaseModel = require('../../../baas/core/BaseModel');

class job_survey extends BaseModel {
  static schema() {
    return [
      { name: 'id', type: 'primary key', validation: [], defaultValue: null, mapping: null },
      { name: 'customer_id', type: 'integer', validation: 'required', defaultValue: null, mapping: null },
      { name: 'engineer_id', type: 'integer', validation: [], defaultValue: null, mapping: null },
      { name: 'site_name', type: 'string', validation: 'required', defaultValue: null, mapping: null },
      { name: 'location', type: 'string', validation: 'required', defaultValue: null, mapping: null },
      { name: 'asset_type', type: 'string', validation: 'enum:solar,wind,custom', defaultValue: 'solar', mapping: null },
      { name: 'scheduled_time', type: 'datetime', validation: [], defaultValue: null, mapping: null },
      { name: 'status', type: 'string', validation: 'enum:pending,scheduled,in_progress,completed,cancelled,revisit_needed', defaultValue: 'pending', mapping: null },
      { name: 'system_size', type: 'string', validation: [], defaultValue: null, mapping: null },
      { name: 'access_instructions', type: 'text', validation: [], defaultValue: null, mapping: null },
      { name: 'contact_name', type: 'string', validation: [], defaultValue: null, mapping: null },
      { name: 'contact_phone', type: 'string', validation: [], defaultValue: null, mapping: null },
      { name: 'survey_purpose', type: 'json', validation: [], defaultValue: null, mapping: null },
      { name: 'additional_notes', type: 'text', validation: [], defaultValue: null, mapping: null },
      { name: 'internal_notes', type: 'text', validation: [], defaultValue: null, mapping: null },
      { name: 'customer_summary', type: 'text', validation: [], defaultValue: null, mapping: null },
      { name: 'created_at', type: 'timestamp', validation: 'date', defaultValue: 'CURRENT_TIMESTAMP', mapping: null },
      { name: 'updated_at', type: 'timestamp', validation: 'date', defaultValue: 'CURRENT_TIMESTAMP', mapping: null },
    ];
  }
}

module.exports = job_survey;


