const BaseModel = require('../../../baas/core/BaseModel');

class report extends BaseModel {
  static schema() {
    return [
      { name: 'id', type: 'primary key', validation: [], defaultValue: null, mapping: null },
      { name: 'job_id', type: 'integer', validation: 'required', defaultValue: null, mapping: null },
      { name: 'thermal_images', type: 'json', validation: [], defaultValue: null, mapping: null },
      { name: 'rgb_photos', type: 'json', validation: [], defaultValue: null, mapping: null },
      { name: 'video_footage', type: 'json', validation: [], defaultValue: null, mapping: null },
      { name: 'flight_logs', type: 'json', validation: [], defaultValue: null, mapping: null },
      { name: 'ai_analysis', type: 'text', validation: [], defaultValue: null, mapping: null },
      { name: 'compliance_docs', type: 'text', validation: [], defaultValue: null, mapping: null },
      { name: 'internal_notes', type: 'text', validation: [], defaultValue: null, mapping: null },
      { name: 'customer_summary', type: 'text', validation: [], defaultValue: null, mapping: null },
      { name: 'uploaded_at', type: 'datetime', validation: [], defaultValue: null, mapping: null },
      { name: 'created_at', type: 'timestamp', validation: 'date', defaultValue: 'CURRENT_TIMESTAMP', mapping: null },
      { name: 'updated_at', type: 'timestamp', validation: 'date', defaultValue: 'CURRENT_TIMESTAMP', mapping: null },
    ];
  }
}

module.exports = report;


