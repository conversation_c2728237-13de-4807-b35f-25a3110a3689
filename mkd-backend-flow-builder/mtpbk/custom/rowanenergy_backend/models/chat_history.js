const { DataTypes } = require('sequelize');

module.exports = function(sequelize) {
  const ChatHistory = sequelize.define('rowanenergy_chat_history', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    user_id: {
      type: DataTypes.INTEGER,
      allowNull: true, // null for anonymous users
      comment: 'User ID if logged in, null for anonymous users'
    },
    session_id: {
      type: DataTypes.STRING,
      allowNull: false,
      comment: 'Temporary session ID for anonymous users or logged in users'
    },
    chat_type: {
      type: DataTypes.ENUM('booking_assistant', 'survey_analysis'),
      allowNull: false,
      comment: 'Type of chat conversation'
    },
    role: {
      type: DataTypes.ENUM('user', 'assistant'),
      allowNull: false,
      comment: 'Message sender role'
    },
    content: {
      type: DataTypes.TEXT,
      allowNull: false,
      comment: 'Message content'
    },
    metadata: {
      type: DataTypes.JSON,
      allowNull: true,
      comment: 'Additional metadata like job_id, site_type selections, etc.'
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    },
    updated_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    }
  }, {
    tableName: 'rowanenergy_chat_history',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      {
        fields: ['user_id']
      },
      {
        fields: ['session_id']
      },
      {
        fields: ['chat_type']
      },
      {
        fields: ['created_at']
      }
    ]
  });

  return ChatHistory;
};
