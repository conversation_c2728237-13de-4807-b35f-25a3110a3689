const BaseModel = require('../../../baas/core/BaseModel');

class checklist extends BaseModel {
  static schema() {
    return [
      { name: 'id', type: 'primary key', validation: [], defaultValue: null, mapping: null },
      { name: 'job_id', type: 'integer', validation: 'required', defaultValue: null, mapping: null },
      { name: 'equipment_checklist', type: 'json', validation: [], defaultValue: null, mapping: null },
      { name: 'risk_assessment', type: 'json', validation: [], defaultValue: null, mapping: null },
      { name: 'risk_mitigation_plan', type: 'text', validation: [], defaultValue: null, mapping: null },
      { name: 'required_uploads', type: 'json', validation: [], defaultValue: null, mapping: null },
      { name: 'status', type: 'string', validation: 'enum:pending,in_progress,completed', defaultValue: 'pending', mapping: null },
      { name: 'last_updated', type: 'datetime', validation: [], defaultValue: null, mapping: null },
      { name: 'created_at', type: 'timestamp', validation: 'date', defaultValue: 'CURRENT_TIMESTAMP', mapping: null },
      { name: 'updated_at', type: 'timestamp', validation: 'date', defaultValue: 'CURRENT_TIMESTAMP', mapping: null },
    ];
  }
}

module.exports = checklist;


