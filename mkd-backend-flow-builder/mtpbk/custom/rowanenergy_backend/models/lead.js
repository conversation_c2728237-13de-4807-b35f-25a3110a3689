const BaseModel = require('../../../baas/core/BaseModel');

class lead extends BaseModel {
  static schema() {
    return [
      { name: 'id', type: 'primary key', validation: [], defaultValue: null, mapping: null },
      { name: 'name', type: 'string', validation: 'required', defaultValue: null, mapping: null },
      { name: 'email', type: 'string', validation: 'required,email', defaultValue: null, mapping: null },
      { name: 'phone', type: 'string', validation: [], defaultValue: null, mapping: null },
      { name: 'site_address', type: 'string', validation: [], defaultValue: null, mapping: null },
      { name: 'asset_type', type: 'string', validation: [], defaultValue: null, mapping: null },
      { name: 'site_details', type: 'text', validation: [], defaultValue: null, mapping: null },
      { name: 'source', type: 'string', validation: 'enum:chatbot,form,phone', defaultValue: 'form', mapping: null },
      { name: 'status', type: 'string', validation: 'enum:new,qualified,converted,abandoned', defaultValue: 'new', mapping: null },
      { name: 'region', type: 'string', validation: [], defaultValue: null, mapping: null },
      { name: 'job_id', type: 'integer', validation: [], defaultValue: null, mapping: null },
      { name: 'follow_up_needed', type: 'boolean', validation: [], defaultValue: '1', mapping: null },
      { name: 'created_at', type: 'timestamp', validation: 'date', defaultValue: 'CURRENT_TIMESTAMP', mapping: null },
      { name: 'updated_at', type: 'timestamp', validation: 'date', defaultValue: 'CURRENT_TIMESTAMP', mapping: null },
    ];
  }
}

module.exports = lead;


