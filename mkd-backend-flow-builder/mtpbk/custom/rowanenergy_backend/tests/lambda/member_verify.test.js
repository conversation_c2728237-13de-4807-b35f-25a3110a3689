const APITestFramework = require("../../../../tests/apitesting.base.js");

const BASE_URL = "http://localhost:5172";

/**
 * Verify API Tests
 * Class-based implementation of the Verify API tests
 */
class VerifyTests {
  constructor() {
    this.framework = new APITestFramework();
    this.baseUrl = BASE_URL;
    this.setupTests();
  }

  setupTests() {
    this.framework.describe("member Verify API Tests", () => {
      // Test case for verify
      this.framework.addTestCase("member Verify API - Success", async () => {
        const response = await this.framework.makeRequest(
          `${this.baseUrl}/v1/api/rowanenergy/member/lambda/verify`,
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              token: `abc123xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx`,
            }),
          }
        );

        // Assertions for the health check
        this.framework.assert(
          response.status === 200,
          "Verify member should return 200 status"
        );

        this.framework.assert(
          response.body.error === false,
          "Verify member error flag should be false"
        );
      });

      // Add more test cases here as needed
      this.framework.addTestCase("member Verify API - Invalid Token", async () => {
        const response = await this.framework.makeRequest(
          `${this.baseUrl}/v1/api/rowanenergy/member/lambda/verify`,
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              token: "invalid-token",
            }),
          }
        );

        // Assertions for invalid token
        this.framework.assert(
          response.status === 400,
          "Verify with invalid token should return 400 status"
        );

        this.framework.assert(
          response.body.error === true,
          "Verify with invalid token should have error flag set to true"
        );
      });
    });
  }

  // Helper method to run all tests
  async runTests() {
    try {
      // Run the tests and return the results directly
      return await this.framework.runTests();
    } catch (error) {
      console.error("Test execution failed:", error);
      throw error;
    }
  }
}

// Create an instance of the test class and run the tests
const tests = new VerifyTests();
module.exports = tests
  .runTests()
  .then((report) => {
    if (report.failed > 0) {
      process.exit(1);
    }
    return report; // Important: return the report for the test runner
  })
  .catch((error) => {
    console.error("Test framework error:", error);
    process.exit(1);
  });
