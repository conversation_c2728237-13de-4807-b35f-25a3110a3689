const APITestFramework = require("../../../../tests/apitesting.base.js");

const BASE_URL = "http://localhost:5172";

/**
 * TreeQL API Tests
 * Class-based implementation of the TreeQL API tests
 */
class TreeQLTests {
  constructor() {
    this.framework = new APITestFramework();
    
    // Define expected response schemas
    this.treeqlListSchema = {
      error: false,
      list: "object",
      total: "number",
      limit: "number",
      num_pages: "number",
      page: "number"
    };
    
    this.treeqlItemSchema = {
      error: false,
      model: "object"
    };
    
    this.successResponseSchema = {
      error: false,
      message: "string"
    };
    
    this.setupTests();
  }

  setupTests() {
    this.framework.describe("super_admin TreeQL API Tests", () => {
      let authToken;

      // Setup before each test
      this.framework.beforeEach(async () => {
        // Setup mock data
        authToken = "Bearer mock_auth_token";
      });

      // Test case for Get Records
      this.framework.addTestCase("super_admin Get Records - Success Path", async () => {
        // Create spy to track request
        const requestSpy = this.framework.createSpy(this.framework, "makeRequest");

        // Mock the API response
        this.framework.mockRequest(
          `${BASE_URL}/v1/api/lambda/treeql/users?limit=10&page=1`,
          {
            error: false,
            list: [
              {
                id: 1,
                email: "<EMAIL>",
                first_name: "John",
                last_name: "Doe",
                created_at: "2023-01-01T00:00:00.000Z"
              }
            ],
            total: 1,
            limit: 10,
            num_pages: 1,
            page: 1
          },
          {
            status: 200,
            headers: {
              "Content-Type": "application/json"
            }
          }
        );

        const response = await this.framework.makeRequest(
          `${BASE_URL}/v1/api/lambda/treeql/users?limit=10&page=1`,
          {
            method: "GET",
            headers: {
              "Content-Type": "application/json",
              Authorization: authToken
            }
          }
        );

        // Assertions
        this.framework.assert(
          response.status === 200,
          "Get Records should return 200 status"
        );
        this.framework.assert(
          response.body.error === false,
          "Get Records error flag should be false"
        );

        // Enhanced assertions
        this.framework.assertions.assertResponseValid(response, this.treeqlListSchema);
        this.framework.assertions.assertEquals(
          response.body.total,
          1,
          "Should return correct total count"
        );

        // Verify request was made correctly
        this.framework.assert(
          requestSpy.callCount() === 1,
          "API should be called exactly once"
        );
      });

      // Test case for Get Record by ID
      this.framework.addTestCase("super_admin Get Record by ID - Success Path", async () => {
        // Mock the API response
        this.framework.mockRequest(
          `${BASE_URL}/v1/api/lambda/treeql/users/1`,
          {
            error: false,
            model: {
              id: 1,
              email: "<EMAIL>",
              first_name: "John",
              last_name: "Doe",
              created_at: "2023-01-01T00:00:00.000Z"
            }
          },
          {
            status: 200,
            headers: {
              "Content-Type": "application/json"
            }
          }
        );

        const response = await this.framework.makeRequest(
          `${BASE_URL}/v1/api/lambda/treeql/users/1`,
          {
            method: "GET",
            headers: {
              "Content-Type": "application/json",
              Authorization: authToken
            }
          }
        );

        // Assertions
        this.framework.assert(
          response.status === 200,
          "Get Record by ID should return 200 status"
        );
        this.framework.assert(
          response.body.error === false,
          "Get Record by ID error flag should be false"
        );

        // Enhanced assertions
        this.framework.assertions.assertResponseValid(response, this.treeqlItemSchema);
        this.framework.assertions.assertEquals(
          response.body.model.id,
          1,
          "Should return correct record ID"
        );
      });

      // Test case for Get Record by ID - Not Found
      this.framework.addTestCase("super_admin Get Record by ID - Not Found", async () => {
        // Mock the API response for error case
        this.framework.mockRequest(
          `${BASE_URL}/v1/api/lambda/treeql/users/999`,
          {
            error: true,
            message: "Record not found"
          },
          {
            status: 404,
            headers: {
              "Content-Type": "application/json"
            }
          }
        );

        const response = await this.framework.makeRequest(
          `${BASE_URL}/v1/api/lambda/treeql/users/999`,
          {
            method: "GET",
            headers: {
              "Content-Type": "application/json",
              Authorization: authToken
            }
          }
        );

        // Assertions for error case
        this.framework.assertions.assertEquals(
          response.status,
          404,
          "Should return 404 status for record not found"
        );
        this.framework.assertions.assertEquals(
          response.body.error,
          true,
          "Error flag should be true"
        );
        this.framework.assertions.assertEquals(
          response.body.message,
          "Record not found",
          "Should return correct error message"
        );
      });

      // Test case for Create Record
      this.framework.addTestCase("super_admin Create Record - Success Path", async () => {
        // Mock the API response
        this.framework.mockRequest(
          `${BASE_URL}/v1/api/lambda/treeql/users`,
          {
            error: false,
            model: {
              id: 2,
              email: "<EMAIL>",
              first_name: "Jane",
              last_name: "Smith",
              created_at: "2023-01-02T00:00:00.000Z"
            },
            message: "Record created successfully"
          },
          {
            status: 201,
            headers: {
              "Content-Type": "application/json"
            }
          }
        );

        const response = await this.framework.makeRequest(
          `${BASE_URL}/v1/api/lambda/treeql/users`,
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              Authorization: authToken
            },
            body: JSON.stringify({
              email: "<EMAIL>",
              first_name: "Jane",
              last_name: "Smith",
              password: "password123"
            })
          }
        );

        // Assertions
        this.framework.assert(
          response.status === 201,
          "Create Record should return 201 status"
        );
        this.framework.assert(
          response.body.error === false,
          "Create Record error flag should be false"
        );

        // Enhanced assertions
        this.framework.assertions.assertEquals(
          response.body.message,
          "Record created successfully",
          "Should return success message"
        );
        this.framework.assertions.assertEquals(
          response.body.model.email,
          "<EMAIL>",
          "Should return created record email"
        );
      });

      // Test case for Update Record
      this.framework.addTestCase("super_admin Update Record - Success Path", async () => {
        // Mock the API response
        this.framework.mockRequest(
          `${BASE_URL}/v1/api/lambda/treeql/users/1`,
          {
            error: false,
            model: {
              id: 1,
              email: "<EMAIL>",
              first_name: "John",
              last_name: "Updated",
              created_at: "2023-01-01T00:00:00.000Z"
            },
            message: "Record updated successfully"
          },
          {
            status: 200,
            headers: {
              "Content-Type": "application/json"
            }
          }
        );

        const response = await this.framework.makeRequest(
          `${BASE_URL}/v1/api/lambda/treeql/users/1`,
          {
            method: "PUT",
            headers: {
              "Content-Type": "application/json",
              Authorization: authToken
            },
            body: JSON.stringify({
              email: "<EMAIL>",
              last_name: "Updated"
            })
          }
        );

        // Assertions
        this.framework.assert(
          response.status === 200,
          "Update Record should return 200 status"
        );
        this.framework.assert(
          response.body.error === false,
          "Update Record error flag should be false"
        );

        // Enhanced assertions
        this.framework.assertions.assertEquals(
          response.body.message,
          "Record updated successfully",
          "Should return success message"
        );
        this.framework.assertions.assertEquals(
          response.body.model.email,
          "<EMAIL>",
          "Should return updated email"
        );
      });

      // Test case for Delete Record
      this.framework.addTestCase("super_admin Delete Record - Success Path", async () => {
        // Mock the API response
        this.framework.mockRequest(
          `${BASE_URL}/v1/api/lambda/treeql/users/1`,
          {
            error: false,
            message: "Record deleted successfully"
          },
          {
            status: 200,
            headers: {
              "Content-Type": "application/json"
            }
          }
        );

        const response = await this.framework.makeRequest(
          `${BASE_URL}/v1/api/lambda/treeql/users/1`,
          {
            method: "DELETE",
            headers: {
              "Content-Type": "application/json",
              Authorization: authToken
            }
          }
        );

        // Assertions
        this.framework.assert(
          response.status === 200,
          "Delete Record should return 200 status"
        );
        this.framework.assert(
          response.body.error === false,
          "Delete Record error flag should be false"
        );

        // Enhanced assertions
        this.framework.assertions.assertResponseValid(response, this.successResponseSchema);
        this.framework.assertions.assertEquals(
          response.body.message,
          "Record deleted successfully",
          "Should return success message"
        );
      });

      // Test case for Query Records
      this.framework.addTestCase("super_admin Query Records - Success Path", async () => {
        // Mock the API response
        this.framework.mockRequest(
          `${BASE_URL}/v1/api/lambda/treeql/users/query`,
          {
            error: false,
            list: [
              {
                id: 1,
                email: "<EMAIL>",
                first_name: "John",
                last_name: "Doe"
              }
            ],
            total: 1,
            limit: 10,
            num_pages: 1,
            page: 1
          },
          {
            status: 200,
            headers: {
              "Content-Type": "application/json"
            }
          }
        );

        const response = await this.framework.makeRequest(
          `${BASE_URL}/v1/api/lambda/treeql/users/query`,
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              Authorization: authToken
            },
            body: JSON.stringify({
              where: {
                last_name: "Doe"
              },
              limit: 10,
              page: 1
            })
          }
        );

        // Assertions
        this.framework.assert(
          response.status === 200,
          "Query Records should return 200 status"
        );
        this.framework.assert(
          response.body.error === false,
          "Query Records error flag should be false"
        );

        // Enhanced assertions
        this.framework.assertions.assertResponseValid(response, this.treeqlListSchema);
        this.framework.assertions.assertEquals(
          response.body.total,
          1,
          "Should return correct total count"
        );
      });
    });
  }

  async runTests() {
    try {
      // Run the tests and return the results directly
      return await this.framework.runTests();
    } catch (error) {
      console.error("Test execution failed:", error);
      throw error;
    }
  }
}


// Create an instance of the test class and run the tests
const tests = new TreeQLTests();
module.exports = tests
  .runTests()
  .then((report) => {
    if (report.failed > 0) {
      process.exit(1);
    }
    return report; // Important: return the report for the test runner
  })
  .catch((error) => {
    console.error("Test framework error:", error);
    process.exit(1);
  });
