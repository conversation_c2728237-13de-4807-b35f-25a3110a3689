const JwtService = require("../../../baas/services/JwtService");
const StripeService = require("../../../baas/services/StripeService");
const AuthService = require("../../../baas/services/AuthService");
const TokenMiddleware = require("../../../baas/middleware/TokenMiddleware");

const SyncStripeWebhook = require("../../../baas/middleware/SyncStripeWebhook");

const {
  sqlDateFormat,
  sqlDateTimeFormat,
  filterEmptyFields,
} = require("../../../baas/services/UtilService");

const middlewares = [
  SyncStripeWebhook,

];

module.exports = function (app) {
  const config = app.get("configuration");
  const stripe = new StripeService();
  function jsonExtractor(object, property) {
    return `json_unquote(json_extract(${object}, '$.${property}'))`;
  }

  app.use((req, res, next) => {
    req.config = app.get("configuration");
    req.sdk = app.get("sdk");
    req.projectId = "";

    const RoleClass = require(`../roles/super_admin`);
    if (!RoleClass.permissions.canStripe) {
      return res.status(403).json({
        error: true,
        message: `${RoleClass.name} cannot access stripe`,
      });
    }
    next();
  });

  /**
   * products
   * get all
   * get one
   * create one
   * update one
   * archive one //TODO
   */
  app.get(
    "/v1/api/lambda/stripe/products",
    middlewares,
    TokenMiddleware(),
    async function (req, res) {
      try {

        const RoleClass = require(`../roles/super_admin`);
        if (!RoleClass.permissions.canStripe) {
          return res.status(403).json({
            error: true,
            message: `${RoleClass.name} cannot access stripe`,
          });
        }

        const sdk = app.get("sdk");
        let { limit = 10, page = 1, name, stripe_id, status } = req.query;
        /**
         * get products
         */
        sdk.setProjectId("");
        sdk.setTable("stripe_product");

        let fields = filterEmptyFields({ name, stripe_id, status });
        let where = [];
        let whereObj = {};
        Object.entries(fields).forEach(([key, value]) => {
          if (key === "status") {
            where.push(`${key} = ${+value}`);
            whereObj[key] = +value;
          } else {
            where.push(`${key} LIKE '%${value}%'`);
            whereObj[key] = `%${value}%`;
          }
        });

        if (limit === "all") {
          const resource = await sdk.rawQuery(
            `SELECT * from ${sdk.getTable()} WHERE ${
              where.length ? where.join(" AND ") : 1
            }`
          );
          return res.status(200).json({ error: false, list: resource });
        }
        const total = await sdk.count("stripe_product", whereObj);
        const num_pages = Math.ceil(+total / +limit);
        const resource = await sdk.paginateStr("stripe_product", whereObj, "*", +page, +limit);

        res.status(200).json({
          error: false,
          list: resource,
          total,
          limit: +limit,
          num_pages,
          page: +page,
        });
      } catch (err) {
        console.error(err);
        res.status(500).json({
          error: true,
          trace: err,
          message: err.message ?? "Something went wrong",
        });
      }
    }
  );
  
  app.get(
    "/v1/api/lambda/stripe/product/:id",
    middlewares,
    TokenMiddleware(),
    async function (req, res) {
      try {
        /**
         * get a product
         */
        const sdk = app.get("sdk");

        const RoleClass = require(`../roles/super_admin`);
        if (!RoleClass.permissions.canStripe) {
          return res.status(403).json({
            error: true,
            message: `${RoleClass.name} cannot access stripe`,
          });
        }

        const { id } = req.params;
        if (!id) {
          return res
            .status(400)
            .json({ error: true, message: "Product id is missing" });
        }

        sdk.setProjectId("");
        sdk.setTable("stripe_product");

        const product = await sdk.findOne("stripe_product", { id: id });
        if (!product)
          return res
            .status(404)
            .json({ error: true, message: "Product not found" });

        return res.status(200).json({ error: false, model: product });
      } catch (err) {
        res.status(500).json({
          error: true,
          trace: err,
          message: err.message ?? "Something went wrong",
        });
      }
    }
  );

  app.post(
    "/v1/api/lambda/stripe/product",
    middlewares,
    TokenMiddleware(),
    async function (req, res) {
      try {
        /**
         * create product
         */
        const sdk = app.get("sdk");
      
        const { name, description } = req.body;
       

        if (!name) {
          return res.status(400).json({ error: true, message: "Name is required" });
        }

        sdk.setProjectId(req.projectId);
        sdk.setTable("stripe_product");

        const product = await stripe.createStripeProduct({
          name: name,
          description: description,
          metadata: {
            projectId: sdk.getProjectId(),
          },
        });

        const data = await sdk.create("stripe_product", {
          stripe_id: product.id,
          name: name,
          object: JSON.stringify(product),
          status: 1,
          created_at: sqlDateFormat(new Date()),
          updated_at: sqlDateTimeFormat(new Date()),
        });

        res.status(200).json({
          error: false,
          model: data,
          message: "Product created successfully",
        });
      } catch (err) {
        console.error(err);
        let payload = {
          error: true,
          trace: err,
          message: err.message ?? "Something went wrong",
        };
        res.status(500).json(payload);
      }
    }
  );

  app.put(
    "/v1/api/lambda/stripe/product/:id",
    middlewares,
    TokenMiddleware(),
    async function (req, res) {
      try {
        /**
         * update product
         */
        const sdk = req.sdk;
        const { id } = req.params;
        if (!id) {
          return res.status(400).json({
            error: true,
            message: "Parameter/s missing",
            validation: { id: "Id is required" },
          });
        }
        const { name, description, status } = req.body;

        sdk.setProjectId(req.projectId);
        sdk.setTable("stripe_product");

        const productExists = await sdk.findOne("stripe_product", { id: id });
        if (!productExists)
          return res
            .status(404)
            .json({ error: true, message: "Product not found" });

        const product = await stripe.updateStripeProduct(
          productExists.stripe_id,
          {
            name,
            description,
            active: status,
          }
        );

        await sdk.updateById(
          "stripe_product",
          productExists.id,
          {
            name,
            object: JSON.stringify(product),
            status: status,
          }
        );

        res.status(200).json({
          error: false,
          message: "Product updated successfully",
        });
      } catch (err) {
        console.error(err);
        let payload = {
          error: true,
          trace: err,
          message: err.message ?? "Something went wrong",
        };
        res.status(500).json(payload);
      }
    }
  );

   
  app.delete(
    "/v1/api/lambda/stripe/product/:id",
    middlewares,
    TokenMiddleware(),
    async function (req, res) {
      try {
        /**
         * delete product
         */
        const sdk = req.sdk;
        const { id } = req.params;
        if (!id) {
          return res.status(400).json({
            error: true,
            message: "Parameter/s missing",
            validation: { id: "Id is required" },
          });
        }

        sdk.setProjectId(req.projectId);

        // check if product exists
        sdk.setTable("stripe_product");
        const productExists = await sdk.findOne("stripe_product", { id });
        if (!productExists)
          return res
            .status(404)
            .json({ error: true, message: "Product not found" });

        // check if price exists for this product
        sdk.setTable("stripe_price");
        const priceExists = await sdk.findOne("stripe_price", { id });
        if (priceExists)
          return res.status(400).json({
            error: true,
            message: "Cannot delete! Price exists at db for this product.",
          });

        const product = await stripe.deleteStripeProduct({
          productId: productExists.stripe_id,
        });

        await sdk.updateById(
          "stripe_product",
          productExists.id,
          {
            object: JSON.stringify(product),
            status: 0,
          }
        );

        res.status(200).json({
          error: false,
          message: "Product deleted from stripe and set to inactive at db.",
        });
      } catch (err) {
        console.error(err);
        let payload = {
          error: true,
          trace: err,
          message: err.message ?? "Something went wrong",
        };
        res.status(500).json(payload);
      }
    }
  );


   /**
   * prices
   * get all
   * get one
   * create one
   * update one
   * archive one //TODO
   */

   app.get(
    "/v1/api/lambda/stripe/prices",
    middlewares,
    TokenMiddleware(),
    async function (req, res) {
      try {
        /**
         * get plans/prices
         */
        const sdk = req.sdk;
        let {
          limit = 10,
          page = 1,
          stripe_id,
          product_name,
          status,
          type,
          name,
          amount,
          order_by,
          direction,
        } = req.query;

        sdk.setProjectId(req.projectId);

        const priceTable = `${sdk.getProjectId()}_stripe_price`;
        const productTable = `${sdk.getProjectId()}_stripe_product`;
        let query = filterEmptyFields({
          stripe_id,
          product_name,
          status,
          type,
          name,
          amount,
        });

        let where = [];
        let whereObj = {};
        Object.entries(query)?.forEach(([key, value]) => {
          switch (key) {
            case "stripe_id": {
              where.push(`price.stripe_id LIKE '%${value}%'`);
              whereObj[key] = `%${value}%`;
              break;
            }
            case "product_name": {
              where.push(`product.name LIKE '%${value}%'`);
              whereObj[key] = `%${value}%`;
              break;
            }
            case "status": {
              where.push(`price.status = ${+value}`);
              whereObj[key] = +value;
              break;
            }
            case "type": {
              let finalQuery = [];
              value = value.split(",");
              for (const item of value) {
                finalQuery.push(`price.type = '${item}'`);
              }
              where.push(finalQuery.join(" OR "));
              whereObj[key] = value;
              break;
            }
            case "name": {
              where.push(`price.name = '${value}'`);
              whereObj[key] = value;
              break;
            }
            case "amount": {
              where.push(`price.amount <= ${+value}`);
              whereObj[key] = +value;
              break;
            }
          }
        });

        let sqlQuery = `
        SELECT price.id as id, ${jsonExtractor(
          "price.object",
          "currency"
        )} as currency,
          price.stripe_id, price.name, price.amount, price.type, price.status, price.object,
          product.id as productId, product.name as product_name 
        from ${priceTable} as price 
        LEFT JOIN ${productTable} as product ON price.product_id = product.id 
        WHERE ${where.length ? where.join(" AND ") : 1}
      `;

        if (limit === "all") {
          const resource = await sdk.rawQuery(sqlQuery);
          return res.status(200).json({ error: false, list: resource });
        }

        const [{ count: total }] = await sdk.rawQuery(
          `SELECT COUNT(*) as count from ${priceTable} as price LEFT JOIN ${productTable} as product ON price.product_id = product.id 
        WHERE ${where.length ? where.join(" AND ") : 1}`
        );
        const [...resource] = await sdk.rawQuery(`
        ${sqlQuery}
        LIMIT ${(+page - 1) * +limit}, ${+limit}
      `);

        const num_pages = Math.ceil(+total / +limit);
        res.status(200).json({
          error: false,
          list: resource,
          total,
          limit,
          num_pages,
          page,
        });
      } catch (err) {
        console.error(err);
        let payload = {
          error: true,
          trace: err,
          message: err.message ?? "Something went wrong",
        };
        res.status(500).json(payload);
      }
    }
  );

   
  app.get(
    "/v1/api/lambda/stripe/price/:id",
    middlewares,
    TokenMiddleware(),
    async function (req, res) {
      try {
        /**
         * get plan/price
         */
        const sdk = req.sdk;
        const { id } = req.params;

        sdk.setProjectId(req.projectId);
        sdk.setTable("stripe_price");

        const price = await sdk.findOne("stripe_price", { id: id });

        if (!price) {
          return res
            .status(404)
            .json({ error: true, message: "Price not found" });
        }

        res.status(200).json({ error: false, model: price });
      } catch (err) {
        console.error(err);
        let payload = {
          error: true,
          trace: err,
          message: err.message ?? "Something went wrong",
        };
        res.status(500).json(payload);
      }
    }
  );

  app.post(
    "/v1/api/lambda/stripe/price",
    middlewares,
    TokenMiddleware(),
    async function (req, res) {
      try {
        const sdk = req.sdk;
        /**
         * create plan/price
         * req.body.product_id is the db id of product
         */
        // pay per seat metadata_obj.multitenant = 1
        // pay per usage metadata_obj.multitenant_usage = 1
        let {
          product_id,
          name,
          pay_per_usage,
          pay_per_seat,
          amount,
          type,
          interval = "month",
          interval_count = 1,
          trial_days = 0,
          usage_type,
          usage_limit,
        } = req.body;


        if (!name || !amount || !type || !product_id) {
          return res.status(400).json({ 
            error: true, 
            message: `Parameter ${!name ? "name" : ""} ${!amount ? "amount" : ""} ${!type ? "type" : ""} ${!product_id ? "product_id" : ""} missing` 
          });
        }
        sdk.setProjectId(req.projectId);
        sdk.setTable("stripe_product");

        const product = await sdk.findOne("stripe_product", { id: product_id });

        if (!product) {
          return res
            .status(404)
            .json({ error: true, message: "Product not found" });
        }

        const product_stripe_id = product.stripe_id;

        sdk.setTable("stripe_setting");
        const setting = await sdk.findOne("stripe_setting", { key: "currency" });

        let currency = stripe.getConfig().currency ?? "usd";
        if (setting) currency = setting.value;
        currency = currency.toLowerCase();

        // if type recurring check if it is metered or not
        let price, model;
        sdk.setTable("stripe_price");

        const metadata = {
          projectId: sdk.getProjectId(),
          ...(pay_per_seat && { multitenant: 1 }),
          ...(pay_per_usage && { multitenant_usage: 1 }),
        };

        if (type === "one_time") {
          price = await stripe.createStripeOnetimePrice({
            productId: product_stripe_id,
            name,
            amount,
            currency,
            metadata,
          });

          model = await sdk.create("stripe_price", {
            name: name,
            amount: parseFloat(amount),
            stripe_id: price.id,
            type: "one_time",
            product_id: product.id,
            object: JSON.stringify(price),
            status: 1,
            created_at: sqlDateFormat(new Date()),
            updated_at: sqlDateTimeFormat(new Date()),
          });
        } else if (type === "recurring") {
          if (usage_type === "licenced") {
            price = await stripe.createStripeRecurringPrice({
              productId: product_stripe_id,
              name,
              amount,
              currency,
              interval,
              interval_count,
              trial_days,
              metadata,
            });
          } else if (usage_type === "metered") {
            price = await stripe.createStripeRecurringMeteredPrice({
              productId: product_stripe_id,
              name,
              amount,
              currency,
              usage_limit,
              usage_type,
              interval,
              interval_count,
              trial_days,
              metadata,
            });
          }

          model = await sdk.create("stripe_price", {
            stripe_id: price.id,
            product_id: product.id,
            name: name,
            object: JSON.stringify(price),
            amount: parseFloat(amount),
            type: interval === "lifetime" ? "lifetime" : type,
            is_usage_metered: usage_type === "metered" ? true : false,
            usage_limit:
              type === "one_time"
                ? null
                : !isNaN(+usage_limit) && usage_type === "metered"
                ? +usage_limit
                : null,
            status: 1,
            created_at: sqlDateFormat(new Date()),
            updated_at: sqlDateTimeFormat(new Date()),
          });
        }

        res.status(200).json({
          error: false,
          model: model,
        });
      } catch (err) {
        console.error(err);
        let payload = {
          error: true,
          trace: err,
          message: err.message ?? "Something went wrong",
        };
        return res.status(500).json(payload);
      }
    }
  );

  
  app.put(
    "/v1/api/lambda/stripe/price/:id",
    middlewares,
    TokenMiddleware(),
    async function (req, res) {
      try {
        /**
         * update plan/price
         * handle update later
         */
        const sdk = req.sdk;
        const { id } = req.params;
        if (!id) {
          return res.status(400).json({
            error: true,
            message: "Price id is missing",
            validation: { id: "Price id is missing" },
          });
        }
        const { name, status } = req.body;

        sdk.setProjectId(req.projectId);
        sdk.setTable("stripe_price");

        const price = await sdk.findOne("stripe_price", { id });
        if (!price) {
          return res.status(404).json({
            error: true,
            message: "Price not found",
          });
        }

        const priceUpdated = await stripe.updateStripePrice(filterEmptyFields({
          price_id: price.stripe_id,
          name,
          status,
        }));

        await sdk.updateById(
          "stripe_price",
          price.id,
          {
            name,
            status,
            object: JSON.stringify(priceUpdated),
            updated_at: sqlDateTimeFormat(new Date()),
          }
        );

        res.status(200).json({ error: false, model: price });
      } catch (err) {
        console.error(err);
        let payload = {
          error: true,
          trace: err,
          message: err.message ?? "Something went wrong",
        };
        res.status(500).json(payload);
      }
    }
  );

   
  app.delete(
    "/v1/api/lambda/stripe/price/:id",
    middlewares,
    TokenMiddleware(),
    async function (req, res) {
      try {
        /**
         * delete plan/price
         */
        const sdk = req.sdk;
        const { id } = req.params;
        if (!id) {
          return res.status(403).json({
            error: true,
            message: "Parameter/s missing",
            validation: [{ id: "Price id is missing" }],
          });
        }
        sdk.setProjectId(req.projectId);
        sdk.setTable("stripe_price");
        const price = await sdk.findOne("stripe_price", { id });
        if (!price) {
          return res.status(404).json({
            error: true,
            message: "Price not found",
          });
        }

        const price_stripe_id = price.stripe_id;
        await stripe.deactivateStripePrice({ price_id: price_stripe_id });
        await sdk.updateById(
          "stripe_price",
          price.id,
          { status: 0 }
        );

        res.status(200).json({
          error: false,
          message:
            "Price is deactivated successfully. Now, you can delete the row from the stripe_price table.",
        });
      } catch (err) {
        console.error(err);
        let payload = {
          error: true,
          trace: err,
          message: err.message ?? "Something went wrong",
        };
        res.status(500).json(payload);
      }
    }
  );


    /**
   * customer subscription
   * get
   * create
   * register -> create
   * update
   * cancel
   */
    app.get(
      "/v1/api/lambda/stripe/customer/subscription",
      middlewares,
      TokenMiddleware(),
      async function (req, res) {
        try {
          /**
           * check id exists
           * check customer exists
           * return customer subscription with the plan name and id
           */
          const sdk = req.sdk;
  
          const user_id = req.user_id;
  
          sdk.setProjectId(req.projectId);
          const userTable = `${sdk.getProjectId()}_user`;
          const stripeSubTable = `${sdk.getProjectId()}_stripe_subscription`;
          const stripePriceTable = `${sdk.getProjectId()}_stripe_price`;
  
          const customer = await sdk.rawQuery(`
          SELECT u.*, s.id as subId, p.id AS planId
          FROM ${userTable} AS u LEFT JOIN ${stripeSubTable} AS s ON s.user_id = u.id AND (s.status = 'active' OR s.status = 'trialing') LEFT JOIN ${stripePriceTable} AS p ON s.price_id = p.id WHERE u.id = ${user_id} ;
        `);
  
          return res.status(200).json({
            error: false,
            customer: customer[0],
          });
        } catch (err) {
          console.error(err);
          let payload = {
            error: true,
            trace: err,
            message: err.message ?? "Something went wrong",
          };
          res.status(500).json(payload);
        }
      }
    );

    app.get(
      "/v1/api/lambda/stripe/customer/subscriptions",
      middlewares,
      TokenMiddleware(),
      async function (req, res) {
        try {
          /**
           * get subscriptions
           */
          const sdk = req.sdk;
          let { limit = 10, page = 1 } = req.query;
  
          sdk.setProjectId(req.projectId);
          const user_id = req.user_id;
          const subscriptionTable = `${sdk.getProjectId()}_stripe_subscription`;
          const priceTable = `${sdk.getProjectId()}_stripe_price`;
          const userTable = `${sdk.getProjectId()}_user`;
  
          let query = filterEmptyFields({
            user_id,
          });
  
          let where = [];
          Object.entries(query)?.forEach(([key, value]) => {
            switch (key) {
              case "user_id": {
                where.push(`sub.user_id = ${+value} `);
                break;
              }
            }
          });
  
          let sqlQuery = `
            SELECT sub.id as subId, 
              ${jsonExtractor("sub.object", "created")} as createdAt,
              ${jsonExtractor(
                "sub.object",
                "current_period_start"
              )} as currentPeriodStart,
              ${jsonExtractor(
                "sub.object",
                "current_period_end"
              )} as currentPeriodEnd,
              sub.status as status,
              price.name as planName, price.type as planType, price.amount as planAmount, price.trial_days as trialDays,
              user.email as userEmail
            from ${subscriptionTable} as sub
            LEFT JOIN ${priceTable} as price ON sub.price_id = price.id
            LEFT JOIN ${userTable} as user ON sub.user_id = user.id
            WHERE ${where.length ? where.join(" AND ") : 1}`;
  
          if (limit === "all") {
            const [...resource] = await sdk.rawQuery(sqlQuery);
            return res.status(200).json({ error: false, list: resource });
          }
  
          const [{ count: total }] = await sdk.rawQuery(
            `SELECT COUNT(*) as count from ${subscriptionTable} as sub
            LEFT JOIN ${priceTable} as price ON sub.price_id = price.id        
            LEFT JOIN ${userTable} as user ON sub.user_id = user.id
            WHERE ${where.length ? where.join(" AND ") : 1} `
          );
          const [...resource] = await sdk.rawQuery(
            `${sqlQuery}
            LIMIT ${(+page - 1) * +limit}, ${+limit}
            `
          );
  
          const num_pages = Math.ceil(+total / +limit);
          res.status(200).json({
            error: false,
            list: resource,
            total,
            limit,
            num_pages,
            page,
          });
        } catch (err) {
          console.error(err);
          let payload = {
            error: true,
            trace: err,
            message: err.message ?? "Something went wrong",
          };
          res.status(500).json(payload);
        }
      }
    );

    app.post(
      "/v1/api/lambda/stripe/customer/subscription",
      middlewares,
      TokenMiddleware(),
      async function (req, res) {
        try {
          const sdk = req.sdk;
          const { planId } = req.body;
  
  
          if (!planId) {
            return res.status(400).json({
              error: true,
              message: "Plan id is missing",
            });
          }
  
          sdk.setProjectId(req.projectId);
  
          const userId = req.user_id;
          const userTable = `${sdk.getProjectId()}_user`;
          const stripeSubTable = `${sdk.getProjectId()}_stripe_subscription`;
          const stripePriceTable = `${sdk.getProjectId()}_stripe_price`;
  
          const customer = await sdk.rawQuery(`
          SELECT u.*, s.id as subId, s.stripe_id as subStripeId,
          ${jsonExtractor(
            "s.object",
            "items.data[0].id"
          )} as subItemId, p.id AS planId, p.type as planType
          FROM ${userTable} AS u LEFT JOIN ${stripeSubTable} AS s ON s.user_id = u.id AND (s.status = 'active' OR s.status = 'trialing') 
          LEFT JOIN ${stripePriceTable} AS p ON s.price_id = p.id 
          WHERE u.id = ${userId} ;
        `);
  
          if (!customer[0]) {
            return res
              .status(404)
              .json({ error: true, message: "Customer not found" });
          }
  
          if (customer[0].subId) {
            return res.status(401).json({
              error: true,
              message: "Customer already has an active subscription",
            });
          }
  
          if (customer[0].stripe_uid === null)
            return res.status(404).json({
              error: true,
              message: "Customer Stripe ID not found, please add a card first.",
            });
  
          const stripeCustomer = await stripe.retrieveStripeCustomer({
            customerId: customer[0].stripe_uid,
          });
          console.log(stripeCustomer);
          if (
            !stripeCustomer.default_source &&
            !stripeCustomer.sources?.data?.length &&
            !stripeCustomer.invoice_settings?.default_payment_method
          ) {
            return res.status(403).json({
              error: true,
              message:
                "You don't have a valid card attached, please add one and try again",
            });
          }
  
          sdk.setTable("stripe_price");
          const metadata = {
            projectId: sdk.getProjectId(),
          };
  
          const plan = await sdk.findOne("stripe_price", { id: planId });
          if (!plan) {
            return res
              .status(404)
              .json({ error: true, message: "Plan not found" });
          }
          await stripe.createStripeSubscription({
            customerId: customer[0].stripe_uid,
            priceId: plan.stripe_id,
            default_payment_method:
              stripeCustomer.default_source ||
              stripeCustomer.sources?.data[0]?.id ||
              stripeCustomer.invoice_settings?.default_payment_method,
            trial_from_plan: true,
            metadata,
          });
  
          // await stripe.updateInvoice(subscription.latest_invoice, { projectId: req.projectId });
          res
            .status(200)
            .json({ error: false, message: "User subscribed successfully" });
        } catch (err) {
          console.error(err);
          let payload = {
            error: true,
            trace: err,
            message: err.message || "Something went wrong",
          };
          return res.status(500).json(payload);
        }
      }
    );

    app.post(
      "/v1/api/lambda/stripe/subscription/usage-charge",
      middlewares,
      TokenMiddleware(),
      async function (req, res) {
        try {
          const sdk = req.sdk;
          const { subId, quantity } = req.body;
        
  
          if (!subId || !quantity) {
            return res.status(400).json({
              error: true,
              message: "Sub id and quantity are required",
            });
          }
  
          sdk.setProjectId(req.projectId);
  
          const stripeSubTable = `${sdk.getProjectId()}_stripe_subscription`;
  
          const sub = await sdk.rawQuery(`
          SELECT ${jsonExtractor("s.object", "items.data[0].id")} as subItemId
          FROM ${stripeSubTable} as s
          WHERE s.id = ${subId} and (s.status = 'active' OR s.status = 'trialing');
        `);
  
          if (!sub[0]) {
            return res
              .status(404)
              .json({ error: true, message: "Subscription is not found" });
          }
  
          const charge = await stripe.createUsageCharge({
            subItemId: sub[0].subItemId,
            quantity,
          });
          await res.status(200).json({
            error: false,
            message: "Charge recorded successfully",
            model: charge,
          });
        } catch (err) {
          console.error(err);
          let payload = {
            error: true,
            trace: err,
            message: err.message || "Something went wrong",
          };
          return res.status(500).json(payload);
        }
      }
    );

    app.post(
      "/v1/api/lambda/stripe/customer/register-subscribe",
      middlewares,
      TokenMiddleware(),
      async function (req, res) {
        try {
          const sdk = req.sdk;
          const role = "user";
          const { planId, first_name, last_name, email, password, cardToken, verify = 0 } =
            req.body;

  
          if (!planId || !email || !cardToken || !password) {
            return res.status(400).json({
              error: true,
              message: "Plan id (planId), email, card token (cardToken) and password are required",
            });
          }
  
          sdk.setProjectId(req.projectId);
  
          sdk.setTable("stripe_price");
          const plan = await sdk.findOne("stripe_price", { id: planId });
          if (!plan) {
            return res.status(404).json({
              error: true,
              message: "Plan not found",
            });
          }
  
          /**
           * create customer with the card, create subscription
           */
          const service = new AuthService();
          const result = await service.register(
            sdk,
            sdk.getProjectId(),
            email,
            password,
            role,
            verify ?? 0,
            first_name,
            last_name,
          );
          if (!result) {
            return res.status(403).json({
              error: true,
              message: "User registration failed",
            });
          }
          const metadata = {
            projectId: sdk.getProjectId(),
          };
  
          const customer = await stripe.createStripeCustomerWithCard({
            email,
            tokenId: cardToken,
            metadata,
          });
          sdk.setTable("user");
          await sdk.updateById("user", result.id, { stripe_uid: customer.id });
  
          await stripe.createStripeSubscription({
            customerId: customer.id,
            priceId: plan.stripe_id,
            default_payment_method: customer.default_source,
            trial_from_plan: true,
            metadata,
          });
  
          res.status(200).json({
            error: false,
            message: "User registered & subscribed successfully",
            role,
            token: JwtService.createAccessToken(
              {
                user_id: result.id,
                role,
              },
              config.access_jwt_expire,
              config.jwt_key
            ),
            expire_at: config.access_jwt_expire,
            user_id: result.id,
          });
        } catch (err) {
          console.error(err);
          let payload = {
            error: true,
            trace: err,
            message: err.message || "Something went wrong",
          };
          return res.status(500).json(payload);
        }
      }
    );
    app.put(
      "/v1/api/lambda/stripe/customer/subscription",
      middlewares,
      TokenMiddleware(),
      async function (req, res) {
        /**
         * get user_id, get his current active subscription and the type of it
         * either a normal recurring one or a lifetime one.
         * if normal change get the subitem and change the price
         * if lifetime mark it as cancelled and create new subscription
         */
        const sdk = req.sdk;
        try {
          const {
            //  userId,
            activeSubscriptionId,
            newPlanId,
          } = req.body;
  
  
          if (!activeSubscriptionId || !newPlanId) {
            return res.status(400).json({
              error: true,
              message: "Active subscription id (activeSubscriptionId) and new plan id (newPlanId) are required",
            });
          }
          const userId = req.user_id;
          sdk.setProjectId(req.projectId);
  
          const userTable = `${sdk.getProjectId()}_user`;
          const stripeSubTable = `${sdk.getProjectId()}_stripe_subscription`;
          const stripePriceTable = `${sdk.getProjectId()}_stripe_price`;
  
          const customer = await sdk.rawQuery(`
          SELECT u.*, s.id as subId, s.stripe_id as subStripeId, 
          ${jsonExtractor(
            "s.object",
            "items.data[0].id"
          )} as subItemId, p.id AS planId, p.type as planType
          FROM ${userTable} AS u LEFT JOIN ${stripeSubTable} AS s ON s.user_id = u.id AND (s.status = 'active' OR s.status = 'trialing') 
          LEFT JOIN ${stripePriceTable} AS p ON s.price_id = p.id 
          WHERE u.id = ${userId} ;
        `);
  
          if (!customer[0]) {
            return res
              .status(404)
              .json({ error: true, message: "Customer not found" });
          }
  
          if (customer[0].subId !== activeSubscriptionId) {
            return res.status(404).json({
              error: true,
              message: "Passed subscription id doesn't match the customer record",
            });
          }
  
          const stripeCustomer = await stripe.retrieveStripeCustomer({
            customerId: customer[0].stripe_uid,
          });
          if (
            !stripeCustomer.default_source &&
            !stripeCustomer.sources?.data?.length &&
            !stripeCustomer.invoice_settings?.default_payment_method
          ) {
            return res.status(403).json({
              error: true,
              message:
                "You don't have a valid card attached, please add one and try again",
            });
          }
  
          sdk.setTable("stripe_price");
          const newPlan = await sdk.findOne("stripe_price", { id: newPlanId });
          if (!newPlan) {
            return res
              .status(404)
              .json({ error: true, message: "Plan not found" });
          }
          const metadata = {
            projectId: sdk.getProjectId(),
          };
          if (
            customer[0].planType === "lifetime" &&
            newPlan[0].type === "recurring"
          ) {
            /**
             * TODO: should be somewhat refunded
             */
            const subscription = await stripe.createStripeSubscription({
              customerId: customer[0].stripe_uid,
              priceId: newPlan[0].stripe_id,
              default_payment_method:
                stripeCustomer.default_source ||
                stripeCustomer.sources?.data[0]?.id ||
                stripeCustomer.invoice_settings?.default_payment_method,
              metadata,
            });
  
            await sdk.updateById("stripe_subscription", customer[0].subId, {
              status: "canceled",
              updated_at: sqlDateTimeFormat(new Date()),
            });
          } else if (
            customer[0].planType === "recurring" &&
            newPlan[0].type === "recurring"
          ) {
            const updatedSubscription = await stripe.changeStripeSubscriptionPlan(
              {
                subscriptionId: customer[0].subStripeId,
                subItemId: customer[0].subItemId,
                newPriceId: newPlan[0].stripe_id,
              }
            );
          } else if (
            customer[0].planType === "recurring" &&
            newPlan[0].type === "lifetime"
          ) {
            //TODO
          }
  
          res
            .status(200)
            .json({ error: false, message: "Plan changed successfully" });
        } catch (err) {
          console.error(err);
          let payload = {
            error: true,
            trace: err,
            message: err.message || "Something went wrong",
          };
          return res.status(500).json(payload);
        }
      }
    );
    app.delete(
      "/v1/api/lambda/stripe/customer/subscription/:id",
      middlewares,
      TokenMiddleware(),
      async function (req, res) {
        try {
          const sdk = req.sdk;
          /**
           * cancel subscription
           */
          const { id } = req.params;
          const { cancel_type } = req.body;
          const user_id = req.user_id;
  
          sdk.setProjectId(req.projectId);
          sdk.setTable("stripe_subscription");
  
          const priceTable = `${sdk.getProjectId()}_stripe_price`;
          const sub = await sdk.rawQuery(
            `select sub.*, price.type as planType from ${sdk.getTable()} as sub left join ${priceTable} as price on sub.price_id = price.id where sub.status != 'canceled' and sub.id = ${+id} and sub.user_id = ${user_id}`
          );
  
          if (!sub[0]) {
            return res
              .status(404)
              .json({ error: true, message: "Subscription not found" });
          }
  
          if (sub[0].planType === "lifetime") {
            await sdk.updateById("stripe_subscription", sub[0].id, {
              status: "canceled",
              updated_at: sqlDateTimeFormat(new Date()),
            });
          } else {
            if (cancel_type == "at_period_end") {
              await stripe.cancelStripeSubscriptionAtPeriodEnd({
                subscriptionId: sub[0].stripe_id,
              });
            } else {
              await stripe.cancelStripeSubscription({
                subscriptionId: sub[0].stripe_id,
              });
            }
          }
  
          res.status(200).json({
            error: false,
            message: "Subscription is canceled successfully",
          });
        } catch (err) {
          console.error(err);
          let payload = {
            error: true,
            trace: err,
            message: err.message ?? "Something went wrong",
          };
          res.status(500).json(payload);
        }
      }
    );


    
  /**
   * checkout session
   * create
   */
  app.post(
    "/v1/api/lambda/stripe/checkout",
    middlewares,
    TokenMiddleware(),
    async function (req, res) {
      /**
       * Params: success_url, mode, cancel_url, client_reference_id, customer, customer_email
       *
       */
      const sdk = req.sdk;
      sdk.setProjectId(req.projectId);
      const {
        mode,
        success_url,
        cancel_url,
        shipping_address_collection,
        shipping_options,
        payment_method_types,
        payment_intent_data,
        phone_number_collection,
        line_items,
        metadata,
      } = req.body;

      try {
        
        if (!mode || !success_url || !cancel_url || !payment_method_types || !line_items) {
          return res.status(400).json({
            error: true,
            message: "Parameter/s missing",
            validation: {
              mode: "Mode is required",
              success_url: "Success URL is required",
              cancel_url: "Cancel URL is required",
              payment_method_types: "Payment method types are required",
              line_items: "Line items are required",
            },
          });
        }
        const customer_id = req.user_id;
        sdk.setTable("user");
        const customer = await sdk.findOne("user", { id: customer_id });
        if (!customer) {
          return res
            .status(404)
            .json({ error: true, message: "Customer not found" });
        }
        if (!customer.stripe_uid) {
          const stripe_customer = await stripe.createStripeCustomer({
            email: customer.email,
          });
          customer.stripe_uid = stripe_customer.id;
          await sdk.updateById("user", customer.id, { stripe_uid: stripe_customer.id });
        }

        if (mode == "subscription") {
          sdk.setTable("stripe_subscription");
          const customerSubscriptionsActive = await sdk.find("stripe_subscription", {
            user_id: customer.id,
            status: "active",
          });
          if (customerSubscriptionsActive.length > 0) {
            return res.status(400).json({
              error: true,
              message: "Customer already has an active subscription",
            });
          }
          const customerSubscriptionsTrialing = await sdk.find("stripe_subscription", {
            user_id: customer.id,
            status: "trialing",
          });
          if (customerSubscriptionsTrialing.length > 0) {
            return res.status(400).json({
              error: true,
              message: "Customer already has a trialing subscription",
            });
          }
        }

      

        let params = {
          mode,
          success_url,
          cancel_url,
          customer: customer.stripe_uid,
          payment_method_types,
          shipping_address_collection,
          shipping_options,
          payment_intent_data,
          line_items,
          metadata: {
            ...metadata,
            projectId: sdk.getProjectId(),
          },
          phone_number_collection,
        };

        const checkout = await stripe.createCheckoutSession(params);

        await sdk.create("stripe_checkout", {
          user_id: customer.id,
          stripe_id: checkout.id,
          object: JSON.stringify(checkout),
          created_at: sqlDateFormat(new Date()),
          updated_at: sqlDateTimeFormat(new Date()),
        });

        res.status(200).json({
          error: false,
          model: checkout,
        });
      } catch (err) {
        console.error(err);
        let payload = {
          error: true,
          trace: err,
          message: err.message || "Something went wrong",
        };
        res.status(500).json(payload);
      }
    }
  );



    /**
   * stripe customer
   * get
   * get invoices
   * get orders
   * create
   * delete
   */
    app.get(
      "/v1/api/lambda/stripe/customer",
      middlewares,
      TokenMiddleware(),
      async function (req, res) {
        try {
          /**
           * get customer
           */
          const sdk = req.sdk;
          if (!req.query.id) {
            return res.status(403).json({
              error: true,
              message: "ID Missing",
              validation: [{ id: "User ID missing" }],
            });
          }
  
          sdk.setProjectId(req.projectId);
          sdk.setTable("user");
          const userData = await sdk.findOne("user", { id: req.query.id });
          if (!userData) {
            res.status(404).json({ error: true, message: "User not found" });
          }
          if (!userData.stripe_uid) {
            return res.status(404).json({
              error: true,
              message: "Stripe ID not found",
              validation: [{ id: "Stripe ID not found" }],
            });
          }
          let customer = await stripe.retrieveStripeCustomer({
            customerId: userData.stripe_uid,
          });
          // retrieve customer data from stripe to ensure it is still exists
          res.status(200).json({ error: false, model: customer });
        } catch (err) {
          console.error(err);
          let payload = {
            error: true,
            trace: err,
            message: err.message ?? "Something went wrong",
          };
          res.status(500).json(payload);
        }
      }
    );

    app.get(
      "/v1/api/lambda/stripe/customer/invoices",
      middlewares,
      TokenMiddleware(),
      async function (req, res) {
        try {
          /**
           * get card
           */
          const sdk = req.sdk;
          const { after = null, before = null, limit = 10 } = req.query;
          const id = req.user_id;
  
          sdk.setProjectId(req.projectId);
          sdk.setTable("user");
  
          const user = await sdk.findOne("user", { id: id });
          if (!user) {
            return res
              .status(404)
              .json({ error: true, message: "User not found" });
          }
          const customer_stripe_id = user.stripe_uid;
  
          if (!customer_stripe_id) {
            // this user has no invoices
            return res.status(200).json({
              error: false,
              list: {
                object: "list",
                url: "/v1/invoices",
                has_more: false,
                data: [],
              },
              limit: limit,
            });
          }
  
          const invoices = await stripe.retrieveCustomerInvoices({
            customerId: customer_stripe_id,
            after,
            before,
            limit,
          });
  
          res.status(200).json({
            error: false,
            list: invoices,
            limit: limit,
          });
        } catch (err) {
          console.error(err);
          let payload = {
            error: true,
            trace: err,
            message: err.message ?? "Something went wrong",
          };
          res.status(500).json(payload);
        }
      }
    );
  
    app.get(
      "/v1/api/lambda/stripe/customer/orders",
      middlewares,
      TokenMiddleware(),
      async function (req, res) {
        try {
          const { limit = 10, page = 1 } = req.query;
          const id = req.user_id;
          const sdk = req.sdk;
          sdk.setProjectId(req.projectId);
          sdk.setTable("user");
  
          const user = await sdk.findOne("user", { id: id });
          if (!user) {
            return res
              .status(404)
              .json({ error: true, message: "User not found" });
          }
  
          const ordersTable = `${sdk.getProjectId()}_stripe_order`;
          const priceTable = `${sdk.getProjectId()}_stripe_price`;
          const userTable = `${sdk.getProjectId()}_user`;
  
          const where = [`o.user_id = ${id}`];
          /**
           * get all orders of that user id join it with user and price to get details
           */
          let sqlQuery = `
                SELECT 
                  ${jsonExtractor("o.object", "amount")} as amount,
                  ${jsonExtractor("o.object", "status")} as status,
                  ${jsonExtractor("o.object", "currency")} as currency,
                  ${jsonExtractor("o.object", "created")} as created_at,
                  ${jsonExtractor("p.object", "nickname")} as product_name,
                  u.email as customer
                from ${ordersTable} as o
                LEFT JOIN ${userTable} as u ON o.user_id = u.id
                LEFT JOIN ${priceTable} as p ON o.price_id = p.id
                WHERE ${where.length ? where.join(" AND ") : 1}`;
  
          if (limit === "all") {
            let [...resource] = await sdk.rawQuery(sqlQuery);
            resource.map((row) => (row.object = JSON.parse(row.object)));
            return res.status(200).json({ error: false, list: resource });
          }
  
          const [{ count: total }] = await sdk.rawQuery(
            `
                SELECT COUNT(*) as count
                from ${ordersTable} as o
                LEFT JOIN ${userTable} as u ON o.user_id = u.id
                LEFT JOIN ${priceTable} as p ON o.price_id = p.id
                WHERE ${where.length ? where.join(" AND ") : 1}`
          );
  
          const [...resource] = await sdk.rawQuery(
            `${sqlQuery}
                LIMIT ${(+page - 1) * +limit}, ${+limit}
              `
          );
  
          const num_pages = Math.ceil(+total / +limit);
          res.status(200).json({
            error: false,
            list: resource,
            total,
            limit,
            num_pages,
            page,
          });
        } catch (err) {
          console.error(err);
          let payload = {
            error: true,
            trace: err,
            message: err.message ?? "Something went wrong",
          };
          res.status(500).json(payload);
        }
      }
    );
  
    app.post(
      "/v1/api/lambda/stripe/customer",
      middlewares,
      TokenMiddleware(),
      async function (req, res) {
        try {
          /**
           * create customer with card
           */
          const sdk = req.sdk;
          const userId = req.user_id;
          const { cardToken } = req.body;
          sdk.setProjectId(req.projectId);
  
          sdk.setTable("user");
  
          const user = await sdk.findOne("user", { id: userId });
          if (!user) {
            return res
              .status(404)
              .json({ error: true, message: "User not found" });
          }
  
          const metadata = {
            projectId: sdk.getProjectId(),
          };
  
          const stripeCustomer = await stripe.createStripeCustomer({
            email: user.email,
            tokenId: cardToken,
            metadata,
          });
  
          await sdk.updateById("user", userId, { stripe_uid: stripeCustomer.id });
          const updatedUser = await sdk.findOne("user", { id: userId });
          res.status(200).json({
            error: false,
            model: updatedUser,
          });
        } catch (err) {
          console.error(err);
          let payload = {
            error: true,
            trace: err,
            message: err.message ?? "Something went wrong",
          };
          res.status(500).json(payload);
        }
      }
    );
  
    app.delete(
      "/v1/api/lambda/stripe/customer",
      middlewares,
      TokenMiddleware(),
      async function (req, res) {
        try {
          /**
           * delete customer
           */
          const sdk = req.sdk;
          const userId = req.user_id;
  
          sdk.setProjectId(req.projectId);
  
          const user = await sdk.findOne("user", { id: userId });
          if (!user) {
            return res
              .status(404)
              .json({ error: true, message: "User not found" });
          }
          const customer_stripe_id = user.stripe_uid;
          await stripe.deleteStripeCustomer({ customerId: customer_stripe_id });
  
          res
            .status(200)
            .json({ error: false, message: "Customer deleted successfully" });
        } catch (err) {
          console.error(err);
          let payload = {
            error: true,
            trace: err,
            message: err.message ?? "Something went wrong",
          };
          res.status(500).json(payload);
        }
      }
    );


    /**
   * subscriptions
   * get one
   * get all
   */
  // subscription
  app.get(
    "/v1/api/lambda/stripe/subscription/:id",
    middlewares,
    TokenMiddleware(),
    async function (req, res) {
      try {
        /**
         * get subscription
         */
        const sdk = req.sdk;
        const { id } = req.params;

        sdk.setProjectId(req.projectId);
        sdk.setTable("stripe_subscription");
        const subscriptions = await sdk.findOne("stripe_subscription", { id });
        if (!subscriptions) {
          res
            .status(404)
            .json({ error: true, message: "Subscription not found" });
        }
        res.status(200).json({
          error: false,
          model: subscriptions,
        });
      } catch (err) {
        console.error(err);
        let payload = {
          error: true,
          trace: err,
          message: err.message ?? "Something went wrong",
        };
        res.status(500).json(payload);
      }
    }
  );

  app.get(
    "/v1/api/lambda/stripe/subscriptions",
    middlewares,
    TokenMiddleware(),
    async function (req, res) {
      try {
        /**
         * get subscriptions
         */
        const sdk = req.sdk;
        let {
          limit = 10,
          page = 1,
          user_id,
          customer_email,
          plan_name,
          sub_status,
          plan_type,
          currentPeriodStart,
          currentPeriodEnd,
          order_by,
          direction,
        } = req.query;

        sdk.setProjectId(req.projectId);

        const subscriptionTable = `${sdk.getProjectId()}_stripe_subscription`;
        const priceTable = `${sdk.getProjectId()}_stripe_price`;
        const userTable = `${sdk.getProjectId()}_user`;

        let query = filterEmptyFields({
          user_id,
          customer_email,
          plan_name,
          sub_status,
          plan_type,
          currentPeriodStart,
          currentPeriodEnd,
        });

        let where = [];
        Object.entries(query)?.forEach(([key, value]) => {
          switch (key) {
            case "user_id": {
              where.push(`sub.user_id = ${+value} `);
              break;
            }
            case "customer_email": {
              where.push(`user.email LIKE '%${value}%' `);
              break;
            }
            case "plan_name": {
              where.push(`price.name LIKE '%${value}%' `);
              break;
            }
            case "sub_status": {
              where.push(`sub.status = '${value}' `);
              break;
            }
            case "plan_type": {
              where.push(`price.type = '${value}' `);
              break;
            }
            case "currentPeriodStart": {
              where.push(
                `DATE(FROM_UNIXTIME(${jsonExtractor(
                  "sub.object",
                  "current_period_start"
                )})) = DATE(FROM_UNIXTIME(${value}))`
              );
              break;
            }
            case "currentPeriodEnd": {
              where.push(
                `DATE(FROM_UNIXTIME(${jsonExtractor(
                  "sub.object",
                  "current_period_end"
                )})) = DATE(FROM_UNIXTIME(${value}))`
              );
              break;
            }
          }
        });

        let sqlQuery = `
          SELECT sub.id as subId, ${jsonExtractor(
            "sub.object",
            "created"
          )} as createdAt,
            ${jsonExtractor(
              "sub.object",
              "current_period_start"
            )} as currentPeriodStart,            
            ${jsonExtractor(
              "sub.object",
              "current_period_end"
            )} as currentPeriodEnd, sub.status as status,
            price.is_usage_metered as isMetered, price.name as planName, price.type as planType, price.amount as planAmount, price.trial_days as trialDays,
            user.email as userEmail, sub.user_id as user_id, sub.object as details
          from ${subscriptionTable} as sub
          LEFT JOIN ${priceTable} as price ON sub.price_id = price.id
          LEFT JOIN ${userTable} as user ON sub.user_id = user.id
          WHERE ${where.length ? where.join(" AND ") : 1}`;

        if (limit === "all") {
          const [...resource] = await sdk.rawQuery(sqlQuery);
          return res.status(200).json({ error: false, list: resource });
        }

        const [{ count: total }] = await sdk.rawQuery(
          `SELECT COUNT(*) as count from ${subscriptionTable} as sub
          LEFT JOIN ${priceTable} as price ON sub.price_id = price.id        
          LEFT JOIN ${userTable} as user ON sub.user_id = user.id
          WHERE ${where.length ? where.join(" AND ") : 1} `
        );
        const [...resource] = await sdk.rawQuery(
          `${sqlQuery}
          ${limit ? `LIMIT ${(+page - 1) * +limit}, ${+limit} ` : ""}
          `
        );

        const num_pages = Math.ceil(+total / +limit);
        for (let x of resource) {
          try {
            details = JSON.parse(x.details);
            x.details = details;
            delete x.details;
            x.interval = details?.plan?.interval ? details.plan.interval : "";
          } catch (e) {
            console.log(e);
            delete x.details;
            x.interval = "";
          }
        }
        res.status(200).json({
          error: false,
          list: resource,
          total,
          limit,
          num_pages,
          page,
        });
      } catch (err) {
        console.error(err);
        let payload = {
          error: true,
          trace: err,
          message: err.message ?? "Something went wrong",
        };
        res.status(500).json(payload);
      }
    }
  );

  app.delete(
    "/v1/api/lambda/stripe/subscription/:id",
    middlewares,
    TokenMiddleware(),
    async function (req, res) {
      try {
        /**
         * cancel subscription
         */
        const sdk = req.sdk;
        const { id: subscriptionId } = req.params;
        const { cancel_type } = req.body;

        sdk.setProjectId(req.projectId);
        sdk.setTable("stripe_subscription");

        const priceTable = `${sdk.getProjectId()}_stripe_price`;
        const sub = await sdk.rawQuery(
          `select sub.*, price.type as planType from ${sdk.getTable()} as sub left join ${priceTable} as price on sub.price_id = price.id where sub.status != 'canceled' and sub.id = ${+subscriptionId} `
        );

        if (!sub[0]) {
          return res.status(404).json({
            error: true,
            message: `Subscription of id ${+subscriptionId} not found`,
          });
        }

        if (sub[0].planType === "lifetime") {
          sdk.setTable("stripe_subscription");
          await sdk.updateById("stripe_subscription", sub[0].id, {
            status: "canceled",
            updated_at: sqlDateTimeFormat(new Date()),
          });
        } else {
          if (cancel_type == "at_period_end") {
            await stripe.cancelStripeSubscriptionAtPeriodEnd({
              subscriptionId: sub[0].stripe_id,
            });
          } else {
            await stripe.cancelStripeSubscription({
              subscriptionId: sub[0].stripe_id,
            });
          }
        }

        res.status(200).json({
          error: false,
          message: "Subscription is canceled successfully",
        });
      } catch (err) {
        console.error(err);
        let payload = {
          error: true,
          trace: err,
          message: err.message ?? "Something went wrong",
        };
        res.status(500).json(payload);
      }
    }
  );


    /**
   * customer cards
   * get one
   * get all
   * create card token
   * create one
   * set card as default
   * update one //TODO
   * delete one
   */

    app.get(
      "/v1/api/lambda/stripe/customer/cards",
      middlewares,
      TokenMiddleware(),
      async function (req, res) {
        try {
          /**
           * get card
           */
          const sdk = req.sdk;
          const { after = null, before = null, limit = 10 } = req.query;
          const id = req.user_id;
  
          sdk.setProjectId(req.projectId);
          sdk.setTable("user");
  
          const user = await sdk.findOne("user", { id: id });
          if (!user) {
            return res
              .status(404)
              .json({ error: true, message: "User not found" });
          }
          const customer_stripe_id = user.stripe_uid;
          if (!customer_stripe_id)
            return res.status(200).json({
              error: false,
              data: null,
              limit: limit,
            });
          const cards = await stripe.retrieveStripeCustomerAllCards({
            customerId: customer_stripe_id,
            after,
            before,
            limit,
          });
  
          res.status(200).json({
            error: false,
            data: cards,
            limit: limit,
          });
        } catch (err) {
          console.error(err);
          let payload = {
            error: true,
            trace: err,
            message: err.message ?? "Something went wrong",
          };
          res.status(500).json(payload);
        }
      }
    );
  
    app.get(
      "/v1/api/lambda/stripe/customer/card",
      middlewares,
      TokenMiddleware(),
      async function (req, res) {
        try {
          /**
           * get one card
           */
          const sdk = req.sdk;
          const { cardId } = req.query;
          const id = req.user_id;
  
          sdk.setProjectId(req.projectId);
          sdk.setTable("user");
  
          const user = await sdk.findOne("user", { id: id });
          if (!user) {
            return res
              .status(404)
              .json({ error: true, message: "User not found" });
          }
          const customer_stripe_id = user.stripe_uid;
          if (!customer_stripe_id)
            return res.status(200).json({
              error: false,
              data: null,
            });
          const card = await stripe.retrieveStripeCustomerCardDetails({
            customerId: customer_stripe_id,
            cardId: cardId,
          });
  
          res.status(200).json({
            error: false,
            data: card,
          });
        } catch (err) {
          console.error(err);
          let payload = {
            error: true,
            trace: err,
            message: err.message ?? "Something went wrong",
          };
          res.status(500).json(payload);
        }
      }
    );
  
  
    app.post(
      "/v1/api/lambda/stripe/customer/card/create_token",
      middlewares,
      TokenMiddleware(),
      async function (req, res) {
        try {
          /**
           * create card token
           * body should be {number, exp_month, exp_year, cvc}
           * validate card data and create token
           */
  
          const { card_number, exp_month, exp_year, cvc } = req.body;
  
          
          if (!card_number || !exp_month || !exp_year || !cvc) {
            return res.status(400).json({ 
              error: true, 
              message: "Invalid card data",
              validation: {
                card_number: !card_number ? "Card number is required" : "",
                exp_month: !exp_month ? "Expiration month is required" : "",
                exp_year: !exp_year ? "Expiration year is required" : "",
                cvc: !cvc ? "CVC is required" : "",
              }
            });
          }
          /**
           * create a new customer with the token
           */
          let token = await stripe.createStripeCardToken({
            card_number,
            exp_month,
            exp_year,
            cvc,
          });
  
          res.status(200).json({
            error: false,
            token: token.id,
            message: "Customer card validated successfully",
          });
        } catch (err) {
          console.error(err);
          let payload = {
            error: true,
            trace: err,
            message: err.message ?? "Something went wrong",
          };
          res.status(500).json(payload);
        }
      }
    );
  
    app.post(
      "/v1/api/lambda/stripe/customer/card",
      middlewares,
      TokenMiddleware(),
      async function (req, res) {
        try {
          /**
           * update customer
           * body should be {user_id, cardToken}
           * this should create a customer in stripe if doesn't exist and add the card to him
           */
          const sdk = req.sdk;
          const userId = req.user_id;
          const { sourceToken } = req.body;
  
          if (!sourceToken) {
            return res.status(400).json({
              error: true,
              message: "Source token (sourceToken) is required",
            });
          }
  
          sdk.setProjectId(req.projectId);
  
          const user = await sdk.findOne("user", { id: userId });
          if (!user) {
            return res
              .status(404)
              .json({ error: true, message: "User not found" });
          }
  
          const customerStripeId = user.stripe_uid;
          const metadata = {
            projectId: sdk.getProjectId(),
          };
          if (!customerStripeId) {
            /**
             * create a new customer with the token
             */
            var customer = await stripe.createStripeCustomerWithCard({
              email: user.email,
              tokenId: sourceToken,
              metadata,
            });
  
            await sdk.updateById("user", user.id, { stripe_uid: customer.id });
          } else {
            /**
             * just add the card
             */
            var customer = await stripe.addNewCardToStripeCustomer({
              customerId: customerStripeId,
              tokenId: sourceToken,
              metadata,
            });
          }
  
          res.status(200).json({
            error: false,
            model: customer,
            message: "Customer card added successfully",
          });
        } catch (err) {
          console.error(err);
          let payload = {
            error: true,
            trace: err,
            message: err.message ?? "Something went wrong",
          };
          res.status(500).json(payload);
        }
      }
    );

    app.put(
      "/v1/api/lambda/stripe/customer/card/:id/set-default",
      middlewares,
      TokenMiddleware(),
      async function (req, res) {
        try {
          /**
           * update customer card
           */
          const sdk = req.sdk;
          const userId = req.user_id;
          const { id: cardId } = req.params;

          if (!cardId) {
            return res.status(400).json({
              error: true,
              message: "Card ID is required",
            });
          }
  
         
  
          sdk.setProjectId(req.projectId);
          sdk.setTable("user");
          const user = await sdk.findOne("user", { id: userId });
          if (!user) {
            return res
              .status(404)
              .json({ error: true, message: "User not found" });
          }
  
          if (!user.stripe_uid) {
            return res
              .status(404)
              .json({ error: true, message: "User is not a stripe customer" });
          }
  
          await stripe.setDefaultCard({
            customer_id: user.stripe_uid,
            card_id: cardId,
          });
          /**
           * update credit card for customer
           */
          res
            .status(200)
            .json({ error: false, message: "Card successfully set as default" });
        } catch (err) {
          console.error(err);
          let payload = {
            error: true,
            trace: err,
            message: err.message ?? "Something went wrong",
          };
          res.status(500).json(payload);
        }
      }
    );

    app.delete(
      "/v1/api/lambda/stripe/customer/card/:id",
      middlewares,
      TokenMiddleware(),
      async function (req, res) {
        try {
          /**
           * delete customer card
           */
          const sdk = req.sdk;
          const userId = req.user_id;
          const { id: cardId } = req.params;
  
          if (!cardId) {
            return res.status(400).json({
              error: true,
              message: "Card ID is required",
            });
          }
  
          sdk.setProjectId(req.projectId);
          sdk.setTable("user");
          const user = await sdk.findOne("user", { id: userId });
          if (!user) {
            return res
              .status(404)
              .json({ error: true, message: "User not found" });
          }
  
          if (!user.stripe_uid) {
            return res
              .status(404)
              .json({ error: true, message: "User is not a stripe customer" });
          }
  
          await stripe.deleteStripeCustomerCard({
            customerId: user.stripe_uid,
            cardId: cardId,
          });
          res.status(200).json({
            error: false,
            message: "Card deleted successfully",
            isDeleted: true,
          });
        } catch (err) {
          console.error(err);
          let payload = {
            error: true,
            trace: err,
            message: err.message ?? "Something went wrong",
          };
          res.status(500).json(payload);
        }
      }
    );

    
  // All types of payment methods

  // https://docs.stripe.com/api/payment_methods/create?lang=node

  app.get(
    "/v1/api/lambda/stripe/payment-methods-configurations",
    middlewares,
    async function (req, res) {
      try {
        const sdk = req.sdk;
        const paymentMethodsConfigurations =
          await stripe.listPaymentMethodsConfiguration();
        res.status(200).json({
          error: false,
          list: paymentMethodsConfigurations,
        });
      } catch (err) {
        console.error(err);
        let payload = {
          error: true,
          trace: err,
          message: err.message ?? "Something went wrong",
        };
        res.status(500).json(payload);
      }
    }
  );

  app.get(
    "/v1/api/lambda/stripe/payment-methods",
    middlewares,
    TokenMiddleware(),
    async function (req, res) {
      try {
        const sdk = req.sdk;

        const id = req.user_id;

        sdk.setProjectId(req.projectId);

        const user = await sdk.findOne("user", { id: id });

        if (!user) {
          return res
            .status(404)
            .json({ error: true, message: "User not found" });
        }

        const customer_stripe_id = user.stripe_uid;
        if (!customer_stripe_id)
          return res.status(200).json({
            error: false,
            data: null,
            list: [],
          });

        const paymentMethods = await stripe.retrieveStripePaymentMethodAll({
          customerId: customer_stripe_id,
        });
        const customer = await stripe.retrieveStripeCustomer({
          customerId: customer_stripe_id,
        });

        for (const paymentMethod of paymentMethods) {
          if (
            paymentMethod.id ==
            customer.invoice_settings?.default_payment_method
          ) {
            paymentMethod.is_default = true;
          } else {
            paymentMethod.is_default = false;
          }
        }

        res.status(200).json({
          error: false,
          list: paymentMethods,
        });
      } catch (err) {
        console.error(err);
        let payload = {
          error: true,
          trace: err,
          message: err.message ?? "Something went wrong",
        };
        res.status(500).json(payload);
      }
    }
  );

  app.post(
    "/v1/api/lambda/stripe/payment-method",
    middlewares,
    TokenMiddleware(),
    async function (req, res) {
      try {
        const sdk = req.sdk;
        const { type, details, billing_details } = req.body;
        const userId = req.user_id;

        sdk.setProjectId(req.projectId);
        sdk.setTable("user");

        const user = await sdk.findOne("user", { id: userId });
        if (!user) {
          return res
            .status(404)
            .json({ error: true, message: "User not found" });
        }

        const preference = await sdk.findOne("preference", { user_id: userId });

        const customerStripeId = user.stripe_uid;

        const metadata = {
          projectId: sdk.getProjectId(),
        };

        if (!customerStripeId) {
          /**
           * create a new customer
           */
          var customer = await stripe.createStripeCustomer({
            email: user.email,
            metadata,
          });

          await sdk.updateById("user", user.id, { stripe_uid: customer.id });

          if (type === "us_bank_account") {
            let data = await stripe.createAndVerifyBankAccount({
              ...details,
              customerId: customer.id,
            });
            delete details["country"];
            delete details["currency"];
            await stripe.setDefaultPaymentMethod({
              customer_id: customer.id,
              paymentMethod_id: data.id,
            });
          } else {
            const paymentMethod = await stripe.createStripePaymentMethod({
              type,
              details,
              billing_details: {
                name: preference?.first_name + " " + preference?.last_name,
              },
            });
            const attach = await stripe.attachStripePaymentMethod({
              paymentMethodId: paymentMethod.id,
              customerId: customer.id,
            });
            await stripe.setDefaultPaymentMethod({
              customer_id: customer.id,
              paymentMethod_id: paymentMethod.id,
            });
          }
        } else {
          /**
           * just add the payment method
           */
          let paymentMethod = {};
          if (type === "us_bank_account") {
            let data = await stripe.createAndVerifyBankAccount({
              ...details,
              customerId: customerStripeId,
            });
            delete details["country"];
            delete details["currency"];
            await stripe.setDefaultPaymentMethod({
              customer_id: customerStripeId,
              paymentMethod_id: data.id,
            });
            // paymentMethod = await stripe.createStripePaymentMethod({ type, details, billing_details: {"name": user[0].first_name + " " + user[0].last_name} });
          } else {
            paymentMethod = await stripe.createStripePaymentMethod({
              type,
              details,
              billing_details: {
                name: preference?.first_name + " " + preference?.last_name,
              },
            });

            const attach = await stripe.attachStripePaymentMethod({
              paymentMethodId: paymentMethod.id,
              customerId: customerStripeId,
            });
            await stripe.setDefaultPaymentMethod({
              customer_id: customerStripeId,
              paymentMethod_id: paymentMethod.id,
            });
          }
        }

        res.status(200).json({
          error: false,

          message: "Payment method added successfully",
        });
      } catch (err) {
        console.error(err);
        return res.status(500).json({
          error: true,
          message: err.message ?? "Something went wrong",
        });
      }
    }
  );

  app.put(
    "/v1/api/lambda/stripe/customer/payment-method/:id/set-default",
    middlewares,
    TokenMiddleware(),
    async function (req, res) {
      try {
        /**
         * update customer payment method
         */
        const sdk = req.sdk;
        const userId = req.user_id;
        const { id: paymentMethodId } = req.params;

       
        if (!paymentMethodId) {
          return res.status(400).json({
            error: true,
            message: "Payment method ID (:id) is required",
          });
        }

        sdk.setProjectId(req.projectId);

        const user = await sdk.findOne("user", { id: userId });
        if (!user) {
          return res
            .status(404)
            .json({ error: true, message: "User not found" });
        }

        if (!user.stripe_uid) {
          return res
            .status(404)
            .json({ error: true, message: "User is not a stripe customer" });
        }

        await stripe.setDefaultPaymentMethod({
          customer_id: user.stripe_uid,
          paymentMethod_id: paymentMethodId,
        });
        /**
         * update credit card for customer
         */
        res.status(200).json({
          error: false,
          message: "Payment Method successfully set as default",
        });
      } catch (err) {
        console.error(err);
        let payload = {
          error: true,
          trace: err,
          message: err.message ?? "Something went wrong",
        };
        res.status(500).json(payload);
      }
    }
  );

  app.delete(
    "/v1/api/lambda/stripe/payment-method/:id",
    middlewares,
    TokenMiddleware(),
    async function (req, res) {
      try {
        const sdk = req.sdk;
        const userId = req.user_id;
        const { id: paymentMethodId } = req.params;

        sdk.setProjectId(req.projectId);

        const user = await sdk.findOne("user", { id: userId });
        if (!user) {
          return res
            .status(404)
            .json({ error: true, message: "User not found" });
        }

        const customerStripeId = user.stripe_uid;

        await stripe.detachStripePaymentMethod({
          paymentMethodId,
          customerId: customerStripeId,
        });

        return res.status(200).json({
          error: false,
          message: "Payment method deleted successfully",
        });
      } catch (err) {
        console.error(err);
        let payload = {
          error: true,
          trace: err,
          message: err.message ?? "Something went wrong",
        };
        res.status(500).json(payload);
      }
    }
  );

   /**
   * invoices
   * get all
   */
  app.get(
    "/v1/api/lambda/stripe/invoices",
    middlewares,
    TokenMiddleware(),
    async function (req, res) {
      try {
        /**
         * get card
         */
        const sdk = req.sdk;
        const { after = null, before = null, limit = 10 } = req.query;

        const invoices = await stripe.retrieveCustomerInvoices({
          after,
          before,
          limit,
        });

        res.status(200).json({
          error: false,
          list: invoices,
          limit: limit,
        });
      } catch (err) {
        console.error(err);
        let payload = {
          error: true,
          trace: err,
          message: err.message ?? "Something went wrong",
        };
        res.status(500).json(payload);
      }
    }
  );

  app.get(
    "/v1/api/lambda/stripe/invoices-v2",
    middlewares,
    TokenMiddleware(),
    async function (req, res) {
      try {
        const { limit = 10, page = 1, customer_email, status } = req.query;
        const sdk = req.sdk;
        sdk.setProjectId(req.projectId);

        const db = sdk.getDatabase();
        const invoicesTable = `${sdk.getProjectId()}_stripe_invoice`;

        const where = [];
        /**
         * get all invoices
         */
        let sqlQuery = `
        SELECT 
          ${jsonExtractor("i.object", "status")} as status,
          ${jsonExtractor("i.object", "currency")} as currency,
          ${jsonExtractor("i.object", "amount_due")} as amount_due,
          ${jsonExtractor("i.object", "amount_paid")} as amount_paid,
          ${jsonExtractor("i.object", "amount_remaining")} as amount_remaining,
          ${jsonExtractor("i.object", "created")} as created_at
        FROM ${invoicesTable} as i
        WHERE ${where.length ? where.join(" AND ") : 1}`;

        if (limit === "all") {
          let [...resource] = await sdk.rawQuery(sqlQuery);
          resource.map((row) => (row.object = JSON.parse(row.object)));
          return res.status(200).json({ error: false, list: resource });
        }

        const [{ count: total }] = await sdk.rawQuery(
          `
        SELECT COUNT(*) as count
        FROM ${invoicesTable} as i
        WHERE ${where.length ? where.join(" AND ") : 1}`
        );

        const [...resource] = await sdk.rawQuery(
          `${sqlQuery}
          LIMIT ${(+page - 1) * +limit}, ${+limit}
        `
        );

        const num_pages = Math.ceil(+total / +limit);
        res.status(200).json({
          error: false,
          list: resource,
          total,
          limit,
          num_pages,
          page,
        });
      } catch (err) {
        console.error(err);
        let payload = {
          error: true,
          trace: err,
          message: err.message ?? "Something went wrong",
        };
        res.status(500).json(payload);
      }
    }
  );

    /**
   * orders
   * get all
   */
    app.get(
      "/v1/api/lambda/stripe/orders",
      middlewares,
      TokenMiddleware(),
      async function (req, res) {
        try {
          const { limit = 10, page = 1, customer_email, product_name } = req.query;
          const sdk = req.sdk;
          sdk.setProjectId(req.projectId);
  
          const ordersTable = `${sdk.getProjectId()}_stripe_order`;
          const priceTable = `${sdk.getProjectId()}_stripe_price`;
          const userTable = `${sdk.getProjectId()}_user`;
  
          let query = filterEmptyFields({
            customer_email,
            product_name,
          });
  
          let where = [];
          Object.entries(query)?.forEach(([key, value]) => {
            switch (key) {
              case "product_name": {
                where.push(
                  `${jsonExtractor("p.object", "nickname")} LIKE '%${value}%'`
                );
                break;
              }
              case "customer_email": {
                where.push(`u.email LIKE '%${value}%' `);
                break;
              }
            }
          });
  
          let sqlQuery = `
              SELECT u.email as customer,
                ${jsonExtractor("o.object", "amount")} as amount,
                ${jsonExtractor("o.object", "status")} as status,
                ${jsonExtractor("o.object", "currency")} as currency,
                ${jsonExtractor("o.object", "created")} as created_at,
                ${jsonExtractor(
                  "p.object",
                  "nickname"
                )} as product_name            
              from ${ordersTable} as o
              LEFT JOIN ${userTable} as u ON o.user_id = u.id
              LEFT JOIN ${priceTable} as p ON o.price_id = p.id
              WHERE ${where.length ? where.join(" AND ") : 1}`;
  
          if (limit === "all") {
            let [...resource] = await sdk.rawQuery(sqlQuery);
            resource.map((row) => (row.object = JSON.parse(row.object)));
            return res.status(200).json({ error: false, list: resource });
          }
  
          const [{ count: total }] = await sdk.rawQuery(`
          SELECT COUNT(*) as count
          from ${ordersTable} as o
          LEFT JOIN ${userTable} as u ON o.user_id = u.id
          LEFT JOIN ${priceTable} as p ON o.price_id = p.id
          WHERE ${where.length ? where.join(" AND ") : 1}
        `);
  
          const [...resource] = await sdk.rawQuery(
            `${sqlQuery}
              LIMIT ${(+page - 1) * +limit}, ${+limit}
            `
          );
  
          const num_pages = Math.ceil(+total / +limit);
          res.status(200).json({
            error: false,
            list: resource,
            total,
            limit,
            num_pages,
            page,
          });
        } catch (err) {
          console.error(err);
          let payload = {
            error: true,
            trace: err,
            message: err.message ?? "Something went wrong",
          };
          res.status(500).json(payload);
        }
      }
    );
  
    /**
     * refunds
     * get one    //TO REVIEW
     * get all    //TO REVIEW
     * create one
     * cancel one //TO REVIEW
     */
    app.get(
      "/v1/api/lambda/stripe/refund",
      middlewares,
      TokenMiddleware(),
      async function (req, res) {
        try {
          /**
           * get one refund
           */
          const sdk = req.sdk;
          if (!req.query.id) {
            return res.status(403).json({
              error: true,
              message: "ID Missing",
              validation: [{ id: "Refund ID missing" }],
            });
          }
          sdk.setProjectId(req.projectId);
          sdk.setTable("stripe_refund");
          const refundData = await sdk.findOne("stripe_refund", { id: req.query.id });
          if (!refundData)
            res.status(404).json({ error: true, message: "Refund not found" });
  
          const refund = await stripe.retrieveStripeRefund({
            refund_id: refundData.stripe_id,
          });
  
          res.status(200).json({
            error: false,
            model: refund,
          });
        } catch (err) {
          console.error(err);
          let payload = {
            error: true,
            trace: err,
            message: err.message ?? "Something went wrong",
          };
          res.status(500).json(payload);
        }
      }
    );

    app.post(
      "/v1/api/lambda/stripe/refund",
      middlewares,
      TokenMiddleware(),
      async function (req, res) {
        try {
          /**
           * create refund of a subscription (recurrent payment)
           * user_id is the customer_id whose refund is being created
           * reasons: duplicate, fraudulent, requested_by_customer
           * user_id from body because only admin has permission to create refund
           * status 1 -> refund created, 0 -> refund cancelled
           */
  
          const {
            user_id,
            subscription_id,
            subscription_stripe_id,
            amount,
            reason,
          } = req.body;
  
         
          if (!user_id || !subscription_id || !subscription_stripe_id || !amount || !reason)
            return res.status(400).json({
              error: true,
              message: "Invalid request",
              validation: {
                user_id: !user_id ? "User ID is required" : "",
                subscription_id: !subscription_id ? "Subscription ID is required" : "",
                subscription_stripe_id: !subscription_stripe_id ? "Subscription Stripe ID is required" : "",
                amount: !amount ? "Amount is required" : "",
                reason: !reason ? "Reason is required" : "",
              },
            });
  
          const sdk = req.sdk;
  
          sdk.setProjectId(req.projectId);
          sdk.setTable("user");
          const customerData = await sdk.findOne("user", { id: user_id });
          if (!customerData)
            res.status(404).json({ error: true, message: "Customer not found" });
          if (!customerData.stripe_uid)
            res
              .status(404)
              .json({ error: true, message: "Customer stripe id found" });
  
          sdk.setTable("stripe_invoice");
          const invoice = await sdk.findOne("stripe_invoice", { user_id: user_id });
          if (!invoice)
            return res
              .status(404)
              .json({ error: true, message: "Invoice not found" });
  
          let invoice_obj = invoice.object;
          invoice_obj = JSON.parse(invoice_obj);
  
          let charge_id = invoice_obj.charge; // last charge id
          if (!charge_id)
            return res
              .status(404)
              .json({ error: true, message: "Charge id not found" });
  
          const refund = await stripe.createStripeRefund({
            charge_id: charge_id,
            amount: req.body.amount,
            reason: req.body.reason,
            metadata: {
              projectId: sdk.getProjectId(),
              userId: user_id,
              customerStripeId: customerData.stripe_uid,
              subscriptionStripeId: subscription_stripe_id,
            },
          });
  
          sdk.setTable("stripe_refund");
          const refundData = await sdk.insert("stripe_refund", {
            user_id: user_id,
            stripe_id: refund.id,
            charge_id: charge_id,
            subscription_id: subscription_id,
            amount: amount,
            currency: refund.currency,
            reason: reason,
            object: JSON.stringify(refund),
            status: 1,
            created_at: sqlDateFormat(new Date()),
            updated_at: sqlDateTimeFormat(new Date()),
          });
  
          await sdk.updateById("stripe_subscription", subscription_id, {
            status: "refunded",
            updated_at: sqlDateTimeFormat(new Date()),
          });
  
          res.status(200).json({
            error: false,
            model: refundData,
          });
        } catch (err) {
          console.error(err);
          let payload = {
            error: true,
            trace: err,
            message: err.message ?? "Something went wrong",
          };
          res.status(500).json(payload);
        }
      }
    );

    app.get(
      "/v1/api/lambda/stripe/refunds",
      middlewares,
      TokenMiddleware(),
      async function (req, res) {
        try {
          /**
           * get all refunds
           */
          const sdk = req.sdk;
          let { limit = 10, page = 1, user_id } = req.query;
  
          sdk.setProjectId(req.projectId);
  
          const refundTable = `${sdk.getProjectId()}_stripe_refund`;
          const userTable = `${sdk.getProjectId()}_user`;
          const preferenceTable = `${sdk.getProjectId()}_preference`;
          let query = filterEmptyFields({
            user_id,
          });
  
          let where = [];
          Object.entries(query)?.forEach(([key, value]) => {
            switch (key) {
              case "user_id": {
                where.push(`refund.user_id = ${+value} `);
                break;
              }
            }
          });
  
          let sqlQuery = `
          SELECT refund.id as refId,
            ${jsonExtractor("refund.object", "created")} as createdAt,
            ${jsonExtractor("refund.object", "charge")} as chargeId,
            ${jsonExtractor("refund.object", "amount")} as amount,
            ${jsonExtractor("refund.object", "reason")} as reason,
            ${jsonExtractor("refund.object", "status")} as status,
            refund.object as refundObject,
            user.id as userId,
            user.email as userEmail,
            preference.first_name as userFirstName,
            preference.last_name as userLastName 
          FROM ${refundTable} as refund 
          LEFT JOIN ${userTable} as user ON refund.user_id = user.id
          LEFT JOIN ${preferenceTable} as preference ON user.id = preference.user_id
          WHERE ${where.length ? where.join(" AND ") : 1}
        `;
  
          if (limit === "all") {
            const resource = await sdk.rawQuery(sqlQuery);
            return res.status(200).json({ error: false, list: resource });
          }
  
          const [{ count: total }] = await sdk.rawQuery(
            `SELECT COUNT(*) as count from ${refundTable} as refund
            LEFT JOIN ${userTable} as user ON refund.user_id = user.id 
            WHERE ${where.length ? where.join(" AND ") : 1}`
          );
          const [...resource] = await sdk.rawQuery(`
          ${sqlQuery}
          LIMIT ${(+page - 1) * +limit}, ${+limit}
        `);
  
          const num_pages = Math.ceil(+total / +limit);
          res.status(200).json({
            error: false,
            list: resource,
            total,
            limit,
            num_pages,
            page,
          });
        } catch (err) {
          console.error(err);
          let payload = {
            error: true,
            trace: err,
            message: err.message ?? "Something went wrong",
          };
          res.status(500).json(payload);
        }
      }
    );

      /**
   * disputes
   * get one               //TO REVIEW
   * get all               //TO REVIEW
   * create & submit one   //TO REVIEW
   * update & submit one   //TO REVIEW
   * cancel one            //TO REVIEW
   */
  app.get(
    "/v1/api/lambda/stripe/dispute",
    middlewares,
    TokenMiddleware(),
    async function (req, res) {
      try {
        /**
         * get dispute from DB
         */
        const sdk = req.sdk;
        if (!req.query.id) {
          return res.status(403).json({
            error: true,
            message: "ID Missing",
            validation: [{ id: "Dispute ID missing" }],
          });
        }
        sdk.setProjectId(req.projectId);
        sdk.setTable("stripe_dispute");
        const disputeData = await sdk.findOne("stripe_dispute", { id: req.query.id });
        if (!disputeData) {
          return res.status(404).json({ error: true, message: "Dispute not found" });
        }
        const dispute = await stripe.retrieveStripeDispute({
          dispute_id: disputeData.stripe_id,
        });
        res.status(200).json({
          error: false,
          model: dispute,
        });
      } catch (err) {
        console.error(err);
        let payload = {
          error: true,
          trace: err,
          message: err.message ?? "Something went wrong",
        };
        res.status(500).json(payload);
      }
    }
  );
  app.get(
    "/v1/api/lambda/stripe/disputes",
    middlewares,
    TokenMiddleware(),
    async function (req, res) {
      try {
        /**
         * get dispute list from DB
         */
        const sdk = req.sdk;
        if (!req.query.user_id) {
          return res.status(403).json({
            error: true,
            message: "User ID (user_id) Missing in query",
            validation: [{ id: "User ID missing" }],
          });
        }
        sdk.setProjectId(req.projectId);
        sdk.setTable("stripe_dispute");
        const disputes = await sdk.find("stripe_dispute", { user_id: req.query.user_id });
        if (!disputes.length) {
          return res.status(404).json({ error: true, message: "Dispute not found" });
        }
        res.status(200).json({
          error: false,
          list: disputes,
        });
      } catch (err) {
        console.error(err);
        let payload = {
          error: true,
          trace: err,
          message: err.message ?? "Something went wrong",
        };
        res.status(500).json(payload);
      }
    }
  );
  app.post(
    "/v1/api/lambda/stripe/dispute",
    middlewares,
    TokenMiddleware(),
    async function (req, res) {
      try {
        /**
         * create dispute on stripe
         * subscription_id = the subscription->stripe_id
         * reason: canceled, duplicate, fraudulent, merchandise_not_as_described, not_received
         * reason_description: explanation
         * reason_description.explanation: "Some explanation"
         * amount: 100 (without cents)
         */

        const { user_id, subscription_id, amount, reason, reason_description } =
          req.body;

       
        if (!user_id || !subscription_id || !amount || !reason || !reason_description)
          return res.status(400).json({
            error: true,
            message: "Invalid request",
            validation: {
              user_id: !user_id ? "User ID is required" : "",
              subscription_id: !subscription_id ? "Subscription ID is required" : "",
              amount: !amount ? "Amount is required" : "",
              reason: !reason ? "Reason is required" : "",
              reason_description: !reason_description ? "Reason description is required" : "",
            },
          });

        const sdk = req.sdk;
        sdk.setProjectId(req.projectId);

        const customerData = await sdk.findOne("user", { id: user_id });
        if (!customerData)
          return res.status(404).json({ error: true, message: "Customer not found" });
        if (!customerData.stripe_uid)
          return res.status(404).json({ error: true, message: "Customer stripe id found" });

        const invoice = await sdk.findOne("stripe_invoice", { user_id: user_id });
        if (!invoice)
          return res
            .status(404)
            .json({ error: true, message: "Invoice not found" });

        // let invoice_obj = invoice[0].object;
        // invoice_obj = JSON.parse(invoice_obj);

        // let charge_id = invoice_obj.charge; // last charge id
        // if (!charge_id) return res.status(404).json({ error: true, message: "Charge id not found" });

        // crosscheck subscription stripe id->no need
        // if (invoice_obj.subscription !== req.body.subscription_id) {
        //   return res.status(404).json({ error: true, message: "Subscription stripe id mismatch. Try again." });
        // }

        const disputeCreated = await stripe.createStripeDispute({
          amount: req.body.amount,
          reason: req.body.reason,
          reason_description: req.body.reason_description,
          metadata: {
            projectId: sdk.getProjectId(),
            userId: user_id,
            customerStripeId: customerData.stripe_uid,
            subscriptionId: subscription_id,
          },
        });

        console.log("disputeCreated", disputeCreated);

        // create dispute
        sdk.setTable("stripe_dispute");
        let disputePayload = {
          user_id: req.body.user_id,
          stripe_id: disputeCreated.id,
          amount: req.body.amount,
          subscription_id: req.body.subscription_id,
          reason: req.body.reason,
          reason_description: JSON.stringify(req.body.reason_description),
          object: JSON.stringify(disputeCreated),
          status: disputeCreated.status,
          created_at: sqlDateFormat(new Date()),
          updated_at: sqlDateTimeFormat(new Date()),
        };
        const disputeData = await sdk.create("stripe_dispute", disputePayload);

        const disputeSubmitted = await stripe.submitStripeDispute({
          dispute_id: disputeCreated.id,
        });

        console.log("disputeSubmitted", disputeSubmitted);

        // update dispute
        await sdk.updateById("stripe_dispute", disputeData.id, {
          status: disputeSubmitted.status,
          object: JSON.stringify(disputeSubmitted),
          updated_at: sqlDateTimeFormat(new Date()),
        });

        disputePayload = {
          ...disputePayload,
          status: disputeSubmitted.status,
        };

        res.status(200).json({
          error: false,
          model: disputePayload,
          message: "Dispute created & submitted successfully",
        });
      } catch (err) {
        console.error(err);
        let payload = {
          error: true,
          trace: err,
          message: err.message ?? "Something went wrong",
        };
        res.status(500).json(payload);
      }
    }
  );
  app.put(
    "/v1/api/lambda/stripe/dispute",
    middlewares,
    TokenMiddleware(),
    async function (req, res) {
      try {
        /**
         * submit dispute on stripe
         */
        const sdk = req.sdk;
        if (!req.query.id) {
          return res.status(403).json({
            error: true,
            message: "ID Missing",
            validation: [{ id: "Dispute ID missing" }],
          });
        }
        sdk.setProjectId(req.projectId);
        sdk.setTable("stripe_dispute");
        const disputeData = await sdk.findOne("stripe_dispute", { id: req.query.id });
        if (!disputeData) {
          return res.status(404).json({ error: true, message: "Dispute not found" });
        }
        const dispute = await stripe.updateStripeDispute({
          dispute_id: disputeData.stripe_id,
          reason: req.body.reason,
          reason_description: req.body.reason_description,
        });

        res.status(200).json({
          error: false,
          model: dispute,
        });
      } catch (err) {
        console.error(err);
        let payload = {
          error: true,
          trace: err,
          message: err.message ?? "Something went wrong",
        };
        res.status(500).json(payload);
      }
    }
  );
  app.delete(
    "/v1/api/lambda/stripe/dispute",
    middlewares,
    TokenMiddleware(),
    async function (req, res) {
      try {
        /**
         * delete dispute on stripe
         */
        const sdk = req.sdk;
        if (!req.query.id) {
          return res.status(403).json({
            error: true,
            message: "ID Missing",
            validation: [{ id: "Dispute ID missing" }],
          });
        }
        sdk.setProjectId(req.projectId);
        sdk.setTable("stripe_dispute");
        const disputeData = await sdk.findOne("stripe_dispute", { id: req.query.id });
        if (!disputeData) {
          return res.status(404).json({ error: true, message: "Dispute not found" });
        }
        const dispute = await stripe.closeStripeDispute({
          dispute_id: disputeData.stripe_id,
        });
        res.status(200).json({
          error: false,
          model: dispute,
          message: "Dispute closed",
        });
      } catch (err) {
        console.error(err);
        let payload = {
          error: true,
          trace: err,
          message: err.message ?? "Something went wrong",
        };
        res.status(500).json(payload);
      }
    }
  );


    /**
   * coupons
   * get
   * get all  //TODO
   * create   //TODO
   * update   //TODO
   * delete   //TODO
   */
    app.get(
      "/v1/api/lambda/stripe/coupon",
      middlewares,
      TokenMiddleware(),
      async function (req, res) {
        try {
          /**
           * get coupon on stripe
           */
          const sdk = req.sdk;
          if (!req.query.id) {
            return res.status(403).json({
              error: true,
              message: "ID Missing in query",
              validation: [{ id: "Coupon ID missing" }],
            });
          }
          sdk.setProjectId(req.projectId);
          sdk.setTable("stripe_payment_coupon");
          const coupon = await sdk.findOne("stripe_payment_coupon", { id: req.query.id });
          if (!coupon) {
            return res.status(404).json({
              error: true,
              message: "Coupon not found",
            });
          }
          res.json({
            error: false,
            model: coupon,
          });
        } catch (err) {
          console.error(err);
          let payload = {
            error: true,
            trace: err,
            message: err.message ?? "Something went wrong",
          };
          res.status(500).json(payload);
        }
      }
    );
  
    app.get(
      "/v1/api/lambda/stripe/paymentlinks",
      middlewares,
      TokenMiddleware(),
      async function (req, res) {
        try {
          /**
           * get payment links
           */
          const sdk = req.sdk;
  
          sdk.setProjectId(req.projectId);
  
          const result = await stripe.retrieveStripePaymentLinks();
          let links = result.data.filter(
            (link) => link.metadata.projectId == req.projectId
          );
  
          res.status(200).json({ error: false, list: links });
        } catch (err) {
          res.status(500).json({
            error: true,
            trace: err,
            message: err.message ?? "Something went wrong",
          });
        }
      }
    );
  
    app.post(
      "/v1/api/lambda/stripe/mobile/intent",
      middlewares,
      TokenMiddleware(),
      async function (req, res) {
        try {
          /**
           * get mobile payment intent
           */
          const sdk = req.sdk;
          sdk.setProjectId(req.projectId);
  
  
          if (!amount || !user_id) 
            return res.status(403).json({
              error: true,
              message: "Invalid request",
              validation: {
                amount: !amount ? "Amount (amount) is required" : "",
                user_id: !user_id ? "User ID (user_id) is required" : "",
              }
            });
  
          const { user_id, amount, currency } = req.body;
  
          //get customer
          sdk.setTable("user");
          const user = await sdk.findOne("user", { id: user_id });
          if (!user) {
            return res.status(403).json({ error: true, message: "Invalid User" });
          }
  
          let customer = !user.stripe_uid
            ? await stripe.createStripeCustomer({ email: user.email })
            : await stripe.retrieveStripeCustomer({
                customerId: user.stripe_uid,
              });
          await sdk.updateWhere({ stripe_uid: customer.id }, { id: user_id });
  
          const ephemeralKey = await stripe.createCustomerEphemeralKey({
            customer: customer.id,
          });
  
          const paymentIntent = await stripe.createPaymentIntentAutomatic({
            amount: amount * 100,
            currency: currency ? currency : "usd",
            customer: customer.id,
          });
  
          res.status(200).json({
            paymentIntent: paymentIntent.client_secret,
            ephemeralKey: ephemeralKey.secret,
            ephemeralKeyRaw: ephemeralKey,
            customer: customer.id,
            publishableKey: config.stripe.publish_key,
          });
        } catch (err) {
          res.status(500).json({
            error: true,
            trace: err,
            message: "Something went wrong",
          });
        }
      }
    );

  // ? means optional
  // ! required
  // !? required on condition
  const error = {
    message: "Error",
  };
  return [
    //products
    {
      method: "GET",
      name: "Get stripe products",
      url: "/v2/api/lambda/stripe/products",
      paginated: true,
      canGetAll: true,
      query: [
        "limit!:integer|'all'",
        "page!?[limit != 'all']:integer", //page is required if limit doesn't equal 'all'
        "name?:string",
        "stripe_id?:string",
        "status?:integer",
      ],
      params: null,
      body: null,
      successResponse:
        "{ error: false, list:array[], total:integer|undefined, limit:integer|undefined, num_pages:integer|undefined, page:integer|undefined }", //list containing all matching products
      errors: [
        {
          status: 500,
          error: true,
          message: error.message || "Something went wrong",
          trace: error, //error object for debuging
        },
      ],
    },
    {
      method: "GET",
      name: "Get single stripe product",
      url: "/v2/api/lambda/stripe/product/:id",
      paginated: false,
      canGetAll: false,
      query: null,
      params: ["id!:integer"],
      body: null,
      successResponse: "{ error: false, model: object{} }", //model containing the product
      errors: [
        {
          status: 404,
          error: true,
          message: "Product not found",
        },
        {
          status: 500,
          error: true,
          message: error.message || "Something went wrong",
          trace: error, //error object for debuging
        },
      ],
    },
    {
      method: "POST",
      name: "Create a single stripe product",
      url: "/v2/api/lambda/stripe/product",
      paginated: false,
      canGetAll: false,
      query: null,
      params: null,
      body: ["name!:string, description?:string"],
      successResponse:
        "{ error: false, model: object{}, message: 'Product created successfully' }", //model containing the newly created product
      errors: [
        {
          status: 400,
          error: true,
          message: "Parameter/s missing",
          validation: { name: "Name is required" },
        },
        {
          status: 500,
          error: true,
          message: error.message || "Something went wrong",
          trace: error, //error object for debuging
        },
      ],
    },
    {
      method: "PUT",
      name: "Edit a single stripe product",
      url: "/v2/api/lambda/stripe/product/:id",
      paginated: false,
      canGetAll: false,
      query: null,
      params: ["id!:integer"],
      body: ["name?:string", "stripe_id?:string", "status?:integer"],
      successResponse:
        "{ error: false, message: 'Product updated successfully' }",
      errors: [
        {
          status: 400,
          error: true,
          message: "Parameter/s missing",
          validation: { id: "Id is required" },
        },
        {
          status: 404,
          error: true,
          message: "Product not found",
        },
        {
          status: 500,
          error: true,
          message: error.message || "Something went wrong",
          trace: error, //error object for debuging
        },
      ],
    },
    //prices
    {
      method: "GET",
      name: "Get stripe prices",
      url: "/v2/api/lambda/stripe/prices",
      paginated: true,
      canGetAll: true,
      query: [
        "limit!:integer|'all'",
        "page!?[limit != 'all']:integer", //page is required if limit doesn't equal 'all'
        "stripe_id?:string",
        "product_name?:string",
        "status?:integer",
        "type?:string",
        "name?:string",
        "amount?:integer",
      ],
      params: null,
      body: null,
      successResponse:
        "{ error: false, list:array[], total:integer|undefined, limit:integer|undefined, num_pages:integer|undefined, page:integer|undefined }", //list containing all matching prices
      errors: [
        {
          status: 500,
          error: true,
          message: error.message || "Something went wrong",
          trace: error, //error object for debuging
        },
      ],
    },
    {
      method: "GET",
      name: "Get single stripe price",
      url: "/v2/api/lambda/stripe/price/:id",
      paginated: false,
      canGetAll: false,
      query: null,
      params: ["id!:integer"],
      body: null,
      successResponse: "{ error: false, model: object{} }", //model containing the product
      errors: [
        {
          status: 404,
          error: true,
          message: "Price not found",
        },
        {
          status: 500,
          error: true,
          message: error.message || "Something went wrong",
          trace: error, //error object for debuging
        },
      ],
    },
    {
      method: "POST",
      name: "Create a single stripe price",
      url: "/v2/api/lambda/stripe/price",
      paginated: false,
      canGetAll: false,
      query: null,
      params: null,
      body: [
        "product_id!: integer",
        "name!: string",
        "amount!: number",
        "type!: 'recurring'|'one_time'",
        "interval!?[type == 'recurring']: 'day'|'week'|'month'|'year'", //required if type is 'recurring'
        "interval_count!?[interval]: integer", //required if interval selected
        "trial_days?: integer",
        "usage_type!?[interval]: 'licenced'|'metered'", //required if interval selected
        "usage_limit!?[usage_type == 'metered']: integer", //required if usafe type is 'metered'
      ],
      successResponse:
        "{ error: false, model: object{}, message: 'Price created successfully' }", //model containing the newly created product
      errors: [
        {
          status: 400,
          error: true,
          message: "Parameter/s missing",
          validation: {
            product_id: "Product id is required",
            name: "Name is required",
            amount: "Amount is required",
            type: "Type is required",
          },
        },
        {
          status: 404,
          error: true,
          message: "Product not found",
        },
        {
          status: 500,
          error: true,
          message: error.message || "Something went wrong",
          trace: error, //error object for debuging
        },
      ],
    },
    {
      method: "PUT",
      name: "Edit a single stripe price",
      url: "/v2/api/lambda/stripe/price/:id",
      paginated: false,
      canGetAll: false,
      query: null,
      params: ["id!:integer"],
      body: ["name?:string", "status?:integer"],
      successResponse:
        "{ error: false, message: 'Price updated successfully' }",
      errors: [
        {
          status: 400,
          error: true,
          message: "Parameter/s missing",
          validation: { id: "Id is required" },
        },
        {
          status: 404,
          error: true,
          message: "Price not found",
        },
        {
          status: 500,
          error: true,
          message: error.message || "Something went wrong",
          trace: error, //error object for debuging
        },
      ],
    },
    //customer
    {
      method: "GET",
      name: "Get logged in user stripe subscription",
      url: "/v2/api/lambda/stripe/customer/subscription",
      paginated: false,
      canGetAll: false,
      query: null,
      params: null,
      body: null,
      successResponse: "{ error: false, customer: object{} }", //customer containing the user object with subscription and plan ids attached
      errors: [
        {
          status: 500,
          error: true,
          message: error.message || "Something went wrong",
          trace: error, //error object for debuging
        },
      ],
    },
    {
      method: "GET",
      name: "Get logged in user stripe subscriptions",
      url: "/v2/api/lambda/stripe/customer/subscriptions",
      paginated: true,
      canGetAll: true,
      query: [
        "limit!:integer|'all'",
        "page!?[limit != 'all']:integer", //page is required if limit doesn't equal 'all'
      ],
      params: null,
      body: null,
      successResponse:
        "{ error: false, list:array[], total:integer|undefined, limit:integer|undefined, num_pages:integer|undefined, page:integer|undefined }", //list containing all of logged in user subscriptions
      errors: [
        {
          status: 500,
          error: true,
          message: error.message || "Something went wrong",
          trace: error, //error object for debuging
        },
      ],
    },
    {
      method: "POST",
      name: "Create a stripe subscription for a logged in user",
      url: "/v2/api/lambda/stripe/customer/subscription",
      paginated: false,
      canGetAll: false,
      query: null,
      params: null,
      body: ["planId!: integer"],
      successResponse:
        "{ error: false, message: 'User subscribed successfully' }",
      errors: [
        {
          status: 400,
          error: true,
          message: "Validation Error",
          validation: { planId: "Plan id is required" },
        },
        {
          status: 404,
          error: true,
          message: "Customer not found",
        },
        {
          status: 404,
          error: true,
          message: "Plan not found",
        },
        {
          status: 401,
          error: true,
          message: "Customer already has an active subscription",
        },
        {
          status: 403,
          error: true,
          message:
            "You don't have a valid card attached, please add one and try again",
        },
        {
          status: 500,
          error: true,
          message: error.message || "Something went wrong",
          trace: error, //error object for debuging
        },
      ],
    },
    {
      method: "POST",
      name: "Create a stripe charge for a usage metered subscription",
      url: "/v2/api/lambda/stripe/subscription/usage-charge",
      paginated: false,
      canGetAll: false,
      query: null,
      params: null,
      body: ["subId!: string", "quantity!: integer"],
      successResponse:
        "{ error: false, model: object{}, message: 'Charge recorded successfully' }", //model containing the recored charge
      errors: [
        {
          status: 400,
          error: true,
          message: "Validation Error",
          validation: {
            planId: "Plan id is required",
            quantity: "Quantity is required",
          },
        },
        {
          status: 404,
          error: true,
          message: "Subscription not found",
        },
        {
          status: 500,
          error: true,
          message: error.message || "Something went wrong",
          trace: error, //error object for debuging
        },
      ],
    },
    {
      method: "POST",
      name: "Register new user then subscribe",
      url: "/v2/api/lambda/stripe/customer/register-subscribe",
      paginated: false,
      canGetAll: false,
      query: null,
      params: null,
      body: [
        "planId!: integer",
        "email!: string",
        "password!: string",
        "cardToken!: string",
      ],
      successResponse:
        "{ error: false, model: object{}, message: 'User registered & subscribed successfully', role: 'user', token: string, expire_at: Date, user_id: integer }",
      errors: [
        {
          status: 400,
          error: true,
          message: "Validation Error",
          validation: {
            planId: "Plan id is required",
            email: "Email is required",
            password: "Password is required",
            cardToken: "Card token is required",
          },
        },
        {
          status: 403,
          error: true,
          message: "User exists",
        },
        {
          status: 404,
          error: true,
          message: "Subscription not found",
        },
        {
          status: 500,
          error: true,
          message: error.message || "Something went wrong",
          trace: error, //error object for debuging
        },
      ],
    },
    {
      method: "PUT",
      name: "Upgrade or downgrade subscription of logged in user",
      url: "/v2/api/lambda/stripe/customer/subscription",
      paginated: false,
      canGetAll: false,
      query: null,
      params: null,
      body: ["activeSubscriptionId!: integer", "newPlanId!: integer"],
      successResponse: "{ error: false, message: 'Plan changed successfully' }",
      errors: [
        {
          status: 400,
          error: true,
          message: "Validation Error",
          validation: {
            planId: "Plan id is required",
            email: "Email is required",
            password: "Password is required",
            cardToken: "Card token is required",
          },
        },
        {
          status: 404,
          error: true,
          message: "Customer not found",
        },
        {
          status: 404,
          error: true,
          message: "Passed subscription id doesn't match the customer record",
        },
        {
          status: 403,
          error: true,
          message:
            "You don't have a valid card attached, please add one and try again",
        },
        {
          status: 404,
          error: true,
          message: "Plan not found",
        },
        {
          status: 500,
          error: true,
          message: error.message || "Something went wrong",
          trace: error, //error object for debuging
        },
      ],
    },
    {
      method: "DELETE",
      name: "Cancel subscription of logged in user",
      url: "/v2/api/lambda/stripe/customer/subscription/:id",
      paginated: false,
      canGetAll: false,
      query: null,
      params: ["id!: integer"],
      body: ["cancel_type!: 'at_period_end'|'any'"],
      successResponse:
        "{ error: false, message: 'Subscription is canceled successfully' }",
      errors: [
        {
          status: 400,
          error: true,
          message: "Validation Error",
          validation: { id: "Id is required" },
        },
        {
          status: 404,
          error: true,
          message: "Subscription not found",
        },
        {
          status: 500,
          error: true,
          message: error.message || "Something went wrong",
          trace: error, //error object for debuging
        },
      ],
    },
    {
      method: "POST",
      name: "Create checkout session",
      url: "/v2/api/lambda/stripe/checkout",
      paginated: false,
      canGetAll: false,
      query: null,
      params: null,
      body: [
        "mode!: 'payment'",
        "success_url!: string",
        "cancel_url!: string",
        "payment_method_types!: ['card|...']",
        "payment_intent_data!: object{}", //used to pass custom metadata parameters so we identify the charge and do proper calculation
        "phone_number_collection?: boolean",
        "line_items!: array[]",
        "metadata?: object{}",
        "shipping_address_collection?: object{}",
        "shipping_options",
      ],
      successResponse: "{ error: false, model: object{} }", //model contains checkout object
      errors: [
        {
          status: 400,
          error: true,
          message: "Validation Error",
          validation: {
            mode: "mode is required",
            success_url: "success_url is required",
            cancel_url: "cancel_url is required",
            payment_method_types: "payment_method_types is required",
            payment_intent_data: "payment_intent_data is required",
          },
        },
        {
          status: 401,
          error: true,
          message: "Customer already has an active subscription",
        },
        {
          status: 404,
          error: true,
          message: "Customer not found",
        },
        {
          status: 403,
          error: true,
          message: "Add a card before trying to subscribe",
        },
        {
          status: 403,
          error: true,
          message:
            "You don't have a valid card attached, please add one and try again",
        },
        {
          status: 500,
          error: true,
          message: error.message || "Something went wrong",
          trace: error, //error object for debuging
        },
      ],
    },
    {
      method: "GET",
      name: "Get logged in customer stripe invoices",
      url: "/v2/api/lambda/stripe/customer/invoices",
      paginated: true, //stripe cursor pagination system
      canGetAll: false,
      query: ["after?: string, before?: string, limit!: integer"],
      params: null,
      body: null,
      successResponse: "{ error: false, list: array[], limit: integer }", //list contains invoices object
      errors: [
        {
          status: 404,
          error: true,
          message: "User not found",
        },
        {
          status: 500,
          error: true,
          message: error.message || "Something went wrong",
          trace: error, //error object for debuging
        },
      ],
    },
    {
      method: "GET",
      name: "Get logged in customer stripe orders",
      url: "/v2/api/lambda/stripe/customer/orders",
      paginated: true,
      canGetAll: true,
      query: [
        "limit!:integer|'all'",
        "page!?[limit != 'all']:integer", //page is required if limit doesn't equal 'all'
      ],
      params: null,
      body: null,
      successResponse:
        "{ error: false, list:array[], total:integer|undefined, limit:integer|undefined, num_pages:integer|undefined, page:integer|undefined }", //list containing all orders of logged in user
      errors: [
        {
          status: 404,
          error: true,
          message: "User not found",
        },
        {
          status: 500,
          error: true,
          message: error.message || "Something went wrong",
          trace: error, //error object for debuging
        },
      ],
    },
    {
      method: "POST",
      name: "Create stripe profile of an existing and logged in customer with a credit card token",
      url: "/v2/api/lambda/stripe/customer",
      paginated: false,
      canGetAll: false,
      query: null,
      params: null,
      body: ["cardToken!: string"],
      successResponse: "{ error: false, model: object{} }", //model containing the updated customer object in system
      errors: [
        {
          status: 404,
          error: true,
          message: "User not found",
        },
        {
          status: 500,
          error: true,
          message: error.message || "Something went wrong",
          trace: error, //error object for debuging
        },
      ],
    },
    {
      method: "DELETE",
      name: "Delete customer stripe profile",
      url: "/v2/api/lambda/stripe/customer",
      paginated: false,
      canGetAll: false,
      query: null,
      params: null,
      body: null,
      successResponse:
        "{ error: false, message: 'Customer deleted successfully' }",
      errors: [
        {
          status: 404,
          error: true,
          message: "User not found",
        },
        {
          status: 500,
          error: true,
          message: error.message || "Something went wrong",
          trace: error, //error object for debuging
        },
      ],
    },
  ];
};

// API definition for Postman collection
module.exports.getPostmanDefinition = function () {
  return [
    //products
    {
      method: "GET",
      name: "Get stripe products",
      url: "/v2/api/lambda/stripe/products",
      paginated: true,
      canGetAll: true,
      query: [
        "limit!:integer|'all'",
        "page!?[limit != 'all']:integer", //page is required if limit doesn't equal 'all'
        "name?:string",
        "stripe_id?:string",
        "status?:integer",
      ],
      params: null,
      body: null,
      successResponse:
        "{ error: false, list:array[], total:integer|undefined, limit:integer|undefined, num_pages:integer|undefined, page:integer|undefined }", //list containing all matching products
      errors: [
        {
          status: 500,
          error: true,
          message: error.message || "Something went wrong",
          trace: error, //error object for debuging
        },
      ],
    },
    {
      method: "GET",
      name: "Get single stripe product",
      url: "/v2/api/lambda/stripe/product/:id",
      paginated: false,
      canGetAll: false,
      query: null,
      params: ["id!:integer"],
      body: null,
      successResponse: "{ error: false, model: object{} }", //model containing the product
      errors: [
        {
          status: 404,
          error: true,
          message: "Product not found",
        },
        {
          status: 500,
          error: true,
          message: error.message || "Something went wrong",
          trace: error, //error object for debuging
        },
      ],
    },
    {
      method: "POST",
      name: "Create a single stripe product",
      url: "/v2/api/lambda/stripe/product",
      paginated: false,
      canGetAll: false,
      query: null,
      params: null,
      body: ["name!:string, description?:string"],
      successResponse:
        "{ error: false, model: object{}, message: 'Product created successfully' }", //model containing the newly created product
      errors: [
        {
          status: 400,
          error: true,
          message: "Parameter/s missing",
          validation: { name: "Name is required" },
        },
        {
          status: 500,
          error: true,
          message: error.message || "Something went wrong",
          trace: error, //error object for debuging
        },
      ],
    },
    {
      method: "PUT",
      name: "Edit a single stripe product",
      url: "/v2/api/lambda/stripe/product/:id",
      paginated: false,
      canGetAll: false,
      query: null,
      params: ["id!:integer"],
      body: ["name?:string", "stripe_id?:string", "status?:integer"],
      successResponse:
        "{ error: false, message: 'Product updated successfully' }",
      errors: [
        {
          status: 400,
          error: true,
          message: "Parameter/s missing",
          validation: { id: "Id is required" },
        },
        {
          status: 404,
          error: true,
          message: "Product not found",
        },
        {
          status: 500,
          error: true,
          message: error.message || "Something went wrong",
          trace: error, //error object for debuging
        },
      ],
    },
    //prices
    {
      method: "GET",
      name: "Get stripe prices",
      url: "/v2/api/lambda/stripe/prices",
      paginated: true,
      canGetAll: true,
      query: [
        "limit!:integer|'all'",
        "page!?[limit != 'all']:integer", //page is required if limit doesn't equal 'all'
        "stripe_id?:string",
        "product_name?:string",
        "status?:integer",
        "type?:string",
        "name?:string",
        "amount?:integer",
      ],
      params: null,
      body: null,
      successResponse:
        "{ error: false, list:array[], total:integer|undefined, limit:integer|undefined, num_pages:integer|undefined, page:integer|undefined }", //list containing all matching prices
      errors: [
        {
          status: 500,
          error: true,
          message: error.message || "Something went wrong",
          trace: error, //error object for debuging
        },
      ],
    },
    {
      method: "GET",
      name: "Get single stripe price",
      url: "/v2/api/lambda/stripe/price/:id",
      paginated: false,
      canGetAll: false,
      query: null,
      params: ["id!:integer"],
      body: null,
      successResponse: "{ error: false, model: object{} }", //model containing the product
      errors: [
        {
          status: 404,
          error: true,
          message: "Price not found",
        },
        {
          status: 500,
          error: true,
          message: error.message || "Something went wrong",
          trace: error, //error object for debuging
        },
      ],
    },
    {
      method: "POST",
      name: "Create a single stripe price",
      url: "/v2/api/lambda/stripe/price",
      paginated: false,
      canGetAll: false,
      query: null,
      params: null,
      body: [
        "product_id!: integer",
        "name!: string",
        "amount!: number",
        "type!: 'recurring'|'one_time'",
        "interval!?[type == 'recurring']: 'day'|'week'|'month'|'year'", //required if type is 'recurring'
        "interval_count!?[interval]: integer", //required if interval selected
        "trial_days?: integer",
        "usage_type!?[interval]: 'licenced'|'metered'", //required if interval selected
        "usage_limit!?[usage_type == 'metered']: integer", //required if usafe type is 'metered'
      ],
      successResponse:
        "{ error: false, model: object{}, message: 'Price created successfully' }", //model containing the newly created product
      errors: [
        {
          status: 400,
          error: true,
          message: "Parameter/s missing",
          validation: {
            product_id: "Product id is required",
            name: "Name is required",
            amount: "Amount is required",
            type: "Type is required",
          },
        },
        {
          status: 404,
          error: true,
          message: "Product not found",
        },
        {
          status: 500,
          error: true,
          message: error.message || "Something went wrong",
          trace: error, //error object for debuging
        },
      ],
    },
    {
      method: "PUT",
      name: "Edit a single stripe price",
      url: "/v2/api/lambda/stripe/price/:id",
      paginated: false,
      canGetAll: false,
      query: null,
      params: ["id!:integer"],
      body: ["name?:string", "status?:integer"],
      successResponse:
        "{ error: false, message: 'Price updated successfully' }",
      errors: [
        {
          status: 400,
          error: true,
          message: "Parameter/s missing",
          validation: { id: "Id is required" },
        },
        {
          status: 404,
          error: true,
          message: "Price not found",
        },
        {
          status: 500,
          error: true,
          message: error.message || "Something went wrong",
          trace: error, //error object for debuging
        },
      ],
    },
    //customer
    {
      method: "GET",
      name: "Get logged in user stripe subscription",
      url: "/v2/api/lambda/stripe/customer/subscription",
      paginated: false,
      canGetAll: false,
      query: null,
      params: null,
      body: null,
      successResponse: "{ error: false, customer: object{} }", //customer containing the user object with subscription and plan ids attached
      errors: [
        {
          status: 500,
          error: true,
          message: error.message || "Something went wrong",
          trace: error, //error object for debuging
        },
      ],
    },
    {
      method: "GET",
      name: "Get logged in user stripe subscriptions",
      url: "/v2/api/lambda/stripe/customer/subscriptions",
      paginated: true,
      canGetAll: true,
      query: [
        "limit!:integer|'all'",
        "page!?[limit != 'all']:integer", //page is required if limit doesn't equal 'all'
      ],
      params: null,
      body: null,
      successResponse:
        "{ error: false, list:array[], total:integer|undefined, limit:integer|undefined, num_pages:integer|undefined, page:integer|undefined }", //list containing all of logged in user subscriptions
      errors: [
        {
          status: 500,
          error: true,
          message: error.message || "Something went wrong",
          trace: error, //error object for debuging
        },
      ],
    },
    {
      method: "POST",
      name: "Create a stripe subscription for a logged in user",
      url: "/v2/api/lambda/stripe/customer/subscription",
      paginated: false,
      canGetAll: false,
      query: null,
      params: null,
      body: ["planId!: integer"],
      successResponse:
        "{ error: false, message: 'User subscribed successfully' }",
      errors: [
        {
          status: 400,
          error: true,
          message: "Validation Error",
          validation: { planId: "Plan id is required" },
        },
        {
          status: 404,
          error: true,
          message: "Customer not found",
        },
        {
          status: 404,
          error: true,
          message: "Plan not found",
        },
        {
          status: 401,
          error: true,
          message: "Customer already has an active subscription",
        },
        {
          status: 403,
          error: true,
          message:
            "You don't have a valid card attached, please add one and try again",
        },
        {
          status: 500,
          error: true,
          message: error.message || "Something went wrong",
          trace: error, //error object for debuging
        },
      ],
    },
    {
      method: "POST",
      name: "Create a stripe charge for a usage metered subscription",
      url: "/v2/api/lambda/stripe/subscription/usage-charge",
      paginated: false,
      canGetAll: false,
      query: null,
      params: null,
      body: ["subId!: string", "quantity!: integer"],
      successResponse:
        "{ error: false, model: object{}, message: 'Charge recorded successfully' }", //model containing the recored charge
      errors: [
        {
          status: 400,
          error: true,
          message: "Validation Error",
          validation: {
            planId: "Plan id is required",
            quantity: "Quantity is required",
          },
        },
        {
          status: 404,
          error: true,
          message: "Subscription not found",
        },
        {
          status: 500,
          error: true,
          message: error.message || "Something went wrong",
          trace: error, //error object for debuging
        },
      ],
    },
    {
      method: "POST",
      name: "Register new user then subscribe",
      url: "/v2/api/lambda/stripe/customer/register-subscribe",
      paginated: false,
      canGetAll: false,
      query: null,
      params: null,
      body: [
        "planId!: integer",
        "email!: string",
        "password!: string",
        "cardToken!: string",
      ],
      successResponse:
        "{ error: false, model: object{}, message: 'User registered & subscribed successfully', role: 'user', token: string, expire_at: Date, user_id: integer }",
      errors: [
        {
          status: 400,
          error: true,
          message: "Validation Error",
          validation: {
            planId: "Plan id is required",
            email: "Email is required",
            password: "Password is required",
            cardToken: "Card token is required",
          },
        },
        {
          status: 403,
          error: true,
          message: "User exists",
        },
        {
          status: 404,
          error: true,
          message: "Subscription not found",
        },
        {
          status: 500,
          error: true,
          message: error.message || "Something went wrong",
          trace: error, //error object for debuging
        },
      ],
    },
    {
      method: "PUT",
      name: "Upgrade or downgrade subscription of logged in user",
      url: "/v2/api/lambda/stripe/customer/subscription",
      paginated: false,
      canGetAll: false,
      query: null,
      params: null,
      body: ["activeSubscriptionId!: integer", "newPlanId!: integer"],
      successResponse: "{ error: false, message: 'Plan changed successfully' }",
      errors: [
        {
          status: 400,
          error: true,
          message: "Validation Error",
          validation: {
            planId: "Plan id is required",
            email: "Email is required",
            password: "Password is required",
            cardToken: "Card token is required",
          },
        },
        {
          status: 404,
          error: true,
          message: "Customer not found",
        },
        {
          status: 404,
          error: true,
          message: "Passed subscription id doesn't match the customer record",
        },
        {
          status: 403,
          error: true,
          message:
            "You don't have a valid card attached, please add one and try again",
        },
        {
          status: 404,
          error: true,
          message: "Plan not found",
        },
        {
          status: 500,
          error: true,
          message: error.message || "Something went wrong",
          trace: error, //error object for debuging
        },
      ],
    },
    {
      method: "DELETE",
      name: "Cancel subscription of logged in user",
      url: "/v2/api/lambda/stripe/customer/subscription/:id",
      paginated: false,
      canGetAll: false,
      query: null,
      params: ["id!: integer"],
      body: ["cancel_type!: 'at_period_end'|'any'"],
      successResponse:
        "{ error: false, message: 'Subscription is canceled successfully' }",
      errors: [
        {
          status: 400,
          error: true,
          message: "Validation Error",
          validation: { id: "Id is required" },
        },
        {
          status: 404,
          error: true,
          message: "Subscription not found",
        },
        {
          status: 500,
          error: true,
          message: error.message || "Something went wrong",
          trace: error, //error object for debuging
        },
      ],
    },
    {
      method: "POST",
      name: "Create checkout session",
      url: "/v2/api/lambda/stripe/checkout",
      paginated: false,
      canGetAll: false,
      query: null,
      params: null,
      body: [
        "mode!: 'payment'",
        "success_url!: string",
        "cancel_url!: string",
        "payment_method_types!: ['card|...']",
        "payment_intent_data!: object{}", //used to pass custom metadata parameters so we identify the charge and do proper calculation
        "phone_number_collection?: boolean",
        "line_items!: array[]",
        "metadata?: object{}",
        "shipping_address_collection?: object{}",
        "shipping_options",
      ],
      successResponse: "{ error: false, model: object{} }", //model contains checkout object
      errors: [
        {
          status: 400,
          error: true,
          message: "Validation Error",
          validation: {
            mode: "mode is required",
            success_url: "success_url is required",
            cancel_url: "cancel_url is required",
            payment_method_types: "payment_method_types is required",
            payment_intent_data: "payment_intent_data is required",
          },
        },
        {
          status: 401,
          error: true,
          message: "Customer already has an active subscription",
        },
        {
          status: 404,
          error: true,
          message: "Customer not found",
        },
        {
          status: 403,
          error: true,
          message: "Add a card before trying to subscribe",
        },
        {
          status: 403,
          error: true,
          message:
            "You don't have a valid card attached, please add one and try again",
        },
        {
          status: 500,
          error: true,
          message: error.message || "Something went wrong",
          trace: error, //error object for debuging
        },
      ],
    },
    {
      method: "GET",
      name: "Get logged in customer stripe invoices",
      url: "/v2/api/lambda/stripe/customer/invoices",
      paginated: true, //stripe cursor pagination system
      canGetAll: false,
      query: ["after?: string, before?: string, limit!: integer"],
      params: null,
      body: null,
      successResponse: "{ error: false, list: array[], limit: integer }", //list contains invoices object
      errors: [
        {
          status: 404,
          error: true,
          message: "User not found",
        },
        {
          status: 500,
          error: true,
          message: error.message || "Something went wrong",
          trace: error, //error object for debuging
        },
      ],
    },
    {
      method: "GET",
      name: "Get logged in customer stripe orders",
      url: "/v2/api/lambda/stripe/customer/orders",
      paginated: true,
      canGetAll: true,
      query: [
        "limit!:integer|'all'",
        "page!?[limit != 'all']:integer", //page is required if limit doesn't equal 'all'
      ],
      params: null,
      body: null,
      successResponse:
        "{ error: false, list:array[], total:integer|undefined, limit:integer|undefined, num_pages:integer|undefined, page:integer|undefined }", //list containing all orders of logged in user
      errors: [
        {
          status: 404,
          error: true,
          message: "User not found",
        },
        {
          status: 500,
          error: true,
          message: error.message || "Something went wrong",
          trace: error, //error object for debuging
        },
      ],
    },
    {
      method: "POST",
      name: "Create stripe profile of an existing and logged in customer with a credit card token",
      url: "/v2/api/lambda/stripe/customer",
      paginated: false,
      canGetAll: false,
      query: null,
      params: null,
      body: ["cardToken!: string"],
      successResponse: "{ error: false, model: object{} }", //model containing the updated customer object in system
      errors: [
        {
          status: 404,
          error: true,
          message: "User not found",
        },
        {
          status: 500,
          error: true,
          message: error.message || "Something went wrong",
          trace: error, //error object for debuging
        },
      ],
    },
    {
      method: "DELETE",
      name: "Delete customer stripe profile",
      url: "/v2/api/lambda/stripe/customer",
      paginated: false,
      canGetAll: false,
      query: null,
      params: null,
      body: null,
      successResponse:
        "{ error: false, message: 'Customer deleted successfully' }",
      errors: [
        {
          status: 404,
          error: true,
          message: "User not found",
        },
        {
          status: 500,
          error: true,
          message: error.message || "Something went wrong",
          trace: error, //error object for debuging
        },
      ],
    },
  ];
};


