const TokenMiddleware = require("../../../baas/middleware/TokenMiddleware");

const middlewares = [TokenMiddleware()];

const handleGetDashboard = async (req, res, sdk) => {
  try {
    sdk.setProjectId("rowanenergy");
    
    // Get user data
    const user = await sdk.findOne("user", { id: req.user_id });
    if (!user) {
      return res.status(401).json({
        error: true,
        message: "Invalid Credentials",
      });
    }

    const userData = JSON.parse(user?.data ?? "{}");
    
    // Get user preferences for full name
    const preferences = await sdk.findOne("preference", {
      user_id: req.user_id,
    });

    const firstName = preferences?.first_name ?? userData?.first_name ?? "User";
    const lastName = preferences?.last_name ?? userData?.last_name ?? "";
    const fullName = `${firstName} ${lastName}`.trim();

    // Get job surveys for this user (bookings are called job_survey in the database)
    const bookingsResult = await sdk.rawQuery(
      `SELECT * FROM rowanenergy_job_survey WHERE customer_id = ? ORDER BY scheduled_time ASC LIMIT 10`,
      [req.user_id]
    );

    const bookings = bookingsResult || [];
    
    // Find next upcoming survey
    const now = new Date();
    const upcomingBookings = bookings.filter(booking => {
      if (!booking.scheduled_time) return false;
      const bookingDate = new Date(booking.scheduled_time);
      return bookingDate > now;
    });

    const nextSurvey = upcomingBookings.length > 0 ? upcomingBookings[0] : null;

    // Get recent bookings for reports
    const recentBookings = bookings.filter(booking => 
      booking.status === 'completed' || booking.status === 'in_progress'
    ).slice(0, 5);

    // Calculate stats
    const totalBookings = bookings.length;
    const completedBookings = bookings.filter(b => b.status === 'completed').length;
    const pendingBookings = bookings.filter(b => b.status === 'pending').length;
    const inProgressBookings = bookings.filter(b => b.status === 'in_progress').length;

    return res.status(200).json({
      error: false,
      model: {
        user: {
          id: user.id,
          email: user.email,
          first_name: firstName,
          last_name: lastName,
          full_name: fullName,
          photo: preferences?.photo ?? userData?.photo
        },
        stats: {
          total_bookings: totalBookings,
          completed_bookings: completedBookings,
          pending_bookings: pendingBookings,
          in_progress_bookings: inProgressBookings
        },
        next_survey: nextSurvey ? {
          id: nextSurvey.id,
          site_name: nextSurvey.site_name,
          scheduled_time: nextSurvey.scheduled_time,
          asset_type: nextSurvey.asset_type,
          location: nextSurvey.location,
          status: nextSurvey.status
        } : null,
        recent_reports: recentBookings.map(booking => ({
          id: booking.id,
          site_name: booking.site_name,
          scheduled_time: booking.scheduled_time,
          asset_type: booking.asset_type,
          status: booking.status,
          created_at: booking.created_at
        })),
        all_bookings: bookings.map(booking => ({
          id: booking.id,
          site_name: booking.site_name,
          scheduled_time: booking.scheduled_time,
          asset_type: booking.asset_type,
          location: booking.location,
          status: booking.status,
          created_at: booking.created_at
        }))
      }
    });
  } catch (err) {
    console.error("Get dashboard error:", err);
    return res.status(403).json({
      error: true,
      message: err.message,
    });
  }
};

module.exports = function (app) {
  app.get("/v1/api/rowanenergy/customer/lambda/dashboard-enhanced", middlewares, async (req, res) => {
    await handleGetDashboard(req, res, app.get("sdk"));
  });
};
