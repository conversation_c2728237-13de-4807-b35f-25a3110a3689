const JwtService = require("../../../baas/services/JwtService");
const PasswordService = require("../../../baas/services/PasswordService");
const User = require("../models/user");
const MailService = require("../../../baas/services/MailService");
const { filterEmptyFields } = require("../../../baas/services/UtilService");

module.exports = function (app) {
  const config = app.get("configuration");
  app.post("/v1/api/rowanenergy/drone-engineer/auth/register", async function (req, res) {
    try {
      // Get the role class and check registration permission
      const RoleClass = require(`../roles/drone-engineer`);
      if (!RoleClass.canRegister()) {
        return res.status(403).json({
          error: true,
          message: `${RoleClass.name} cannot register`,
        });
      }

      let verify = RoleClass.canVerifyEmail() ? true : false;
      const needRefreshToken = req.body.is_refresh ? true : false;
      let refreshToken = undefined;

      // Create user object and validate
      const userObj = filterEmptyFields({
        email: req.body.email,
        role_id: RoleClass.slug,
        password: req.body.password,
        login_type: 0,
        status: 0,
        verify: verify ? 0 : 1,
        company_id: req.body.company_id || 0,
        data: JSON.stringify({
          first_name: req.body.first_name || req.body.name || "",
          last_name: req.body.last_name || "",
          photo: req.body.photo || "",
          phone: req.body.phone || "",
          certifications: req.body.certifications || "",
          experience: req.body.experience || ""
        })
      });

      if (!userObj.email) {
        return res.status(403).json({
          error: true,
          message: "Email is required"
        });
      }

      const sdk = app.get("sdk");

      // Check if user already exists
      sdk.setProjectId("rowanenergy");
      const existingUser = await sdk.findOne('user', {
          email: userObj.email
      });

      if (existingUser) {
        return res.status(403).json({
          error: true,
          message: "User already exists with this email"
        });
      }

      // Hash password
      const hashedPassword = await PasswordService.hash(req.body.password);

      // Create user record
      const userData = {
        email: req.body.email,
        password: hashedPassword,
        role_id: RoleClass.slug,
        login_type: 0,
        status: verify ? 0 : 1,
        verify: verify ? 0 : 1,
        company_id: req.body.company_id || 0,
        data: JSON.stringify({
          first_name: req.body.first_name || req.body.name || "",
          last_name: req.body.last_name || "",
          photo: req.body.photo || "",
          phone: req.body.phone || "",
          certifications: req.body.certifications || "",
          experience: req.body.experience || ""
        }),
        created_at: new Date(),
        updated_at: new Date()
      };

      // Insert user
      sdk.setProjectId("rowanenergy");
      const result = await sdk.create('user', userData);

      if (!result) {
        throw new Error('Failed to create user');
      }

      const userId = result.id;

      // Create preference record with default availability settings
      try {
        const defaultAvailability = {
          Monday: { morning: true, afternoon: true, evening: false },
          Tuesday: { morning: true, afternoon: true, evening: false },
          Wednesday: { morning: true, afternoon: true, evening: false },
          Thursday: { morning: true, afternoon: true, evening: false },
          Friday: { morning: true, afternoon: true, evening: false },
          Saturday: { morning: false, afternoon: false, evening: false },
          Sunday: { morning: false, afternoon: false, evening: false }
        };

        const defaultRegions = {
          'North England': true,
          'South England': true,
          'East England': false,
          'West England': false,
          'Wales': false,
          'Scotland': false
        };

        await sdk.create('preference', {
          user_id: userId,
          first_name: req.body.first_name || req.body.name || "",
          last_name: req.body.last_name || "",
          phone: req.body.phone || "",
          photo: req.body.photo || "",
          company: req.body.company || "",
          weekly_availability: JSON.stringify(defaultAvailability),
          region_preferences: JSON.stringify(defaultRegions),
          workload_limit: 'Limit to 1 job per day maximum',
          created_at: new Date(),
          updated_at: new Date()
        });
      } catch (prefError) {
        console.error('Error creating preference record:', prefError);
        // Don't fail registration if preference creation fails
      }

      // Generate tokens
      const tokenPayload = {
        user_id: userId,
        role: RoleClass.slug
      };

      let response = {
        error: false,
        role: RoleClass.slug,
        token: JwtService.createAccessToken(
          tokenPayload,
          config.access_jwt_expire,
          config.jwt_key
        ),
        expire_at: config.access_jwt_expire,
        user_id: userId
      };

      // Handle refresh token if needed
      if (needRefreshToken) {
        refreshToken = JwtService.createAccessToken(
          tokenPayload,
          config.refresh_jwt_expire,
          config.jwt_key
        );

        let expireDate = new Date();
        expireDate.setSeconds(expireDate.getSeconds() + config.refresh_jwt_expire);

        // Save refresh token
        sdk.setProjectId("rowanenergy");
        await sdk.create('tokens', {
          user_id: userId,
          token: refreshToken,
          code: refreshToken,
          type: 1,
          data: "{}",
          expired_at: expireDate,
          updated_at: new Date(),
          created_at: new Date()
        });

        response.refresh_token = refreshToken;
      }

      // If verification is required, generate verification token and send email
      if (verify) {
        const verificationToken = JwtService.createAccessToken(
          { user_id: userId, token: Math.random().toString(36).substring(2, 15) },
          config.verification_token_expire,
          config.jwt_key
        );

        await sdk.create('tokens', {
          user_id: userId,
          token: verificationToken,
          code: verificationToken,
          type: 3,
          data: '{}',
          expired_at: new Date(Date.now() + config.verification_token_expire * 1000),
          updated_at: new Date(),
          created_at: new Date()
        });

        const mailService = new MailService(config);
        const verificationUrl = `${config.app_url}/verify-email`;
        try {
          await mailService.sendVerificationEmail(
            userData.email,
            verificationToken,
            verificationUrl
          );
        } catch (err) {
          console.error(err);
        }
      }

      return res.status(200).json(response);

    } catch (err) {
      res.status(403).json({
        error: true,
        message: err.message
      });
    }
  });

  return [
    {
      method: "POST",
      name: "Drone Engineer Register API",
      url: "/v1/api/rowanenergy/drone-engineer/auth/register",
      successBody: '{ "email": "<EMAIL>", "name": "Engineer Name", "phone": "+1234567890", "certifications": "Part 107 Certified", "experience": "5 years", "is_refresh": true, "password": "a123456"}',
      successPayload: '{"error":false,"role":"drone-engineer","token":"JWT Token","expire_at":3600,"user_id":20}',
      errors: [
        {
          name: "403",
          body: '{"role": "drone-engineer", "password": "a123456", "is_refresh": false}',
          response: '{"error": true,"message": "Email Missing","validation": [{ "field": "email", "message": "Email missing" }]}'
        },
        {
          name: "403",
          body: '{ "email": "<EMAIL>", "is_refresh": false}',
          response: '{"error": true,"message": "Password","validation": [{ "field": "password", "message": "Password missing" }]}'
        },
        {
          name: "403",
          body: '{ "email": "<EMAIL>", "role": "drone-engineer", "password": "a123456", "is_refresh": false}',
          response: '{"error": true,"message": "Some error message"}'
        }
      ],
      needToken: false
    }
  ];
};

// API definition for Postman collection
module.exports.getPostmanDefinition = function () {
  return [
    {
      method: "POST",
      name: "Drone Engineer Registration API",
      url: "/v1/api/rowanenergy/drone-engineer/auth/register",
      successBody: '{ "email": "<EMAIL>", "password": "password123", "name": "New Engineer", "phone": "+1234567890", "certifications": "Part 107", "experience": "3 years" }',
      successPayload: '{"error":false, "message": "Registration successful", "user": {"id": 1, "email": "<EMAIL>", "name": "New Engineer"}}',
      errors: [
        {
          name: "409",
          body: '{ "email": "<EMAIL>", "password": "password123", "name": "Existing Engineer" }',
          response: '{"error":true,"message":"Email already exists"}',
        },
        {
          name: "400",
          body: '{}',
          response: '{"error":true,"message":"Invalid registration data","validation":[{"field": "email", "message": "Email is required"}, {"field": "password", "message": "Password is required"}]}',
        }
      ],
      needToken: false,
    },
  ];
};