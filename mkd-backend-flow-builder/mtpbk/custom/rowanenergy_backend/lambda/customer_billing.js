const TokenMiddleware = require("../../../baas/middleware/TokenMiddleware");
const StripeService = require("../../../baas/services/StripeService");

const middlewares = [TokenMiddleware()];
const stripeService = new StripeService();

const handleGetBillingInfo = async (req, res, sdk) => {
  try {
    sdk.setProjectId("rowanenergy");
    
    // Get user data
    const user = await sdk.findOne("user", { id: req.user_id });
    if (!user) {
      return res.status(401).json({
        error: true,
        message: "Invalid Credentials",
      });
    }

    const userData = JSON.parse(user?.data ?? "{}");
    
    // Check if user has a Stripe customer ID
    let stripeCustomer = null;
    let subscription = null;
    let paymentMethods = [];
    
    if (userData.stripe_customer_id) {
      try {
        // Get Stripe customer details
        const customerResult = await stripeService.retrieveStripeCustomer({
          customerId: userData.stripe_customer_id
        });
        
        if (customerResult && !customerResult.error) {
          stripeCustomer = customerResult;
          
          // Get active subscriptions
          const subscriptionsResult = await stripeService.listStripeSubscription({
            customer: userData.stripe_customer_id,
            status: 'active'
          });
          
          if (subscriptionsResult && subscriptionsResult.data && subscriptionsResult.data.length > 0) {
            subscription = subscriptionsResult.data[0]; // Get the first active subscription
          }
          
          // Get payment methods (newer API)
          const paymentMethodsResult = await stripeService.retrieveStripePaymentMethodAll({
            customerId: userData.stripe_customer_id,
            type: 'card'
          });
          
          if (paymentMethodsResult && paymentMethodsResult.data) {
            paymentMethods = paymentMethodsResult.data;
          }

          // Also get card sources (older API) and merge with payment methods
          const cardSourcesResult = await stripeService.retrieveStripeCustomerAllCards({
            customerId: userData.stripe_customer_id
          });
          
          if (cardSourcesResult && cardSourcesResult.data) {
            // Convert sources to payment method format for consistency
            const formattedSources = cardSourcesResult.data.map(source => ({
              id: source.id,
              object: 'payment_method',
              card: {
                brand: source.brand,
                exp_month: source.exp_month,
                exp_year: source.exp_year,
                last4: source.last4,
                funding: source.funding
              },
              type: 'card',
              created: source.created || Date.now(),
              customer: source.customer
            }));
            
            // Merge sources with payment methods, avoiding duplicates
            paymentMethods = [...paymentMethods, ...formattedSources];
          }
        }
      } catch (stripeError) {
        console.error("Stripe API error:", stripeError);
      }
    }

    return res.status(200).json({
      error: false,
      model: {
        user: {
          id: user.id,
          email: user.email,
          stripe_customer_id: userData.stripe_customer_id || null
        },
        billing: {
          customer: stripeCustomer || null,
          subscription: subscription || null,
          payment_methods: paymentMethods || [],
          current_plan: subscription ? {
            name: subscription.items?.data[0]?.price?.nickname || 'Premium Plan',
            amount: subscription.items?.data[0]?.price?.unit_amount / 100,
            currency: subscription.items?.data[0]?.price?.currency,
            interval: subscription.items?.data[0]?.price?.recurring?.interval,
            status: subscription.status
          } : {
            name: 'Free Plan',
            amount: 0,
            currency: 'usd',
            interval: 'month',
            status: 'active'
          }
        }
      }
    });
  } catch (err) {
    console.error("Get billing info error:", err);
    return res.status(403).json({
      error: true,
      message: err.message,
    });
  }
};

const handleCreateCardToken = async (req, res, sdk) => {
  try {
    const { card_number, exp_month, exp_year, cvc } = req.body;
    
    if (!card_number || !exp_month || !exp_year || !cvc) {
      return res.status(400).json({
        error: true,
        message: "Card details are required (card_number, exp_month, exp_year, cvc)",
      });
    }

    // Create card token using Stripe
    const tokenResult = await stripeService.createStripeCardToken({
      card_number,
      exp_month,
      exp_year,
      cvc
    });

    if (tokenResult.error) {
      return res.status(400).json({
        error: true,
        message: tokenResult.message || "Failed to create card token",
      });
    }

    return res.status(200).json({
      error: false,
      token: tokenResult
    });
  } catch (err) {
    console.error("Create card token error:", err);
    return res.status(500).json({
      error: true,
      message: err.message,
    });
  }
};

const handleCreateStripeCustomer = async (req, res, sdk) => {
  try {
    sdk.setProjectId("rowanenergy");
    
    const user = await sdk.findOne("user", { id: req.user_id });
    if (!user) {
      return res.status(401).json({
        error: true,
        message: "Invalid Credentials",
      });
    }

    const userData = JSON.parse(user?.data ?? "{}");
    
    // Check if customer already exists
    if (userData.stripe_customer_id) {
      return res.status(400).json({
        error: true,
        message: "Stripe customer already exists",
      });
    }

    // Create Stripe customer
    const customerResult = await stripeService.createStripeCustomer({
      email: user.email,
      metadata: {
        user_id: req.user_id,
        platform: 'rowanenergy',
        name: `${userData.first_name || ''} ${userData.last_name || ''}`.trim() || user.email
      }
    });

    if (customerResult.error) {
      return res.status(400).json({
        error: true,
        message: customerResult.message || "Failed to create Stripe customer",
      });
    }

    // Update user data with Stripe customer ID
    const updatedUserData = {
      ...userData,
      stripe_customer_id: customerResult.id
    };

    await sdk.updateById("user", req.user_id, {
      data: JSON.stringify(updatedUserData)
    });

    return res.status(200).json({
      error: false,
      message: "Stripe customer created successfully",
      customer: customerResult
    });
  } catch (err) {
    console.error("Create Stripe customer error:", err);
    return res.status(403).json({
      error: true,
      message: err.message,
    });
  }
};

const handleCreateSubscription = async (req, res, sdk) => {
  try {
    const { price_id, payment_method_id, card_token } = req.body;
    
    if (!price_id) {
      return res.status(400).json({
        error: true,
        message: "Price ID is required",
      });
    }

    // Validate that this is a real Stripe price ID, not a fallback
    if (price_id.includes('fallback')) {
      return res.status(400).json({
        error: true,
        message: "Cannot subscribe to fallback plans. Please contact support.",
      });
    }

    sdk.setProjectId("rowanenergy");
    
    const user = await sdk.findOne("user", { id: req.user_id });
    if (!user) {
      return res.status(401).json({
        error: true,
        message: "Invalid Credentials",
      });
    }

    const userData = JSON.parse(user?.data ?? "{}");
    let customerId = userData.stripe_customer_id;
    
    // If no customer exists or card_token is provided (new payment method), create/update customer
    if (!customerId || card_token) {
      const customerData = {
        email: user.email,
        metadata: {
          user_id: req.user_id,
          platform: 'rowanenergy',
          name: `${userData.first_name || ''} ${userData.last_name || ''}`.trim() || user.email
        }
      };

      if (card_token) {
        customerData.tokenId = card_token;
      }

      if (!customerId) {
        // Create new customer
        const customerResult = await stripeService.createStripeCustomer(customerData);
        if (customerResult.error) {
          return res.status(400).json({
            error: true,
            message: customerResult.message || "Failed to create Stripe customer",
          });
        }
        customerId = customerResult.id;

        // Update user data with Stripe customer ID
        const updatedUserData = {
          ...userData,
          stripe_customer_id: customerId
        };

        await sdk.updateById("user", req.user_id, {
          data: JSON.stringify(updatedUserData)
        });
      } else if (card_token) {
        // Add new card to existing customer
        const cardResult = await stripeService.addNewCardToStripeCustomer({
          tokenId: card_token,
          customerId: customerId,
          metadata: { platform: 'rowanenergy' }
        });
        
        if (cardResult.error) {
          return res.status(400).json({
            error: true,
            message: cardResult.message || "Failed to add payment method",
          });
        }
      }
    }

    // Create subscription
    const subscriptionData = {
      customerId: customerId,
      priceId: price_id,
      metadata: {
        user_id: req.user_id,
        platform: 'rowanenergy'
      }
    };

    if (payment_method_id) {
      subscriptionData.default_payment_method = payment_method_id;
    }

    const subscriptionResult = await stripeService.createStripeSubscription(subscriptionData);

    if (subscriptionResult.error) {
      return res.status(400).json({
        error: true,
        message: subscriptionResult.message || "Failed to create subscription",
      });
    }

    return res.status(200).json({
      error: false,
      message: "Subscription created successfully",
      subscription: subscriptionResult
    });
  } catch (err) {
    console.error("Create subscription error:", err);
    return res.status(403).json({
      error: true,
      message: err.message,
    });
  }
};

const handleCancelSubscription = async (req, res, sdk) => {
  try {
    const { subscription_id, immediate = false } = req.body;
    
    if (!subscription_id) {
      return res.status(400).json({
        error: true,
        message: "Subscription ID is required",
      });
    }

    let cancelResult;
    
    if (immediate) {
      // Cancel subscription immediately (useful for payment method addition cleanup)
      cancelResult = await stripeService.cancelStripeSubscription({
        subscriptionId: subscription_id
      });
    } else {
      // Cancel subscription at period end (more user-friendly for real cancellations)
      cancelResult = await stripeService.cancelStripeSubscriptionAtPeriodEnd({
        subscriptionId: subscription_id,
        days: 30, // Provide default days_until_due to avoid Stripe error
        collection_method: "charge_automatically" // Use automatic collection to avoid days_until_due requirement
      });
    }

    if (cancelResult.error) {
      return res.status(400).json({
        error: true,
        message: cancelResult.message || "Failed to cancel subscription",
      });
    }

    return res.status(200).json({
      error: false,
      message: immediate ? "Subscription cancelled immediately" : "Subscription cancelled successfully",
      subscription: cancelResult
    });
  } catch (err) {
    console.error("Cancel subscription error:", err);
    return res.status(403).json({
      error: true,
      message: err.message,
    });
  }
};

const handleAddPaymentMethod = async (req, res, sdk) => {
  try {
    const { card_token } = req.body;
    
    if (!card_token) {
      return res.status(400).json({
        error: true,
        message: "Card token is required",
      });
    }

    sdk.setProjectId("rowanenergy");
    
    const user = await sdk.findOne("user", { id: req.user_id });
    if (!user) {
      return res.status(401).json({
        error: true,
        message: "Invalid Credentials",
      });
    }

    const userData = JSON.parse(user?.data ?? "{}");
    let customerId = userData.stripe_customer_id;
    
    // Create Stripe customer if doesn't exist
    if (!customerId) {
      const customerResult = await stripeService.createStripeCustomer({
        email: user.email,
        metadata: {
          user_id: req.user_id,
          platform: 'rowanenergy',
          name: `${userData.first_name || ''} ${userData.last_name || ''}`.trim() || user.email
        }
      });

      if (customerResult.error) {
        return res.status(400).json({
          error: true,
          message: customerResult.message || "Failed to create customer",
        });
      }

      customerId = customerResult.id;

      // Update user data with Stripe customer ID
      const updatedUserData = {
        ...userData,
        stripe_customer_id: customerId
      };

      await sdk.updateById("user", req.user_id, {
        data: JSON.stringify(updatedUserData)
      });
    }

    // Add the payment method directly to the customer (Stripe handles verification)
    const cardResult = await stripeService.addNewCardToStripeCustomer({
      tokenId: card_token,
      customerId: customerId,
      metadata: { 
        platform: 'rowanenergy',
        added_via: 'payment_method_form'
      }
    });

    if (cardResult.error) {
      return res.status(400).json({
        error: true,
        message: cardResult.message || "Failed to add payment method",
      });
    }

    return res.status(200).json({
      error: false,
      message: "Payment method added successfully",
      payment_method: cardResult
    });
  } catch (err) {
    console.error("Add payment method error:", err);
    return res.status(403).json({
      error: true,
      message: err.message,
    });
  }
};

const handleGetAvailablePlans = async (req, res, sdk) => {
  try {
    sdk.setProjectId("rowanenergy");
    let plans = [];

    try {
      // First try to get plans from Stripe API
      const plansResult = await stripeService.retrieveStripePrices({
        active: true,
        expand: ['data.product']
      });

      if (!plansResult.error && plansResult.data) {
        // Filter and format plans for Hover Lens
        plans = plansResult.data
          .filter(price => price.product?.metadata?.platform === 'rowanenergy')
          .map(price => ({
            id: price.id,
            product_id: price.product.id,
            name: price.nickname || price.product.name,
            description: price.product.description,
            amount: price.unit_amount / 100,
            currency: price.currency,
            interval: price.recurring?.interval,
            interval_count: price.recurring?.interval_count,
            features: price.product.metadata?.features ? JSON.parse(price.product.metadata.features) : []
          }));
      }
    } catch (stripeError) {
      console.error("Stripe API error, falling back to database:", stripeError.message);
    }

    // If no plans from Stripe, try to get from database
    if (plans.length === 0) {
      try {
        const dbPricesQuery = `
          SELECT 
            sp.stripe_price_id as id,
            sp.stripe_product_id as product_id,
            sp.nickname as name,
            spr.description,
            sp.unit_amount,
            sp.currency,
            sp.recurring_interval as recurring_interval,
            sp.recurring_interval_count as interval_count,
            spr.metadata
          FROM rowanenergy_stripe_prices sp
          JOIN rowanenergy_stripe_products spr ON sp.stripe_product_id = spr.stripe_product_id
          WHERE sp.active = 1 AND spr.active = 1
          ORDER BY sp.unit_amount ASC
        `;
        
        const dbPlans = await sdk.rawQuery(dbPricesQuery, []);

        console.log('dbPlans', dbPlans);
        
        plans = dbPlans.map(plan => ({
          id: plan.id,
          product_id: plan.product_id,
          name: plan.name,
          description: plan.description,
          amount: plan.unit_amount / 100,
          currency: plan.currency,
          interval: plan.recurring_interval,
          interval_count: plan.interval_count,
          features: plan.metadata ? JSON.parse(plan.metadata).features || [] : []
        }));
        
        console.log('Loaded plans from database:', plans.length);
      } catch (dbError) {
        console.error("Database query error:", dbError.message);
      }
    }

    // If still no plans, provide default fallback
    if (plans.length === 0) {
      plans = [
        {
          id: 'premium-monthly-fallback',
          name: 'Premium Plan',
          description: 'Perfect for growing businesses with unlimited survey bookings and priority support',
          amount: 49,
          currency: 'usd',
          interval: 'month',
          features: [
            'Unlimited survey bookings',
            'Priority engineer assignment',
            'Advanced reporting & analytics',
            'Priority support',
            'Custom branding on reports'
          ]
        },
        {
          id: 'enterprise-monthly-fallback',
          name: 'Enterprise Plan',
          description: 'For large organizations requiring dedicated support and custom solutions',
          amount: 99,
          currency: 'usd',
          interval: 'month',
          features: [
            'Everything in Premium',
            'Dedicated account manager',
            'Custom SLA agreements',
            'API access',
            'White-label solution',
            'Multi-location management'
          ]
        }
      ];
    }

    return res.status(200).json({
      error: false,
      model: {
        plans: plans,
        free_plan: {
          id: 'free',
          name: 'Free Plan',
          description: 'Basic features for getting started',
          amount: 0,
          currency: 'usd',
          interval: 'month',
          features: [
            'Up to 2 survey bookings per month',
            'Basic reporting',
            'Email support'
          ]
        }
      }
    });
  } catch (err) {
    console.error("Get available plans error:", err);
    return res.status(500).json({
      error: true,
      message: err.message,
    });
  }
};

module.exports = function (app) {
  // Get billing information
  app.get("/v1/api/rowanenergy/customer/lambda/billing-info", middlewares, async (req, res) => {
    await handleGetBillingInfo(req, res, app.get("sdk"));
  });

  // Create card token
  app.post("/v1/api/rowanenergy/customer/lambda/create-card-token", middlewares, async (req, res) => {
    await handleCreateCardToken(req, res, app.get("sdk"));
  });

  // Create Stripe customer
  app.post("/v1/api/rowanenergy/customer/lambda/create-stripe-customer", middlewares, async (req, res) => {
    await handleCreateStripeCustomer(req, res, app.get("sdk"));
  });

  // Create subscription
  app.post("/v1/api/rowanenergy/customer/lambda/create-subscription", middlewares, async (req, res) => {
    await handleCreateSubscription(req, res, app.get("sdk"));
  });

  // Cancel subscription
  app.post("/v1/api/rowanenergy/customer/lambda/cancel-subscription", middlewares, async (req, res) => {
    await handleCancelSubscription(req, res, app.get("sdk"));
  });

  // Get available plans
  app.get("/v1/api/rowanenergy/customer/lambda/available-plans", middlewares, async (req, res) => {
    await handleGetAvailablePlans(req, res, app.get("sdk"));
  });

  // Add payment method
  app.post("/v1/api/rowanenergy/customer/lambda/add-payment-method", middlewares, async (req, res) => {
    await handleAddPaymentMethod(req, res, app.get("sdk"));
  });
};
