const JwtService = require("../../../baas/services/JwtService");
const PasswordService = require("../../../baas/services/PasswordService");
const User = require("../models/user");
const { sqlDateFormat, sqlDateTimeFormat, filterEmptyFields } = require("../../../baas/services/UtilService");

module.exports = function (app) {
  app.post("/v1/api/rowanenergy/member/auth/login", async function (req, res) {
    try {
      const config = app.get('configuration');
      const sdk = app.get("sdk");
      sdk.setProjectId("rowanenergy");

      // Check role permissions
      const Role = require(`../roles/member`);
      if (!Role.canLogin()) {
        return res.status(403).json({
          error: true,
          message: "Forbidden access"
        });
      }

      // Validate input using User model
      const userInput = filterEmptyFields({
        email: req.body.email,
        password: req.body.password,
        role_id: Role.slug,
        login_type: 0,
        status: 0,
        verify: 0,
        company_id: 0
      });

      if (!userInput.email) {
        return res.status(400).json({
          error: true,
          message: "Email is required"
        });
      }

      // Query user from database
      const user = await sdk.findOne('user', {
        email: req.body.email,
        role_id: Role.slug
      });

      if (!user) {
        return res.status(403).json({
          error: true,
          message: "Invalid credentials"
        });
      }

      // Verify password using PasswordService
      const validPassword = await PasswordService.compareHash(req.body.password, user.password);
      console.log(validPassword);
      if (!validPassword) {
        return res.status(403).json({
          error: true,
          message: "Invalid credentials"
        });
      }

      // Check account status
      if (user.status == 2) {
        console.log(user.status);
        return res.status(403).json({
          error: true,
          message: "Your account is disabled"
        });
      }

      // Check email verification
      if (!user.verify) {
        console.log(user.verify);
        return res.status(403).json({
          error: true,
          message: "Your email is not verified"
        });
      }

      // Handle refresh token if needed
      let refreshToken;
      if (req.body.is_refresh) {
        refreshToken = JwtService.createAccessToken(
          {
            user_id: user.id,
            role: Role.slug
          },
          config.refresh_jwt_expire,
          config.jwt_key
        );

        let expireDate = new Date();
        expireDate.setSeconds(expireDate.getSeconds() + config.refresh_jwt_expire);

        // Save refresh token to tokens table
        await sdk.create('tokens', {
          user_id: user.id,
          token: refreshToken,
          code: refreshToken,
          expired_at: expireDate,
          type: 1,
          created_at: new Date(),
          updated_at: new Date(),
        });
      }

      // Parse user data if it exists
      let userData = {};
      if (user.data) {
        try {
          userData = JSON.parse(user.data);
        } catch (e) {
          userData = {};
        }
      }

      return res.status(200).json({
        error: false,
        role: Role.slug,
        token: JwtService.createAccessToken(
          {
            user_id: user.id,
            role: Role.slug
          },
          config.access_jwt_expire,
          config.jwt_key
        ),
        refresh_token: refreshToken,
        expire_at: config.access_jwt_expire,
        user_id: user.id,
        first_name: userData.first_name ?? "",
        last_name: userData.last_name ?? "",
        photo: userData.photo ?? ""
      });

    } catch (err) {
      console.error(err);
      res.status(403).json({
        error: true,
        message: err.message
      });
    }
  });

  // API definition for Postman collection
  module.exports.getPostmanDefinition = function () {
    return [
      {
        method: "POST",
        name: "Member Login API",
        url: "/v1/api/rowanenergy/member/auth/login",
        successBody: '{ "email": "<EMAIL>", "password": "password123" }',
        successPayload: '{"error":false, "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...", "refresh_token": "...", "user_id": 1, "role": "member"}',
        errors: [
          {
            name: "403",
            body: '{ "email": "<EMAIL>", "password": "wrongpassword" }',
            response: '{"error":true,"message":"Invalid credentials"}',
          },
          {
            name: "400",
            body: '{}',
            response: '{"error":true,"message":"Validation failed","validation":[{"field": "email", "message": "Email is required"}, {"field": "password", "message": "Password is required"}]}',
          }
        ],
        needToken: false,
      },
    ];
  };
};