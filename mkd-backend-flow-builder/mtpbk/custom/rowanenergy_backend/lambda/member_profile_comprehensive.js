const TokenMiddleware = require("../../../baas/middleware/TokenMiddleware");
const PasswordService = require("../../../baas/services/PasswordService");

const middlewares = [TokenMiddleware()];

const handleGetProfile = async (req, res, sdk) => {
  try {
    // Get user data
    sdk.setProjectId("rowanenergy");
    const user = await sdk.findOne("user", { id: req.user_id });

    if (!user) {
      return res.status(401).json({
        error: true,
        message: "Invalid Credentials",
      });
    }
    
    const userData = JSON.parse(user?.data ?? "{}");

    // Get user preferences
    const preferences = await sdk.findOne("preference", {
      user_id: req.user_id,
    });

    return res.status(200).json({
      error: false,
      model: {
        ...user,
        id: user.id,
        email: user.email,
        status: user.status,
        role_id: user.role_id,
        first_name: preferences?.first_name ?? userData?.first_name,
        last_name: preferences?.last_name ?? userData?.last_name,
        phone: preferences?.phone ?? userData?.phone,
        company: preferences?.company ?? userData?.company,
        photo: preferences?.photo ?? userData?.photo,
        data: userData,
      },
    });
  } catch (err) {
    console.error("Get profile error:", err);
    return res.status(403).json({
      error: true,
      message: err.message,
    });
  }
};

const handleUpdateProfile = async (req, res, sdk) => {
  try {
    sdk.setProjectId("rowanenergy");
    const user = await sdk.findOne("user", { id: req.user_id });

    if (!user) {
      return res.status(401).json({
        error: true,
        message: "Invalid Credentials",
      });
    }

    const userData = JSON.parse(user?.data ?? "{}");
    const payload = req.body.payload || req.body;

    // Update user data in the 'data' field
    const updatedUserData = {
      ...userData,
      first_name: payload?.first_name ?? userData?.first_name,
      last_name: payload?.last_name ?? userData?.last_name,
      phone: payload?.phone ?? userData?.phone,
      company: payload?.company ?? userData?.company,
      photo: payload?.photo ?? userData?.photo,
    };

    // Update user table
    const updateData = {
      data: JSON.stringify(updatedUserData),
    };

    await sdk.updateById("user", req.user_id, updateData);

    // Update or create preferences
    const existingPreference = await sdk.findOne("preference", {
      user_id: req.user_id,
    });

    const preferenceData = {
      user_id: req.user_id,
      first_name: payload?.first_name ?? userData?.first_name,
      last_name: payload?.last_name ?? userData?.last_name,
      phone: payload?.phone ?? userData?.phone,
      company: payload?.company ?? userData?.company,
      photo: payload?.photo ?? userData?.photo,
    };

    if (existingPreference) {
      await sdk.updateById("preference", existingPreference.id, preferenceData);
    } else {
      await sdk.create("preference",preferenceData);
    }

    return res.status(200).json({
      error: false,
      message: "Profile updated successfully",
    });
  } catch (err) {
    console.error("Update profile error:", err);
    return res.status(403).json({
      error: true,
      message: err.message,
    });
  }
};

const handleUpdatePassword = async (req, res, sdk) => {
  try {
    if (!req.body.password) {
      return res.status(400).json({
        error: true,
        message: "Password is required",
      });
    }

    sdk.setProjectId("rowanenergy");
    const user = await sdk.findOne("user", { id: req.user_id });

    if (!user) {
      return res.status(401).json({
        error: true,
        message: "Invalid Credentials",
      });
    }

    // Hash the new password
    const hashPassword = await PasswordService.hash(req.body.password);
    
    await sdk.updateById("user", req.user_id, {
      password: hashPassword
    });

    return res.status(200).json({
      error: false,
      message: "Password updated successfully",
    });
  } catch (err) {
    console.error("Update password error:", err);
    return res.status(403).json({
      error: true,
      message: err.message,
    });
  }
};

const handleUploadProfileImage = async (req, res, sdk) => {
  try {
    if (!req.body.photo) {
      return res.status(400).json({
        error: true,
        message: "Photo URL is required",
      });
    }

    sdk.setProjectId("rowanenergy");
    const user = await sdk.findOne("user", { id: req.user_id });

    if (!user) {
      return res.status(401).json({
        error: true,
        message: "Invalid Credentials",
      });
    }

    const userData = JSON.parse(user?.data ?? "{}");
    
    // Update user data with photo
    const updatedUserData = {
      ...userData,
      photo: req.body.photo,
    };

    await sdk.updateById("user", req.user_id, {
      data: JSON.stringify(updatedUserData),
    });

    // Update preferences as well
    const existingPreference = await sdk.findOne("preference", {
      user_id: req.user_id,
    });

    if (existingPreference) {
      await sdk.updateById("preference", existingPreference.id, {
        photo: req.body.photo
      });
    } else {
      await sdk.create("preference",{
        user_id: req.user_id,
        photo: req.body.photo
      });
    }

    return res.status(200).json({
      error: false,
      message: "Profile image updated successfully",
      photo: req.body.photo,
    });
  } catch (err) {
    console.error("Upload profile image error:", err);
    return res.status(403).json({
      error: true,
      message: err.message,
    });
  }
};

module.exports = function (app) {
  // Get profile
  app.get("/v1/api/rowanenergy/member/lambda/profile-comprehensive", middlewares, async (req, res) => {
    await handleGetProfile(req, res, app.get("sdk"));
  });
  
  app.get("/v1/api/rowanenergy/customer/lambda/profile-comprehensive", middlewares, async (req, res) => {
    await handleGetProfile(req, res, app.get("sdk"));
  });

  // Update profile
  app.post("/v1/api/rowanenergy/member/lambda/profile-comprehensive", middlewares, async (req, res) => {
    await handleUpdateProfile(req, res, app.get("sdk"));
  });
  
  app.post("/v1/api/rowanenergy/customer/lambda/profile-comprehensive", middlewares, async (req, res) => {
    await handleUpdateProfile(req, res, app.get("sdk"));
  });

  // Update password
  app.post("/v1/api/rowanenergy/member/lambda/update-password", middlewares, async (req, res) => {
    await handleUpdatePassword(req, res, app.get("sdk"));
  });
  
  app.post("/v1/api/rowanenergy/customer/lambda/update-password", middlewares, async (req, res) => {
    await handleUpdatePassword(req, res, app.get("sdk"));
  });

  // Upload profile image
  app.post("/v1/api/rowanenergy/member/lambda/upload-profile-image", middlewares, async (req, res) => {
    await handleUploadProfileImage(req, res, app.get("sdk"));
  });
  
  app.post("/v1/api/rowanenergy/customer/lambda/upload-profile-image", middlewares, async (req, res) => {
    await handleUploadProfileImage(req, res, app.get("sdk"));
  });
};
