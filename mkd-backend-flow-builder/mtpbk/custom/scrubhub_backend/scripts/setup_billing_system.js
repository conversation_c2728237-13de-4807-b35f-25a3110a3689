/**
 * Complete Billing System Setup Script for ScrubHub
 * This script sets up both Stripe products/prices and database records
 * 
 * Usage: 
 * cd /path/to/mkd-backend-flow-builder
 * node mtpbk/custom/scrubhub_backend/scripts/setup_billing_system.js
 */

const StripeService = require('../../../baas/services/StripeService');

// Initialize services
const stripeService = new StripeService();

// Database configuration (update with your actual DB config)
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 3306,
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'mkd_staging'
};

// Products configuration for ScrubHub plans
const PRODUCTS_CONFIG = [
  {
    name: 'Pro Plan',
    description: 'For serious renters who want an edge in finding the perfect medical housing',
    price: 19.00,
    currency: 'cad',
    target_audience: 'renters',
    features: [
      'All Basic features',
      'Early access to new listings',
      'Enhanced profile visibility',
      'Priority support',
      'Advanced search filters',
      'Saved search alerts'
    ]
  },
  {
    name: 'Landlord Plan',
    description: 'For landlords managing medical housing properties with professional tools',
    price: 49.00,
    currency: 'cad',
    target_audience: 'landlords',
    features: [
      'Post and manage unlimited listings',
      'Tenant screening tools',
      'Lease management system',
      'Dedicated landlord support',
      'Analytics and reporting',
      'Priority listing placement',
      'Bulk operations',
      'Professional verification badge'
    ]
  }
];

class ScrubHubBillingSetup {
  constructor() {
    this.createdProducts = [];
    this.createdPrices = [];
  }

  async createStripeProduct(productConfig) {
    console.log(`📦 Creating Stripe product: ${productConfig.name}`);
    
    const product = await stripeService.createStripeProduct({
      name: productConfig.name,
      description: productConfig.description,
      metadata: {
        platform: 'scrubhub',
        plan_type: productConfig.name.toLowerCase().replace(' plan', ''),
        target_audience: productConfig.target_audience,
        features: JSON.stringify(productConfig.features),
        currency: productConfig.currency
      }
    });

    if (product.error) {
      throw new Error(`Failed to create product ${productConfig.name}: ${product.message}`);
    }

    console.log(`✅ Stripe product created: ${product.id}`);
    this.createdProducts.push(product);
    return product;
  }

  async createStripePrice(product, productConfig) {
    console.log(`💰 Creating Stripe price for: ${productConfig.name}`);
    
    const price = await stripeService.createStripeRecurringPrice({
      productId: product.id,
      name: `${productConfig.name} - Monthly`,
      amount: productConfig.price, // StripeService converts to cents internally
      currency: productConfig.currency,
      interval: 'month',
      interval_count: 1,
      trial_days: 7, // 7-day trial for ScrubHub plans
      metadata: {
        platform: 'scrubhub',
        plan_type: productConfig.name.toLowerCase().replace(' plan', ''),
        target_audience: productConfig.target_audience
      }
    });

    if (price.error) {
      throw new Error(`Failed to create price for ${productConfig.name}: ${price.message}`);
    }

    console.log(`✅ Stripe price created: ${price.id} ($${productConfig.price} ${productConfig.currency.toUpperCase()}/month)`);
    this.createdPrices.push({ price, product, config: productConfig });
    return price;
  }

  async setupStripeProducts() {
    console.log('🚀 Setting up ScrubHub Stripe products and prices...\n');
    
    for (const productConfig of PRODUCTS_CONFIG) {
      try {
        const product = await this.createStripeProduct(productConfig);
        const price = await this.createStripePrice(product, productConfig);
        console.log(''); // Empty line for readability
      } catch (error) {
        console.error(`❌ Error setting up ${productConfig.name}:`, error.message);
        throw error;
      }
    }
  }

  generateSQLInserts() {
    console.log('📝 Generating SQL insert statements...\n');
    
    let sql = '-- Auto-generated SQL for ScrubHub Stripe products and prices\n';
    sql += '-- Generated on: ' + new Date().toISOString() + '\n\n';
    
    // Products
    sql += '-- Insert Stripe Products\n';
    this.createdProducts.forEach(product => {
      const metadata = JSON.stringify(product.metadata).replace(/'/g, "\\'");
      sql += `INSERT INTO scrubhub_stripe_product (stripe_product_id, name, description, active, metadata, created_at, updated_at) VALUES\n`;
      sql += `('${product.id}', '${product.name}', '${product.description}', ${product.active ? 1 : 0}, '${metadata}', NOW(), NOW());\n\n`;
    });
    
    // Prices
    sql += '-- Insert Stripe Prices\n';
    this.createdPrices.forEach(({ price, product, config }) => {
      const metadata = JSON.stringify(price.metadata).replace(/'/g, "\\'");
      sql += `INSERT INTO scrubhub_stripe_price (stripe_price_id, stripe_product_id, unit_amount, currency, recurring_interval, recurring_interval_count, nickname, active, trial_period_days, metadata, created_at, updated_at) VALUES\n`;
      sql += `('${price.id}', '${product.id}', ${price.unit_amount}, '${price.currency}', '${price.recurring.interval}', ${price.recurring.interval_count}, '${price.nickname}', ${price.active ? 1 : 0}, ${price.recurring.trial_period_days || 0}, '${metadata}', NOW(), NOW());\n\n`;
    });

    // Subscription plans mapping
    sql += '-- Insert Subscription Plans\n';
    this.createdPrices.forEach(({ price, product, config }) => {
      const features = JSON.stringify(config.features).replace(/'/g, "\\'");
      sql += `INSERT INTO scrubhub_subscription_plan (name, description, price, currency, stripe_price_id, stripe_product_id, target_audience, features, trial_days, active, created_at, updated_at) VALUES\n`;
      sql += `('${config.name}', '${config.description}', ${config.price}, '${config.currency}', '${price.id}', '${product.id}', '${config.target_audience}', '${features}', 7, 1, NOW(), NOW());\n\n`;
    });

    return sql;
  }

  async saveSQL(sqlContent) {
    const fs = require('fs');
    const path = require('path');
    
    const filename = `scrubhub_stripe_inserts_${Date.now()}.sql`;
    const filepath = path.join(__dirname, filename);
    
    fs.writeFileSync(filepath, sqlContent);
    console.log(`📄 SQL file saved: ${filepath}`);
    
    return filepath;
  }

  displaySummary() {
    console.log('\n🎉 ScrubHub Billing Setup Complete!\n');
    console.log('=' * 60);
    
    this.createdPrices.forEach(({ price, product, config }) => {
      console.log(`\n🏷️  ${product.name}`);
      console.log(`   Product ID: ${product.id}`);
      console.log(`   Price ID: ${price.id}`);
      console.log(`   Amount: $${config.price} ${config.currency.toUpperCase()}/month`);
      console.log(`   Target: ${config.target_audience}`);
      console.log(`   Features: ${config.features.length} features`);
      console.log(`   Trial: 7 days`);
    });
    
    console.log('\n📋 Next Steps:');
    console.log('1. ✅ Products created in Stripe');
    console.log('2. ✅ SQL insert statements generated');
    console.log('3. 🔄 Run the generated SQL against your database');
    console.log('4. 🧪 Test the subscription flow in your frontend');
    console.log('5. 🔗 Update your subscription API endpoints');
    
    console.log('\n🔗 Useful Links:');
    console.log('- Stripe Dashboard: https://dashboard.stripe.com/products');
    console.log('- Test Cards: https://stripe.com/docs/testing#cards');
    console.log('- ScrubHub Subscription API: /v1/api/scrubhub/subscription');
  }

  async cleanup() {
    console.log('\n🧹 Cleaning up in case of errors...');
    
    // Archive any created products if script fails midway
    for (const product of this.createdProducts) {
      try {
        await stripeService.updateStripeProduct(product.id, { active: false });
        console.log(`🗑️  Archived product: ${product.id}`);
      } catch (error) {
        console.error(`❌ Failed to archive ${product.id}:`, error.message);
      }
    }
  }
}

async function main() {
  const setup = new ScrubHubBillingSetup();
  
  try {
    // Step 1: Create Stripe products and prices
    await setup.setupStripeProducts();
    
    // Step 2: Generate SQL inserts
    const sqlContent = setup.generateSQLInserts();
    const sqlFile = await setup.saveSQL(sqlContent);
    
    // Step 3: Display summary
    setup.displaySummary();
    
    console.log(`\n📁 SQL File: ${sqlFile}`);
    console.log('\n✨ Run the following to execute the SQL:');
    console.log(`mysql -u [username] -p [database] < ${sqlFile}`);
    
  } catch (error) {
    console.error('\n💥 Setup failed:', error.message);
    console.error(error.stack);
    
    await setup.cleanup();
    process.exit(1);
  }
}

// Handle command line arguments
const args = process.argv.slice(2);

if (args.includes('--help') || args.includes('-h')) {
  console.log('ScrubHub Billing System Setup\n');
  console.log('This script creates Stripe products/prices and generates SQL for your database.\n');
  console.log('Plans:');
  console.log('- Pro Plan: $19 CAD/month for serious renters');
  console.log('- Landlord Plan: $49 CAD/month for property managers\n');
  console.log('Requirements:');
  console.log('- Valid Stripe API keys in environment');
  console.log('- StripeService configured and working\n');
  console.log('Usage:');
  console.log('  node setup_billing_system.js     # Run the setup');
  console.log('  node setup_billing_system.js -h  # Show this help');
} else {
  console.log('🏠 ScrubHub Billing System Setup');
  console.log('==================================\n');
  main();
}
