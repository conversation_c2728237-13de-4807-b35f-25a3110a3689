-- Auto-generated SQL for ScrubHub Stripe products and prices
-- Generated on: 2025-08-18T20:19:25.868Z

-- Insert Stripe Products
INSERT INTO scrubhub_stripe_product (stripe_product_id, name, description, active, metadata, created_at, updated_at) VALUES
('prod_StMF41o3JZyU2S', 'Pro Plan', 'For serious renters who want an edge in finding the perfect medical housing', 1, '{"currency":"cad","features":"[\"All Basic features\",\"Early access to new listings\",\"Enhanced profile visibility\",\"Priority support\",\"Advanced search filters\",\"Saved search alerts\"]","plan_type":"pro","platform":"scrubhub","target_audience":"renters"}', NOW(), NOW());

INSERT INTO scrubhub_stripe_product (stripe_product_id, name, description, active, metadata, created_at, updated_at) VALUES
('prod_StMFXPuJ0T2kvE', 'Landlord Plan', 'For landlords managing medical housing properties with professional tools', 1, '{"currency":"cad","features":"[\"Post and manage unlimited listings\",\"Tenant screening tools\",\"Lease management system\",\"Dedicated landlord support\",\"Analytics and reporting\",\"Priority listing placement\",\"Bulk operations\",\"Professional verification badge\"]","plan_type":"landlord","platform":"scrubhub","target_audience":"landlords"}', NOW(), NOW());

-- Insert Stripe Prices
INSERT INTO scrubhub_stripe_price (stripe_price_id, stripe_product_id, unit_amount, currency, recurring_interval, recurring_interval_count, nickname, active, trial_period_days, metadata, created_at, updated_at) VALUES
('price_1RxZWaBgOlWo0lDUgsph1R9q', 'prod_StMF41o3JZyU2S', 1900, 'cad', 'month', 1, 'Pro Plan - Monthly', 1, 7, '{"plan_type":"pro","platform":"scrubhub","target_audience":"renters"}', NOW(), NOW());

INSERT INTO scrubhub_stripe_price (stripe_price_id, stripe_product_id, unit_amount, currency, recurring_interval, recurring_interval_count, nickname, active, trial_period_days, metadata, created_at, updated_at) VALUES
('price_1RxZWbBgOlWo0lDUxHcAWCKB', 'prod_StMFXPuJ0T2kvE', 4900, 'cad', 'month', 1, 'Landlord Plan - Monthly', 1, 7, '{"plan_type":"landlord","platform":"scrubhub","target_audience":"landlords"}', NOW(), NOW());

-- Insert Subscription Plans
INSERT INTO scrubhub_subscription_plan (name, description, price, currency, stripe_price_id, stripe_product_id, target_audience, features, trial_days, active, created_at, updated_at) VALUES
('Pro Plan', 'For serious renters who want an edge in finding the perfect medical housing', 19, 'cad', 'price_1RxZWaBgOlWo0lDUgsph1R9q', 'prod_StMF41o3JZyU2S', 'renters', '["All Basic features","Early access to new listings","Enhanced profile visibility","Priority support","Advanced search filters","Saved search alerts"]', 7, 1, NOW(), NOW());

INSERT INTO scrubhub_subscription_plan (name, description, price, currency, stripe_price_id, stripe_product_id, target_audience, features, trial_days, active, created_at, updated_at) VALUES
('Landlord Plan', 'For landlords managing medical housing properties with professional tools', 49, 'cad', 'price_1RxZWbBgOlWo0lDUxHcAWCKB', 'prod_StMFXPuJ0T2kvE', 'landlords', '["Post and manage unlimited listings","Tenant screening tools","Lease management system","Dedicated landlord support","Analytics and reporting","Priority listing placement","Bulk operations","Professional verification badge"]', 7, 1, NOW(), NOW());

