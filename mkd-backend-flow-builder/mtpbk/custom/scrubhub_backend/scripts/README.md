# ScrubHub Billing System Setup

This script sets up the complete billing system for ScrubHub, including Stripe products, prices, and database records for the subscription plans.

## Plans Overview

### Pro Plan - $19 CAD/month
**Target Audience:** Serious renters who want an edge
- All Basic features
- Early access to new listings
- Enhanced profile visibility
- Priority support
- Advanced search filters
- Saved search alerts
- 7-day free trial

### Landlord Plan - $49 CAD/month
**Target Audience:** Landlords managing properties
- Post and manage unlimited listings
- Tenant screening tools
- Lease management system
- Dedicated landlord support
- Analytics and reporting
- Priority listing placement
- Bulk operations
- Professional verification badge
- 7-day free trial

## Prerequisites

1. **Stripe Account**: Valid Stripe API keys configured in environment
2. **Database**: MySQL database with ScrubHub tables created
3. **StripeService**: Working StripeService configuration

## Environment Variables

Ensure these are set in your environment:
```bash
STRIPE_SECRET_KEY=sk_test_...
STRIPE_PUBLISHABLE_KEY=pk_test_...
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=your_password
DB_NAME=mkd_staging
```

## Usage

### 1. Run the Setup Script

```bash
cd /path/to/mkd-backend-flow-builder
node mtpbk/custom/scrubhub_backend/scripts/setup_billing_system.js
```

### 2. Execute Generated SQL

The script will generate a SQL file with all the necessary inserts. Execute it:

```bash
mysql -u [username] -p [database] < scrubhub_stripe_inserts_[timestamp].sql
```

### 3. Verify Setup

Check your Stripe dashboard and database to ensure everything was created correctly.

## What the Script Does

1. **Creates Stripe Products**: Two products (Pro Plan, Landlord Plan)
2. **Creates Stripe Prices**: Monthly recurring prices with 7-day trials
3. **Generates SQL**: Inserts for all database tables
4. **Creates Records**: 
   - `scrubhub_stripe_product`
   - `scrubhub_stripe_price` 
   - `scrubhub_subscription_plan`

## Database Tables Created

The script works with these tables (created by sql_migrations.sql):

- `scrubhub_stripe_product` - Stripe product information
- `scrubhub_stripe_price` - Stripe pricing information
- `scrubhub_subscription_plan` - Plan details and features
- `scrubhub_subscription` - User subscriptions

## Testing the Setup

After running the script:

1. **Import Postman Collection**: Use the updated collection with billing endpoints
2. **Test Billing Info**: `GET /v1/api/scrubhub/customer/lambda/billing-info`
3. **Test Available Plans**: `GET /v1/api/scrubhub/customer/lambda/available-plans`
4. **Test Card Token Creation**: `POST /v1/api/scrubhub/customer/lambda/create-card-token`
5. **Test Subscription Creation**: `POST /v1/api/scrubhub/customer/lambda/create-subscription`
6. **Test Subscription Cancellation**: `POST /v1/api/scrubhub/customer/lambda/cancel-subscription`

## API Endpoints

### New Customer Billing Lambda Endpoints (Recommended)
```
GET /v1/api/scrubhub/customer/lambda/billing-info           # Complete billing info
GET /v1/api/scrubhub/customer/lambda/available-plans        # Available plans for user
POST /v1/api/scrubhub/customer/lambda/create-card-token     # Create card token
POST /v1/api/scrubhub/customer/lambda/create-stripe-customer # Create Stripe customer
POST /v1/api/scrubhub/customer/lambda/add-payment-method    # Add payment method
POST /v1/api/scrubhub/customer/lambda/create-subscription   # Create subscription
POST /v1/api/scrubhub/customer/lambda/cancel-subscription   # Cancel subscription
```

### Legacy Subscription Endpoints
```
GET /v1/api/scrubhub/subscription/plans     # Get subscription plans
GET /v1/api/scrubhub/subscription           # Get current subscription
POST /v1/api/scrubhub/subscription          # Create subscription
DELETE /v1/api/scrubhub/subscription        # Cancel subscription
```

## Troubleshooting

### Common Issues

1. **Stripe API Keys**: Ensure test keys are used for development
2. **Database Connection**: Verify database credentials and connectivity
3. **Missing Tables**: Run sql_migrations.sql first
4. **Duplicate Products**: Script will fail if products already exist

### Error Recovery

If the script fails midway, it will attempt to clean up by archiving any created Stripe products.

### Manual Cleanup

If needed, you can manually archive products in Stripe dashboard:
- Go to Products section
- Find ScrubHub products
- Archive them

## Production Deployment

For production:

1. **Use Live Stripe Keys**: Switch to live API keys
2. **Update Currency**: Ensure CAD is supported in your Stripe account
3. **Test Webhooks**: Configure webhook endpoints for subscription events
4. **Monitor Billing**: Set up monitoring for failed payments and cancellations

## Support

For issues with the billing setup:
1. Check Stripe dashboard for product/price creation
2. Verify database records were inserted correctly
3. Test API endpoints with Postman collection
4. Check application logs for detailed error messages

## Next Steps

After successful setup:
1. Integrate subscription checks in your frontend
2. Implement feature gating based on subscription status
3. Set up webhook handling for subscription events
4. Configure email notifications for billing events
5. Add subscription management UI for users
