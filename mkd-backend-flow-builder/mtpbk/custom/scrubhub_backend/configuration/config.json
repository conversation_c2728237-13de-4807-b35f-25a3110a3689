{"settings": {"globalKey": "key_1755519597570_aldd0oxw9", "databaseType": "mysql", "authType": "session", "timezone": "UTC", "dbHost": "localhost", "dbPort": "3306", "dbUser": "root", "dbPassword": "root", "dbName": "database_2025-08-18", "id": "scrubhub", "isPWA": false, "isMultiTenant": false, "model_namespace": "scrubhub", "payment_option": "subscription"}, "models": [{"id": "model_1755519618782_50zm40v8z", "name": "job", "fields": [{"name": "id", "type": "primary key", "defaultValue": "", "validation": ""}, {"name": "task", "type": "string", "defaultValue": "", "validation": "required"}, {"name": "arguments", "type": "json", "defaultValue": "", "validation": ""}, {"name": "time_interval", "type": "string", "defaultValue": "once", "validation": ""}, {"name": "retries", "type": "integer", "defaultValue": "1", "validation": ""}, {"name": "status", "type": "mapping", "mapping": "0:Pending,1:Failed,2:Processing,3:Completed", "defaultValue": "0", "validation": ""}, {"name": "created_at", "type": "timestamp", "defaultValue": "CURRENT_TIMESTAMP", "validation": "date"}, {"name": "updated_at", "type": "timestamp", "defaultValue": "CURRENT_TIMESTAMP", "validation": "date"}]}, {"id": "model_1755519618782_vl7meriy7", "name": "uploads", "fields": [{"name": "id", "type": "primary key", "defaultValue": "", "validation": ""}, {"name": "url", "type": "string", "defaultValue": "", "validation": "required"}, {"name": "caption", "type": "string", "defaultValue": "", "validation": ""}, {"name": "user_id", "type": "foreign key", "defaultValue": "", "validation": ""}, {"name": "width", "type": "integer", "defaultValue": "", "validation": ""}, {"name": "height", "type": "integer", "defaultValue": "", "validation": ""}, {"name": "type", "type": "mapping", "mapping": "0:Image,1:s3,2:Video,3:base64", "defaultValue": "0", "validation": "required,enum:0,1,2,3"}, {"name": "created_at", "type": "timestamp", "defaultValue": "CURRENT_TIMESTAMP", "validation": "date"}, {"name": "updated_at", "type": "timestamp", "defaultValue": "CURRENT_TIMESTAMP", "validation": "date"}]}, {"id": "model_1755519618782_gkwdnb2ax", "name": "tokens", "fields": [{"name": "id", "type": "primary key", "defaultValue": "", "validation": ""}, {"name": "user_id", "type": "foreign key", "defaultValue": "", "validation": "required"}, {"name": "token", "type": "string", "defaultValue": "", "validation": "required"}, {"name": "code", "type": "string", "defaultValue": "", "validation": "required"}, {"name": "type", "type": "mapping", "mapping": "0:Access,1:<PERSON><PERSON><PERSON>,2:<PERSON><PERSON>,3:<PERSON><PERSON><PERSON>,4:<PERSON>", "defaultValue": "0", "validation": "required,enum:0,1,2,3,4"}, {"name": "data", "type": "json", "defaultValue": "", "validation": ""}, {"name": "status", "type": "mapping", "mapping": "0:Inactive,1:Active", "defaultValue": "1", "validation": "required,enum:0,1"}, {"name": "created_at", "type": "timestamp", "defaultValue": "CURRENT_TIMESTAMP", "validation": "date"}, {"name": "updated_at", "type": "timestamp", "defaultValue": "CURRENT_TIMESTAMP", "validation": "date"}, {"name": "expired_at", "type": "timestamp", "defaultValue": "", "validation": "date"}]}, {"id": "model_1755519618782_v1f8fdd1d", "name": "preference", "fields": [{"name": "id", "type": "primary key", "defaultValue": "", "validation": ""}, {"name": "first_name", "type": "string", "defaultValue": "", "validation": ""}, {"name": "last_name", "type": "string", "defaultValue": "", "validation": ""}, {"name": "phone", "type": "string", "defaultValue": "", "validation": ""}, {"name": "photo", "type": "string", "defaultValue": "", "validation": ""}, {"name": "user_id", "type": "integer", "defaultValue": "", "validation": "required"}]}, {"id": "model_1755519618782_8s3hrkwmp", "name": "user", "fields": [{"name": "id", "type": "primary key", "defaultValue": "", "validation": ""}, {"name": "email", "type": "string", "defaultValue": "", "validation": "required,email"}, {"name": "password", "type": "password", "defaultValue": "", "validation": "required"}, {"name": "login_type", "type": "mapping", "mapping": "0:<PERSON>,1:Google,2:Microsoft,3:Apple,4:Twitter,5:Facebook", "defaultValue": "0", "validation": "required,enum:0,1,2,3,4,5"}, {"name": "role_id", "type": "string", "defaultValue": "", "validation": ""}, {"name": "data", "type": "json", "defaultValue": "", "validation": ""}, {"name": "status", "type": "mapping", "mapping": "0:Active,1:Inactive,2:Suspend", "defaultValue": "0", "validation": "required,enum:0,1,2"}, {"name": "verify", "type": "boolean", "defaultValue": "0", "validation": "required"}, {"name": "two_factor_authentication", "type": "boolean", "defaultValue": "0", "validation": ""}, {"name": "company_id", "type": "integer", "defaultValue": "0", "validation": ""}, {"name": "stripe_uid", "type": "string", "defaultValue": "", "validation": ""}, {"name": "created_at", "type": "timestamp", "defaultValue": "CURRENT_TIMESTAMP", "validation": "date"}]}, {"id": "model_1755521108869_zprbvt1t2", "name": "stripe_product", "fields": [{"name": "id", "type": "primary key", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "created_at", "type": "date", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "updated_at", "type": "datetime", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "name", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "product_id", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "stripe_id", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "object", "type": "long text", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "status", "type": "integer", "defaultValue": "", "validation": "", "validationOptions": {}}]}, {"id": "model_1755521108869_ubfcqao4w", "name": "stripe_price", "fields": [{"name": "id", "type": "primary key", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "created_at", "type": "date", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "updated_at", "type": "datetime", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "name", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "product_id", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "stripe_id", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "is_usage_metered", "type": "integer", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "usage_limit", "type": "integer", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "object", "type": "medium text", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "amount", "type": "float", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "trial_days", "type": "integer", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "type", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "status", "type": "integer", "defaultValue": "", "validation": "", "validationOptions": {}}]}, {"id": "model_1755521108869_lpmvvk49e", "name": "stripe_subscription", "fields": [{"name": "id", "type": "primary key", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "stripe_id", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "price_id", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "user_id", "type": "integer", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "object", "type": "json", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "status", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "is_lifetime", "type": "boolean", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "created_at", "type": "date", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "updated_at", "type": "datetime", "defaultValue": "", "validation": "", "validationOptions": {}}]}, {"id": "model_1755521108869_rgs3u91vx", "name": "stripe_checkout", "fields": [{"name": "id", "type": "primary key", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "user_id", "type": "integer", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "stripe_id", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "object", "type": "json", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "created_at", "type": "date", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "updated_at", "type": "datetime", "defaultValue": "", "validation": "", "validationOptions": {}}]}, {"id": "model_1755521108869_vnn16rb0j", "name": "stripe_webhook", "fields": [{"name": "id", "type": "primary key", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "stripe_id", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "idempotency_key", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "description", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "event_type", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "resource_type", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "object", "type": "json", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "is_handled", "type": "boolean", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "created_at", "type": "date", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "updated_at", "type": "datetime", "defaultValue": "", "validation": "", "validationOptions": {}}]}, {"id": "model_1755521108869_oz75as6a9", "name": "stripe_setting", "fields": [{"name": "id", "type": "primary key", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "key", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "value", "type": "medium text", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "created_at", "type": "date", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "updated_at", "type": "datetime", "defaultValue": "", "validation": "", "validationOptions": {}}]}, {"id": "model_1755521108869_o9vjk<PERSON>ur", "name": "stripe_order", "fields": [{"name": "id", "type": "primary key", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "user_id", "type": "integer", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "price_id", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "stripe_id", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "object", "type": "json", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "created_at", "type": "date", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "updated_at", "type": "datetime", "defaultValue": "", "validation": "", "validationOptions": {}}]}, {"id": "model_1755521108869_68rk6nink", "name": "stripe_invoice", "fields": [{"name": "id", "type": "primary key", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "user_id", "type": "integer", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "stripe_id", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "object", "type": "json", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "created_at", "type": "date", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "updated_at", "type": "datetime", "defaultValue": "", "validation": "", "validationOptions": {}}]}, {"id": "model_1755521108869_uzye3ganx", "name": "stripe_refund", "fields": [{"name": "id", "type": "primary key", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "user_id", "type": "integer", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "stripe_id", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "object", "type": "json", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "charge_id", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "subscription_id", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "amount", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "currency", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "reason", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "status", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "created_at", "type": "date", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "updated_at", "type": "datetime", "defaultValue": "", "validation": "", "validationOptions": {}}]}, {"id": "model_1755521108869_bd4mk6bxs", "name": "stripe_dispute", "fields": [{"name": "id", "type": "primary key", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "user_id", "type": "integer", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "stripe_id", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "subscription_id", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "object", "type": "json", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "amount", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "reason", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "reason_description", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "status", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "created_at", "type": "date", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "updated_at", "type": "datetime", "defaultValue": "", "validation": "", "validationOptions": {}}]}], "roles": [{"id": "role_admin_1755519618782", "name": "Super Admin", "slug": "super_admin", "permissions": {"routes": [], "canCreateUsers": true, "canEditUsers": true, "canDeleteUsers": true, "canManageRoles": true, "canUpdateOtherUsers": true}}, {"id": "role_member_1755519618782", "name": "Member", "slug": "member", "permissions": {"routes": [], "canCreateUsers": false, "canEditUsers": false, "canDeleteUsers": false, "canManageRoles": false, "canUpdateOtherUsers": false, "companyScoped": false}}, {"id": "role_1755521014591_q28pok69z", "name": "recruiter", "slug": "recruiter", "permissions": {"routes": [], "canCreateUsers": false, "canEditUsers": false, "canDeleteUsers": false, "canManageRoles": false, "canLogin": true, "canRegister": true, "canForgot": false, "canReset": false, "canGoogleLogin": true, "canAppleLogin": true, "canMicrosoftLogin": true, "canMagicLinkLogin": false, "canTwitterLogin": false, "needs2FA": true, "canSetPermissions": true, "canPreference": true, "canVerifyEmail": true, "canUpload": true, "canStripe": true, "canStripeWebhook": true, "canRealTime": true, "canAI": true, "canUpdateEmail": true, "canUpdatePassword": true, "canUpdateOtherUsers": false, "treeql": {"enabled": true, "models": {"job": {"allowed": true, "blacklistedFields": [], "operations": {"get": true, "getOne": true, "getAll": true, "post": true, "put": true, "delete": true, "paginate": true, "join": true}}, "uploads": {"allowed": true, "blacklistedFields": [], "operations": {"get": true, "getOne": true, "getAll": true, "post": true, "put": true, "delete": true, "paginate": true, "join": true}}, "tokens": {"allowed": true, "blacklistedFields": [], "operations": {"get": true, "getOne": true, "getAll": true, "post": true, "put": true, "delete": true, "paginate": true, "join": true}}, "preference": {"allowed": true, "blacklistedFields": [], "operations": {"get": true, "getOne": true, "getAll": true, "post": true, "put": true, "delete": true, "paginate": true, "join": true}}, "user": {"allowed": true, "blacklistedFields": [], "operations": {"get": true, "getOne": true, "getAll": true, "post": true, "put": true, "delete": true, "paginate": true, "join": true}}}}}}, {"id": "role_1755521053507_c5lme1zqo", "name": "hospital", "slug": "hospital", "permissions": {"routes": [], "canCreateUsers": true, "canEditUsers": true, "canDeleteUsers": true, "canManageRoles": true, "canLogin": true, "canRegister": true, "canForgot": false, "canReset": false, "canGoogleLogin": true, "canAppleLogin": true, "canMicrosoftLogin": true, "canMagicLinkLogin": false, "canTwitterLogin": false, "needs2FA": true, "canSetPermissions": true, "canPreference": true, "canVerifyEmail": true, "canUpload": true, "canStripe": true, "canStripeWebhook": true, "canRealTime": true, "canAI": true, "canUpdateEmail": true, "canUpdatePassword": true, "canUpdateOtherUsers": true, "treeql": {"enabled": true, "models": {"user": {"allowed": true, "blacklistedFields": [], "operations": {"get": true, "getOne": true, "getAll": true, "post": true, "put": true, "delete": true, "paginate": true, "join": true}}, "preference": {"allowed": true, "blacklistedFields": [], "operations": {"get": true, "getOne": true, "getAll": true, "post": true, "put": true, "delete": true, "paginate": true, "join": true}}, "tokens": {"allowed": true, "blacklistedFields": [], "operations": {"get": true, "getOne": true, "getAll": true, "post": true, "put": true, "delete": true, "paginate": true, "join": true}}, "uploads": {"allowed": true, "blacklistedFields": [], "operations": {"get": true, "getOne": true, "getAll": true, "post": true, "put": true, "delete": true, "paginate": true, "join": true}}, "job": {"allowed": true, "blacklistedFields": [], "operations": {"get": true, "getOne": true, "getAll": true, "post": true, "put": true, "delete": true, "paginate": true, "join": true}}}}}}], "routes": []}