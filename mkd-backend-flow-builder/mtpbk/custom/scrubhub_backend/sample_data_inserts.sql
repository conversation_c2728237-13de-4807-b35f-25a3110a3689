-- ScrubHub Sample Data Inserts
-- This file contains realistic sample data for all ScrubHub API tables

-- ==================== USERS ====================
-- Insert sample users with different roles
INSERT INTO scrubhub_user (email, password, login_type, role_id, data, status, verify, name, first_name, last_name, phone, role, verified, stripe_customer_id, created_at, updated_at) VALUES
-- Tenants
('<EMAIL>', '$2a$10$zdFHCUM9iAoVH4HwjBmz3uC0EXdgCd1zI3JKl4fxXkH4SUtiMpHeC', 0, '0', '{"address": "123 Main St, Toronto, ON", "city": "Toronto", "province_state": "ON", "country": "Canada"}', 0, 1, 'John <PERSON>', 'John', 'Doe', '******-555-0123', 0, 1, 'cus_tenant_001', NOW(), NOW()),
('<EMAIL>', '$2a$10$YvQw0qQkpXT8bhha2SmiQOoJsIHbIEblw7kDJ8agaCHvx6UDMXOCu', 0, '0', '{"address": "456 Queen St, Vancouver, BC", "city": "Vancouver", "province_state": "BC", "country": "Canada"}', 0, 1, 'Sarah Wilson', 'Sarah', 'Wilson', '******-555-0456', 0, 1, 'cus_tenant_002', NOW(), NOW()),
('<EMAIL>', '$2a$10$IO2Bxnb2tOwNzGZjDlQlNe98sUvwHwQUatFC1A.uxQe9L/Qb1SxSm', 0, '0', '{"address": "789 College St, Montreal, QC", "city": "Montreal", "province_state": "QC", "country": "Canada"}', 0, 1, 'Mike Chen', 'Mike', 'Chen', '******-555-0789', 0, 1, 'cus_tenant_003', NOW(), NOW()),

-- Landlords
('<EMAIL>', '$2a$10$zdFHCUM9iAoVH4HwjBmz3uC0EXdgCd1zI3JKl4fxXkH4SUtiMpHeC', 0, '1', '{"company": "Smith Properties", "address": "100 Bay St, Toronto, ON", "city": "Toronto", "province_state": "ON", "country": "Canada"}', 0, 1, 'Robert Smith', 'Robert', 'Smith', '******-555-1001', 1, 1, 'cus_landlord_001', NOW(), NOW()),
('<EMAIL>', '$2a$10$YvQw0qQkpXT8bhha2SmiQOoJsIHbIEblw7kDJ8agaCHvx6UDMXOCu', 0, '1', '{"company": "Vancouver Rentals", "address": "200 Granville St, Vancouver, BC", "city": "Vancouver", "province_state": "BC", "country": "Canada"}', 0, 1, 'Jane Property', 'Jane', 'Property', '******-555-2001', 1, 1, 'cus_landlord_002', NOW(), NOW()),

-- Hospitals
('<EMAIL>', '$2a$10$IO2Bxnb2tOwNzGZjDlQlNe98sUvwHwQUatFC1A.uxQe9L/Qb1SxSm', 0, '2', '{"hospital_name": "Toronto General Hospital", "contact_person": "Dr. Emily Johnson", "address": "200 Elizabeth St, Toronto, ON", "city": "Toronto", "province_state": "ON", "country": "Canada"}', 0, 1, 'Toronto General Hospital', 'Emily', 'Johnson', '******-555-3001', 2, 1, 'cus_hospital_001', NOW(), NOW()),
('<EMAIL>', '$2a$10$zdFHCUM9iAoVH4HwjBmz3uC0EXdgCd1zI3JKl4fxXkH4SUtiMpHeC', 0, '2', '{"hospital_name": "Vancouver General Hospital", "contact_person": "Dr. Michael Wong", "address": "899 W 12th Ave, Vancouver, BC", "city": "Vancouver", "province_state": "BC", "country": "Canada"}', 0, 1, 'Vancouver General Hospital', 'Michael', 'Wong', '******-555-3002', 2, 1, 'cus_hospital_002', NOW(), NOW()),

-- Recruiters
('<EMAIL>', '$2a$10$YvQw0qQkpXT8bhha2SmiQOoJsIHbIEblw7kDJ8agaCHvx6UDMXOCu', 0, '3', '{"company": "Medical Staffing Solutions", "contact_person": "Lisa Rodriguez", "address": "300 King St, Toronto, ON", "city": "Toronto", "province_state": "ON", "country": "Canada"}', 0, 1, 'Lisa Rodriguez', 'Lisa', 'Rodriguez', '+1-************', 3, 1, 'cus_recruiter_001', NOW(), NOW()),

-- Admin
('<EMAIL>', '$2a$10$IO2Bxnb2tOwNzGZjDlQlNe98sUvwHwQUatFC1A.uxQe9L/Qb1SxSm', 0, '4', '{"role": "super_admin", "permissions": ["all"]}', 0, 1, 'Admin User', 'Admin', 'User', '******-555-9999', 4, 1, 'cus_admin_001', NOW(), NOW());

-- ==================== PROPERTIES ====================
-- Insert sample properties
INSERT INTO scrubhub_property (landlord_id, title, description, address, city, province_state, country, price, tier, duration_days, status, amenities, property_type, bedrooms, bathrooms, furnished, available_from, expires_at, created_at, updated_at) VALUES
(4, 'Modern 2BR Condo Downtown Toronto', 'Beautiful modern 2-bedroom condo in the heart of downtown Toronto. Features include granite countertops, stainless steel appliances, in-unit laundry, and stunning city views. Perfect for medical professionals working at nearby hospitals.', '123 Bay Street, Unit 1205', 'Toronto', 'ON', 'Canada', 2800.00, 1, 30, 0, '["gym", "concierge", "rooftop_terrace", "parking", "storage"]', 'condo', 2, 2, 1, '2024-02-01', DATE_ADD(NOW(), INTERVAL 30 DAY), NOW(), NOW()),

(4, 'Spacious 1BR Near University of Toronto', 'Bright and spacious 1-bedroom apartment just steps from University of Toronto and major hospitals. Ideal for medical students and residents. Includes utilities and high-speed internet.', '456 College Street, Unit 8B', 'Toronto', 'ON', 'Canada', 2200.00, 0, 30, 0, '["utilities_included", "internet", "laundry", "bike_storage"]', 'apartment', 1, 1, 0, '2024-01-15', DATE_ADD(NOW(), INTERVAL 30 DAY), NOW(), NOW()),

(5, 'Luxury 3BR House Vancouver West Side', 'Stunning 3-bedroom house in prestigious Vancouver West Side. Close to VGH and UBC. Features include hardwood floors, modern kitchen, private garden, and mountain views.', '789 Maple Avenue', 'Vancouver', 'BC', 'Canada', 4500.00, 2, 60, 0, '["garden", "parking", "dishwasher", "fireplace", "mountain_view"]', 'house', 3, 2, 1, '2024-03-01', DATE_ADD(NOW(), INTERVAL 60 DAY), NOW(), NOW()),

(5, 'Cozy Studio Near VGH', 'Perfect studio apartment for medical residents. Located just 5 minutes walk from Vancouver General Hospital. Fully furnished with all essentials included.', '321 Oak Street, Unit 4A', 'Vancouver', 'BC', 'Canada', 1800.00, 0, 30, 0, '["furnished", "utilities_included", "gym", "security"]', 'studio', 0, 1, 1, '2024-01-20', DATE_ADD(NOW(), INTERVAL 30 DAY), NOW(), NOW());

-- ==================== PROPERTY PHOTOS ====================
-- Insert sample property photos
INSERT INTO scrubhub_property_photo (property_id, file_path, sort_order, created_at) VALUES
(1, '/uploads/properties/prop1_living_room.jpg', 1, NOW()),
(1, '/uploads/properties/prop1_kitchen.jpg', 2, NOW()),
(1, '/uploads/properties/prop1_bedroom.jpg', 3, NOW()),
(1, '/uploads/properties/prop1_bathroom.jpg', 4, NOW()),
(1, '/uploads/properties/prop1_view.jpg', 5, NOW()),

(2, '/uploads/properties/prop2_main.jpg', 1, NOW()),
(2, '/uploads/properties/prop2_bedroom.jpg', 2, NOW()),
(2, '/uploads/properties/prop2_kitchen.jpg', 3, NOW()),

(3, '/uploads/properties/prop3_exterior.jpg', 1, NOW()),
(3, '/uploads/properties/prop3_living.jpg', 2, NOW()),
(3, '/uploads/properties/prop3_kitchen.jpg', 3, NOW()),
(3, '/uploads/properties/prop3_garden.jpg', 4, NOW()),

(4, '/uploads/properties/prop4_studio.jpg', 1, NOW()),
(4, '/uploads/properties/prop4_bathroom.jpg', 2, NOW());

-- ==================== JOBS ====================
-- Insert sample job postings
INSERT INTO scrubhub_job (poster_id, title, description, location, type, department, salary_min, salary_max, requirements, contact_email, contact_phone, status, expires_at, created_at, updated_at) VALUES
(6, 'Internal Medicine Resident Position', 'We are seeking a dedicated Internal Medicine Resident to join our team at Toronto General Hospital. This position offers excellent training opportunities in a world-class academic medical center.', 'Toronto, ON', 'residency', 'Internal Medicine', 65000.00, 75000.00, 'MD degree, MCCEE certification, strong clinical skills, excellent communication abilities', '<EMAIL>', '******-555-3001', 0, DATE_ADD(NOW(), INTERVAL 45 DAY), NOW(), NOW()),

(6, 'Emergency Medicine Fellowship', 'Exciting Emergency Medicine Fellowship opportunity at Toronto General Hospital. Join our dynamic emergency department and gain experience in trauma, critical care, and emergency procedures.', 'Toronto, ON', 'fellowship', 'Emergency Medicine', 80000.00, 90000.00, 'Completed residency in Emergency Medicine, FRCPC certification preferred, trauma experience', '<EMAIL>', '******-555-3001', 0, DATE_ADD(NOW(), INTERVAL 60 DAY), NOW(), NOW()),

(7, 'Staff Cardiologist Position', 'Vancouver General Hospital is seeking a Staff Cardiologist to join our Cardiology Department. Excellent opportunity for career growth in a leading Canadian hospital.', 'Vancouver, BC', 'staff', 'Cardiology', 250000.00, 350000.00, 'FRCPC in Cardiology, minimum 3 years experience, research experience preferred', '<EMAIL>', '******-555-3002', 0, DATE_ADD(NOW(), INTERVAL 30 DAY), NOW(), NOW()),

(8, 'Locum Tenens Family Physician', 'Medical Staffing Solutions is seeking Family Physicians for locum tenens positions across Ontario. Flexible scheduling and competitive compensation.', 'Various locations, ON', 'locum', 'Family Medicine', 150.00, 200.00, 'CCFP certification, valid Ontario license, minimum 2 years experience', '<EMAIL>', '+1-************', 0, DATE_ADD(NOW(), INTERVAL 90 DAY), NOW(), NOW());

-- ==================== JOB APPLICANTS ====================
-- Insert sample job applications
INSERT INTO scrubhub_job_applicant (job_id, applicant_id, cover_letter, resume_url, status, applied_at, created_at) VALUES
(1, 1, 'I am very interested in the Internal Medicine Resident position at Toronto General Hospital. My clinical experience and dedication to patient care make me an ideal candidate.', '/uploads/resumes/john_doe_resume.pdf', 'pending', NOW(), NOW()),
(1, 2, 'As a recent medical graduate, I am excited about the opportunity to begin my residency training in Internal Medicine at your prestigious institution.', '/uploads/resumes/sarah_wilson_resume.pdf', 'pending', NOW(), NOW()),
(2, 3, 'I am writing to express my strong interest in the Emergency Medicine Fellowship. My experience in emergency settings has prepared me well for this role.', '/uploads/resumes/mike_chen_resume.pdf', 'reviewed', NOW(), NOW()),
(3, 1, 'I would like to apply for the Staff Cardiologist position. My extensive experience in interventional cardiology would be valuable to your team.', '/uploads/resumes/john_doe_cardio_resume.pdf', 'interviewed', NOW(), NOW()),
(4, 2, 'I am interested in the locum tenens opportunities. My flexibility and experience in family medicine make me a strong candidate.', '/uploads/resumes/sarah_wilson_family_resume.pdf', 'pending', NOW(), NOW());

-- ==================== MARKETPLACE ITEMS ====================
-- Insert sample marketplace items
INSERT INTO scrubhub_marketplace_item (seller_id, title, description, price, `condition`, category, status, phone_revealed, listing_fee, created_at) VALUES
(1, 'Stethoscope - Littmann Classic III', 'Excellent condition Littmann Classic III stethoscope. Used for 2 years during medical school. Perfect for students or residents.', 180.00, 'excellent', 'medical_equipment', 0, 0, 5.99, NOW()),
(2, 'Medical Textbooks Bundle', 'Complete set of medical textbooks including Harrison\'s Internal Medicine, Gray\'s Anatomy, and Robbins Pathology. Great for medical students.', 450.00, 'good', 'textbooks', 0, 0, 9.99, NOW()),
(3, 'White Coat - Size Medium', 'Professional white coat, size medium. Embroidered with previous owner\'s name (can be removed). Perfect condition.', 45.00, 'like_new', 'clothing', 0, 0, 2.99, NOW()),
(1, 'Otoscope/Ophthalmoscope Set', 'Welch Allyn diagnostic set with otoscope and ophthalmoscope. Includes carrying case and extra specula.', 320.00, 'good', 'medical_equipment', 0, 0, 7.99, NOW()),
(2, 'Study Desk with Lamp', 'Sturdy study desk with built-in lamp. Perfect for medical students. Some wear but very functional.', 120.00, 'fair', 'furniture', 0, 0, 4.99, NOW());

-- ==================== MARKETPLACE ITEM PHOTOS ====================
-- Insert sample marketplace item photos
INSERT INTO scrubhub_marketplace_item_photo (item_id, file_path, sort_order, created_at) VALUES
(1, '/uploads/marketplace/stethoscope_main.jpg', 1, NOW()),
(1, '/uploads/marketplace/stethoscope_detail.jpg', 2, NOW()),
(2, '/uploads/marketplace/textbooks_stack.jpg', 1, NOW()),
(2, '/uploads/marketplace/textbooks_individual.jpg', 2, NOW()),
(3, '/uploads/marketplace/white_coat_front.jpg', 1, NOW()),
(3, '/uploads/marketplace/white_coat_back.jpg', 2, NOW()),
(4, '/uploads/marketplace/diagnostic_set_case.jpg', 1, NOW()),
(4, '/uploads/marketplace/diagnostic_set_open.jpg', 2, NOW()),
(5, '/uploads/marketplace/study_desk_main.jpg', 1, NOW());

-- ==================== SUBLETS ====================
-- Insert sample sublet requests
INSERT INTO scrubhub_sublet (property_id, tenant_id, price, start_date, end_date, status, service_tier, service_fee, created_at) VALUES
(1, 1, 2800.00, '2024-06-01', '2024-08-31', 'active', 'premium', 150.00, NOW()),
(2, 2, 2200.00, '2024-07-01', '2024-09-30', 'active', 'basic', 75.00, NOW()),
(3, 3, 4500.00, '2024-05-15', '2024-07-15', 'pending', 'premium', 200.00, NOW());

-- ==================== FAVORITES ====================
-- Insert sample user favorites
INSERT INTO scrubhub_favorite (user_id, property_id, created_at) VALUES
(1, 2, NOW()),
(1, 3, NOW()),
(2, 1, NOW()),
(2, 4, NOW()),
(3, 1, NOW()),
(3, 2, NOW());

-- ==================== RECENTLY VIEWED ====================
-- Insert sample recently viewed properties
INSERT INTO scrubhub_recently_viewed (user_id, property_id, viewed_at, created_at) VALUES
(1, 1, NOW(), NOW()),
(1, 2, DATE_SUB(NOW(), INTERVAL 1 HOUR), DATE_SUB(NOW(), INTERVAL 1 HOUR)),
(1, 3, DATE_SUB(NOW(), INTERVAL 2 HOUR), DATE_SUB(NOW(), INTERVAL 2 HOUR)),
(2, 4, NOW(), NOW()),
(2, 1, DATE_SUB(NOW(), INTERVAL 30 MINUTE), DATE_SUB(NOW(), INTERVAL 30 MINUTE)),
(3, 2, NOW(), NOW()),
(3, 3, DATE_SUB(NOW(), INTERVAL 45 MINUTE), DATE_SUB(NOW(), INTERVAL 45 MINUTE));

-- ==================== INQUIRIES ====================
-- Insert sample property inquiries
INSERT INTO scrubhub_inquiry (property_id, name, email, phone, message, status, created_at) VALUES
(1, 'John Doe', '<EMAIL>', '******-555-0123', 'I am interested in viewing this property. I am a medical resident at Toronto General Hospital and looking for accommodation close to work.', 'new', NOW()),
(2, 'Sarah Wilson', '<EMAIL>', '******-555-0456', 'Is this property still available? I am starting my residency in February and need accommodation near the university.', 'responded', NOW()),
(3, 'Mike Chen', '<EMAIL>', '******-555-0789', 'Beautiful property! I would like to schedule a viewing. I am a staff physician and can provide references.', 'new', NOW()),
(4, 'Dr. Emily Johnson', '<EMAIL>', '******-555-1234', 'I am looking for short-term accommodation for a visiting fellowship. Is this property available for 6 months?', 'new', NOW());

-- ==================== TENANT NOTICES ====================
-- Insert sample tenant notices (N9 forms)
INSERT INTO scrubhub_tenant_notice (user_id, notice_type, pdf_url, form_data, created_at) VALUES
(1, 'N9', '/uploads/notices/n9_notice_001.pdf', '{"tenant_name": "John Doe", "tenant_address": "123 Main St, Toronto, ON", "landlord_name": "Robert Smith", "landlord_address": "100 Bay St, Toronto, ON", "rental_address": "123 Bay Street, Unit 1205", "termination_date": "2024-04-30", "reason": "End of lease term", "signature_section": {"signed_by": "tenant", "first_name": "John", "last_name": "Doe", "phone_number": "******-555-0123", "signature": "John Doe", "signed_at": "2024-01-15T10:30:00.000Z"}}', NOW()),
(2, 'N9', '/uploads/notices/n9_notice_002.pdf', '{"tenant_name": "Sarah Wilson", "tenant_address": "456 Queen St, Vancouver, BC", "landlord_name": "Jane Property", "landlord_address": "200 Granville St, Vancouver, BC", "rental_address": "321 Oak Street, Unit 4A", "termination_date": "2024-05-31", "reason": "Relocation for work", "signature_section": {"signed_by": "tenant", "first_name": "Sarah", "last_name": "Wilson", "phone_number": "******-555-0456", "signature": "Sarah Wilson", "signed_at": "2024-01-20T14:15:00.000Z"}}', NOW());

-- ==================== CREDIT REPORTS ====================
-- Insert sample credit reports
INSERT INTO scrubhub_credit_report (user_id, trustee_reference, report_url, personal_info, status, created_at) VALUES
(1, 'TR001234567', '/uploads/credit_reports/credit_report_001.pdf', '{"first_name": "John", "last_name": "Doe", "date_of_birth": "1995-03-15", "sin": "123-456-789", "address": "123 Main St, Toronto, ON"}', 'completed', NOW()),
(2, 'TR001234568', '/uploads/credit_reports/credit_report_002.pdf', '{"first_name": "Sarah", "last_name": "Wilson", "date_of_birth": "1994-07-22", "sin": "987-654-321", "address": "456 Queen St, Vancouver, BC"}', 'completed', NOW()),
(3, 'TR001234569', NULL, '{"first_name": "Mike", "last_name": "Chen", "date_of_birth": "1996-11-08", "sin": "456-789-123", "address": "789 College St, Montreal, QC"}', 'pending', NOW());

-- ==================== PAYMENTS ====================
-- Insert sample payment records
INSERT INTO scrubhub_payment (user_id, amount, currency, purpose, stripe_transaction_id, status, metadata, created_at) VALUES
(1, 19.99, 'CAD', 'n9_form', 'pi_1234567890abcdef', 1, '{"notice_id": 1}', NOW()),
(2, 19.99, 'CAD', 'n9_form', 'pi_0987654321fedcba', 1, '{"notice_id": 2}', NOW()),
(1, 29.99, 'CAD', 'credit_report', 'pi_1111222233334444', 1, '{"report_id": 1}', NOW()),
(2, 29.99, 'CAD', 'credit_report', 'pi_5555666677778888', 1, '{"report_id": 2}', NOW()),
(3, 29.99, 'CAD', 'credit_report', 'pi_9999000011112222', 0, '{"report_id": 3}', NOW()),
(4, 149.99, 'CAD', 'property_listing', 'pi_aaabbbcccdddeeef', 1, '{"property_id": 1, "tier": "paid"}', NOW()),
(5, 299.99, 'CAD', 'property_listing', 'pi_fffeeeddddcccbbb', 1, '{"property_id": 3, "tier": "featured"}', NOW()),
(1, 4.99, 'CAD', 'phone_reveal', 'pi_1234abcd5678efgh', 1, '{"item_id": 2}', NOW());

-- ==================== ADMIN ACTIONS ====================
-- Insert sample admin actions
INSERT INTO scrubhub_admin_action (admin_id, action_type, target_type, target_id, description, metadata, created_at) VALUES
(9, 'user_verification', 'user', 6, 'Verified hospital account for Toronto General Hospital', '{"verification_documents": ["hospital_license.pdf", "contact_verification.pdf"]}', NOW()),
(9, 'property_approval', 'property', 1, 'Approved featured property listing', '{"tier": "featured", "duration": 60}', NOW()),
(9, 'job_moderation', 'job', 1, 'Approved job posting after review', '{"moderation_notes": "All requirements met"}', NOW()),
(9, 'marketplace_moderation', 'marketplace_item', 1, 'Approved marketplace item listing', '{"category": "medical_equipment"}', NOW());

-- ==================== PREFERENCES ====================
-- Insert sample user preferences
INSERT INTO scrubhub_preference (first_name, last_name, phone, photo, user_id, created_at, updated_at) VALUES
('John', 'Doe', '******-555-0123', '/uploads/profiles/john_doe_avatar.jpg', 1, NOW(), NOW()),
('Sarah', 'Wilson', '******-555-0456', '/uploads/profiles/sarah_wilson_avatar.jpg', 2, NOW(), NOW()),
('Mike', 'Chen', '******-555-0789', '/uploads/profiles/mike_chen_avatar.jpg', 3, NOW(), NOW()),
('Robert', 'Smith', '******-555-1001', '/uploads/profiles/robert_smith_avatar.jpg', 4, NOW(), NOW()),
('Jane', 'Property', '******-555-2001', '/uploads/profiles/jane_property_avatar.jpg', 5, NOW(), NOW()),
('Emily', 'Johnson', '******-555-3001', '/uploads/profiles/emily_johnson_avatar.jpg', 6, NOW(), NOW()),
('Michael', 'Wong', '******-555-3002', '/uploads/profiles/michael_wong_avatar.jpg', 7, NOW(), NOW()),
('Lisa', 'Rodriguez', '+1-************', '/uploads/profiles/lisa_rodriguez_avatar.jpg', 8, NOW(), NOW());

-- ==================== TOKENS ====================
-- Insert sample authentication tokens (for testing purposes)
INSERT INTO scrubhub_tokens (user_id, token, code, type, data, status, created_at, updated_at, expired_at) VALUES
(1, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...', 'AUTH001', 0, '{"device": "mobile", "ip": "*************"}', 1, NOW(), NOW(), DATE_ADD(NOW(), INTERVAL 7 DAY)),
(2, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...', 'AUTH002', 0, '{"device": "web", "ip": "*************"}', 1, NOW(), NOW(), DATE_ADD(NOW(), INTERVAL 7 DAY)),
(6, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...', 'AUTH003', 0, '{"device": "web", "ip": "*************"}', 1, NOW(), NOW(), DATE_ADD(NOW(), INTERVAL 7 DAY));

-- ==================== UPLOADS ====================
-- Insert sample file uploads
INSERT INTO scrubhub_uploads (url, caption, user_id, width, height, type, created_at, updated_at) VALUES
('/uploads/properties/prop1_living_room.jpg', 'Living room with city view', 4, 1920, 1080, 0, NOW(), NOW()),
('/uploads/properties/prop1_kitchen.jpg', 'Modern kitchen with granite countertops', 4, 1920, 1080, 0, NOW(), NOW()),
('/uploads/marketplace/stethoscope_main.jpg', 'Littmann Classic III Stethoscope', 1, 800, 600, 1, NOW(), NOW()),
('/uploads/resumes/john_doe_resume.pdf', 'John Doe Resume', 1, 0, 0, 2, NOW(), NOW()),
('/uploads/profiles/sarah_wilson_avatar.jpg', 'Profile picture', 2, 400, 400, 3, NOW(), NOW());
