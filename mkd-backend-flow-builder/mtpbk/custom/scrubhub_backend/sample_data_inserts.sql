-- ScrubHub Sample Data Inserts
-- This file contains realistic sample data for all ScrubHub API tables
-- Based on sql_migrations.sql and enhanced job posting API fields

-- ==================== USERS ====================
-- Insert sample users with different roles (includes all migration fields)
INSERT INTO scrubhub_user (email, password, login_type, role_id, data, status, verify, name, first_name, last_name, phone, role, verified, stripe_customer_id, profile_image, created_at, updated_at) VALUES
-- Tenants (role = 0)
('<EMAIL>', '$2a$10$zdFHCUM9iAoVH4HwjBmz3uC0EXdgCd1zI3JKl4fxXkH4SUtiMpHeC', 0, '0', '{"address": "123 Main St, Toronto, ON", "city": "Toronto", "province_state": "ON", "country": "Canada", "medical_school": "University of Toronto", "graduation_year": 2023}', 0, 1, '<PERSON>', '<PERSON>', 'Doe', '******-555-0123', 0, 1, 'cus_tenant_001', '/uploads/profiles/john_doe.jpg', NOW(), NOW()),
('<EMAIL>', '$2a$10$YvQw0qQkpXT8bhha2SmiQOoJsIHbIEblw7kDJ8agaCHvx6UDMXOCu', 0, '0', '{"address": "456 Queen St, Vancouver, BC", "city": "Vancouver", "province_state": "BC", "country": "Canada", "medical_school": "UBC Faculty of Medicine", "graduation_year": 2022}', 0, 1, 'Sarah Wilson', 'Sarah', 'Wilson', '******-555-0456', 0, 1, 'cus_tenant_002', '/uploads/profiles/sarah_wilson.jpg', NOW(), NOW()),
('<EMAIL>', '$2a$10$IO2Bxnb2tOwNzGZjDlQlNe98sUvwHwQUatFC1A.uxQe9L/Qb1SxSm', 0, '0', '{"address": "789 College St, Montreal, QC", "city": "Montreal", "province_state": "QC", "country": "Canada", "medical_school": "McGill University", "graduation_year": 2024}', 0, 1, 'Mike Chen', 'Mike', 'Chen', '******-555-0789', 0, 1, 'cus_tenant_003', '/uploads/profiles/mike_chen.jpg', NOW(), NOW()),

-- Landlords (role = 1)
('<EMAIL>', '$2a$10$zdFHCUM9iAoVH4HwjBmz3uC0EXdgCd1zI3JKl4fxXkH4SUtiMpHeC', 0, '1', '{"company": "Smith Properties", "address": "100 Bay St, Toronto, ON", "city": "Toronto", "province_state": "ON", "country": "Canada", "business_license": "BL123456"}', 0, 1, 'Robert Smith', 'Robert', 'Smith', '******-555-1001', 1, 1, 'cus_landlord_001', '/uploads/profiles/robert_smith.jpg', NOW(), NOW()),
('<EMAIL>', '$2a$10$YvQw0qQkpXT8bhha2SmiQOoJsIHbIEblw7kDJ8agaCHvx6UDMXOCu', 0, '1', '{"company": "Vancouver Rentals", "address": "200 Granville St, Vancouver, BC", "city": "Vancouver", "province_state": "BC", "country": "Canada", "business_license": "BL789012"}', 0, 1, 'Jane Property', 'Jane', 'Property', '******-555-2001', 1, 1, 'cus_landlord_002', '/uploads/profiles/jane_property.jpg', NOW(), NOW()),

-- Hospitals (role = 2)
('<EMAIL>', '$2a$10$IO2Bxnb2tOwNzGZjDlQlNe98sUvwHwQUatFC1A.uxQe9L/Qb1SxSm', 0, '2', '{"hospital_name": "Toronto General Hospital", "contact_person": "Dr. Emily Johnson", "address": "200 Elizabeth St, Toronto, ON", "city": "Toronto", "province_state": "ON", "country": "Canada", "hospital_license": "HL001", "accreditation": "CAHO"}', 0, 1, 'Toronto General Hospital', 'Emily', 'Johnson', '+1-************', 2, 1, 'cus_hospital_001', '/uploads/profiles/tgh_logo.jpg', NOW(), NOW()),
('<EMAIL>', '$2a$10$zdFHCUM9iAoVH4HwjBmz3uC0EXdgCd1zI3JKl4fxXkH4SUtiMpHeC', 0, '2', '{"hospital_name": "Vancouver General Hospital", "contact_person": "Dr. Michael Wong", "address": "899 W 12th Ave, Vancouver, BC", "city": "Vancouver", "province_state": "BC", "country": "Canada", "hospital_license": "HL002", "accreditation": "CAHO"}', 0, 1, 'Vancouver General Hospital', 'Michael', 'Wong', '******-555-3002', 2, 1, 'cus_hospital_002', '/uploads/profiles/vgh_logo.jpg', NOW(), NOW()),

-- Recruiters (role = 3)
('<EMAIL>', '$2a$10$YvQw0qQkpXT8bhha2SmiQOoJsIHbIEblw7kDJ8agaCHvx6UDMXOCu', 0, '3', '{"company": "Medical Staffing Solutions", "contact_person": "Lisa Rodriguez", "address": "300 King St, Toronto, ON", "city": "Toronto", "province_state": "ON", "country": "Canada", "agency_license": "AL001"}', 0, 1, 'Lisa Rodriguez', 'Lisa', 'Rodriguez', '******-555-4001', 3, 1, 'cus_recruiter_001', '/uploads/profiles/lisa_rodriguez.jpg', NOW(), NOW()),

-- Admin (role = 4)
('<EMAIL>', '$2a$10$IO2Bxnb2tOwNzGZjDlQlNe98sUvwHwQUatFC1A.uxQe9L/Qb1SxSm', 0, '4', '{"role": "super_admin", "permissions": ["all"], "department": "Platform Operations"}', 0, 1, 'Admin User', 'Admin', 'User', '******-555-9999', 4, 1, 'cus_admin_001', '/uploads/profiles/admin_avatar.jpg', NOW(), NOW());

-- ==================== PROPERTIES ====================
-- Insert sample properties
INSERT INTO scrubhub_property (landlord_id, title, description, address, city, province_state, country, price, tier, duration_days, status, amenities, property_type, bedrooms, bathrooms, furnished, available_from, expires_at, created_at, updated_at) VALUES
(4, 'Modern 2BR Condo Downtown Toronto', 'Beautiful modern 2-bedroom condo in the heart of downtown Toronto. Features include granite countertops, stainless steel appliances, in-unit laundry, and stunning city views. Perfect for medical professionals working at nearby hospitals.', '123 Bay Street, Unit 1205', 'Toronto', 'ON', 'Canada', 2800.00, 1, 30, 0, '["gym", "concierge", "rooftop_terrace", "parking", "storage"]', 'condo', 2, 2, 1, '2024-02-01', DATE_ADD(NOW(), INTERVAL 30 DAY), NOW(), NOW()),

(4, 'Spacious 1BR Near University of Toronto', 'Bright and spacious 1-bedroom apartment just steps from University of Toronto and major hospitals. Ideal for medical students and residents. Includes utilities and high-speed internet.', '456 College Street, Unit 8B', 'Toronto', 'ON', 'Canada', 2200.00, 0, 30, 0, '["utilities_included", "internet", "laundry", "bike_storage"]', 'apartment', 1, 1, 0, '2024-01-15', DATE_ADD(NOW(), INTERVAL 30 DAY), NOW(), NOW()),

(5, 'Luxury 3BR House Vancouver West Side', 'Stunning 3-bedroom house in prestigious Vancouver West Side. Close to VGH and UBC. Features include hardwood floors, modern kitchen, private garden, and mountain views.', '789 Maple Avenue', 'Vancouver', 'BC', 'Canada', 4500.00, 2, 60, 0, '["garden", "parking", "dishwasher", "fireplace", "mountain_view"]', 'house', 3, 2, 1, '2024-03-01', DATE_ADD(NOW(), INTERVAL 60 DAY), NOW(), NOW()),

(5, 'Cozy Studio Near VGH', 'Perfect studio apartment for medical residents. Located just 5 minutes walk from Vancouver General Hospital. Fully furnished with all essentials included.', '321 Oak Street, Unit 4A', 'Vancouver', 'BC', 'Canada', 1800.00, 0, 30, 0, '["furnished", "utilities_included", "gym", "security"]', 'studio', 0, 1, 1, '2024-01-20', DATE_ADD(NOW(), INTERVAL 30 DAY), NOW(), NOW());

-- ==================== PROPERTY PHOTOS ====================
-- Insert sample property photos
INSERT INTO scrubhub_property_photo (property_id, file_path, sort_order, created_at) VALUES
(1, '/uploads/properties/prop1_living_room.jpg', 1, NOW()),
(1, '/uploads/properties/prop1_kitchen.jpg', 2, NOW()),
(1, '/uploads/properties/prop1_bedroom.jpg', 3, NOW()),
(1, '/uploads/properties/prop1_bathroom.jpg', 4, NOW()),
(1, '/uploads/properties/prop1_view.jpg', 5, NOW()),

(2, '/uploads/properties/prop2_main.jpg', 1, NOW()),
(2, '/uploads/properties/prop2_bedroom.jpg', 2, NOW()),
(2, '/uploads/properties/prop2_kitchen.jpg', 3, NOW()),

(3, '/uploads/properties/prop3_exterior.jpg', 1, NOW()),
(3, '/uploads/properties/prop3_living.jpg', 2, NOW()),
(3, '/uploads/properties/prop3_kitchen.jpg', 3, NOW()),
(3, '/uploads/properties/prop3_garden.jpg', 4, NOW()),

(4, '/uploads/properties/prop4_studio.jpg', 1, NOW()),
(4, '/uploads/properties/prop4_bathroom.jpg', 2, NOW());

-- ==================== JOBS ====================
-- Insert sample job postings with all enhanced fields from migrations
-- Note: Using correct ENUM values and fixing job_type issue
INSERT INTO scrubhub_job (
  poster_id, title, description, location, type, department, salary_min, salary_max, requirements, contact_email, contact_phone, status, expires_at,
  location_type, job_type, has_start_date, start_date, payment_type, show_pay_by, daily_updates_email, resume_required, allow_email_contact,
  has_application_deadline, application_deadline, posting_duration_days, created_at, updated_at
) VALUES
-- Internal Medicine Resident Position
(6, 'Internal Medicine Resident Position',
 'We are seeking a dedicated Internal Medicine Resident to join our team at Toronto General Hospital. This position offers excellent training opportunities in a world-class academic medical center with exposure to complex cases and cutting-edge research.',
 '200 Elizabeth Street, Toronto, ON M5G 2C4', 'residency', 'Internal Medicine', 65000.00, 75000.00,
 'MD degree, MCCEE certification, strong clinical skills, excellent communication abilities, commitment to patient care',
 '<EMAIL>', '+1-************', 0, DATE_ADD(NOW(), INTERVAL 45 DAY),
 'in_person', 'full_time', 1, '2024-07-01', 'yearly', 'range', '<EMAIL>', 1, 0,
 1, '2024-06-15', 45, NOW(), NOW()),

-- Emergency Medicine Fellowship
(6, 'Emergency Medicine Fellowship',
 'Exciting Emergency Medicine Fellowship opportunity at Toronto General Hospital. Join our dynamic emergency department and gain experience in trauma, critical care, and emergency procedures. Research opportunities available.',
 '200 Elizabeth Street, Toronto, ON M5G 2C4', 'fellowship', 'Emergency Medicine', 80000.00, 90000.00,
 'Completed residency in Emergency Medicine, FRCPC certification preferred, trauma experience, research interest',
 '<EMAIL>', '+1-************', 0, DATE_ADD(NOW(), INTERVAL 60 DAY),
 'in_person', 'full_time', 1, '2024-09-01', 'yearly', 'range', '<EMAIL>', 1, 1,
 1, '2024-07-31', 60, NOW(), NOW()),

-- Staff Cardiologist Position
(7, 'Staff Cardiologist Position',
 'Vancouver General Hospital is seeking a Staff Cardiologist to join our Cardiology Department. Excellent opportunity for career growth in a leading Canadian hospital with state-of-the-art facilities and collaborative environment.',
 '899 W 12th Ave, Vancouver, BC V5Z 1M9', 'staff', 'Cardiology', 250000.00, 350000.00,
 'FRCPC in Cardiology, minimum 3 years experience, research experience preferred, interventional cardiology skills an asset',
 '<EMAIL>', '******-555-3002', 0, DATE_ADD(NOW(), INTERVAL 30 DAY),
 'in_person', 'permanent', 0, NULL, 'yearly', 'range', '<EMAIL>', 1, 1,
 1, '2024-05-31', 30, NOW(), NOW()),

-- Family Physician Position (Fixed job_type from 'contract' to valid ENUM)
(8, 'Family Physician - Locum Tenens',
 'Medical Staffing Solutions is seeking Family Physicians for locum tenens positions across Ontario. Flexible scheduling and competitive compensation. Perfect for physicians seeking work-life balance.',
 'Various locations across Ontario', 'locum', 'Family Medicine', 150.00, 200.00,
 'CCFP certification, valid Ontario license, minimum 2 years experience, flexibility for travel',
 '<EMAIL>', '******-555-4001', 0, DATE_ADD(NOW(), INTERVAL 90 DAY),
 'in_person', 'freelance', 0, NULL, 'hourly', 'range', '<EMAIL>', 1, 1,
 0, NULL, 90, NOW(), NOW()),

-- Registered Nurse ICU Position
(6, 'Registered Nurse - ICU',
 'Full-time RN position in our state-of-the-art ICU department. Join our dedicated team providing critical care to patients in a fast-paced, technology-rich environment.',
 '200 Elizabeth Street, Toronto, ON M5G 2C4', 'staff', 'Critical Care', 75000.00, 95000.00,
 'BSN required, 2+ years ICU experience preferred, ACLS certification, strong critical thinking skills',
 '<EMAIL>', '+1-************', 0, DATE_ADD(NOW(), INTERVAL 60 DAY),
 'in_person', 'full_time', 1, '2024-08-01', 'yearly', 'range', '<EMAIL>', 1, 0,
 1, '2024-07-15', 60, NOW(), NOW()),

-- Remote Telehealth Physician
(8, 'Telehealth Family Physician',
 'Remote telehealth position for licensed family physicians. Provide virtual consultations and follow-up care. Flexible hours with competitive compensation.',
 'Remote - Ontario based', 'staff', 'Family Medicine', 120000.00, 160000.00,
 'CCFP certification, valid Ontario license, telehealth experience preferred, excellent communication skills',
 '<EMAIL>', '******-555-4001', 0, DATE_ADD(NOW(), INTERVAL 45 DAY),
 'remote', 'part_time', 0, NULL, 'yearly', 'range', '<EMAIL>', 1, 1,
 1, '2024-06-30', 45, NOW(), NOW());

-- ==================== JOB APPLICANTS ====================
-- Insert sample job applications (Fixed column name: applicant_id -> user_id, removed created_at)
INSERT INTO scrubhub_job_applicant (job_id, user_id, cover_letter, resume_url, status, applied_at) VALUES
(1, 1, 'I am very interested in the Internal Medicine Resident position at Toronto General Hospital. My clinical experience during medical school and dedication to patient care make me an ideal candidate for this program.', '/uploads/resumes/john_doe_resume.pdf', 'pending', NOW()),
(1, 2, 'As a recent medical graduate from UBC, I am excited about the opportunity to begin my residency training in Internal Medicine at your prestigious institution. I am particularly drawn to the research opportunities available.', '/uploads/resumes/sarah_wilson_resume.pdf', 'pending', NOW()),
(2, 3, 'I am writing to express my strong interest in the Emergency Medicine Fellowship. My experience in emergency settings during residency has prepared me well for this advanced training opportunity.', '/uploads/resumes/mike_chen_resume.pdf', 'reviewed', NOW()),
(3, 1, 'I would like to apply for the Staff Cardiologist position. My extensive experience in interventional cardiology and research background would be valuable additions to your cardiology team.', '/uploads/resumes/john_doe_cardio_resume.pdf', 'interviewed', NOW()),
(4, 2, 'I am interested in the locum tenens opportunities across Ontario. My flexibility and comprehensive family medicine experience make me a strong candidate for various practice settings.', '/uploads/resumes/sarah_wilson_family_resume.pdf', 'pending', NOW()),
(5, 3, 'I am applying for the ICU Registered Nurse position. My critical care experience and ACLS certification align perfectly with your requirements.', '/uploads/resumes/mike_chen_nursing_resume.pdf', 'pending', NOW());

-- ==================== MARKETPLACE ITEMS ====================
-- Insert sample marketplace items (Fixed column name: condition -> item_condition)
INSERT INTO scrubhub_marketplace_item (seller_id, title, description, price, item_condition, category, status, phone_revealed, listing_fee, created_at) VALUES
(1, 'Stethoscope - Littmann Classic III', 'Excellent condition Littmann Classic III stethoscope. Used for 2 years during medical school. Perfect acoustic quality for students or residents. Includes original box and warranty card.', 180.00, 'excellent', 'medical_equipment', 0, 0, 5.99, NOW()),
(2, 'Medical Textbooks Bundle', 'Complete set of essential medical textbooks including Harrison\'s Internal Medicine 21st Edition, Gray\'s Anatomy for Students, and Robbins Basic Pathology. Great condition with minimal highlighting.', 450.00, 'good', 'textbooks', 0, 0, 9.99, NOW()),
(3, 'White Coat - Size Medium', 'Professional white coat, size medium. Embroidered with previous owner\'s name (can be easily removed). Perfect condition, barely used. Ideal for medical students or residents.', 45.00, 'like_new', 'clothing', 0, 0, 2.99, NOW()),
(1, 'Otoscope/Ophthalmoscope Set', 'Welch Allyn diagnostic set with otoscope and ophthalmoscope. Includes carrying case, extra specula, and batteries. Excellent working condition.', 320.00, 'good', 'medical_equipment', 0, 0, 7.99, NOW()),
(2, 'Study Desk with Lamp', 'Sturdy wooden study desk with built-in adjustable lamp. Perfect for medical students. Some minor wear but very functional. Dimensions: 48"W x 24"D x 30"H.', 120.00, 'fair', 'furniture', 0, 0, 4.99, NOW()),
(3, 'Medical Reference Books', 'Collection of specialty medical reference books including Cardiology, Neurology, and Pediatrics texts. Various publishers and editions. Great for residents.', 200.00, 'good', 'textbooks', 0, 0, 6.99, NOW());

-- ==================== MARKETPLACE ITEM PHOTOS ====================
-- Insert sample marketplace item photos
INSERT INTO scrubhub_marketplace_item_photo (item_id, file_path, sort_order, created_at) VALUES
(1, '/uploads/marketplace/stethoscope_main.jpg', 1, NOW()),
(1, '/uploads/marketplace/stethoscope_detail.jpg', 2, NOW()),
(1, '/uploads/marketplace/stethoscope_box.jpg', 3, NOW()),
(2, '/uploads/marketplace/textbooks_stack.jpg', 1, NOW()),
(2, '/uploads/marketplace/textbooks_individual.jpg', 2, NOW()),
(2, '/uploads/marketplace/textbooks_spines.jpg', 3, NOW()),
(3, '/uploads/marketplace/white_coat_front.jpg', 1, NOW()),
(3, '/uploads/marketplace/white_coat_back.jpg', 2, NOW()),
(4, '/uploads/marketplace/diagnostic_set_case.jpg', 1, NOW()),
(4, '/uploads/marketplace/diagnostic_set_open.jpg', 2, NOW()),
(4, '/uploads/marketplace/diagnostic_set_components.jpg', 3, NOW()),
(5, '/uploads/marketplace/study_desk_main.jpg', 1, NOW()),
(5, '/uploads/marketplace/study_desk_lamp.jpg', 2, NOW()),
(6, '/uploads/marketplace/reference_books_collection.jpg', 1, NOW()),
(6, '/uploads/marketplace/reference_books_titles.jpg', 2, NOW());

-- ==================== SUBLETS ====================
-- Insert sample sublet requests (Removed service_fee column that doesn't exist)
INSERT INTO scrubhub_sublet (property_id, tenant_id, price, start_date, end_date, status, service_tier, created_at) VALUES
(1, 1, 2800.00, '2024-06-01', '2024-08-31', 'active', 'premium', NOW()),
(2, 2, 2200.00, '2024-07-01', '2024-09-30', 'active', 'basic', NOW()),
(3, 3, 4500.00, '2024-05-15', '2024-07-15', 'pending', 'premium', NOW()),
(4, 1, 1800.00, '2024-08-01', '2024-10-31', 'active', 'basic', NOW());

-- ==================== FAVORITES ====================
-- Insert sample user favorites
INSERT INTO scrubhub_favorite (user_id, property_id, created_at) VALUES
(1, 2, NOW()),
(1, 3, NOW()),
(1, 4, NOW()),
(2, 1, NOW()),
(2, 4, NOW()),
(3, 1, NOW()),
(3, 2, NOW()),
(3, 3, NOW());

-- ==================== RECENTLY VIEWED ====================
-- Insert sample recently viewed properties (Removed created_at column that doesn't exist)
INSERT INTO scrubhub_recently_viewed (user_id, property_id, viewed_at) VALUES
(1, 1, NOW()),
(1, 2, DATE_SUB(NOW(), INTERVAL 1 HOUR)),
(1, 3, DATE_SUB(NOW(), INTERVAL 2 HOUR)),
(1, 4, DATE_SUB(NOW(), INTERVAL 3 HOUR)),
(2, 4, NOW()),
(2, 1, DATE_SUB(NOW(), INTERVAL 30 MINUTE)),
(2, 3, DATE_SUB(NOW(), INTERVAL 1 HOUR)),
(3, 2, NOW()),
(3, 3, DATE_SUB(NOW(), INTERVAL 45 MINUTE)),
(3, 1, DATE_SUB(NOW(), INTERVAL 2 HOUR));

-- ==================== INQUIRIES ====================
-- Insert sample property inquiries (Fixed status to use integers: 0=new, 1=responded, 2=closed)
INSERT INTO scrubhub_inquiry (property_id, name, email, phone, message, status, created_at) VALUES
(1, 'John Doe', '<EMAIL>', '******-555-0123', 'I am interested in viewing this property. I am a medical resident at Toronto General Hospital and looking for accommodation close to work. When would be a good time for a viewing?', 0, NOW()),
(2, 'Sarah Wilson', '<EMAIL>', '******-555-0456', 'Is this property still available? I am starting my residency in February and need accommodation near the university. I can provide references and proof of income.', 1, NOW()),
(3, 'Mike Chen', '<EMAIL>', '******-555-0789', 'Beautiful property! I would like to schedule a viewing. I am a staff physician and can provide excellent references. Are pets allowed?', 0, NOW()),
(4, 'Dr. Emily Johnson', '<EMAIL>', '******-555-1234', 'I am looking for short-term accommodation for a visiting fellowship. Is this property available for 6 months? I am a practicing physician with excellent credit.', 0, NOW()),
(1, 'Dr. Lisa Rodriguez', '<EMAIL>', '******-555-4001', 'I represent several medical professionals looking for quality accommodation. Would you consider multiple applications for similar units?', 1, NOW());

-- ==================== TENANT NOTICES ====================
-- Insert sample tenant notices (N9 forms)
INSERT INTO scrubhub_tenant_notice (user_id, notice_type, pdf_url, form_data, created_at) VALUES
(1, 'N9', '/uploads/notices/n9_notice_001.pdf',
 '{"tenant_name": "John Doe", "tenant_address": "123 Main St, Toronto, ON", "landlord_name": "Robert Smith", "landlord_address": "100 Bay St, Toronto, ON", "rental_address": "123 Bay Street, Unit 1205", "termination_date": "2024-04-30", "reason": "End of lease term", "signature_section": {"signed_by": "tenant", "first_name": "John", "last_name": "Doe", "phone_number": "******-555-0123", "signature": "John Doe", "signed_at": "2024-01-15T10:30:00.000Z", "ip_address": "*************", "user_agent": "Mozilla/5.0"}}',
 NOW()),
(2, 'N9', '/uploads/notices/n9_notice_002.pdf',
 '{"tenant_name": "Sarah Wilson", "tenant_address": "456 Queen St, Vancouver, BC", "landlord_name": "Jane Property", "landlord_address": "200 Granville St, Vancouver, BC", "rental_address": "321 Oak Street, Unit 4A", "termination_date": "2024-05-31", "reason": "Relocation for work", "signature_section": {"signed_by": "tenant", "first_name": "Sarah", "last_name": "Wilson", "phone_number": "******-555-0456", "signature": "Sarah Wilson", "signed_at": "2024-01-20T14:15:00.000Z", "ip_address": "*************", "user_agent": "Mozilla/5.0"}}',
 NOW());

-- ==================== CREDIT REPORTS ====================
-- Insert sample credit reports (Removed personal_info column that doesn't exist)
INSERT INTO scrubhub_credit_report (user_id, trustee_reference, report_url, status, created_at) VALUES
(1, 'TR001234567', '/uploads/credit_reports/credit_report_001.pdf', 'completed', NOW()),
(2, 'TR001234568', '/uploads/credit_reports/credit_report_002.pdf', 'completed', NOW()),
(3, 'TR001234569', NULL, 'pending', NOW());

-- DUPLICATE SECTIONS REMOVED - Using the corrected versions above

-- DUPLICATE SECTIONS REMOVED - All corrected data is above in the main sections
