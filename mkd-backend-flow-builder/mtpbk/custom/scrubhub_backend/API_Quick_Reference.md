# ScrubHub API Quick Reference

## Base URL
```
http://localhost:3000
```

## Authentication
Most endpoints require Bearer token authentication:
```
Authorization: Bearer {jwt_token}
```

## API Endpoints Summary

### 🔐 Authentication
| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| POST | `/v1/api/scrubhub/member/auth/register` | Register member/tenant | No |
| POST | `/v1/api/scrubhub/member/auth/login` | Login member/tenant | No |
| POST | `/v1/api/scrubhub/hospital/auth/register` | Register hospital | No |
| POST | `/v1/api/scrubhub/hospital/auth/login` | Login hospital | No |
| POST | `/v1/api/scrubhub/recruiter/auth/register` | Register recruiter | No |
| POST | `/v1/api/scrubhub/recruiter/auth/login` | Login recruiter | No |

### 📍 Locations & Search (Enhanced with Universities, Hospitals & Popularity)
| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| GET | `/v1/api/scrubhub/locations` | Search locations, universities, hospitals with popularity | No |
| GET | `/v1/api/scrubhub/rentals/near/{location}` | Find rentals near location | No |
| GET | `/v1/api/scrubhub/properties/map` | Search by map bounds | No |

**Enhanced Location Search Features:**
- **Multi-type search:** locations, universities, hospitals, medical_schools
- **Popularity scoring:** Based on search frequency and activity
- **Smart filtering:** Search by name, city, or province
- **Popular recommendations:** Top-rated locations in each category

### 🏥 Medical Schools
| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| GET | `/v1/api/scrubhub/medical-schools` | Get all medical schools | No |
| GET | `/v1/api/scrubhub/medical-schools/{id}` | Get school with nearby properties | No |

### 🏠 Properties
| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| GET | `/v1/api/scrubhub/properties` | Search properties with filters | No |
| GET | `/v1/api/scrubhub/properties/{id}` | Get property details | No |
| POST | `/v1/api/scrubhub/properties` | Create property listing | Yes (Landlord) |
| PUT | `/v1/api/scrubhub/properties/{id}` | Update property | Yes (Owner) |
| DELETE | `/v1/api/scrubhub/properties/{id}` | Delete property | Yes (Owner) |
| GET | `/v1/api/scrubhub/landlord/properties` | Get landlord's properties | Yes (Landlord) |

### ⭐ Favorites & Recently Viewed
| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| GET | `/v1/api/scrubhub/favorites` | Get user favorites | Yes |
| POST | `/v1/api/scrubhub/favorites` | Add to favorites | Yes |
| DELETE | `/v1/api/scrubhub/favorites/{property_id}` | Remove from favorites | Yes |
| POST | `/v1/api/scrubhub/favorites/toggle` | Toggle favorite status | Yes |
| GET | `/v1/api/scrubhub/recently-viewed` | Get recently viewed | Yes |
| POST | `/v1/api/scrubhub/recently-viewed` | Add to recently viewed | Yes |

### 📧 Inquiries
| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| POST | `/v1/api/scrubhub/inquiries` | Submit property inquiry | No |
| POST | `/v1/api/scrubhub/properties/{id}/inquire` | Inquire about specific property | No |
| GET | `/v1/api/scrubhub/inquiries` | Get landlord inquiries | Yes (Landlord) |
| GET | `/v1/api/scrubhub/properties/{id}/inquiries` | Get property inquiries | Yes (Landlord) |
| PUT | `/v1/api/scrubhub/inquiries/{id}/respond` | Mark as responded | Yes (Landlord) |
| PUT | `/v1/api/scrubhub/inquiries/{id}/status` | Update inquiry status | Yes (Landlord) |

### 🏠 Sublets
| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| POST | `/v1/api/scrubhub/sublets` | Create sublet request | Yes |
| GET | `/v1/api/scrubhub/sublets` | Get user's sublets | Yes |

### 💼 Jobs
| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| GET | `/v1/api/scrubhub/jobs` | Search jobs | No |
| GET | `/v1/api/scrubhub/jobs/{id}` | Get job details | No |
| POST | `/v1/api/scrubhub/jobs` | Create job posting (with posting_fee & payment_method_id) | Yes (Hospital/Recruiter) |
| POST | `/v1/api/scrubhub/jobs/{id}/apply` | Apply to job | No |
| GET | `/v1/api/scrubhub/jobs/{id}/applications` | Get job applications | Yes (Job Owner) |
| GET | `/v1/api/scrubhub/jobs/{id}/applicants` | Get job applicants | Yes (Job Owner) |
| PUT | `/v1/api/scrubhub/applicants/{id}/status` | Update applicant status | Yes (Job Owner) |

### 💼 Job Management (Hospital/Recruiter)
| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| GET | `/v1/api/scrubhub/my-jobs` | Get my posted jobs | Yes (Hospital/Recruiter) |
| GET | `/v1/api/scrubhub/my-jobs/{id}` | Get job details for editing | Yes (Job Owner) |
| PUT | `/v1/api/scrubhub/my-jobs/{id}` | Update job posting | Yes (Job Owner) |
| DELETE | `/v1/api/scrubhub/my-jobs/{id}` | Delete job posting | Yes (Job Owner) |

### 🛒 Marketplace
| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| GET | `/v1/api/scrubhub/marketplace` | Search marketplace items | No |
| POST | `/v1/api/scrubhub/marketplace` | Create marketplace item (with images array) | Yes |
| POST | `/v1/api/scrubhub/marketplace/{id}/reveal-phone` | Reveal seller phone | Yes |

**Note:** Marketplace items support up to 10 images via the `images` array field.

### 💳 Credit Check & Legal
| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| POST | `/v1/api/scrubhub/credit-check` | Request credit check | Yes |
| GET | `/v1/api/scrubhub/credit-reports` | Get user's credit reports | Yes |
| GET | `/v1/api/scrubhub/credit-reports/{id}/download` | Download credit report | Yes |
| POST | `/v1/api/scrubhub/n9-form` | Generate N9 form | Yes |
| GET | `/v1/api/scrubhub/n9-form/{id}/download` | Download N9 form | Yes |
| POST | `/v1/api/scrubhub/n9-notice` | Create N9 notice | Yes |
| GET | `/v1/api/scrubhub/notices` | Get user notices | Yes |

### 👤 Profile & Dashboard
| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| GET | `/v1/api/scrubhub/profile` | Get user profile | Yes |
| PUT | `/v1/api/scrubhub/profile` | Update profile (includes profile_photo) | Yes |
| GET | `/v1/api/scrubhub/dashboard` | Get role-specific dashboard | Yes |

### 💰 Subscriptions & Billing
| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| GET | `/v1/api/scrubhub/customer/lambda/billing-info` | Get complete billing information | Yes |
| GET | `/v1/api/scrubhub/customer/lambda/available-plans` | Get available subscription plans | Yes |
| POST | `/v1/api/scrubhub/customer/lambda/create-card-token` | Create card token for payments | Yes |
| POST | `/v1/api/scrubhub/customer/lambda/create-stripe-customer` | Create Stripe customer | Yes |
| POST | `/v1/api/scrubhub/customer/lambda/add-payment-method` | Add payment method | Yes |
| POST | `/v1/api/scrubhub/customer/lambda/create-subscription` | Create subscription or one-time payment | Yes |
| POST | `/v1/api/scrubhub/customer/lambda/cancel-subscription` | Cancel subscription | Yes |
| GET | `/v1/api/scrubhub/customer/lambda/get-invoices` | Get user invoices (paginated) | Yes |
| GET | `/v1/api/scrubhub/subscription/plans` | Get subscription plans (legacy) | No |
| GET | `/v1/api/scrubhub/subscription` | Get subscription status (legacy) | Yes |
| POST | `/v1/api/scrubhub/subscription` | Create subscription (legacy) | Yes |

**Payment Types:**
- `payment_type: "subscription"` - Recurring subscription with Stripe price ID
- `payment_type: "onetime"` - One-time payment with numeric amount (e.g., 39.99) - **Uses direct charges, fully handled on backend**

**Invoices Endpoint:**
- Returns both Stripe invoices (subscriptions) and local payment records (charges)
- Supports pagination with `page` and `limit` parameters
- Filter by payment type using `type` parameter (job_posting, property_listing, marketplace_listing, etc.)
- Includes invoice PDFs and hosted URLs for Stripe invoices

### 🏢 Enterprise Applications
| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| POST | `/v1/api/scrubhub/enterprise/apply` | Submit enterprise bulk listing application | Yes (Hospital/Recruiter) |
| GET | `/v1/api/scrubhub/enterprise/applications` | Get user's enterprise applications | Yes (Hospital/Recruiter) |

### 👤 Admin
| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| GET | `/v1/api/scrubhub/admin/dashboard` | Get admin dashboard | Yes (Admin) |
| POST | `/v1/api/scrubhub/admin/verify-user/{id}` | Verify user account | Yes (Admin) |
| POST | `/v1/api/scrubhub/admin/approve-sublet/{id}` | Approve sublet request | Yes (Admin) |
| GET | `/v1/api/scrubhub/admin/enterprise-applications` | Get all enterprise applications | Yes (Admin) |
| PUT | `/v1/api/scrubhub/admin/enterprise-applications/{id}` | Review enterprise application | Yes (Admin) |

### 🔗 Webhooks
| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| POST | `/v1/api/scrubhub/webhook/stripe` | Stripe payment webhook | No (Webhook) |

## Common Query Parameters

### Pagination
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 10)

### Property Search
- `city`: Filter by city
- `location`: Search near location name
- `medical_school_id`: Filter by specific medical school
- `radius`: Search radius in km (default: 25)
- `min_price`, `max_price`: Price range
- `bedrooms`: Number of bedrooms
- `property_type`: apartment, house, condo, etc.
- `furnished`: true/false
- `available_from`: Date (YYYY-MM-DD)
- `near_medical_school`: true/false
- `sort_by`: Field to sort by
- `sort_order`: ASC/DESC

### Job Search
- `location`: Job location
- `type`: residency, fellowship, staff, etc.
- `hospital_id`: Filter by hospital
- `salary_min`, `salary_max`: Salary range

### Marketplace Search
- `category`: Item category
- `item_condition`: new, used, like_new, etc.
- `min_price`, `max_price`: Price range
- `search`: Text search in title/description

## Response Formats

### Success Response
```json
{
  "error": false,
  "list": [...],
  "model": {...},
  "message": "Success message",
  "pagination": {
    "total": 100,
    "totalPages": 10,
    "currentPage": 1,
    "limit": 10
  }
}
```

### Error Response
```json
{
  "error": true,
  "message": "Error description"
}
```

## Pricing Structure

### Property Listings
- **Free**: 2 listings, 30 days each
- **Paid**: $4.99 (30 days), $9.99 (60 days), $14.99 (90 days)
- **Featured**: +$19.99 (top of search results)

### Services
- **Sublet Self-Managed**: $49.99
- **Sublet Premium**: $599+
- **Job Posting**: $99.99 (paid listings)
- **Credit Check**: $29.99 (30-day reusable)
- **N9 Form**: $19.99
- **Phone Reveal**: $2.99-$19.99 (based on item value)

### Subscriptions
- **Basic**: $29.99/month (10 listings, 60 days, featured)
- **Premium**: $59.99/month (50 listings, 90 days, priority support)
- **Enterprise**: $199.99/month (unlimited, 365 days, dedicated manager)

## User Roles

| Role | ID | Description | Permissions |
|------|----|-----------|-----------| 
| Tenant | 0 | Property seekers | View, favorite, inquire, apply |
| Landlord | 1 | Property owners | All tenant + create/manage properties |
| Hospital | 2 | Medical institutions | Job posting, applicant management |
| Recruiter | 3 | Medical recruiters | Job posting, applicant management |
| Admin | 4 | System administrators | Full system access |

## Status Codes

### Property Status
- 0: Active
- 1: Inactive
- 2: Pending payment
- 3: Pending approval

### Payment Status
- 0: Pending
- 1: Completed
- 2: Failed
- 3: Refunded

### Inquiry Status
- 0: New
- 1: Responded

### Job Application Status
- 0: New
- 1: Interviewing
- 2: Rejected
- 3: Hired

## Testing Tips

1. **Start with Authentication**: Register and login users for each role
2. **Use Environment Variables**: Set `base_url` and `auth_token` in Postman
3. **Test Error Cases**: Try invalid data, missing auth, wrong roles
4. **Verify Payments**: Use Stripe test mode for payment testing
5. **Check Permissions**: Ensure role-based access works correctly
6. **Test Pagination**: Try different page sizes and numbers
7. **Location Testing**: Use real Canadian cities and postal codes
8. **File Uploads**: Test image uploads for properties and marketplace

## Common Test Data

### Canadian Cities
- Toronto, ON
- Vancouver, BC
- Montreal, QC
- Calgary, AB
- Ottawa, ON

### Medical Schools
- University of Toronto Faculty of Medicine
- McGill University Faculty of Medicine
- University of British Columbia Faculty of Medicine
- University of Alberta Faculty of Medicine

### Property Types
- apartment, house, condo, townhouse, studio, room

### Job Types
- residency, fellowship, staff, locum, research

### Marketplace Categories
- medical_equipment, textbooks, furniture, electronics, clothing
