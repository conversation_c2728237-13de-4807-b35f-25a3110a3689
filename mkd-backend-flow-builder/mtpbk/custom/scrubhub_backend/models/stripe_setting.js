
const BaseModel = require('../../../baas/core/BaseModel');

class stripe_setting extends BaseModel {
  static schema() {
    return [
      {
        "name": "id",
        "type": "primary key",
        "validation": [],
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "key",
        "type": "string",
        "validation": [],
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "value",
        "type": "medium text",
        "validation": [],
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "created_at",
        "type": "date",
        "validation": [],
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "updated_at",
        "type": "datetime",
        "validation": [],
        "defaultValue": null,
        "mapping": null
      }
    ];
  }




}

module.exports = stripe_setting;
