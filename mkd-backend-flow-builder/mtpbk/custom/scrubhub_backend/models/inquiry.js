const BaseModel = require('../../../baas/core/BaseModel');

class inquiry extends BaseModel {
  static schema() {
    return [
      {
        "name": "id",
        "type": "primary key",
        "validation": [],
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "property_id",
        "type": "integer",
        "validation": "required",
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "name",
        "type": "string",
        "validation": "required",
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "email",
        "type": "string",
        "validation": "required,email",
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "phone",
        "type": "string",
        "validation": [],
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "message",
        "type": "text",
        "validation": [],
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "status",
        "type": "mapping",
        "validation": [],
        "defaultValue": "0",
        "mapping": "0:new,1:responded"
      },
      {
        "name": "created_at",
        "type": "timestamp",
        "validation": "date",
        "defaultValue": "CURRENT_TIMESTAMP",
        "mapping": null
      }
    ];
  }

  transformStatus(value) {
    const mappings = {
      '0': 'new',
      '1': 'responded'
    };
    return mappings[value] || value;
  }

  static mapping () {
    return {
      "status": {
        "0": "new",
        "1": "responded"
      }
    };
  }
}

module.exports = inquiry;
