const BaseModel = require('../../../baas/core/BaseModel');

class job_applicant extends BaseModel {
  static schema() {
    return [
      {
        "name": "id",
        "type": "primary key",
        "validation": [],
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "job_id",
        "type": "integer",
        "validation": "required",
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "applicant_name",
        "type": "string",
        "validation": "required",
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "resume_url",
        "type": "string",
        "validation": [],
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "email",
        "type": "string",
        "validation": "required,email",
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "phone",
        "type": "string",
        "validation": [],
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "cover_letter",
        "type": "text",
        "validation": [],
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "status",
        "type": "mapping",
        "validation": [],
        "defaultValue": "0",
        "mapping": "0:applied,1:interviewing,2:rejected,3:hired"
      },
      {
        "name": "created_at",
        "type": "timestamp",
        "validation": "date",
        "defaultValue": "CURRENT_TIMESTAMP",
        "mapping": null
      }
    ];
  }

  transformStatus(value) {
    const mappings = {
      '0': 'applied',
      '1': 'interviewing',
      '2': 'rejected',
      '3': 'hired'
    };
    return mappings[value] || value;
  }

  static mapping () {
    return {
      "status": {
        "0": "applied",
        "1": "interviewing",
        "2": "rejected",
        "3": "hired"
      }
    };
  }
}

module.exports = job_applicant;
