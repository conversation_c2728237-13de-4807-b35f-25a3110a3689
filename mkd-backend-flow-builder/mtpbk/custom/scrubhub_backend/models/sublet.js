const BaseModel = require('../../../baas/core/BaseModel');

class sublet extends BaseModel {
  static schema() {
    return [
      {
        "name": "id",
        "type": "primary key",
        "validation": [],
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "property_id",
        "type": "integer",
        "validation": "required",
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "tenant_id",
        "type": "integer",
        "validation": "required",
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "price",
        "type": "decimal",
        "validation": "required",
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "start_date",
        "type": "date",
        "validation": "required",
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "end_date",
        "type": "date",
        "validation": "required",
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "service_tier",
        "type": "mapping",
        "validation": "required,enum:0,1",
        "defaultValue": "0",
        "mapping": "0:self_managed,1:premium"
      },
      {
        "name": "status",
        "type": "mapping",
        "validation": [],
        "defaultValue": "0",
        "mapping": "0:pending,1:active,2:expired,3:cancelled,4:pending_admin_review"
      },
      {
        "name": "created_at",
        "type": "timestamp",
        "validation": "date",
        "defaultValue": "CURRENT_TIMESTAMP",
        "mapping": null
      }
    ];
  }

  transformServiceTier(value) {
    const mappings = {
      '0': 'self_managed',
      '1': 'premium'
    };
    return mappings[value] || value;
  }

  transformStatus(value) {
    const mappings = {
      '0': 'pending',
      '1': 'active',
      '2': 'expired',
      '3': 'cancelled',
      '4': 'pending_admin_review'
    };
    return mappings[value] || value;
  }

  static mapping () {
    return {
      "service_tier": {
        "0": "self_managed",
        "1": "premium"
      },
      "status": {
        "0": "pending",
        "1": "active",
        "2": "expired",
        "3": "cancelled",
        "4": "pending_admin_review"
      }
    };
  }
}

module.exports = sublet;
