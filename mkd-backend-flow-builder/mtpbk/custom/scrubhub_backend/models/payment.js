const BaseModel = require('../../../baas/core/BaseModel');

class payment extends BaseModel {
  static schema() {
    return [
      {
        "name": "id",
        "type": "primary key",
        "validation": [],
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "user_id",
        "type": "integer",
        "validation": "required",
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "amount",
        "type": "decimal",
        "validation": "required",
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "currency",
        "type": "string",
        "validation": [],
        "defaultValue": "CAD",
        "mapping": null
      },
      {
        "name": "purpose",
        "type": "string",
        "validation": "required",
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "stripe_transaction_id",
        "type": "string",
        "validation": [],
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "status",
        "type": "mapping",
        "validation": [],
        "defaultValue": "0",
        "mapping": "0:pending,1:completed,2:failed,3:refunded"
      },
      {
        "name": "metadata",
        "type": "json",
        "validation": [],
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "created_at",
        "type": "timestamp",
        "validation": "date",
        "defaultValue": "CURRENT_TIMESTAMP",
        "mapping": null
      }
    ];
  }

  transformStatus(value) {
    const mappings = {
      '0': 'pending',
      '1': 'completed',
      '2': 'failed',
      '3': 'refunded'
    };
    return mappings[value] || value;
  }

  static mapping () {
    return {
      "status": {
        "0": "pending",
        "1": "completed",
        "2": "failed",
        "3": "refunded"
      }
    };
  }
}

module.exports = payment;
