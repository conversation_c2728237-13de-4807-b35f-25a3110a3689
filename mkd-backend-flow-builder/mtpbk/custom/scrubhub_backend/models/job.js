
const BaseModel = require('../../../baas/core/BaseModel');

class job extends BaseModel {
  static schema() {
    return [
      {
        "name": "id",
        "type": "primary key",
        "validation": [],
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "poster_id",
        "type": "integer",
        "validation": "required",
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "title",
        "type": "string",
        "validation": "required",
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "description",
        "type": "text",
        "validation": "required",
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "location",
        "type": "string",
        "validation": "required",
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "type",
        "type": "string",
        "validation": "required",
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "department",
        "type": "string",
        "validation": [],
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "salary_min",
        "type": "decimal",
        "validation": [],
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "salary_max",
        "type": "decimal",
        "validation": [],
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "requirements",
        "type": "text",
        "validation": [],
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "contact_email",
        "type": "string",
        "validation": "email",
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "contact_phone",
        "type": "string",
        "validation": [],
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "status",
        "type": "mapping",
        "validation": [],
        "defaultValue": "0",
        "mapping": "0:active,1:closed,2:draft"
      },
      {
        "name": "expires_at",
        "type": "timestamp",
        "validation": [],
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "created_at",
        "type": "timestamp",
        "validation": "date",
        "defaultValue": "CURRENT_TIMESTAMP",
        "mapping": null
      },
      {
        "name": "updated_at",
        "type": "timestamp",
        "validation": "date",
        "defaultValue": "CURRENT_TIMESTAMP",
        "mapping": null
      }
    ];
  }


  transformStatus(value) {
    const mappings = {
      '0': 'active',
      '1': 'closed',
      '2': 'draft'
    };
    return mappings[value] || value;
  }

  static mapping () {
    return {
      "status": {
        "0": "active",
        "1": "closed",
        "2": "draft"
      }
    };
  }

}

module.exports = job;
