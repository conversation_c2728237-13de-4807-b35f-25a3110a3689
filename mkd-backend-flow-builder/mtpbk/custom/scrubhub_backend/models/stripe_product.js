
const BaseModel = require('../../../baas/core/BaseModel');

class stripe_product extends BaseModel {
  static schema() {
    return [
      {
        "name": "id",
        "type": "primary key",
        "validation": [],
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "created_at",
        "type": "date",
        "validation": [],
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "updated_at",
        "type": "datetime",
        "validation": [],
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "name",
        "type": "string",
        "validation": [],
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "product_id",
        "type": "string",
        "validation": [],
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "stripe_id",
        "type": "string",
        "validation": [],
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "object",
        "type": "long text",
        "validation": [],
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "status",
        "type": "integer",
        "validation": [],
        "defaultValue": null,
        "mapping": null
      }
    ];
  }




}

module.exports = stripe_product;
