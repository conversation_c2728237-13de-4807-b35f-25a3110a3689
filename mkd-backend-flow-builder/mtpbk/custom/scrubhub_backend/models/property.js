const BaseModel = require('../../../baas/core/BaseModel');

class property extends BaseModel {
  static schema() {
    return [
      {
        "name": "id",
        "type": "primary key",
        "validation": [],
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "landlord_id",
        "type": "integer",
        "validation": "required",
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "title",
        "type": "string",
        "validation": "required",
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "description",
        "type": "text",
        "validation": [],
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "address",
        "type": "text",
        "validation": "required",
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "city",
        "type": "string",
        "validation": "required",
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "province_state",
        "type": "string",
        "validation": "required",
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "country",
        "type": "string",
        "validation": [],
        "defaultValue": "Canada",
        "mapping": null
      },
      {
        "name": "price",
        "type": "decimal",
        "validation": "required",
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "tier",
        "type": "mapping",
        "validation": "required,enum:0,1,2",
        "defaultValue": "0",
        "mapping": "0:free,1:paid,2:featured"
      },
      {
        "name": "duration_days",
        "type": "integer",
        "validation": [],
        "defaultValue": "30",
        "mapping": null
      },
      {
        "name": "status",
        "type": "mapping",
        "validation": [],
        "defaultValue": "0",
        "mapping": "0:active,1:inactive,2:expired,3:draft"
      },
      {
        "name": "amenities",
        "type": "json",
        "validation": [],
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "property_type",
        "type": "string",
        "validation": [],
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "bedrooms",
        "type": "integer",
        "validation": [],
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "bathrooms",
        "type": "integer",
        "validation": [],
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "furnished",
        "type": "boolean",
        "validation": [],
        "defaultValue": "0",
        "mapping": null
      },
      {
        "name": "available_from",
        "type": "date",
        "validation": [],
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "expires_at",
        "type": "timestamp",
        "validation": [],
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "created_at",
        "type": "timestamp",
        "validation": "date",
        "defaultValue": "CURRENT_TIMESTAMP",
        "mapping": null
      },
      {
        "name": "updated_at",
        "type": "timestamp",
        "validation": "date",
        "defaultValue": "CURRENT_TIMESTAMP",
        "mapping": null
      }
    ];
  }

  transformTier(value) {
    const mappings = {
      '0': 'free',
      '1': 'paid',
      '2': 'featured'
    };
    return mappings[value] || value;
  }

  transformStatus(value) {
    const mappings = {
      '0': 'active',
      '1': 'inactive',
      '2': 'expired',
      '3': 'draft'
    };
    return mappings[value] || value;
  }

  static mapping () {
    return {
      "tier": {
        "0": "free",
        "1": "paid",
        "2": "featured"
      },
      "status": {
        "0": "active",
        "1": "inactive",
        "2": "expired",
        "3": "draft"
      }
    };
  }
}

module.exports = property;
