# ScrubHub API Testing Guide

## Overview
This guide provides comprehensive instructions for testing the ScrubHub API using the provided Postman collection. The API supports a medical housing and recruitment platform with role-based access for tenants, landlords, hospitals, and recruiters.

## Setup Instructions

### 1. Import Postman Collection
1. Open Postman
2. Click "Import" button
3. Select the `ScrubHub_API_Postman_Collection.json` file
4. The collection will be imported with all endpoints organized by functionality

### 2. Environment Variables
Set up the following environment variables in Postman:
- `base_url`: Your API base URL (e.g., `http://localhost:3000`)
- `auth_token`: JWT token obtained from login (will be set automatically after login)

### 3. Database Setup
Ensure your database has the required tables as defined in `sql_migrations.sql`:
- scrubhub_user
- scrubhub_property
- scrubhub_property_photo
- scrubhub_location
- scrubhub_medical_school
- scrubhub_favorite
- scrubhub_recently_viewed
- scrubhub_inquiry
- scrubhub_sublet
- scrubhub_job
- scrubhub_job_applicant
- scrubhub_marketplace_item
- scrubhub_credit_report
- scrubhub_tenant_notice
- scrubhub_subscription
- scrubhub_payment

## Testing Workflow

### Phase 1: Authentication Testing

#### 1.1 User Registration
Test user registration for different roles:

**Tenant/Landlord Registration:**
```
POST /v1/api/scrubhub/member/lambda/register
```
- Creates users with role 0 (tenant) or 1 (landlord)
- Test with valid email, password, and required fields
- Verify duplicate email prevention

**Hospital Registration:**
```
POST /v1/api/scrubhub/hospital/lambda/register
```
- Creates users with role 2 (hospital)
- Test hospital-specific registration flow

**Recruiter Registration:**
```
POST /v1/api/scrubhub/recruiter/lambda/register
```
- Creates users with role 3 (recruiter)
- Test recruiter-specific registration flow

#### 1.2 User Login
Test login for each user type:
```
POST /v1/api/scrubhub/{role}/lambda/login
```
- Verify JWT token generation
- Test invalid credentials handling
- Set `auth_token` variable from response

### Phase 2: Core Property Management

#### 2.1 Location-Based Search
**Get Available Locations:**
```
GET /v1/api/scrubhub/locations
```
- Returns all stored locations in the system
- No authentication required

**Search Rentals Near Location:**
```
GET /v1/api/scrubhub/rentals/near/{location}
```
- Test with different location names (Toronto, Vancouver, etc.)
- Verify radius-based filtering
- Check distance calculations

#### 2.2 Property CRUD Operations
**Create Property (Landlord only):**
```
POST /v1/api/scrubhub/properties
```
- Requires landlord authentication
- Test different property types and tiers
- Verify payment processing for paid listings

**Search Properties:**
```
GET /v1/api/scrubhub/properties
```
- Test various filter combinations
- Verify medical school proximity filtering
- Test pagination

**Property Details:**
```
GET /v1/api/scrubhub/properties/{id}
```
- Test property detail retrieval
- Verify photo and landlord information

### Phase 3: Medical School Integration

#### 3.1 Medical Schools Directory
**Get Medical Schools:**
```
GET /v1/api/scrubhub/medical-schools
```
- Test province/city filtering
- Verify school information

**School Details with Nearby Properties:**
```
GET /v1/api/scrubhub/medical-schools/{id}
```
- Test radius-based property search around schools
- Verify distance calculations

### Phase 4: User Interactions

#### 4.1 Favorites Management
**Add to Favorites:**
```
POST /v1/api/scrubhub/favorites
```
- Requires authentication
- Test duplicate prevention

**Get Favorites:**
```
GET /v1/api/scrubhub/favorites
```
- Verify user-specific favorites
- Test pagination

#### 4.2 Recently Viewed
**Add to Recently Viewed:**
```
POST /v1/api/scrubhub/recently-viewed
```
- Test automatic cleanup (max 10 items)
- Verify chronological ordering

#### 4.3 Property Inquiries
**Submit Inquiry:**
```
POST /v1/api/scrubhub/inquiries
```
- Test inquiry submission without authentication
- Verify required field validation

**Manage Inquiries (Landlord):**
```
GET /v1/api/scrubhub/inquiries
```
- Test status filtering (new/responded)
- Verify landlord-specific inquiries

### Phase 5: Subletting Services

#### 5.1 Sublet Creation
**Self-Managed Sublet:**
```
POST /v1/api/scrubhub/sublets
```
- Test with `service_tier: "self_managed"`
- Verify $49.99 fee processing

**Premium Sublet:**
```
POST /v1/api/scrubhub/sublets
```
- Test with `service_tier: "premium"`
- Verify $599+ fee and admin review status

### Phase 6: Job Management

#### 6.1 Job Posting (Hospital/Recruiter)
**Create Free Job:**
```
POST /v1/api/scrubhub/jobs
```
- Test with `is_paid_posting: false`
- Verify immediate activation

**Create Paid Job:**
```
POST /v1/api/scrubhub/jobs
```
- Test with `is_paid_posting: true`
- Verify payment processing ($99.99 fee)

#### 6.2 Job Applications
**Apply to Job:**
```
POST /v1/api/scrubhub/jobs/{id}/apply
```
- Test application submission
- Verify duplicate application prevention

**Manage Applications:**
```
GET /v1/api/scrubhub/jobs/{id}/applicants
PUT /v1/api/scrubhub/applicants/{id}/status
```
- Test applicant status updates
- Verify job owner permissions

### Phase 7: Marketplace

#### 7.1 Item Management
**Create Marketplace Item:**
```
POST /v1/api/scrubhub/marketplace
```
- Test different price tiers
- Verify fee calculation based on item value

**Search Items:**
```
GET /v1/api/scrubhub/marketplace
```
- Test category and condition filtering
- Verify search functionality

#### 7.2 Phone Reveal Feature
**Reveal Phone Number:**
```
POST /v1/api/scrubhub/marketplace/{id}/reveal-phone
```
- Test dynamic pricing based on item value
- Verify payment processing

### Phase 8: Legal & Financial Services

#### 8.1 Credit Check Integration
**Request Credit Check:**
```
POST /v1/api/scrubhub/credit-check
```
- Test Trustee API integration simulation
- Verify 30-day reusability

**Download Credit Report:**
```
GET /v1/api/scrubhub/credit-reports/{id}/download
```
- Test report access after payment

#### 8.2 N9 Form Generation
**Generate N9 Form:**
```
POST /v1/api/scrubhub/n9-form
```
- Test PDF generation simulation
- Verify $19.99 fee processing

### Phase 9: Subscription Management

#### 9.1 Subscription Plans
**Get Current Subscription:**
```
GET /v1/api/scrubhub/subscription
```
- Test free plan default
- Verify feature limitations

**Subscribe to Plans:**
```
POST /v1/api/scrubhub/subscription
```
- Test Basic ($29.99), Premium ($59.99), Enterprise ($199.99)
- Verify feature unlocking

### Phase 10: Dashboard & Profile

#### 10.1 User Profile
**Get Profile:**
```
GET /v1/api/scrubhub/profile
```
- Verify role-specific information
- Test sensitive data filtering

**Update Profile:**
```
PUT /v1/api/scrubhub/profile
```
- Test field restrictions
- Verify update permissions

#### 10.2 Role-Specific Dashboards
**Get Dashboard:**
```
GET /v1/api/scrubhub/dashboard
```
- Test different role dashboards
- Verify statistics and recent activity

## Error Testing

### Authentication Errors
- Test expired tokens
- Test invalid credentials
- Test role-based access restrictions

### Validation Errors
- Test missing required fields
- Test invalid data formats
- Test business rule violations

### Payment Processing
- Test Stripe webhook simulation
- Test payment failure scenarios
- Test refund processing

## Performance Testing

### Load Testing
- Test concurrent user scenarios
- Verify database query performance
- Test pagination with large datasets

### Search Performance
- Test location-based search with various radii
- Test complex property filtering
- Test medical school proximity calculations

## Security Testing

### Input Validation
- Test SQL injection prevention
- Test XSS protection
- Test file upload security

### Authorization
- Test role-based access control
- Test resource ownership verification
- Test token validation

## Monitoring & Logging

### API Metrics
- Response times for each endpoint
- Error rates and types
- User activity patterns

### Business Metrics
- Property listing conversion rates
- Payment processing success rates
- User engagement metrics

## Troubleshooting

### Common Issues
1. **Database Connection**: Verify database is running and accessible
2. **Stripe Integration**: Ensure webhook endpoints are configured
3. **File Uploads**: Check file storage permissions
4. **Email Services**: Verify SMTP configuration for notifications

### Debug Mode
Enable debug logging to trace:
- SQL queries and performance
- Payment processing flows
- External API integrations
- User authentication flows

## Next Steps

After completing basic testing:
1. Set up automated testing suite
2. Configure monitoring and alerting
3. Implement rate limiting
4. Set up production environment
5. Configure backup and disaster recovery

This comprehensive testing approach ensures all ScrubHub API functionality works correctly across different user roles and use cases.
