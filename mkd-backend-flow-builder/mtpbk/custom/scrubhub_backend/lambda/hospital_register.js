const JwtService = require("../../../baas/services/JwtService");
const PasswordService = require("../../../baas/services/PasswordService");
const { filterEmptyFields, sqlDateTimeFormat } = require("../../../baas/services/UtilService");

module.exports = function (app) {
  const config = app.get("configuration");

  app.post("/v1/api/scrubhub/hospital/auth/register", async function (req, res) {
    try {
      // Validate required fields
      if (!req.body.email || !req.body.password || !req.body.hospital_name) {
        return res.status(403).json({
          error: true,
          message: "Email, password, and hospital name are required"
        });
      }

      const needRefreshToken = req.body.is_refresh ? true : false;
      let refreshToken = undefined;

      // Create user object and validate
      const userObj = filterEmptyFields({
        email: req.body.email,
        role: 2, // hospital role
        password: req.body.password,
        login_type: 0,
        status: 0,
        verified: 0, // Hospitals need verification
        data: JSON.stringify({
          hospital_name: req.body.hospital_name || "",
          contact_person: req.body.contact_person || "",
          phone: req.body.phone || "",
          address: req.body.address || "",
          city: req.body.city || "",
          province_state: req.body.province_state || "",
          country: req.body.country || "Canada",
          license_number: req.body.license_number || "",
          specialties: req.body.specialties || []
        })
      });

      const sdk = app.get("sdk");
      sdk.setProjectId("scrubhub");

      // Check if user already exists
      const existingUser = await sdk.findOne('user', {
        email: userObj.email
      });

      if (existingUser) {
        return res.status(403).json({
          error: true,
          message: "User already exists with this email"
        });
      }

      // Hash password
      const hashedPassword = await PasswordService.hash(req.body.password);

      // Create user record
      const userData = {
        email: req.body.email,
        password: hashedPassword,
        name: req.body.hospital_name,
        phone: req.body.phone,
        address: req.body.address,
        city: req.body.city,
        province_state: req.body.province_state,
        country: req.body.country || 'Canada',
        role: 2, // hospital
        login_type: 0,
        status: 0,
        verified: 0, // Hospitals need verification
        data: JSON.stringify({
          hospital_name: req.body.hospital_name || "",
          contact_person: req.body.contact_person || "",
          phone: req.body.phone || "",
          address: req.body.address || "",
          city: req.body.city || "",
          province_state: req.body.province_state || "",
          country: req.body.country || "Canada",
          license_number: req.body.license_number || "",
          specialties: req.body.specialties || []
        }),
        created_at: sqlDateTimeFormat(new Date()),
        updated_at: sqlDateTimeFormat(new Date())
      };

      // Insert user
      const result = await sdk.create('user', userData);

      if (!result) {
        throw new Error('Failed to create user');
      }

      const userId = result.id;

      // Create preference record
      try {
        await sdk.create('preference', {
          user_id: userId,
          hospital_name: req.body.hospital_name || "",
          contact_person: req.body.contact_person || "",
          phone: req.body.phone || "",
          address: req.body.address || "",
          city: req.body.city || "",
          province_state: req.body.province_state || "",
          country: req.body.country || "Canada",
          created_at: sqlDateTimeFormat(new Date()),
          updated_at: sqlDateTimeFormat(new Date())
        });
      } catch (prefError) {
        console.error('Error creating preference record:', prefError);
        // Don't fail registration if preference creation fails
      }

      // Generate tokens
      const tokenPayload = {
        user_id: userId,
        role: 'hospital',
        email: req.body.email
      };

      let response = {
        error: false,
        role: 'hospital',
        token: JwtService.createAccessToken(
          tokenPayload,
          config.access_jwt_expire,
          config.jwt_key
        ),
        expire_at: config.access_jwt_expire,
        user_id: userId,
        message: 'Hospital registration successful. Verification required for posting jobs.'
      };

      // Handle refresh token if needed
      if (needRefreshToken) {
        refreshToken = JwtService.createAccessToken(
          tokenPayload,
          config.refresh_jwt_expire,
          config.jwt_key
        );

        let expireDate = new Date();
        expireDate.setSeconds(expireDate.getSeconds() + config.refresh_jwt_expire);

        // Save refresh token
        await sdk.create('tokens', {
          user_id: userId,
          token: refreshToken,
          code: refreshToken,
          type: 1,
          data: "{}",
          expired_at: expireDate,
          updated_at: sqlDateTimeFormat(new Date()),
          created_at: sqlDateTimeFormat(new Date())
        });

        response.refresh_token = refreshToken;
      }

      return res.status(200).json(response);

    } catch (err) {
      console.log(err);
      res.status(403).json({
        error: true,
        message: err.message
      });
    }
  });


  return [
    {
      method: "POST",
      name: "Hospital Register API",
      url: "/v1/api/scrubhub/hospital/auth/register",
      successBody: '{ "email": "<EMAIL>", "hospital_name": "Toronto General Hospital", "contact_person": "Dr. Smith", "password": "password123", "phone": "******-555-0123", "city": "Toronto", "province_state": "ON", "is_refresh": true}',
      successPayload: '{"error":false,"role":"hospital","token":"JWT Token","expire_at":3600,"user_id":1}',
      errors: [
        {
          name: "403",
          body: '{"password": "password123", "is_refresh": false}',
          response: '{"error": true,"message": "Email, password, and hospital name are required"}'
        },
        {
          name: "403",
          body: '{ "email": "<EMAIL>", "hospital_name": "Test Hospital", "password": "password123"}',
          response: '{"error": true,"message": "User already exists with this email"}'
        }
      ],
      needToken: false
    }
  ];
};
