const JwtService = require("../../../baas/services/JwtService");
const PasswordService = require("../../../baas/services/PasswordService");
const { sqlDateTimeFormat, filterEmptyFields } = require("../../../baas/services/UtilService");

module.exports = function (app) {
  app.post("/v1/api/scrubhub/hospital/auth/login", async function (req, res) {
    try {
      const config = app.get('configuration');
      const sdk = app.get("sdk");
      sdk.setProjectId("scrubhub");

      // Validate input
      const userInput = filterEmptyFields({
        email: req.body.email,
        password: req.body.password
      });

      if (!userInput.email) {
        return res.status(400).json({
          error: true,
          message: "Email is required"
        });
      }

      if (!userInput.password) {
        return res.status(400).json({
          error: true,
          message: "Password is required"
        });
      }

      // Query user from database (hospitals have role 2)
      const user = await sdk.findOne('user', {
        email: req.body.email,
        role: 2 // hospital role
      });

      if (!user) {
        return res.status(403).json({
          error: true,
          message: "Invalid credentials"
        });
      }

      // Verify password using PasswordService
      const validPassword = await PasswordService.compareHash(req.body.password, user.password);
      if (!validPassword) {
        return res.status(403).json({
          error: true,
          message: "Invalid credentials"
        });
      }

      // Check account status
      if (user.status == 2) {
        return res.status(403).json({
          error: true,
          message: "Your account is disabled"
        });
      }

      // Handle refresh token if needed
      let refreshToken;
      if (req.body.is_refresh) {
        refreshToken = JwtService.createAccessToken(
          {
            user_id: user.id,
            role: 'hospital',
            email: user.email
          },
          config.refresh_jwt_expire,
          config.jwt_key
        );

        let expireDate = new Date();
        expireDate.setSeconds(expireDate.getSeconds() + config.refresh_jwt_expire);

        // Save refresh token to tokens table
        await sdk.create('tokens', {
          user_id: user.id,
          token: refreshToken,
          code: refreshToken,
          expired_at: expireDate,
          type: 1,
          created_at: sqlDateTimeFormat(new Date()),
          updated_at: sqlDateTimeFormat(new Date()),
        });
      }

      // Parse user data if it exists
      let userData = {};
      if (user.data) {
        try {
          userData = JSON.parse(user.data);
        } catch (e) {
          userData = {};
        }
      }

      // Update last login
      await sdk.updateById('user', user.id, {
        updated_at: sqlDateTimeFormat(new Date())
      });

      return res.status(200).json({
        error: false,
        role: 'hospital',
        token: JwtService.createAccessToken(
          {
            user_id: user.id,
            role: 'hospital',
            email: user.email
          },
          config.access_jwt_expire,
          config.jwt_key
        ),
        refresh_token: refreshToken,
        expire_at: config.access_jwt_expire,
        user_id: user.id,
        hospital_name: userData.hospital_name ?? "",
        contact_person: userData.contact_person ?? "",
        email: user.email,
        phone: userData.phone ?? "",
        message: 'Login successful'
      });

    } catch (err) {
      console.error(err);
      res.status(403).json({
        error: true,
        message: err.message
      });
    }
  });

  return [
    {
      method: "POST",
      name: "Hospital Login API",
      url: "/v1/api/scrubhub/hospital/auth/login",
      successBody: '{ "email": "<EMAIL>", "password": "password123", "is_refresh": true }',
      successPayload: '{"error":false, "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...", "refresh_token": "...", "user_id": 1, "role": "hospital"}',
      errors: [
        {
          name: "403",
          body: '{ "email": "<EMAIL>", "password": "wrongpassword" }',
          response: '{"error":true,"message":"Invalid credentials"}',
        },
        {
          name: "400",
          body: '{}',
          response: '{"error":true,"message":"Email is required"}',
        }
      ],
      needToken: false,
    }
  ];
};
