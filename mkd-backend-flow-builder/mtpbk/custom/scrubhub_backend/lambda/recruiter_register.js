const JwtService = require("../../../baas/services/JwtService");
const PasswordService = require("../../../baas/services/PasswordService");
const { filterEmptyFields, sqlDateTimeFormat } = require("../../../baas/services/UtilService");

module.exports = function (app) {
  const config = app.get("configuration");

  app.post("/v1/api/scrubhub/recruiter/auth/register", async function (req, res) {
    try {
      // Validate required fields
      if (!req.body.email || !req.body.password || !req.body.company_name) {
        return res.status(403).json({
          error: true,
          message: "Email, password, and company name are required"
        });
      }

      const needRefreshToken = req.body.is_refresh ? true : false;
      let refreshToken = undefined;

      // Create user object and validate
      const userObj = filterEmptyFields({
        email: req.body.email,
        role: 3, // recruiter role
        password: req.body.password,
        login_type: 0,
        status: 0,
        verified: 0, // Recruiters need verification
        data: JSON.stringify({
          company_name: req.body.company_name || "",
          contact_person: req.body.contact_person || "",
          first_name: req.body.first_name || "",
          last_name: req.body.last_name || "",
          phone: req.body.phone || "",
          address: req.body.address || "",
          city: req.body.city || "",
          province_state: req.body.province_state || "",
          country: req.body.country || "Canada",
          license_number: req.body.license_number || "",
          specialization: req.body.specialization || []
        })
      });

      const sdk = app.get("sdk");
      sdk.setProjectId("scrubhub");

      // Check if user already exists
      const existingUser = await sdk.findOne('user', {
        email: userObj.email
      });

      if (existingUser) {
        return res.status(403).json({
          error: true,
          message: "User already exists with this email"
        });
      }

      // Hash password
      const hashedPassword = await PasswordService.hash(req.body.password);

      // Create user record
      const userData = {
        email: req.body.email,
        password: hashedPassword,
        name: req.body.company_name,
        phone: req.body.phone,
        address: req.body.address,
        city: req.body.city,
        province_state: req.body.province_state,
        country: req.body.country || 'Canada',
        role: 3, // recruiter
        login_type: 0,
        status: 0,
        verified: 0, // Recruiters need verification
        data: JSON.stringify({
          company_name: req.body.company_name || "",
          contact_person: req.body.contact_person || "",
          first_name: req.body.first_name || "",
          last_name: req.body.last_name || "",
          phone: req.body.phone || "",
          address: req.body.address || "",
          city: req.body.city || "",
          province_state: req.body.province_state || "",
          country: req.body.country || "Canada",
          license_number: req.body.license_number || "",
          specialization: req.body.specialization || []
        }),
        created_at: sqlDateTimeFormat(new Date()),
        updated_at: sqlDateTimeFormat(new Date())
      };

      // Insert user
      const result = await sdk.create('user', userData);

      if (!result) {
        throw new Error('Failed to create user');
      }

      const userId = result.id;

      // Create preference record
      try {
        await sdk.create('preference', {
          user_id: userId,
          company_name: req.body.company_name || "",
          contact_person: req.body.contact_person || "",
          first_name: req.body.first_name || "",
          last_name: req.body.last_name || "",
          phone: req.body.phone || "",
          address: req.body.address || "",
          city: req.body.city || "",
          province_state: req.body.province_state || "",
          country: req.body.country || "Canada",
          created_at: sqlDateTimeFormat(new Date()),
          updated_at: sqlDateTimeFormat(new Date())
        });
      } catch (prefError) {
        console.error('Error creating preference record:', prefError);
        // Don't fail registration if preference creation fails
      }

      // Generate tokens
      const tokenPayload = {
        user_id: userId,
        role: 'recruiter',
        email: req.body.email
      };

      let response = {
        error: false,
        role: 'recruiter',
        token: JwtService.createAccessToken(
          tokenPayload,
          config.access_jwt_expire,
          config.jwt_key
        ),
        expire_at: config.access_jwt_expire,
        user_id: userId,
        message: 'Recruiter registration successful. Verification required for posting jobs.'
      };

      // Handle refresh token if needed
      if (needRefreshToken) {
        refreshToken = JwtService.createAccessToken(
          tokenPayload,
          config.refresh_jwt_expire,
          config.jwt_key
        );

        let expireDate = new Date();
        expireDate.setSeconds(expireDate.getSeconds() + config.refresh_jwt_expire);

        // Save refresh token
        await sdk.create('tokens', {
          user_id: userId,
          token: refreshToken,
          code: refreshToken,
          type: 1,
          data: "{}",
          expired_at: expireDate,
          updated_at: sqlDateTimeFormat(new Date()),
          created_at: sqlDateTimeFormat(new Date())
        });

        response.refresh_token = refreshToken;
      }

      return res.status(200).json(response);

    } catch (err) {
      console.log(err);
      res.status(403).json({
        error: true,
        message: err.message
      });
    }
  });

  return [
    {
      method: "POST",
      name: "Recruiter Register API",
      url: "/v1/api/scrubhub/recruiter/auth/register",
      successBody: '{ "email": "<EMAIL>", "company_name": "MedStaff Solutions", "contact_person": "John Smith", "password": "password123", "phone": "******-555-0123", "city": "Toronto", "province_state": "ON", "is_refresh": true}',
      successPayload: '{"error":false,"role":"recruiter","token":"JWT Token","expire_at":3600,"user_id":1}',
      errors: [
        {
          name: "403",
          body: '{"password": "password123", "is_refresh": false}',
          response: '{"error": true,"message": "Email, password, and company name are required"}'
        },
        {
          name: "403",
          body: '{ "email": "<EMAIL>", "company_name": "Test Company", "password": "password123"}',
          response: '{"error": true,"message": "User already exists with this email"}'
        }
      ],
      needToken: false
    }
  ];
};
