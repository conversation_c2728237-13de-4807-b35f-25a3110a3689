const JwtService = require("../../../baas/services/JwtService");
const PasswordService = require("../../../baas/services/PasswordService");
const MailService = require("../../../baas/services/MailService");
const { filterEmptyFields, sqlDateTimeFormat } = require("../../../baas/services/UtilService");

module.exports = function (app) {
  const config = app.get("configuration");

  app.post("/v1/api/scrubhub/member/auth/register", async function (req, res) {
    try {
      // Validate required fields
      if (!req.body.email || !req.body.password) {
        return res.status(403).json({
          error: true,
          message: "Email and password are required"
        });
      }

      const needRefreshToken = req.body.is_refresh ? true : false;
      let refreshToken = undefined;

      // Create user object and validate
      const userObj = filterEmptyFields({
        email: req.body.email,
        role: 0, // tenant/member role
        password: req.body.password,
        login_type: 0,
        status: 0,
        verified: 0,
        data: JSON.stringify({
          first_name: req.body.first_name || "",
          last_name: req.body.last_name || "",
          phone: req.body.phone || "",
          address: req.body.address || "",
          city: req.body.city || "",
          province_state: req.body.province_state || "",
          country: req.body.country || "Canada"
        })
      });

      const sdk = app.get("sdk");
      sdk.setProjectId("scrubhub");

      // Check if user already exists
      const existingUser = await sdk.findOne('user', {
        email: userObj.email
      });

      if (existingUser) {
        return res.status(403).json({
          error: true,
          message: "User already exists with this email"
        });
      }

      // Hash password
      const hashedPassword = await PasswordService.hash(req.body.password);

      // Create user record
      const userData = {
        email: req.body.email,
        password: hashedPassword,
        name: `${req.body.first_name || ''} ${req.body.last_name || ''}`.trim() || req.body.email,
        phone: req.body.phone,
        address: req.body.address,
        city: req.body.city,
        province_state: req.body.province_state,
        country: req.body.country || 'Canada',
        role: 0, // tenant/member
        login_type: 0,
        status: 0,
        verified: 0,
        data: JSON.stringify({
          first_name: req.body.first_name || "",
          last_name: req.body.last_name || "",
          phone: req.body.phone || "",
          address: req.body.address || "",
          city: req.body.city || "",
          province_state: req.body.province_state || "",
          country: req.body.country || "Canada"
        }),
        created_at: sqlDateTimeFormat(new Date()),
        updated_at: sqlDateTimeFormat(new Date())
      };

      // Insert user
      const result = await sdk.create('user', userData);

      if (!result) {
        throw new Error('Failed to create user');
      }

      const userId = result.id;

      // Create preference record
      try {
        await sdk.create('preference', {
          user_id: userId,
          first_name: req.body.first_name || "",
          last_name: req.body.last_name || "",
          phone: req.body.phone || "",
          address: req.body.address || "",
          city: req.body.city || "",
          province_state: req.body.province_state || "",
          country: req.body.country || "Canada",
          created_at: sqlDateTimeFormat(new Date()),
          updated_at: sqlDateTimeFormat(new Date())
        });
      } catch (prefError) {
        console.error('Error creating preference record:', prefError);
        // Don't fail registration if preference creation fails
      }

      // Generate tokens
      const tokenPayload = {
        user_id: userId,
        role: 'member',
        email: req.body.email
      };

      let response = {
        error: false,
        role: 'member',
        token: JwtService.createAccessToken(
          tokenPayload,
          config.access_jwt_expire,
          config.jwt_key
        ),
        expire_at: config.access_jwt_expire,
        user_id: userId,
        message: 'Registration successful'
      };

      // Handle refresh token if needed
      if (needRefreshToken) {
        refreshToken = JwtService.createAccessToken(
          tokenPayload,
          config.refresh_jwt_expire,
          config.jwt_key
        );

        let expireDate = new Date();
        expireDate.setSeconds(expireDate.getSeconds() + config.refresh_jwt_expire);

        // Save refresh token
        await sdk.create('tokens', {
          user_id: userId,
          token: refreshToken,
          code: refreshToken,
          type: 1,
          data: "{}",
          expired_at: expireDate,
          updated_at: sqlDateTimeFormat(new Date()),
          created_at: sqlDateTimeFormat(new Date())
        });

        response.refresh_token = refreshToken;
      }

      return res.status(200).json(response);

    } catch (err) {
      console.log(err);
      res.status(403).json({
        error: true,
        message: err.message
      });
    }
  });

  return [
    {
      method: "POST",
      name: "Member Register API",
      url: "/v1/api/scrubhub/member/auth/register",
      successBody: '{ "email": "<EMAIL>", "first_name": "John", "last_name": "Doe", "password": "password123", "phone": "******-555-0123", "city": "Toronto", "province_state": "ON", "is_refresh": true}',
      successPayload: '{"error":false,"role":"member","token":"JWT Token","expire_at":3600,"user_id":1}',
      errors: [
        {
          name: "403",
          body: '{"password": "password123", "is_refresh": false}',
          response: '{"error": true,"message": "Email and password are required"}'
        },
        {
          name: "403",
          body: '{ "email": "<EMAIL>", "password": "password123"}',
          response: '{"error": true,"message": "User already exists with this email"}'
        }
      ],
      needToken: false
    }
  ];
};
