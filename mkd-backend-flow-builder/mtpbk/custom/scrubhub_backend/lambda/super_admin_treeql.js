const QueryBuilder = require("../../../baas/core/QueryBuilder");
const DBUtil = require("../../../baas/core/DBUtil");
const RequestUtils = require("../../../baas/core/RequestUtils");
const TokenMiddleware = require("../../../baas/middleware/TokenMiddleware");
const PaginationService = require("../../../baas/services/PaginationService");



module.exports = function (app) {
  // GET single or many record
  app.get("/v1/api/records/scrubhub/super_admin/:table/:id", [TokenMiddleware()], async function (req, res) {
    try {
      let sdk = app.get("sdk"); //uses backendSDK
      const table = req.params.table;
      const id = req.params.id;
      // const role = req.role;

      // Validate input
      if (!table || !id) {
        return res.status(403).json({
          error: true,
          message: "Invalid table or ID"
        });
      }

      // Check role permissions
      const RoleClass = require(`../roles/super_admin`);
      if (RoleClass.slug !== "super_admin") {
        return res.status(403).json({
          error: true,
          message: "Forbidden access"
        });
      }

     

      // Permission checks
      if (!RoleClass.canPerformOperation(table, 'getOne')) {
        return res.status(403).json({
          error: true,
          message: "Forbidden access to get single record"
        });
      }

      
      sdk.setProjectId("scrubhub")

      // Process joins
      let { joins, hasJoin } = RequestUtils.getRequestJoins(req);
      let { includes, hasIncludes } = RequestUtils.getRequestIncludes(req);
      let { excludes, hasExcludes } = RequestUtils.getRequestExcludes(req);
      let joinData = [];

      if (hasJoin) {
        if (!RoleClass.canPerformOperation(table, 'join')) {
          return res.status(403).json({
            error: true,
            message: "Forbidden join access"
          });
        }

        sdk.setProjectId("scrubhub")
        
        for (let joinChain of joins) {
          let currentTable = table;
          let chainData = [];
          
          for (let i = 0; i < joinChain.length; i++) {
            const relationData = joinChain[i].split("|");
            const joinTable = relationData[0];
            const joinForeignKey = relationData[1];
            
            const relationInfo = await guessRelationInfo(
              sdk, 
              currentTable, 
              joinTable, 
              joinForeignKey
            );
            chainData = [...chainData, ...relationInfo];
            
            currentTable = joinTable;
          }
          
          joinData = [...joinData, ...chainData];
        }
      }


      sdk.setProjectId("scrubhub")
      sdk.setTable(table);

      // Process IDs (support single and multiple)  
      let ids = id.split(",");

      
      // Get records with all fields
      let record = await getResult(
        sdk, 
        ids, 
        joinData, 
        {}, // Empty select object to get all fields
        RoleClass.getBlacklistedFields(table)
      );

      let blacklistedFields = RoleClass.getBlacklistedFields(table) 
      if (blacklistedFields.length > 0) {
        hasExcludes = true;
        excludes = [...excludes, ...blacklistedFields]
      }

      // Apply includes and excludes filters if specified
      if ((hasIncludes || hasExcludes) && record) {
        if (Array.isArray(record)) {
          record = record.map(item => filterIncludedFields(item, includes, excludes));
        } else {
          record = filterIncludedFields(record, includes, excludes);
        }
      }

      let data = {model: null}
      if (record && record.length == 1) {
        data['model'] = record[0];
      }

      if (record && record.length > 1) {
        data['list'] = record;
        delete data.model
      }

       // Get field mappings
     // Get field mappings
     const mappings = await getFieldMappings([table, ...joins.flat().map(j => j.split('|')[0])] );

      // Return response
      return res.status(200).json({
        status: 200,
        mappings,
        ...data
      });
    } catch (error) {
      console.error('Error fetching record:', error);
      return res.status(500).json({
        error: true,
        message: "Internal server error",
      });
    }
  });

  // LIST/PAGINATE records
  app.get("/v1/api/records/scrubhub/super_admin/:table", [TokenMiddleware()], async function (req, res) {
    try {
      let sdk = app.get('sdk');
      const table = req.params.table;
      const role = req.role;

      // Validate input
      if (!table) {
        return res.status(400).json({
          error: true,
          message: "Invalid table name"
        });
      }

      // Check role permissions
      const RoleClass = require(`../roles/super_admin`);
      if (RoleClass.slug !== role) {
        return res.status(403).json({
          error: true,
          message: "Forbidden access"
        });
      }

      // Permission checks
      if (!RoleClass.canPerformOperation(table, 'getAll')) {
        return res.status(403).json({
          error: true,
          message: "Forbidden access to list records"
        });
      }

      let { order, direction } = RequestUtils.getRequestOrdering(req);
      let { filterData, hasFilter } = RequestUtils.getRequestFilters(req);
      let { joins, hasJoin } = RequestUtils.getRequestJoins(req);
      let { page, limit, hasPagination } = RequestUtils.getRequestPagination(req);
      let { includes, hasIncludes } = RequestUtils.getRequestIncludes(req);
      let { excludes, hasExcludes } = RequestUtils.getRequestExcludes(req);
      let size = req.query.size ?? 2000;
      let joinData = [];

      sdk.setProjectId("scrubhub");
      sdk.setTable(table);

      // Process joins if present
      if (hasJoin) {
        if (!RoleClass.canPerformOperation(table, 'join')) {
          return res.status(403).json({
            error: true,
            message: "Forbidden join access"
          });
        }
        sdk.setProjectId("scrubhub")
        
        for (let joinChain of joins) {
          let currentTable = table;
          let chainData = [];
          
          for (let i = 0; i < joinChain.length; i++) {
            const relationData = joinChain[i].split("|");
            const joinTable = relationData[0];
            const joinForeignKey = relationData[1];
            
            const relationInfo = await guessRelationInfo(
              sdk, 
              currentTable, 
              joinTable, 
              joinForeignKey
            );
            
            chainData = [...chainData, ...relationInfo];
            
            currentTable = joinTable;
          }
          
          joinData = [...joinData, ...chainData];
        }
      }

      // Get results based on pagination
      let result;
      if (hasPagination) {
        result = await getPaginated(
          sdk,
          page,
          limit,
          order,
          direction,
          filterData,
          joinData,
          {},
          RoleClass.getBlacklistedFields(table)
        );
      } else {
        let list = await getList(
          sdk,
          size,
          order,
          direction,
          filterData,
          joinData,
          {},
          RoleClass.getBlacklistedFields(table)
        );
        result = { list };
      }

      let blacklistedFields = RoleClass.getBlacklistedFields(table) 
      if (blacklistedFields.length > 0) {
        hasExcludes = true;
        excludes = [...excludes, ...blacklistedFields]
      }

       // Apply includes and excludes filters if specified
      if ((hasIncludes || hasExcludes) && result) {
        result.list = result.list.map(item => filterIncludedFields(item, includes, excludes));
        
      }

      // Get field mappings
      const mappings = await getFieldMappings([table, ...joins.flat().map(j => j.split('|')[0])]);

      return res.json({
        status: 200,
        mappings,
        ...result
      });

    } catch (error) {
      console.error('Error in list/paginate endpoint:', error);
      return res.status(500).json({
        error: true,
        message: "Internal server error"
      });
    }
  });

  // CREATE record
  app.post("/v1/api/records/scrubhub/super_admin/:table", [TokenMiddleware()], async function (req, res) {
    try {
      let sdk = app.get("sdk");
      const table = req.params.table;
      const payload = req.body;
      const role = req.role;

      // Validate input
      if (!table) {
        return res.status(400).json({
          error: true,
          message: "Invalid table name"
        });
      }

      // Check role permissions
      const RoleClass = require(`../roles/super_admin`);
      if (RoleClass.slug !== role) {
        return res.status(403).json({
          error: true,
          message: "Forbidden access"
        });
      }

      // Permission checks
      if (!RoleClass.canPerformOperation(table, 'post')) {
        return res.status(403).json({
          error: true,
          message: "Forbidden access to create records"
        });
      }

      // Load and validate model
      const ModelClass = require(`../models/${table}`);
      const model = new ModelClass(payload);

      if (!model.isValid()) {
        return res.status(400).json({
          error: true,
          message: "Validation errors",
          errors: model.getErrors()
        });
      }

      const blacklistedFields = RoleClass.getBlacklistedFields(table) || [];
   
      // Perform insert
      sdk.setProjectId("scrubhub")
      sdk.setTable(table);

      const result = await sdk.create(table, payload);

      // Remove blacklisted fields from response
      const sanitizedResult = Object.keys(result).reduce((acc, key) => {
        if (!blacklistedFields.includes(key)) {
          acc[key] = result[key];
        }
        return acc;
      }, {});

      return res.json({
        status: 200,
        model: sanitizedResult
      });

    } catch (error) {
      console.error('Error in create endpoint:', error);
      return res.status(500).json({
        error: true,
        message: "Internal server error"
      });
    }
  });

  // // UPDATE record
  app.put("/v1/api/records/scrubhub/super_admin/:table/:id?", [TokenMiddleware()], async function (req, res) {
    try {
      let sdk = app.get("sdk");
      const table = req.params.table;
      const id = req.params.id;
      const payload = req.body;
      const role = req.role;

      // Validate input
      if (!table) {
        return res.status(400).json({
          error: true,
          message: "Invalid table name"
        });
      }

      // Check role permissions
      const RoleClass = require(`../roles/super_admin`);
      if (RoleClass.slug !== role) {
        return res.status(403).json({
          error: true,
          message: "Forbidden access"
        });
      }

      // Permission checks
      if (!RoleClass.canPerformOperation(table, 'put')) {
        return res.status(403).json({
          error: true,
          message: "Forbidden access to update records"
        });
      }

      // Load and validate model
      const ModelClass = require(`../models/${table}`);
      const model = new ModelClass(payload);

      if (!model.isValid()) {
        return res.status(400).json({
          error: true,
          message: "Validation errors",
          errors: model.getErrors()
        });
      }

      // Set project and table
      sdk.setProjectId("scrubhub");
      sdk.setTable(table);

      let record = await sdk.updateById(table, id, {
        ...payload,
      });

      // Remove blacklisted fields from response
      const blacklistedFields = RoleClass.getBlacklistedFields(table) || [];
      const sanitizedResult = Object.keys(record).reduce((acc, key) => {
        if (!blacklistedFields.includes(key)) {
          acc[key] = record[key];
        }
        return acc;
      }, {});

      return res.json({
        status: 200,
        model: sanitizedResult,
        message: "Record updated successfully"
      });

    } catch (error) {
      console.error('Error in update endpoint:', error);
      return res.status(500).json({
        error: true,
        message: "Internal server error"
      });
    }
  });

  // DELETE record
  app.delete("/v1/api/records/scrubhub/super_admin/:table/:id?", [TokenMiddleware()], async function (req, res) {
    try {
      let sdk = app.get("sdk");
      const table = req.params.table;
      const id = req.params.id;
      const role = req.role;

      // Validate input
      if (!table || !id) {
        return res.status(400).json({
          error: true,
          message: "Invalid table name or ID"
        });
      }

      // Check role permissions
      const RoleClass = require(`../roles/super_admin`);
      if (RoleClass.slug !== role) {
        return res.status(403).json({
          error: true,
          message: "Forbidden access"
        });
      }

      // Permission checks
      if (!RoleClass.canPerformOperation(table, 'delete')) {
        return res.status(403).json({
          error: true,
          message: "Forbidden access to delete records"
        });
      }

  
      sdk.setProjectId("scrubhub");
      // Perform delete
      const result = await sdk.deleteById(table, id);

      return res.json({
        status: 200,
        success: true,
        message: "Record deleted successfully",
        affectedRows: result?.affectedRows || 0
      });

    } catch (error) {
      console.error('Error in delete endpoint:', error);
      return res.status(500).json({
        error: true,
        message: "Internal server error"
      });
    }
  });
};

// API definition for Postman collection
module.exports.getPostmanDefinition = function () {
  return [
    {
      method: "GET",  
      name: "GET single or many record",
      url: "/v1/api/records/scrubhub/super_admin/:table/:id",
      successPayload: '{"error":false, "mappings": {"table1": {"field1": "field1", "field2": "field2"}, "table2": {"field3": "field3", "field4": "field4"}}, "list": [{"id": 1, "name": "John Doe"}, {"id": 2, "name": "Jane Doe"}]}',
      queryBody: [],
      needToken: true,
      errors: [],
    },
    {
      method: "GET",
      name: "LIST/PAGINATE records",
      url: "/v1/api/records/scrubhub/super_admin/:table",
      successPayload: '{"error":false, "list": [{"id": 1, "name": "John Doe"}, {"id": 2, "name": "Jane Doe"}]}',
      queryBody: [],
      needToken: true,
      errors: [],
    },
    {
      method: "POST",
      name: "CREATE record",
      url: "/v1/api/records/scrubhub/super_admin/:table",
      successPayload: '{"error":false, "model": {"id": 1, "name": "John Doe"}}',
      queryBody: [],
      needToken: true,
      errors: [],
    },
    {
      method: "PUT",
      name: "UPDATE record",
      url: "/v1/api/records/scrubhub/super_admin/:table/:id",
      successPayload: '{"error":false, "model": {"id": 1, "name": "John Doe"}}',
      queryBody: [],
      needToken: true,
      errors: [],
    },
    {
      method: "DELETE",
      name: "DELETE record",
      url: "/v1/api/records/scrubhub/super_admin/:table/:id",
      successPayload: '{"error":false, "message": "Record deleted successfully"}',
      queryBody: [],
      needToken: true,
      errors: [],
    },
  ];
};

/**
 * Guess relation information between tables
 * @param {string} sourceTable 
 * @param {string} joinTable 
 * @param {string} joinForeignKey 
 * @returns {Promise<Array>} Relation information
 */
async function guessRelationInfo(sdk, sourceTable, joinTable, joinForeignKey) {
  const dbUtil = new DBUtil(sdk);
  let relationInfo = await dbUtil.guessRelationInfo(sourceTable, joinTable, joinForeignKey);
  return relationInfo;
}

/**
 * Get field mappings for UI representation
 * @param {Array} table 
 * @param {Object} record 
 * @returns {Promise<Object>} Field mappings
 */
async function getFieldMappings(table, record = {}) {
  let mapping = {};

  // Loop through each table in the array
  for (const tableName of table) {
    try {
      const ModelClass = require(`../models/${tableName}.js`);
      if (ModelClass && typeof ModelClass.mapping === 'function') {
        mapping[tableName] = ModelClass.mapping();
      }
    } catch (error) {
      console.error(`Error loading mapping for table ${tableName}:`, error);
      mapping[tableName] = {};
    }
  }

  return mapping;
}

/**
 * Get database results with optional joins
 * @param {BackendSDK} sdk 
 * @param {Array} ids 
 * @param {Array} joins 
 * @param {Object} select 
 * @returns {Promise<Array>} Query results
 */
async function getResult(sdk, ids = [], joins = [], select = "", blacklistedFields) {
  try {
    let queryBuilder = new QueryBuilder(sdk).whereIn(`${sdk.getTable()}.id`, ids);
    queryBuilder = addBelongsToJoins(queryBuilder, joins, select);

    queryBuilder.select(select);

    let result = await sdk.rawQuery(queryBuilder.get());
    result = await matchRelation(sdk, result, joins);

    if (blacklistedFields && Array.isArray(blacklistedFields)) {
      result = result.map(item => {
        blacklistedFields.forEach(field => {
          delete item[field];
        });
        return item;
      });
    }

    return result;
  } catch (error) {
    console.error('Error in getResult:', error);
    throw error;
  }
}

/**
 * Add belongs-to joins to query
 * @param {QueryBuilder} queryBuilder 
 * @param {Array} joins 
 * @param {Object} select 
 * @returns {QueryBuilder} Updated query builder
 */
function addBelongsToJoins(queryBuilder, joins, select) {
  if (!joins || joins.length === 0) return queryBuilder;

  for (let join of joins) {
    if (join?.type == 2 /* Belongs To */) {
      queryBuilder = queryBuilder.leftJoin(join?.subjectTable, join?.relationTable, join?.column.column_name, "id");
        join?.relationSchema.forEach(column => {
            select[`${join?.relationTable}.${column.column_name}`] = `${join?.relationTable}_${column.column_name}`;
        })
    
    }
   
}

  return queryBuilder;
}



async function getList(sdk, limit = 20, order = "id", direction = "desc", filters = [], joins = [], select = "", blacklistedFields = []) {
  try {
    let queryBuilder = (new QueryBuilder(sdk)).orderBy(order, direction);
    queryBuilder = addQueryFilters(queryBuilder, filters, select);
    queryBuilder = addBelongsToJoins(queryBuilder, joins, select);


    queryBuilder.select(select);

    let result = await sdk.rawQuery(queryBuilder.get(limit));
    result = await matchRelation(sdk, result, joins);

    return result;
  } catch (error) {
    console.error('Error in getList:', error);
    throw error;
  }
}

async function getPaginated(sdk, page = 1, limit = 20, order = "id", direction = "desc", filters = [], joins = [], select = "", blacklistedFields = []) {
  try {
    let paginationService = new PaginationService(page, limit);
    paginationService.setSortField(order);
    paginationService.setSortDirection(direction);

    let countBuilder = (new QueryBuilder(sdk));
    countBuilder = addQueryFilters(countBuilder, filters);
    countBuilder = addBelongsToJoins(countBuilder, joins);
    let count = await sdk.rawQuery(countBuilder.count());
    paginationService.setCount(count[0]?.count);

    // Data query
    let offset = (page - 1) * limit;
    let queryBuilder = (new QueryBuilder(sdk))
      .limit(limit, offset)
      .orderBy(order, direction);

    queryBuilder = addQueryFilters(queryBuilder, filters);
    queryBuilder = addBelongsToJoins(queryBuilder, joins);

    queryBuilder.select(select);

    let result = await sdk.rawQuery(queryBuilder.get());
    result = await matchRelation(sdk, result, joins);

    return {
      list: result,
      page: paginationService.getPage(),
      limit: paginationService.getLimit(),
      total: paginationService.getCount(),
      num_pages: paginationService.getNumPages(),
    };
  } catch (error) {
    console.error('Error in getPaginated:', error);
    throw error;
  }
}

function addQueryFilters(query, filters = []) {
  filters.forEach(filter => {
    let filterTokens = filter.split(",");
    let column = filterTokens[0];
    let operator = filterTokens[1];
    let operand = filterTokens[2];

    if (column === 'id') column = `${query.table}.id`;

    if (operator == "cs") query = query.whereContains(column, operand)
    else if (operator == "ncs") query = query.whereNotContains(column, operand)
    else if (operator == "sw") query = query.whereStartsWith(column, operand)
    else if (operator == "nsw") query = query.whereNotStartsWith(column, operand)
    else if (operator == "ew") query = query.whereEndsWith(column, operand)
    else if (operator == "new") query = query.whereNotEndsWith(column, operand)
    else if (operator == "eq") query = query.where(column, operand)
    else if (operator == "neq") query = query.whereNot(column, operand)
    else if (operator == "lt") query = query.where(column, "<", operand)
    else if (operator == "nlt") query = query.whereNot(column, "<", operand)
    else if (operator == "le") query = query.where(column, "<=", operand)
    else if (operator == "nle") query = query.whereNot(column, "<=", operand)
    else if (operator == "gt") query = query.where(column, ">", operand)
    else if (operator == "ngt") query = query.whereNot(column, ">", operand)
    else if (operator == "ge") query = query.where(column, ">=", operand)
    else if (operator == "nge") query = query.whereNot(column, ">=", operand)
    else if (operator == "is") query = query.whereNull(column)
    else if (operator == "nis") query = query.whereNotNull(column)
    else if (operator == "in") query = query.whereIn(column, filterTokens.slice(2))
    else if (operator == "nin") query = query.whereNotIn(column, filterTokens.slice(2))
    else if (operator == "bt") query = query.whereBetween(column, operand, filterTokens[3]);
    else if (operator == "nbt") query = query.whereNotBetween(column, operand, filterTokens[3]);
    //OR Conditions
    else if (operator == "ocs") query = query.orWhere(column, "LIKE", `'%${operand}%'`)
    else if (operator == "oncs") query = query.orWhere(column, "NOT LIKE", `'%${operand}%'`)
    else if (operator == "osw") query = query.orWhere(column, "LIKE", `'${operand}%'`)
    else if (operator == "onsw") query = query.orWhere(column, "NOT LIKE", `'${operand}%'`)
    else if (operator == "oew") query = query.orWhere(column, "LIKE", `'%${operand}'`)
    else if (operator == "onew") query = query.orWhere(column, "NOT LIKE", `'%${operand}'`)
    else if (operator == "oeq") query = query.orWhere(column, operand)
    else if (operator == "oneq") query = query.orWhere(column, "!=", operand)
    else if (operator == "olt") query = query.orWhere(column, "<", operand)
    else if (operator == "ole") query = query.orWhere(column, "<=", operand)
    else if (operator == "ogt") query = query.orWhere(column, ">", operand)
    else if (operator == "oge") query = query.orWhere(column, ">=", operand)
  })

  return query;
}



async function matchRelation(sdk, result, joins = []) {
    for (let relation of joins) {
        if (relation.type === 2) { // Belongs To
            result = matchBelongsTo(result, relation);
        } else if (relation.type === 1) { // Has One/Many
            result = await matchHasOneOrMany(sdk, result, relation);
        }
    }
    
    return result;
}


function matchBelongsTo(result, join) {
  let matched = [];
  if (Array.isArray(result)) {
    matched = result.map( record => {
                      let relation = {};
                      for (let key in record) {
                          let relationPrefix = `${join?.relationTable}_`;
                          if (key.includes(relationPrefix)) {
                              relation[key.replace(relationPrefix, "")] = record[key];
                              delete record[key];

                          }
                      }
                     
                      record[join?.relation] = relation;
                      return record;
                  })
  }
  return matched;
}


async function matchHasOneOrMany(sdk, result, join) {

  let subjectIds = result.map( item => item.id);
  let query = (new QueryBuilder(sdk)).select()
                                      .from(join?.relation)
                                      .whereIn(join?.column.column_name, subjectIds)
                                      .get();
  let hasManyResults = await sdk.rawQuery(query);
  let matched = [];
  if (Array.isArray(result)) {
      matched = result.map( record => {
          let relations = hasManyResults.filter(relation => record.id == relation[join?.column.column_name]);
          
          let {hasPath, keyPath} = findPath(record, join?.relation); 

          if (hasPath) {
              record[keyPath] = relations
          } else {
              record[join?.relation] = relations;

          }
          return record;
      })
  }

  return matched;
      
}

function findPath (ob, key){
  const path = [];
  const keyExists = (obj) => {
    if (!obj || (typeof obj !== "object" && !Array.isArray(obj))) {
      return false;
    }
    else if (obj.hasOwnProperty(key)) {
      return true;
    }
    else if (Array.isArray(obj)) {
      let parentKey = path.length ? path.pop() : "";

      for (let i = 0; i < obj.length; i++) {
        path.push(`${parentKey}[${i}]`);
        const result = keyExists(obj[i], key);
        if (result) {
          return result;
        }
        path.pop();
      }
    }
    else {
      for (const k in obj) {
        path.push(k);
        const result = keyExists(obj[k], key);
        if (result) {
          return result;
        }
        path.pop();
      }
    }
    return false;
  };

  let hasPath = keyExists(ob);
  let keyPath = path.join(".")

  return {hasPath, keyPath};
}


/**
 * Filter object to only include specified fields and exclude others
 * @param {Object} obj Original object
 * @param {Array} includes Array of field paths to include
 * @param {Array} excludes Array of field paths to exclude
 * @returns {Object} Filtered object
 */
function filterIncludedFields(obj, includes = [], excludes = []) {
  if (!obj) return obj;
  if (!includes.length && !excludes.length) return obj;
  
  let result;
  
  if (includes.length) {
    // Handle includes first
    const includeGroups = includes.reduce((groups, path) => {
      const [root, ...rest] = path.split('.');
      if (!groups[root]) {
        groups[root] = [];
      }
      if (rest.length > 0) {
        groups[root].push(rest.join('.'));
      }
      return groups;
    }, {});
    
    result = {};
    Object.entries(includeGroups).forEach(([root, subPaths]) => {
      if (!obj.hasOwnProperty(root)) return;
      
      if (subPaths.length === 0) {
        result[root] = obj[root];
      } else if (Array.isArray(obj[root])) {
        result[root] = obj[root].map(item => {
          const subResult = {};
          subPaths.forEach(subPath => {
            const value = getNestedValue(item, subPath);
            if (value !== undefined) {
              setNestedValue(subResult, subPath, value);
            }
          });
          return subResult;
        });
      } else if (typeof obj[root] === 'object') {
        result[root] = {};
        subPaths.forEach(subPath => {
          const value = getNestedValue(obj[root], subPath);
          if (value !== undefined) {
            setNestedValue(result[root], subPath, value);
          }
        });
      }
    });
  } else {
    // If no includes, start with full object
    result = {...obj};
  }
  
  // Handle excludes
  if (excludes.length) {
    excludes.forEach(path => {
      if (path.includes('.')) {
        const pathParts = path.split('.');
        const root = pathParts[0];
        
        if (result[root]) {
          if (Array.isArray(result[root])) {
            // Handle array fields
            result[root] = result[root].map(item => {
              const remainingPath = pathParts.slice(1).join('.');
              deleteNestedValue(item, remainingPath);
              return item;
            });
          } else if (typeof result[root] === 'object') {
            // Handle nested objects
            const remainingPath = pathParts.slice(1).join('.');
            deleteNestedValue(result[root], remainingPath);
          }
        }
      } else {
        // Direct field exclusion
        delete result[path];
      }
    });
  }
  
  return result;
}

/**
 * Get value from nested path
 */
function getNestedValue(obj, path) {
  return path.split('.').reduce((current, key) => 
    current && current[key] !== undefined ? current[key] : undefined, obj);
}

/**
 * Set value at nested path
 */
function setNestedValue(obj, path, value) {
  const keys = path.split('.');
  const lastKey = keys.pop();
  const lastObj = keys.reduce((current, key) => {
    current[key] = current[key] || {};
    return current[key];
  }, obj);
  lastObj[lastKey] = value;
}

/**
 * Delete value at nested path
 */
function deleteNestedValue(obj, path) {
  const keys = path.split('.');
  const lastKey = keys.pop();
  const lastObj = keys.reduce((current, key) => 
    current && typeof current === 'object' ? current[key] : undefined, obj);
  
  if (lastObj && typeof lastObj === 'object') {
    delete lastObj[lastKey];
  }
}


