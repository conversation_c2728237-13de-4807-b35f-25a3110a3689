const TokenMiddleware = require("../../../baas/middleware/TokenMiddleware");
const StripeService = require("../services/StripeService");

const middlewares = [TokenMiddleware()];
const stripeService = new StripeService();

const handleGetBillingInfo = async (req, res, sdk) => {
  try {
    sdk.setProjectId("scrubhub");
    
    // Get user data
    const user = await sdk.findOne("user", { id: req.user_id });
    if (!user) {
      return res.status(401).json({
        error: true,
        message: "Invalid Credentials",
      });
    }
    
    // Check if user has a Stripe customer ID
    let stripeCustomer = null;
    let subscription = null;
    let paymentMethods = [];
    
    if (user.stripe_customer_id) {
      try {
        // Get Stripe customer details
        const customerResult = await stripeService.retrieveStripeCustomer({
          customerId: user.stripe_customer_id
        });
        
        if (customerResult && !customerResult.error) {
          stripeCustomer = customerResult;
          
          // Get active subscriptions
          const subscriptionsResult = await stripeService.listStripeSubscription({
            customer: user.stripe_customer_id,
            status: 'active'
          });
          
          if (subscriptionsResult && subscriptionsResult.data && subscriptionsResult.data.length > 0) {
            subscription = subscriptionsResult.data[0]; // Get the first active subscription
          }
          
          // Get payment methods
          const paymentMethodsResult = await stripeService.retrieveStripePaymentMethodAll({
            customerId: user.stripe_customer_id,
            type: 'card'
          });
          
          if (paymentMethodsResult && paymentMethodsResult.data) {
            paymentMethods = paymentMethodsResult.data;
          }

          // Also get card sources (older API) and merge with payment methods
          const cardSourcesResult = await stripeService.retrieveStripeCustomerAllCards({
            customerId: user.stripe_customer_id
          });
          
          if (cardSourcesResult && cardSourcesResult.data) {
            // Convert sources to payment method format for consistency
            const formattedSources = cardSourcesResult.data.map(source => ({
              id: source.id,
              object: 'payment_method',
              card: {
                brand: source.brand,
                exp_month: source.exp_month,
                exp_year: source.exp_year,
                last4: source.last4,
                funding: source.funding
              },
              type: 'card',
              created: source.created || Date.now(),
              customer: source.customer
            }));
            
            // Merge sources with payment methods, avoiding duplicates
            paymentMethods = [...paymentMethods, ...formattedSources];
          }
        }
      } catch (stripeError) {
        console.error("Stripe API error:", stripeError);
      }
    }

    // Get user role for plan targeting
    const roleMapping = { 0: 'renters', 1: 'landlords', 2: 'hospitals', 3: 'recruiters', 4: 'admin' };
    const userRole = roleMapping[user.role] || 'renters';

    return res.status(200).json({
      error: false,
      model: {
        user: {
          id: user.id,
          email: user.email,
          name: user.name,
          role: userRole,
          stripe_customer_id: user.stripe_customer_id || null
        },
        billing: {
          customer: stripeCustomer || null,
          subscription: subscription || null,
          payment_methods: paymentMethods || [],
          current_plan: subscription ? {
            name: subscription.items?.data[0]?.price?.nickname || 'Pro Plan',
            amount: subscription.items?.data[0]?.price?.unit_amount / 100,
            currency: subscription.items?.data[0]?.price?.currency,
            interval: subscription.items?.data[0]?.price?.recurring?.interval,
            status: subscription.status,
            trial_end: subscription.trial_end ? new Date(subscription.trial_end * 1000) : null,
            current_period_end: subscription.current_period_end ? new Date(subscription.current_period_end * 1000) : null
          } : {
            name: 'Free Plan',
            amount: 0,
            currency: 'cad',
            interval: 'month',
            status: 'active',
            features: [
              '2 property listings',
              '30-day listing duration',
              'Basic search',
              'Email support'
            ]
          }
        }
      }
    });
  } catch (err) {
    console.error("Get billing info error:", err);
    return res.status(403).json({
      error: true,
      message: err.message,
    });
  }
};

const handleCreateCardToken = async (req, res, sdk) => {
  try {
    const { card_number, exp_month, exp_year, cvc } = req.body;
    
    if (!card_number || !exp_month || !exp_year || !cvc) {
      return res.status(400).json({
        error: true,
        message: "Card details are required (card_number, exp_month, exp_year, cvc)",
      });
    }

    // Create card token using Stripe
    const tokenResult = await stripeService.createStripeCardToken({
      card_number,
      exp_month,
      exp_year,
      cvc
    });

    if (tokenResult.error) {
      return res.status(400).json({
        error: true,
        message: tokenResult.message || "Failed to create card token",
      });
    }

    return res.status(200).json({
      error: false,
      token: tokenResult
    });
  } catch (err) {
    console.error("Create card token error:", err);
    return res.status(500).json({
      error: true,
      message: err.message,
    });
  }
};

const handleCreateStripeCustomer = async (req, res, sdk) => {
  try {
    sdk.setProjectId("scrubhub");
    
    const user = await sdk.findOne("user", { id: req.user_id });
    if (!user) {
      return res.status(401).json({
        error: true,
        message: "Invalid Credentials",
      });
    }
    
    // Check if customer already exists
    if (user.stripe_customer_id) {
      return res.status(400).json({
        error: true,
        message: "Stripe customer already exists",
      });
    }

    // Create Stripe customer
    const customerResult = await stripeService.createStripeCustomer({
      email: user.email,
      name: user.name,
      metadata: {
        user_id: req.user_id,
        platform: 'scrubhub',
        role: user.role,
        name: user.name || user.email
      }
    });

    if (customerResult.error) {
      return res.status(400).json({
        error: true,
        message: customerResult.message || "Failed to create Stripe customer",
      });
    }

    // Update user with Stripe customer ID
    await sdk.updateById("user", req.user_id, {
      stripe_customer_id: customerResult.id
    });

    return res.status(200).json({
      error: false,
      message: "Stripe customer created successfully",
      customer: customerResult
    });
  } catch (err) {
    console.error("Create Stripe customer error:", err);
    return res.status(403).json({
      error: true,
      message: err.message,
    });
  }
};

const handleCreateSubscription = async (req, res, sdk) => {
  try {
    const { price_id, payment_method_id, payment_type = 'subscription' } = req.body;

    if (!price_id) {
      return res.status(400).json({
        error: true,
        message: "Price ID is required",
      });
    }

    sdk.setProjectId("scrubhub");

    const user = await sdk.findOne("user", { id: req.user_id });
    if (!user) {
      return res.status(401).json({
        error: true,
        message: "Invalid Credentials",
      });
    }

    let customerId = user.stripe_customer_id;
    
    // Create Stripe customer if doesn't exist
    if (!customerId) {
      const customerResult = await stripeService.createStripeCustomer({
        email: user.email,
        name: user.name,
        metadata: {
          user_id: req.user_id,
          platform: 'scrubhub',
          role: user.role
        }
      });

      if (customerResult.error) {
        return res.status(400).json({
          error: true,
          message: customerResult.message || "Failed to create customer",
        });
      }

      customerId = customerResult.id;

      // Update user with Stripe customer ID
      await sdk.updateById("user", req.user_id, {
        stripe_customer_id: customerId
      });
    }

    let subscriptionRecord;

    if (payment_type === 'onetime') {
      // Handle one-time payment
      const amount = typeof price_id === 'number' ? Math.round(price_id * 100) : null; // Convert to cents

      if (!amount || amount <= 0) {
        return res.status(400).json({
          error: true,
          message: "Invalid price amount for one-time payment",
        });
      }

      if (!payment_method_id) {
        return res.status(400).json({
          error: true,
          message: "Payment method is required for one-time payments",
        });
      }

      // Create direct charge
      const chargeData = {
        amount: amount,
        currency: 'cad',
        customer: customerId,
        source: payment_method_id,
        description: `ScrubHub one-time payment - User ${req.user_id}`,
        metadata: {
          user_id: req.user_id,
          platform: 'scrubhub',
          payment_type: 'onetime'
        }
      };

      const chargeResult = await stripeService.createStripeCharge(chargeData);

      if (chargeResult.error) {
        return res.status(400).json({
          error: true,
          message: chargeResult.message || "Failed to process payment",
        });
      }

      // Save one-time payment record as subscription
      subscriptionRecord = await sdk.create("subscription", {
        user_id: req.user_id,
        stripe_customer_id: customerId,
        payment_type: 'onetime',
        status: chargeResult.status === 'succeeded' ? 'active' : 'incomplete',
        created_at: new Date()
      });

      // Also create a payment record for tracking
      await sdk.create("payment", {
        user_id: req.user_id,
        amount: amount / 100, // Convert back to dollars
        currency: 'CAD',
        purpose: 'onetime_payment',
        stripe_transaction_id: chargeResult.id,
        status: chargeResult.status === 'succeeded' ? 1 : 0, // 1=completed, 0=pending
        metadata: JSON.stringify({
          charge_id: chargeResult.id,
          payment_type: 'onetime'
        }),
        created_at: new Date()
      });

    } else {
      // Handle recurring subscription
      const subscriptionData = {
        customer: customerId,
        items: [{ price: price_id }],
        metadata: {
          user_id: req.user_id,
          platform: 'scrubhub'
        }
      };

      // Add payment method if provided
      if (payment_method_id) {
        subscriptionData.default_payment_method = payment_method_id;
      }

      const subscriptionResult = await stripeService.createStripeSubscription(subscriptionData);

      if (subscriptionResult.error) {
        return res.status(400).json({
          error: true,
          message: subscriptionResult.message || "Failed to create subscription",
        });
      }

      // Save subscription to database
      subscriptionRecord = await sdk.create("subscription", {
        user_id: req.user_id,
        stripe_subscription_id: subscriptionResult.id,
        stripe_customer_id: customerId,
        payment_type: 'subscription',
        status: subscriptionResult.status,
        current_period_start: new Date(subscriptionResult.current_period_start * 1000),
        current_period_end: new Date(subscriptionResult.current_period_end * 1000),
        trial_start: subscriptionResult.trial_start ? new Date(subscriptionResult.trial_start * 1000) : null,
        trial_end: subscriptionResult.trial_end ? new Date(subscriptionResult.trial_end * 1000) : null,
        created_at: new Date()
      });
    }

    return res.status(200).json({
      error: false,
      message: "Subscription created successfully",
      subscription: subscriptionResult,
      database_record: subscriptionRecord
    });
  } catch (err) {
    console.error("Create subscription error:", err);
    return res.status(403).json({
      error: true,
      message: err.message,
    });
  }
};

const handleCancelSubscription = async (req, res, sdk) => {
  try {
    sdk.setProjectId("scrubhub");

    const user = await sdk.findOne("user", { id: req.user_id });
    if (!user) {
      return res.status(401).json({
        error: true,
        message: "Invalid Credentials",
      });
    }

    // Get user's active subscription
    const subscription = await sdk.findOne("subscription", {
      user_id: req.user_id,
      status: ['active', 'trialing']
    });

    if (!subscription || !subscription.stripe_subscription_id) {
      return res.status(404).json({
        error: true,
        message: "No active subscription found",
      });
    }

    // Cancel Stripe subscription
    const cancelResult = await stripeService.cancelStripeSubscription(subscription.stripe_subscription_id);

    if (cancelResult.error) {
      return res.status(400).json({
        error: true,
        message: cancelResult.message || "Failed to cancel subscription",
      });
    }

    // Update subscription status in database
    await sdk.updateById("subscription", subscription.id, {
      status: 'canceled',
      canceled_at: new Date(),
      updated_at: new Date()
    });

    return res.status(200).json({
      error: false,
      message: "Subscription canceled successfully",
      subscription: cancelResult
    });
  } catch (err) {
    console.error("Cancel subscription error:", err);
    return res.status(403).json({
      error: true,
      message: err.message,
    });
  }
};

const handleAddPaymentMethod = async (req, res, sdk) => {
  try {
    const { card_token } = req.body;

    if (!card_token) {
      return res.status(400).json({
        error: true,
        message: "Card token is required",
      });
    }

    sdk.setProjectId("scrubhub");

    const user = await sdk.findOne("user", { id: req.user_id });
    if (!user) {
      return res.status(401).json({
        error: true,
        message: "Invalid Credentials",
      });
    }

    let customerId = user.stripe_customer_id;

    // Create Stripe customer if doesn't exist
    if (!customerId) {
      const customerResult = await stripeService.createStripeCustomer({
        email: user.email,
        name: user.name,
        metadata: {
          user_id: req.user_id,
          platform: 'scrubhub',
          role: user.role
        }
      });

      if (customerResult.error) {
        return res.status(400).json({
          error: true,
          message: customerResult.message || "Failed to create customer",
        });
      }

      customerId = customerResult.id;

      // Update user with Stripe customer ID
      await sdk.updateById("user", req.user_id, {
        stripe_customer_id: customerId
      });
    }

    // Add the payment method to the customer
    const cardResult = await stripeService.addNewCardToStripeCustomer({
      tokenId: card_token,
      customerId: customerId,
      metadata: {
        platform: 'scrubhub',
        added_via: 'payment_method_form'
      }
    });

    if (cardResult.error) {
      return res.status(400).json({
        error: true,
        message: cardResult.message || "Failed to add payment method",
      });
    }

    return res.status(200).json({
      error: false,
      message: "Payment method added successfully",
      payment_method: cardResult
    });
  } catch (err) {
    console.error("Add payment method error:", err);
    return res.status(403).json({
      error: true,
      message: err.message,
    });
  }
};

const handleGetAvailablePlans = async (req, res, sdk) => {
  try {
    sdk.setProjectId("scrubhub");

    // Get user to determine target audience
    const user = await sdk.findOne("user", { id: req.user_id });
    const roleMapping = { 0: 'renters', 1: 'landlords', 2: 'hospitals', 3: 'recruiters', 4: 'admin' };
    const userRole = roleMapping[user?.role] || 'renters';

    let plans = [];

    try {
      // First try to get plans from Stripe API
      const plansResult = await stripeService.retrieveStripePrices({
        active: true,
        expand: ['data.product']
      });

      if (!plansResult.error && plansResult.data) {
        // Filter and format plans for ScrubHub
        plans = plansResult.data
          .filter(price => price.product?.metadata?.platform === 'scrubhub')
          .map(price => ({
            id: price.id,
            product_id: price.product.id,
            name: price.nickname || price.product.name,
            description: price.product.description,
            amount: price.unit_amount / 100,
            currency: price.currency,
            interval: price.recurring?.interval,
            interval_count: price.recurring?.interval_count,
            trial_period_days: price.recurring?.trial_period_days || 0,
            target_audience: price.product.metadata?.target_audience,
            features: price.product.metadata?.features ? JSON.parse(price.product.metadata.features) : []
          }))
          .filter(plan => !plan.target_audience || plan.target_audience === userRole);
      }
    } catch (stripeError) {
      console.error("Stripe API error, falling back to database:", stripeError.message);
    }

    // If no plans from Stripe, try to get from database
    if (plans.length === 0) {
      try {
        const dbPricesQuery = `
          SELECT
            sp.stripe_price_id as id,
            sp.stripe_product_id as product_id,
            spl.name,
            spl.description,
            sp.unit_amount,
            sp.currency,
            sp.recurring_interval,
            sp.recurring_interval_count as interval_count,
            sp.trial_period_days,
            spl.target_audience,
            spl.features
          FROM scrubhub_stripe_price sp
          JOIN scrubhub_subscription_plan spl ON sp.stripe_price_id = spl.stripe_price_id
          WHERE sp.active = 1 AND spl.active = 1
          AND (spl.target_audience = ? OR spl.target_audience IS NULL)
          ORDER BY sp.unit_amount ASC
        `;

        const dbPlans = await sdk.rawQuery(dbPricesQuery, [userRole]);

        plans = dbPlans.map(plan => ({
          id: plan.id,
          product_id: plan.product_id,
          name: plan.name,
          description: plan.description,
          amount: plan.unit_amount / 100,
          currency: plan.currency,
          interval: plan.recurring_interval,
          interval_count: plan.interval_count,
          trial_period_days: plan.trial_period_days || 0,
          target_audience: plan.target_audience,
          features: typeof plan.features === 'string' ? JSON.parse(plan.features) : plan.features || []
        }));

        console.log('Loaded plans from database:', plans.length);
      } catch (dbError) {
        console.error("Database query error:", dbError.message);
      }
    }

    // If still no plans, provide default fallback based on user role
    if (plans.length === 0) {
      if (userRole === 'landlords') {
        plans = [
          {
            id: 'landlord-plan-fallback',
            name: 'Landlord Plan',
            description: 'For landlords managing medical housing properties',
            amount: 49,
            currency: 'cad',
            interval: 'month',
            trial_period_days: 7,
            target_audience: 'landlords',
            features: [
              'Post and manage unlimited listings',
              'Tenant screening tools',
              'Lease management system',
              'Dedicated landlord support',
              'Analytics and reporting',
              'Priority listing placement'
            ]
          }
        ];
      } else {
        plans = [
          {
            id: 'pro-plan-fallback',
            name: 'Pro Plan',
            description: 'For serious renters who want an edge',
            amount: 19,
            currency: 'cad',
            interval: 'month',
            trial_period_days: 7,
            target_audience: 'renters',
            features: [
              'All Basic features',
              'Early access to new listings',
              'Enhanced profile visibility',
              'Priority support',
              'Advanced search filters'
            ]
          }
        ];
      }
    }

    // Define free plan based on user role
    const freePlanFeatures = userRole === 'landlords'
      ? [
          '2 property listings',
          '30-day listing duration',
          'Basic tenant inquiries',
          'Email support'
        ]
      : [
          'Basic property search',
          'Save up to 5 favorites',
          'Contact landlords',
          'Email support'
        ];

    return res.status(200).json({
      error: false,
      model: {
        plans: plans,
        user_role: userRole,
        free_plan: {
          id: 'free',
          name: 'Free Plan',
          description: 'Basic features for getting started',
          amount: 0,
          currency: 'cad',
          interval: 'month',
          features: freePlanFeatures
        }
      }
    });
  } catch (err) {
    console.error("Get available plans error:", err);
    return res.status(500).json({
      error: true,
      message: err.message,
    });
  }
};

const handleGetInvoices = async (req, res, sdk) => {
  try {
    sdk.setProjectId("scrubhub");

    const { page = 1, limit = 10, type } = req.query;
    const offset = (parseInt(page) - 1) * parseInt(limit);

    // Get user data
    const user = await sdk.findOne("user", { id: req.user_id });
    if (!user) {
      return res.status(401).json({
        error: true,
        message: "Invalid Credentials",
      });
    }

    let invoices = [];
    let totalCount = 0;

    // Get Stripe invoices if user has Stripe customer ID
    if (user.stripe_customer_id) {
      try {
        // Get Stripe invoices
        const stripeInvoicesResult = await stripeService.listStripeInvoices({
          customer: user.stripe_customer_id,
          limit: parseInt(limit),
          starting_after: offset > 0 ? `offset_${offset}` : undefined
        });

        if (stripeInvoicesResult && stripeInvoicesResult.data) {
          const stripeInvoices = stripeInvoicesResult.data.map(invoice => ({
            id: invoice.id,
            type: 'stripe_invoice',
            amount: invoice.amount_paid / 100, // Convert from cents
            currency: invoice.currency.toUpperCase(),
            status: invoice.status,
            description: invoice.description || 'Subscription payment',
            invoice_pdf: invoice.invoice_pdf,
            hosted_invoice_url: invoice.hosted_invoice_url,
            created_at: new Date(invoice.created * 1000),
            period_start: invoice.period_start ? new Date(invoice.period_start * 1000) : null,
            period_end: invoice.period_end ? new Date(invoice.period_end * 1000) : null,
            subscription_id: invoice.subscription,
            metadata: invoice.metadata || {}
          }));

          invoices = [...invoices, ...stripeInvoices];
        }
      } catch (stripeError) {
        console.error('Error fetching Stripe invoices:', stripeError);
      }
    }

    // Get local payment records (charges, one-time payments, etc.)
    let whereConditions = ['user_id = ?'];
    let whereParams = [req.user_id];

    if (type && type !== 'all') {
      whereConditions.push('purpose = ?');
      whereParams.push(type);
    }

    const whereClause = whereConditions.join(' AND ');

    // Get total count of local payments
    const countQuery = `SELECT COUNT(*) as total FROM scrubhub_payment WHERE ${whereClause}`;
    const countResult = await sdk.rawQuery(countQuery, whereParams);
    const localPaymentCount = countResult[0]?.total || 0;

    // Get local payment records
    const paymentsQuery = `
      SELECT
        p.*,
        'local_payment' as type
      FROM scrubhub_payment p
      WHERE ${whereClause}
      ORDER BY p.created_at DESC
      LIMIT ? OFFSET ?
    `;

    const localPayments = await sdk.rawQuery(paymentsQuery, [...whereParams, parseInt(limit), offset]);

    // Format local payments as invoices
    const localInvoices = localPayments.map(payment => ({
      id: `local_${payment.id}`,
      type: 'local_payment',
      amount: parseFloat(payment.amount),
      currency: payment.currency || 'CAD',
      status: payment.status === 1 ? 'paid' : payment.status === 0 ? 'pending' : 'failed',
      description: getPaymentDescription(payment.purpose, payment.metadata),
      invoice_pdf: null,
      hosted_invoice_url: null,
      created_at: payment.created_at,
      period_start: null,
      period_end: null,
      subscription_id: null,
      metadata: payment.metadata ? JSON.parse(payment.metadata) : {},
      purpose: payment.purpose,
      stripe_transaction_id: payment.stripe_transaction_id
    }));

    invoices = [...invoices, ...localInvoices];

    // Sort all invoices by created_at descending
    invoices.sort((a, b) => new Date(b.created_at) - new Date(a.created_at));

    // Apply pagination to combined results
    const paginatedInvoices = invoices.slice(0, parseInt(limit));
    totalCount = invoices.length + (localPaymentCount > parseInt(limit) ? localPaymentCount - parseInt(limit) : 0);

    return res.status(200).json({
      error: false,
      data: {
        invoices: paginatedInvoices,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: totalCount,
          pages: Math.ceil(totalCount / parseInt(limit))
        }
      },
      message: "Invoices retrieved successfully"
    });

  } catch (error) {
    console.error("Get invoices error:", error);
    return res.status(500).json({
      error: true,
      message: error.message,
    });
  }
};

// Helper function to get payment description based on purpose
const getPaymentDescription = (purpose, metadata) => {
  const metaObj = metadata ? (typeof metadata === 'string' ? JSON.parse(metadata) : metadata) : {};

  switch (purpose) {
    case 'job_posting':
      return `Job Posting Fee - ${metaObj.job_type || 'Position'}`;
    case 'property_listing':
      return `Property Listing Fee - ${metaObj.tier || 'Standard'} tier`;
    case 'marketplace_listing':
      return 'Marketplace Item Listing Fee';
    case 'phone_reveal':
      return 'Phone Number Reveal Fee';
    case 'credit_check':
      return 'Credit Check Report';
    case 'n9_form':
      return 'N9 Form Generation';
    case 'sublet_service':
      return `Sublet Service Fee - ${metaObj.service_tier || 'Standard'}`;
    case 'onetime_payment':
      return 'One-time Payment';
    default:
      return purpose ? purpose.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()) : 'Payment';
  }
};

const handleRemovePaymentMethod = async (req, res, sdk) => {
  try {
    const { payment_method_id } = req.params;

    if (!payment_method_id) {
      return res.status(400).json({
        error: true,
        message: "Payment method ID is required",
      });
    }

    sdk.setProjectId("scrubhub");

    const user = await sdk.findOne("user", { id: req.user_id });
    if (!user) {
      return res.status(401).json({
        error: true,
        message: "Invalid Credentials",
      });
    }

    if (!user.stripe_customer_id) {
      return res.status(400).json({
        error: true,
        message: "No Stripe customer found",
      });
    }

    try {
      // First, try to detach payment method (newer API)
      const detachResult = await stripeService.detachStripePaymentMethod(payment_method_id);

      if (!detachResult.error) {
        return res.status(200).json({
          error: false,
          message: "Payment method removed successfully",
          payment_method: detachResult
        });
      }

      // If detach fails, try to delete as a card source (older API)
      const deleteResult = await stripeService.deleteStripeCustomerCard({
        customerId: user.stripe_customer_id,
        cardId: payment_method_id
      });

      if (deleteResult.error) {
        return res.status(400).json({
          error: true,
          message: deleteResult.message || "Failed to remove payment method",
        });
      }

      return res.status(200).json({
        error: false,
        message: "Payment method removed successfully",
        payment_method: deleteResult
      });

    } catch (stripeError) {
      console.error("Stripe error removing payment method:", stripeError);
      return res.status(400).json({
        error: true,
        message: "Failed to remove payment method from Stripe",
      });
    }

  } catch (err) {
    console.error("Remove payment method error:", err);
    return res.status(500).json({
      error: true,
      message: err.message,
    });
  }
};

const handleDownloadInvoice = async (req, res, sdk) => {
  try {
    sdk.setProjectId("scrubhub");

    const { invoice_id } = req.params;
    const { type } = req.query; // 'stripe' or 'local'

    // Get user data
    const user = await sdk.findOne("user", { id: req.user_id });
    if (!user) {
      return res.status(401).json({
        error: true,
        message: "Invalid Credentials",
      });
    }

    if (type === 'stripe' && user.stripe_customer_id) {
      try {
        // Get Stripe invoice
        const invoice = await stripeService.retrieveStripeInvoice(invoice_id);

        if (invoice.error || invoice.customer !== user.stripe_customer_id) {
          return res.status(404).json({
            error: true,
            message: "Invoice not found or access denied",
          });
        }

        if (invoice.invoice_pdf) {
          // Redirect to Stripe's PDF URL
          return res.redirect(invoice.invoice_pdf);
        } else if (invoice.hosted_invoice_url) {
          // Redirect to hosted invoice page
          return res.redirect(invoice.hosted_invoice_url);
        } else {
          return res.status(404).json({
            error: true,
            message: "Invoice PDF not available",
          });
        }
      } catch (stripeError) {
        console.error('Error fetching Stripe invoice:', stripeError);
        return res.status(500).json({
          error: true,
          message: "Failed to retrieve invoice from Stripe",
        });
      }
    } else if (type === 'local') {
      // Handle local payment invoice generation
      const paymentId = invoice_id.replace('local_', '');

      const payment = await sdk.findOne("payment", {
        id: paymentId,
        user_id: req.user_id
      });

      if (!payment) {
        return res.status(404).json({
          error: true,
          message: "Payment record not found",
        });
      }

      // Generate a simple invoice response (you can enhance this with PDF generation)
      const invoiceData = {
        invoice_id: `INV-${payment.id}`,
        user_name: user.name,
        user_email: user.email,
        amount: payment.amount,
        currency: payment.currency || 'CAD',
        description: getPaymentDescription(payment.purpose, payment.metadata),
        payment_date: payment.created_at,
        status: payment.status === 1 ? 'Paid' : payment.status === 0 ? 'Pending' : 'Failed',
        transaction_id: payment.stripe_transaction_id
      };

      return res.status(200).json({
        error: false,
        invoice: invoiceData,
        message: "Invoice data retrieved successfully"
      });
    } else {
      return res.status(400).json({
        error: true,
        message: "Invalid invoice type or missing Stripe customer ID",
      });
    }

  } catch (error) {
    console.error("Download invoice error:", error);
    return res.status(500).json({
      error: true,
      message: error.message,
    });
  }
};

module.exports = function (app) {
  // Get billing information
  app.get("/v1/api/scrubhub/customer/lambda/billing-info", middlewares, async (req, res) => {
    await handleGetBillingInfo(req, res, app.get("sdk"));
  });

  // Create card token
  app.post("/v1/api/scrubhub/customer/lambda/create-card-token", middlewares, async (req, res) => {
    await handleCreateCardToken(req, res, app.get("sdk"));
  });

  // Create Stripe customer
  app.post("/v1/api/scrubhub/customer/lambda/create-stripe-customer", middlewares, async (req, res) => {
    await handleCreateStripeCustomer(req, res, app.get("sdk"));
  });

  // Create subscription
  app.post("/v1/api/scrubhub/customer/lambda/create-subscription", middlewares, async (req, res) => {
    await handleCreateSubscription(req, res, app.get("sdk"));
  });

  // Cancel subscription
  app.post("/v1/api/scrubhub/customer/lambda/cancel-subscription", middlewares, async (req, res) => {
    await handleCancelSubscription(req, res, app.get("sdk"));
  });

  // Get available plans
  app.get("/v1/api/scrubhub/customer/lambda/available-plans", middlewares, async (req, res) => {
    await handleGetAvailablePlans(req, res, app.get("sdk"));
  });

  // Add payment method
  app.post("/v1/api/scrubhub/customer/lambda/add-payment-method", middlewares, async (req, res) => {
    await handleAddPaymentMethod(req, res, app.get("sdk"));
  });

  // Remove payment method
  app.delete("/v1/api/scrubhub/customer/lambda/remove-payment-method/:payment_method_id", middlewares, async (req, res) => {
    await handleRemovePaymentMethod(req, res, app.get("sdk"));
  });

  // Get user invoices
  app.get("/v1/api/scrubhub/customer/lambda/get-invoices", middlewares, async (req, res) => {
    await handleGetInvoices(req, res, app.get("sdk"));
  });

  // Download invoice
  app.get("/v1/api/scrubhub/customer/lambda/download-invoice/:invoice_id", middlewares, async (req, res) => {
    await handleDownloadInvoice(req, res, app.get("sdk"));
  });
};
