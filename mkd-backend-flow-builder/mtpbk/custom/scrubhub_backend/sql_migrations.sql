-- Add missing columns to jobs table for enhanced job posting features
ALTER TABLE scrubhub_job ADD COLUMN
IF NOT EXISTS location_type ENUM
('in_person', 'remote', 'hybrid') DEFAULT 'in_person' COMMENT 'Job location type';
ALTER TABLE scrubhub_job ADD COLUMN
IF NOT EXISTS job_type ENUM
('full_time', 'part_time', 'permanent', 'fixed_term_contract', 'casual', 'seasonal', 'freelance', 'apprenticeship', 'internship', 'co_op') DEFAULT 'full_time' COMMENT 'Type of employment';
ALTER TABLE scrubhub_job ADD COLUMN
IF NOT EXISTS has_start_date TINYINT
(1) DEFAULT 0 COMMENT 'Whether job has a planned start date';
ALTER TABLE scrubhub_job ADD COLUMN
IF NOT EXISTS start_date DATE NULL COMMENT 'Planned start date for the job';
ALTER TABLE scrubhub_job ADD COLUMN
IF NOT EXISTS payment_type ENUM
('hourly', 'daily', 'weekly', 'monthly', 'yearly', 'per_shift', 'commission', 'contract') DEFAULT 'yearly' COMMENT 'Payment frequency type';
ALTER TABLE scrubhub_job ADD COLUMN
IF NOT EXISTS show_pay_by ENUM
('range', 'starting_amount', 'maximum_amount', 'exact_amount') DEFAULT 'range' COMMENT 'How to display salary information';
ALTER TABLE scrubhub_job ADD COLUMN
IF NOT EXISTS daily_updates_email VARCHAR
(255) NULL COMMENT 'Email for daily job updates';
ALTER TABLE scrubhub_job ADD COLUMN
IF NOT EXISTS resume_required TINYINT
(1) DEFAULT 1 COMMENT 'Whether resume is required for application';
ALTER TABLE scrubhub_job ADD COLUMN
IF NOT EXISTS allow_email_contact TINYINT
(1) DEFAULT 0 COMMENT 'Allow candidates to contact by email';
ALTER TABLE scrubhub_job ADD COLUMN
IF NOT EXISTS has_application_deadline TINYINT
(1) DEFAULT 0 COMMENT 'Whether job has application deadline';
ALTER TABLE scrubhub_job ADD COLUMN
IF NOT EXISTS application_deadline DATE NULL COMMENT 'Application deadline date';
ALTER TABLE scrubhub_job ADD COLUMN
IF NOT EXISTS posting_duration_days INT DEFAULT 30 COMMENT 'Number of days job posting is active';

-- Add indexes for new columns
ALTER TABLE scrubhub_job ADD INDEX idx_location_type (location_type);
ALTER TABLE scrubhub_job ADD INDEX idx_job_type (job_type);
ALTER TABLE scrubhub_job ADD INDEX idx_payment_type (payment_type);
ALTER TABLE scrubhub_job ADD INDEX idx_start_date (start_date);
ALTER TABLE scrubhub_job ADD INDEX idx_application_deadline (application_deadline);
ALTER TABLE scrubhub_job ADD INDEX idx_posting_duration (posting_duration_days);

-- Add missing columns to user table for profile management
ALTER TABLE scrubhub_user ADD COLUMN
IF NOT EXISTS first_name VARCHAR
(255) NULL COMMENT 'User first name';
ALTER TABLE scrubhub_user ADD COLUMN
IF NOT EXISTS last_name VARCHAR
(255) NULL COMMENT 'User last name';
ALTER TABLE scrubhub_user ADD COLUMN
IF NOT EXISTS phone VARCHAR
(20) NULL COMMENT 'User phone number';
ALTER TABLE scrubhub_user ADD COLUMN
IF NOT EXISTS profile_image VARCHAR
(512) NULL COMMENT 'User profile image URL';
ALTER TABLE scrubhub_user ADD COLUMN
IF NOT EXISTS name VARCHAR
(512) NULL COMMENT 'User full name';
ALTER TABLE scrubhub_user ADD COLUMN
IF NOT EXISTS role INT DEFAULT 0 COMMENT 'User role: 0=tenant, 1=landlord, 2=hospital, 3=recruiter, 4=admin';
ALTER TABLE scrubhub_user ADD COLUMN
IF NOT EXISTS verified TINYINT
(1) DEFAULT 0 COMMENT 'Whether user is verified';
ALTER TABLE scrubhub_user ADD COLUMN
IF NOT EXISTS stripe_customer_id VARCHAR
(255) NULL COMMENT 'Stripe customer ID';

-- Add indexes for user table
ALTER TABLE scrubhub_user ADD INDEX idx_first_name (first_name);
ALTER TABLE scrubhub_user ADD INDEX idx_last_name (last_name);
ALTER TABLE scrubhub_user ADD INDEX idx_role (role);
ALTER TABLE scrubhub_user ADD INDEX idx_verified (verified);