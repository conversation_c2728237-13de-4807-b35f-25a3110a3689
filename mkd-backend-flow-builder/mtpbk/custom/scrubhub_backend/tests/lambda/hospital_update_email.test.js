const APITestFramework = require("../../../../tests/apitesting.base.js");

const BASE_URL = "http://localhost:5172";

/**
 * Update Email API Tests
 * Class-based implementation of the Update Email API tests
 */
class UpdateEmailTests {
  constructor() {
    this.framework = new APITestFramework();
    this.baseUrl = BASE_URL;
    this.setupTests();
  }

  setupTests() {
    this.framework.describe("hospital Update Email API Tests", () => {
      // Test case for update email
      this.framework.addTestCase("hospital Update Email API - Success", async () => {
        const authToken = "Bearer YOUR_AUTH_TOKEN";
        const response = await this.framework.makeRequest(
          `${this.baseUrl}/v1/api/scrubhub/hospital/lambda/update/email`,
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              Authorization: authToken,
            },
            body: JSON.stringify({
              email: "<EMAIL>",
            }),
          }
        );

        // Assertions for the health check
        this.framework.assert(
          response.status === 200,
          "Update Email hospital should return 200 status"
        );

        this.framework.assert(
          response.body.error === false,
          "Update Email hospital error flag should be false"
        );
      });

      // Add more test cases here as needed
      this.framework.addTestCase(
        "hospital Update Email API - Invalid Email",
        async () => {
          const authToken = "Bearer YOUR_AUTH_TOKEN";
          const response = await this.framework.makeRequest(
            `${this.baseUrl}/v1/api/scrubhub/hospital/lambda/update/email`,
            {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
                Authorization: authToken,
              },
              body: JSON.stringify({
                email: "invalid-email",
              }),
            }
          );

          // Assertions for invalid email
          this.framework.assert(
            response.status === 400,
            "Update Email with invalid email should return 400 status"
          );

          this.framework.assert(
            response.body.error === true,
            "Update Email with invalid email should have error flag set to true"
          );
        }
      );
    });
  }

  // Helper method to run all tests
  async runTests() {
    try {
      // Run the tests and return the results directly
      return await this.framework.runTests();
    } catch (error) {
      console.error("Test execution failed:", error);
      throw error;
    }
  }
}

// Create an instance of the test class and run the tests
const tests = new UpdateEmailTests();
module.exports = tests
  .runTests()
  .then((report) => {
    if (report.failed > 0) {
      process.exit(1);
    }
    return report; // Important: return the report for the test runner
  })
  .catch((error) => {
    console.error("Test framework error:", error);
    process.exit(1);
  });
