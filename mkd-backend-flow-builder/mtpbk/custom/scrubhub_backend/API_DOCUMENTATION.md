# ScrubHub Backend API Documentation

## Overview
This document outlines all the API endpoints implemented for the ScrubHub platform, including property management, job postings, marketplace, sublets, and admin functionality.

## Base URL
All API endpoints are prefixed with: `/v1/api/scrubhub`

## Authentication
Most endpoints require JWT authentication. Include the token in the Authorization header:
```
Authorization: Bearer <your_jwt_token>
```

## User Roles
- `0` - Tenant
- `1` - Landlord  
- `2` - Hospital
- `3` - Recruiter
- `4` - Admin

---

## Authentication Endpoints

### User Registration
- **POST** `/auth/register`
- **Body**: `{ email, password, name, phone?, address?, city?, province_state?, role }`
- **Response**: `{ error: false, model: user, token, message }`

### User Login
- **POST** `/auth/login`
- **Body**: `{ email, password }`
- **Response**: `{ error: false, model: user, token, message }`

### Hospital Registration
- **POST** `/auth/hospital/register`
- **Body**: `{ email, password, name, phone?, address?, city?, province_state? }`
- **Response**: `{ error: false, model: user, token, message }`

### Hospital Login
- **POST** `/auth/hospital/login`
- **Body**: `{ email, password }`
- **Response**: `{ error: false, model: user, token, message }`

### Recruiter Registration
- **POST** `/auth/recruiter/register`
- **Body**: `{ email, password, name, phone?, address?, city?, province_state? }`
- **Response**: `{ error: false, model: user, token, message }`

### Recruiter Login
- **POST** `/auth/recruiter/login`
- **Body**: `{ email, password }`
- **Response**: `{ error: false, model: user, token, message }`

---

## Property Management

### Get All Properties
- **GET** `/properties`
- **Query Params**: `page, limit, city, min_price, max_price, bedrooms, property_type, furnished, available_from, sort_by, sort_order`
- **Response**: `{ error: false, list: properties[], pagination }`

### Get Property Details
- **GET** `/properties/:id`
- **Response**: `{ error: false, model: property }`

### Create Property Listing (Landlords Only)
- **POST** `/properties`
- **Auth**: Landlord role required
- **Body**: `{ title, description, address, city, province_state, price, tier?, duration_days?, amenities?, property_type?, bedrooms?, bathrooms?, furnished?, available_from?, photos? }`
- **Response**: `{ error: false, model: property, listing_fee, message }`

### Update Property (Landlords Only)
- **PUT** `/properties/:id`
- **Auth**: Landlord role required
- **Body**: Property fields to update
- **Response**: `{ error: false, message }`

### Delete Property (Landlords Only)
- **DELETE** `/properties/:id`
- **Auth**: Landlord role required
- **Response**: `{ error: false, message }`

### Get Landlord's Properties
- **GET** `/landlord/properties`
- **Auth**: Landlord role required
- **Query Params**: `page, limit, status`
- **Response**: `{ error: false, list: properties[], pagination }`

---

## Property Inquiries

### Submit Property Inquiry
- **POST** `/properties/:id/inquire`
- **Body**: `{ name, email, phone?, message? }`
- **Response**: `{ error: false, model: inquiry, message }`

### Get Property Inquiries (Landlords Only)
- **GET** `/properties/:id/inquiries`
- **Auth**: Landlord role required
- **Query Params**: `status, page, limit`
- **Response**: `{ error: false, list: inquiries[], pagination }`

### Update Inquiry Status (Landlords Only)
- **PUT** `/inquiries/:id/status`
- **Auth**: Landlord role required
- **Body**: `{ status }`
- **Response**: `{ error: false, message }`

---

## Favorites & Recently Viewed

### Toggle Favorite Property
- **POST** `/favorites/toggle`
- **Auth**: Required
- **Body**: `{ property_id }`
- **Response**: `{ error: false, favorited: boolean, message }`

### Get User's Favorites
- **GET** `/favorites`
- **Auth**: Required
- **Query Params**: `page, limit`
- **Response**: `{ error: false, list: properties[], pagination }`

### Get Recently Viewed Properties
- **GET** `/recently-viewed`
- **Auth**: Required
- **Response**: `{ error: false, list: properties[] }`

---

## Sublet Management

### Create Sublet Request
- **POST** `/sublets`
- **Auth**: Required
- **Body**: `{ property_id, price, start_date, end_date, service_tier? }`
- **Response**: `{ error: false, model: sublet, service_fee, message }`

### Get User's Sublets
- **GET** `/sublets`
- **Auth**: Required
- **Response**: `{ error: false, list: sublets[] }`

---

## Job Management

### Get All Job Postings
- **GET** `/jobs`
- **Query Params**: `page, limit, location, type, department, min_salary, max_salary, sort_by, sort_order`
- **Response**: `{ error: false, list: jobs[], pagination }`

### Get Job Details
- **GET** `/jobs/:id`
- **Response**: `{ error: false, model: job }`

### Create Job Posting (Hospitals/Recruiters Only)
- **POST** `/jobs`
- **Auth**: Hospital or Recruiter role required
- **Body**: `{ title, description, location, type, department?, salary_min?, salary_max?, requirements?, contact_email?, contact_phone? }`
- **Response**: `{ error: false, model: job, posting_fee, message }`

### Apply for Job
- **POST** `/jobs/:id/apply`
- **Body**: `{ applicant_name, email, phone?, cover_letter?, resume_url? }`
- **Response**: `{ error: false, model: application, message }`

### Get Job Applications (Poster Only)
- **GET** `/jobs/:id/applications`
- **Auth**: Hospital or Recruiter role required
- **Query Params**: `status, page, limit`
- **Response**: `{ error: false, list: applications[], pagination }`

---

## Marketplace

### Get Marketplace Items
- **GET** `/marketplace`
- **Query Params**: `page, limit, category, condition, min_price, max_price, search, sort_by, sort_order`
- **Response**: `{ error: false, list: items[], pagination }`

### Create Marketplace Item
- **POST** `/marketplace`
- **Auth**: Required
- **Body**: `{ title, description, price, condition?, category? }`
- **Response**: `{ error: false, model: item, listing_fee, message }`

### Reveal Phone Number
- **POST** `/marketplace/:id/reveal-phone`
- **Auth**: Required
- **Response**: `{ error: false, phone?, payment_intent?, fee?, message }`

---

## Credit Reports

### Request Credit Report
- **POST** `/credit-report`
- **Auth**: Required
- **Body**: `{ personal_info: { first_name, last_name, date_of_birth?, sin?, address? } }`
- **Response**: `{ error: false, model: credit_report, message }`

---

## N9 Notice Generation

### Generate N9 Form (with Payment)
- **POST** `/n9-form`
- **Auth**: Required
- **Body**:
  ```json
  {
    "tenant_name": "string (required)",
    "tenant_address": "string",
    "landlord_name": "string (required)",
    "landlord_address": "string",
    "rental_address": "string (required)",
    "termination_date": "YYYY-MM-DD (required)",
    "reason": "string",
    "signature_section": {
      "signed_by": "tenant|representative (required)",
      "first_name": "string (required)",
      "last_name": "string (required)",
      "phone_number": "string (required)",
      "signature": "string (required - typed full name)"
    }
  }
  ```
- **Response**: `{ error: false, notice_id, payment_intent, fee, signature_info, message }`

### Generate N9 Notice
- **POST** `/n9-notice`
- **Auth**: Required
- **Body**:
  ```json
  {
    "form_data": {
      "tenant_name": "string",
      "tenant_address": "string",
      "landlord_name": "string",
      "landlord_address": "string",
      "rental_address": "string",
      "termination_date": "YYYY-MM-DD",
      "reason": "string",
      "signature_section": {
        "signed_by": "tenant|representative",
        "first_name": "string (required)",
        "last_name": "string (required)",
        "phone_number": "string (required)",
        "signature": "string (required - typed full name)"
      }
    },
    "notice_type": "N9"
  }
  ```
- **Response**: `{ error: false, model: tenant_notice, pdf_url, signature_info, message }`

### Get User's Notices
- **GET** `/notices`
- **Auth**: Required
- **Response**: `{ error: false, list: notices[] }`

---

## Admin APIs

### Get Admin Dashboard Stats
- **GET** `/admin/dashboard`
- **Auth**: Admin role required
- **Response**: `{ error: false, stats: { total_users, total_properties, total_jobs, total_marketplace_items, pending_verifications, pending_sublets, total_revenue } }`

### Verify User
- **POST** `/admin/verify-user/:id`
- **Auth**: Admin role required
- **Response**: `{ error: false, message }`

### Approve Premium Sublet
- **POST** `/admin/approve-sublet/:id`
- **Auth**: Admin role required
- **Body**: `{ approved: boolean }`
- **Response**: `{ error: false, message }`

---

## Webhooks

### Stripe Webhook
- **POST** `/webhook/stripe`
- **Body**: Stripe webhook payload
- **Response**: `{ received: true }`

---

## Pricing Structure

### Property Listings
- **Free**: $0 (30 days, max 2 listings)
- **Paid**: $4.99 (30 days), $9.99 (60 days), $14.99 (90 days)
- **Featured**: Paid price + $19.99

### Job Postings
- **Full-time**: $99.99
- **Part-time**: $49.99
- **Contract**: $79.99
- **Internship**: $29.99

### Sublet Services
- **Self-managed**: $49.99
- **Premium**: $599.00

### Marketplace
- **≤ $100**: Free
- **$101-$500**: $9.99
- **$501-$2000**: $19.99
- **> $2000**: $49.99

### Phone Reveal
- **Cost**: $2.99 per reveal

---

## Error Responses
All endpoints return errors in the format:
```json
{
  "error": true,
  "message": "Error description"
}
```

Common HTTP status codes:
- `400` - Bad Request (validation errors)
- `401` - Unauthorized (invalid credentials)
- `403` - Forbidden (insufficient permissions)
- `404` - Not Found
- `500` - Internal Server Error
