### Comprehensive Use Case Document for ScrubHub

This document outlines the backend and frontend development plan for ScrubHub, a multi-role property and recruitment platform for the medical housing ecosystem. It is designed to be used by developers, particularly those leveraging AI-powered tools, to build the platform according to the specified business requirements.

### 1. Core Backend Structure & Features

This section details the foundational elements of the ScrubHub backend, including the database schema, key API modules, and overarching functionalities.

#### 1.1. Database Schema

The following tables are essential for storing and managing the platform's data.

| Table Name | Fields | Description |
| --- | --- | --- |
| **Users** | `id` (PK), `role`, `name`, `email` (unique), `password_hash`, `phone`, `address`, `country`, `province/state`, `verified` (bool), `created_at`, `updated_at` | Stores user account information for all roles (Landlord/Renter, Hospital, Recruiter, Admin). |
| **Properties** | `id` (PK), `landlord_id` (FK -> Users), `title`, `description`, `address`, `city`, `province/state`, `country`, `price`, `tier`, `status`, `created_at`, `updated_at` | Contains details of rental properties listed on the platform. |
| **Property_Photos** | `id` (PK), `property_id` (FK -> Properties), `file_path`, `sort_order` | Manages multiple photos for each property, allowing for ordering. |
| **Inquiries** | `id` (PK), `property_id` (FK -> Properties), `name`, `email`, `phone`, `status`, `created_at` | Stores inquiries made by potential renters about a property. |
| **Sublets** | `id` (PK), `property_id` (FK -> Properties), `status`, `created_at` | Manages subletting requests and their status. |
| **Favorites** | `id` (PK), `user_id` (FK -> Users), `property_id` (FK -> Properties), `created_at` | Tracks properties that users have marked as favorites. |
| **Recently_Viewed**| `id` (PK), `user_id` (FK -> Users), `property_id` (FK -> Properties), `viewed_at` | Stores the last 10 properties viewed by a user. |
| **Credit_Reports**| `id` (PK), `user_id` (FK -> Users), `trustee_reference`, `report_url`, `created_at` | Contains information related to user credit reports generated via Trustee API. |
| **Tenant_Notices**| `id` (PK), `user_id` (FK -> Users), `notice_type`, `pdf_url`, `created_at` | Stores generated N9 Tenant Notice forms. |
| **Marketplace_Items**|`id` (PK), `seller_id` (FK -> Users), `title`, `description`, `price`, `status`, `created_at` | Manages items listed in the device marketplace. |
| **Jobs** | `id` (PK), `poster_id` (FK -> Users), `title`, `description`, `location`, `type`, `status`, `created_at` | Contains job postings from hospitals and recruiters. |
| **Job_Applicants**| `id` (PK), `job_id` (FK -> Jobs), `applicant_name`, `resume_url`, `email`, `status`, `created_at` | Stores applications for job postings. |
| **Payments** | `id` (PK), `user_id` (FK -> Users), `amount`, `currency`, `purpose`, `stripe_transaction_id`, `status`, `created_at` | Tracks all payment transactions processed through Stripe. |
| **Admin_Actions** | `id` (PK), `admin_id` (FK -> Users), `action_type`, `target_id`, `target_table`, `notes`, `created_at` | Logs all actions performed by administrators for auditing purposes. |

#### 1.2. Backend API Development Modules (Expected Completion: Week 1)

| Module | Notes & Features |
| --- | --- |
| **Backend API Development** | Implement property listing tiers & pricing logic, subletting fee handling, premium status workflow, and device marketplace pricing logic. This includes integrating the **Trustee API** for credit checks and generating N9 PDFs. |
| **Payment Processing Integration** | Implement **Stripe** for all payment-related transactions, including listing fees, subletting fees, job postings, and N9 form payments. |
| **Availability & Inquiry Handling** | Develop backend logic to manage inquiries, mark them as responded, and trigger email/phone notifications. |
| **Job Posting Backend** | Create workflows for hospital and recruiter job postings, including location-based filtering, applicant status updates, and a review process for bulk listings. |
| **Credit Rating Service** | Integrate the **Trustee API** to generate and reuse credit reports for applicants. |
| **N9 Form Generator** | Develop a service to generate PDFs with pre-filled form fields, available for secure download after payment. |
| **Device Marketplace Backend** | Implement pricing rules, phone number masking logic, and an admin approval process for high-value or bulk listings. |

#### 1.3. Admin Portal and User Verification (Expected Completion: Week 2)

| Module | Notes & Features |
| --- | --- |
| **Admin Portal** | Create moderation tools for premium sublets, bulk job/device listings, billing management, user verification approval, and role management. |
| **User Verification Workflow** | Implement backend handling of landlord document verification before a property can be published. |

### 2. User Roles and Authentication (FR-01)

This section details the different user roles and the authentication process.

*   **Actors**: Landlord/Renter, Recruiter, Hospital, Admin
*   **Flow**:
    1.  Users select their role during sign-up.
    2.  Landlords and Renters share a unified interface.
    3.  Recruiters and Hospitals have separate, role-based portals.
    4.  All users log in securely with session management.
*   **Backend Requirements**:
    *   Implement role-based access control (RBAC).
    *   Secure user authentication and session management.

### 3. Property Listing and Management

This section covers the functionalities related to property listings.

#### 3.1. Property Listing Rules (FR-02)

*   **Actor**: Landlord
*   **Flow**:
    1.  Landlords must be logged in to post rental properties.
    2.  Admin verification is required via two uploaded documents before a listing can be published.
    3.  The system must support multiple photo uploads with drag-and-drop reordering.
*   **Backend Requirements**:
    *   Endpoint for landlords to create and manage property listings.
    *   A verification system that flags unverified landlords and prevents them from publishing listings.
    *   File upload and management capabilities for property photos.

#### 3.2. Listing Tiers & Pricing (FR-03)

*   **Actor**: Landlord
*   **Flow**:
    *   **Free Tier**: 2 listings, 30 days each (limited availability).
    *   **Paid Tiers**:
        *   $4.99 for 30 days.
        *   $9.99 for 60 days.
        *   $14.99 for 90 days.
    *   **Featured Listings**: An additional $19.99 to appear at the top of search results.
*   **Backend Requirements**:
    *   Integration with Stripe to handle payments for different listing tiers.
    *   Logic to manage the duration and visibility of listings based on the selected tier.

#### 3.3. Availability & Inquiry Management (FR-04)

*   **Actor**: Landlord
*   **Flow**:
    1.  Inquiries appear in the "My Listing" section of the landlord's dashboard.
    2.  Landlords can open their email client or phone dialer directly from the inquiry.
    3.  Marking an inquiry as "responded" moves it from the "new" to the "responded" section.
*   **Backend Requirements**:
    *   An endpoint to handle incoming inquiries.
    *   A system to track the status of each inquiry.

### 4. Additional Services

This section details other key services offered by ScrubHub.

#### 4.1. Subletting Services (FR-05)

*   **Actor**: Tenant/Landlord
*   **Flow**:
    *   A one-time fee of $49.99 is charged to sublet a property.
    *   Premium requests (starting at $599) are stored separately for manual admin handling.
*   **Backend Requirements**:
    *   Payment processing for subletting fees.
    *   A separate queue or flagging system for premium sublet requests.

#### 4.2. Credit Rating Integration (FR-07)

*   **Actor**: Renter
*   **Flow**:
    1.  Users submit their required details and payment.
    2.  The Trustee API generates a credit report and background check.
    3.  The report is reusable for multiple applications.
*   **Backend Requirements**:
    *   Secure integration with the Trustee API.
    *   A mechanism to store and retrieve credit reports for reuse.

#### 4.3. N9 Tenant Notice Tool (FR-08)

*   **Actor**: Tenant
*   **Flow**:
    1.  The system fills the N9 PDF form.
    2.  The user can download the form after payment.
*   **Backend Requirements**:
    *   A PDF generation service that populates the N9 form with user data.
    *   Secure file download functionality post-payment.

#### 4.4. Device Marketplace (FR-09)

*   **Actors**: All users
*   **Flow**:
    *   Users can list devices with a pricing logic applied (e.g., $19.99 fee for a $1,000 item).
    *   Phone numbers are hidden until a "Show Phone Number" button is clicked.
*   **Backend Requirements**:
    *   A dynamic pricing model based on the item's value.
    *   Phone number masking to protect user privacy.

### 5. Job Posting and Recruitment

This section outlines the functionalities for hospitals and recruiters.

#### 5.1. Hospital Job Posting (FR-10 & FR-11)

*   **Actor**: Hospital
*   **Flow**:
    *   Hospitals can post paid job listings, search for positions, and manage applicants (view resume, email, mark as "interviewing" or "rejected").
    *   Bulk job listings are stored for admin review before posting.
*   **Backend Requirements**:
    *   A dedicated portal for hospitals to manage job postings and applicants.
    *   An admin review queue for bulk job listings.

#### 5.2. Recruiter Job Posting (FR-13)

*   **Actor**: Recruiter
*   **Flow**:
    *   Recruiters can post jobs, search listings, and manage applicants in a similar manner to hospitals.
*   **Backend Requirements**:
    *   A dedicated portal for recruiters with functionalities similar to the hospital portal.

### 6. Non-Functional Requirements

These requirements define the system's operational qualities.

| ID | Category | Description |
| --- | --- | --- |
| **NFR-01** | **Performance** | The system should return search and listing results within 2 seconds under normal load. |
| **NFR-02** | **Scalability** | Must support 10,000 concurrent users during peak usage periods. |
| **NFR-03** | **Availability** | 99.9% uptime, excluding scheduled maintenance. |
| **NFR-04** | **Security** | All sensitive fields must be encrypted at rest; HTTPS enforced on all connections; secure session tokens with HTTPOnly and Secure flags. |
| **NFR-06** | **Localization** | Support for English and French, including date/time formats, currency, and tax calculations per location. |
| **NFR-08** | **Backup & Recovery**| Automated daily database backups with 14-day retention; restore time target of ≤ 2 hours. |

### 7. Project Constraints and QA Checklist

*   **Compliance**: Mandatory compliance with HIPAA and GDPR.
*   **Security**: The system must be secure against common web attacks such as phishing and injection.
*   **QA Checklist**:
    *   Test all Stripe payment scenarios.
    *   Ensure the Trustee API returns valid credit reports and securely stores the results.
    *   Confirm that landlord verification blocks property listings until admin approval.
    *   Verify that premium sublet requests are routed to the admin portal for manual handling.
    *   Ensure job search respects geographic restrictions.

This comprehensive document provides a clear roadmap for the development of ScrubHub's backend and frontend, with detailed explanations of each feature and flow to facilitate an efficient and accurate build process, especially when using AI-powered development tools.

# ScrubHub Backend Development Guide
## Comprehensive Use Cases & Development Structure

### Table of Contents
1. [System Architecture Overview](#system-architecture-overview)
2. [Database Schema & Models](#database-schema--models)
3. [API Endpoints Structure](#api-endpoints-structure)
4. [Core Backend Features & Flows](#core-backend-features--flows)
5. [Third-Party Integrations](#third-party-integrations)
6. [Security & Authentication](#security--authentication)
7. [Development Implementation Guide](#development-implementation-guide)

---

## System Architecture Overview

### Core System Components
- **Multi-role authentication system** (Tenant/Landlord, Hospital, Recruiter, Admin)
- **Property management engine** with tiered pricing
- **Job posting & applicant tracking system**
- **Device marketplace with dynamic pricing**
- **Payment processing via Stripe**
- **Document generation & legal forms**
- **Admin moderation portal**

### Technology Stack Requirements
- **Backend Framework**: Node.js/Express, Python/Django, or PHP/Laravel
- **Database**: PostgreSQL or MySQL
- **Payment**: Stripe API integration
- **File Storage**: AWS S3 or similar for images/documents
- **PDF Generation**: PDFKit, jsPDF, or similar
- **Email Service**: SendGrid, Mailgun, or AWS SES
- **Cache**: Redis for session management

---

## Database Schema & Models

### Core Tables Structure

#### Users Table
```sql
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    role VARCHAR(20) NOT NULL CHECK (role IN ('tenant', 'landlord', 'hospital', 'recruiter', 'admin')),
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    phone VARCHAR(20),
    address TEXT,
    country VARCHAR(100),
    province_state VARCHAR(100),
    verified BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

#### Properties Table
```sql
CREATE TABLE properties (
    id SERIAL PRIMARY KEY,
    landlord_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    address TEXT NOT NULL,
    city VARCHAR(100) NOT NULL,
    province_state VARCHAR(100) NOT NULL,
    country VARCHAR(100) NOT NULL,
    price DECIMAL(10,2) NOT NULL,
    tier VARCHAR(20) NOT NULL CHECK (tier IN ('free', 'paid', 'featured')),
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'expired', 'draft')),
    expires_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### Property Photos Table
```sql
CREATE TABLE property_photos (
    id SERIAL PRIMARY KEY,
    property_id INTEGER REFERENCES properties(id) ON DELETE CASCADE,
    file_path VARCHAR(500) NOT NULL,
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### Jobs Table
```sql
CREATE TABLE jobs (
    id SERIAL PRIMARY KEY,
    poster_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    title VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    location VARCHAR(255) NOT NULL,
    type VARCHAR(50) NOT NULL,
    salary_min DECIMAL(10,2),
    salary_max DECIMAL(10,2),
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'closed', 'draft')),
    expires_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### Job Applicants Table
```sql
CREATE TABLE job_applicants (
    id SERIAL PRIMARY KEY,
    job_id INTEGER REFERENCES jobs(id) ON DELETE CASCADE,
    applicant_name VARCHAR(255) NOT NULL,
    resume_url VARCHAR(500),
    email VARCHAR(255) NOT NULL,
    status VARCHAR(20) DEFAULT 'applied' CHECK (status IN ('applied', 'interviewing', 'rejected', 'hired')),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### Additional Essential Tables
```sql
-- Inquiries for property availability
CREATE TABLE inquiries (
    id SERIAL PRIMARY KEY,
    property_id INTEGER REFERENCES properties(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) NOT NULL,
    phone VARCHAR(20),
    message TEXT,
    status VARCHAR(20) DEFAULT 'new' CHECK (status IN ('new', 'responded')),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Sublets management
CREATE TABLE sublets (
    id SERIAL PRIMARY KEY,
    property_id INTEGER REFERENCES properties(id) ON DELETE CASCADE,
    tenant_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    price DECIMAL(10,2) NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    service_tier VARCHAR(20) NOT NULL CHECK (service_tier IN ('self_managed', 'premium')),
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'active', 'expired', 'cancelled')),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Marketplace items
CREATE TABLE marketplace_items (
    id SERIAL PRIMARY KEY,
    seller_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    price DECIMAL(10,2) NOT NULL,
    condition VARCHAR(50),
    category VARCHAR(100),
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'sold', 'removed')),
    phone_revealed BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Payments tracking
CREATE TABLE payments (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    amount DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'CAD',
    purpose VARCHAR(100) NOT NULL,
    stripe_transaction_id VARCHAR(255),
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'completed', 'failed', 'refunded')),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Favorites system
CREATE TABLE favorites (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    property_id INTEGER REFERENCES properties(id) ON DELETE CASCADE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, property_id)
);

-- Recently viewed tracking
CREATE TABLE recently_viewed (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    property_id INTEGER REFERENCES properties(id) ON DELETE CASCADE,
    viewed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, property_id)
);

-- Credit reports
CREATE TABLE credit_reports (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    trustee_reference VARCHAR(255) UNIQUE,
    report_url VARCHAR(500),
    expires_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Admin actions audit
CREATE TABLE admin_actions (
    id SERIAL PRIMARY KEY,
    admin_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    action_type VARCHAR(50) NOT NULL,
    target_id INTEGER,
    target_table VARCHAR(50),
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

---

## API Endpoints Structure

### Authentication Endpoints
```
POST /api/auth/register
- Body: { name, email, password, role }
- Returns: { user, token }
- Validation: Email uniqueness, password strength, role validity

POST /api/auth/login
- Body: { email, password }
- Returns: { user, token }
- Features: Rate limiting, session management

POST /api/auth/logout
- Headers: Authorization Bearer token
- Returns: { success: true }

POST /api/auth/forgot-password
- Body: { email }
- Returns: { message }
- Triggers: Password reset email

POST /api/auth/reset-password
- Body: { token, new_password }
- Returns: { success: true }
```

### Property Management Endpoints
```
GET /api/properties
- Query params: ?page=1&limit=10&city=Toronto&min_price=1000&max_price=3000
- Returns: { properties[], pagination, filters }
- Features: Location-based search, price filtering

POST /api/properties
- Headers: Authorization (verified landlords only)
- Body: { title, description, address, price, tier, photos[] }
- Returns: { property }
- Validation: Landlord verification status, pricing rules

GET /api/properties/:id
- Returns: { property, photos[], similar_properties[] }
- Features: View tracking for recently viewed

PUT /api/properties/:id
- Headers: Authorization (owner only)
- Body: { title, description, price, status }
- Returns: { property }

DELETE /api/properties/:id
- Headers: Authorization (owner only)
- Returns: { success: true }

POST /api/properties/:id/inquire
- Body: { name, email, phone, message }
- Returns: { inquiry }
- Features: Email notification to landlord
```

### Job Management Endpoints
```
GET /api/jobs
- Query params: ?location=Toronto&type=residency&hospital_id=123
- Returns: { jobs[], pagination }
- Features: Location-based filtering

POST /api/jobs
- Headers: Authorization (hospital/recruiter only)
- Body: { title, description, location, type, salary_min, salary_max }
- Returns: { job }
- Features: Payment processing for paid posts

GET /api/jobs/:id/applicants
- Headers: Authorization (job owner only)
- Returns: { applicants[] }

POST /api/jobs/:id/apply
- Body: { name, email, resume_file }
- Returns: { application }
- Features: Resume file upload

PUT /api/applicants/:id/status
- Headers: Authorization (job owner only)
- Body: { status: 'interviewing'|'rejected'|'hired' }
- Returns: { applicant }
```

### Marketplace Endpoints
```
GET /api/marketplace
- Query params: ?category=medical&condition=new&max_price=1000
- Returns: { items[], pagination }

POST /api/marketplace
- Headers: Authorization
- Body: { title, description, price, condition, photos[] }
- Returns: { item }
- Features: Dynamic pricing calculation

POST /api/marketplace/bulk
- Headers: Authorization (hospital/recruiter only)
- Body: { csv_file }
- Returns: { upload_status, pending_review_count }
- Features: Admin review queue
```

### Sublet Management Endpoints
```
POST /api/sublets
- Headers: Authorization (tenants only)
- Body: { property_id, start_date, end_date, price, service_tier }
- Returns: { sublet }
- Features: Service tier pricing ($49.99 vs $599+)

GET /api/sublets/my
- Headers: Authorization
- Returns: { sublets[] }

PUT /api/sublets/:id/status
- Headers: Authorization (admin for premium)
- Body: { status }
- Returns: { sublet }
```

---

## Core Backend Features & Flows

### 1. Property Listing Flow

#### Tier-Based Pricing Logic
```javascript
// Pricing calculation service
const calculateListingFee = (tier, duration) => {
  const pricing = {
    free: { cost: 0, duration: 30, limit: 2 },
    paid: {
      30: 4.99,
      60: 9.99,
      90: 14.99
    },
    featured: { additional: 19.99 } // Added to base paid price
  };
  
  if (tier === 'free') return pricing.free;
  if (tier === 'featured') {
    return pricing.paid[duration] + pricing.featured.additional;
  }
  return pricing.paid[duration];
};
```

#### Landlord Verification Workflow
```javascript
// Verification check middleware
const requireVerifiedLandlord = async (req, res, next) => {
  const user = await User.findById(req.userId);
  if (!user.verified && user.role === 'landlord') {
    return res.status(403).json({
      error: 'Landlord verification required',
      verification_status: 'pending',
      required_documents: ['government_id', 'proof_of_ownership']
    });
  }
  next();
};
```

### 2. Inquiry Management System

#### Inquiry Processing Flow
```javascript
// Handle property inquiries
POST /api/properties/:id/inquire
const handleInquiry = async (req, res) => {
  const { name, email, phone, message } = req.body;
  const propertyId = req.params.id;
  
  // Create inquiry record
  const inquiry = await Inquiry.create({
    property_id: propertyId,
    name,
    email,
    phone,
    message,
    status: 'new'
  });
  
  // Get landlord email for notification
  const property = await Property.findById(propertyId).include(User);
  
  // Send email notification to landlord
  await emailService.send({
    to: property.landlord.email,
    template: 'new_inquiry',
    data: { inquiry, property }
  });
  
  return res.json({ inquiry, message: 'Inquiry sent successfully' });
};
```

### 3. Sublet Service Processing

#### Sublet Tier Logic
```javascript
// Sublet service pricing and routing
const processSubletRequest = async (req, res) => {
  const { property_id, service_tier, start_date, end_date, price } = req.body;
  
  const serviceFees = {
    self_managed: 49.99,
    premium: 599.00
  };
  
  const sublet = await Sublet.create({
    property_id,
    tenant_id: req.userId,
    service_tier,
    start_date,
    end_date,
    price,
    status: service_tier === 'premium' ? 'pending_admin_review' : 'active'
  });
  
  // Process payment
  if (service_tier === 'premium') {
    // Route to admin for manual handling
    await AdminQueue.create({
      type: 'premium_sublet_review',
      reference_id: sublet.id,
      status: 'pending'
    });
  }
  
  await processPayment(req.userId, serviceFees[service_tier], 'sublet_service');
  
  return res.json({ sublet });
};
```

### 4. Device Marketplace Pricing Engine

#### Dynamic Fee Calculation
```javascript
// Device marketplace pricing logic
const calculateMarketplaceFee = (itemPrice) => {
  if (itemPrice <= 100) return 0;
  if (itemPrice <= 500) return 9.99;
  if (itemPrice <= 2000) return 19.99;
  return 49.99; // Max fee for items > $2000
};

// Marketplace listing creation
POST /api/marketplace
const createMarketplaceListing = async (req, res) => {
  const { title, description, price, condition, photos } = req.body;
  
  const listingFee = calculateMarketplaceFee(price);
  
  // Create listing
  const item = await MarketplaceItem.create({
    seller_id: req.userId,
    title,
    description,
    price,
    condition,
    status: 'active'
  });
  
  // Process photos
  if (photos && photos.length > 0) {
    await processPhotoUploads(item.id, photos);
  }
  
  // Handle payment if fee applies
  if (listingFee > 0) {
    await processPayment(req.userId, listingFee, 'marketplace_listing');
  }
  
  return res.json({ item, listing_fee: listingFee });
};
```

### 5. Job Posting & Applicant Tracking

#### Hospital Job Management
```javascript
// Job posting for hospitals
POST /api/jobs
const createJobPosting = async (req, res) => {
  const { title, description, type, salary_min, salary_max, application_deadline } = req.body;
  
  const job = await Job.create({
    poster_id: req.userId,
    title,
    description,
    location: req.user.address, // Auto-set from hospital profile
    type,
    salary_min,
    salary_max,
    expires_at: application_deadline,
    status: 'active'
  });
  
  // Process posting fee if applicable
  const postingFee = calculateJobPostingFee(type);
  if (postingFee > 0) {
    await processPayment(req.userId, postingFee, 'job_posting');
  }
  
  return res.json({ job });
};

// Applicant status management
PUT /api/applicants/:id/status
const updateApplicantStatus = async (req, res) => {
  const { status } = req.body;
  const applicantId = req.params.id;
  
  // Verify job ownership
  const applicant = await JobApplicant.findById(applicantId).include(Job);
  if (applicant.job.poster_id !== req.userId) {
    return res.status(403).json({ error: 'Unauthorized' });
  }
  
  await applicant.update({ status });
  
  // Send status update email to applicant
  await emailService.send({
    to: applicant.email,
    template: 'application_status_update',
    data: { applicant, job: applicant.job, status }
  });
  
  return res.json({ applicant });
};
```

### 6. Bulk Listing Management

#### Bulk Upload Processing
```javascript
// Bulk listing handler
POST /api/jobs/bulk
const processBulkJobListing = async (req, res) => {
  const csvFile = req.file;
  
  // Parse CSV
  const parsedData = await parseCSV(csvFile);
  
  // Validate each row
  const validationResults = await validateBulkData(parsedData);
  
  // Store valid entries for admin review
  const bulkUpload = await BulkUpload.create({
    uploader_id: req.userId,
    type: 'job_listings',
    total_rows: parsedData.length,
    valid_rows: validationResults.valid.length,
    invalid_rows: validationResults.invalid.length,
    status: 'pending_admin_review'
  });
  
  // Store valid rows for processing
  for (const validRow of validationResults.valid) {
    await BulkUploadItem.create({
      bulk_upload_id: bulkUpload.id,
      data: validRow,
      status: 'pending'
    });
  }
  
  return res.json({
    upload_id: bulkUpload.id,
    summary: {
      total: parsedData.length,
      valid: validationResults.valid.length,
      invalid: validationResults.invalid.length,
      pending_review: validationResults.valid.length
    },
    errors: validationResults.invalid
  });
};
```

---

## Third-Party Integrations

### 1. Stripe Payment Processing

#### Payment Flow Implementation
```javascript
// Stripe payment service
const stripeService = {
  async createPaymentIntent(amount, currency, purpose, userId) {
    const paymentIntent = await stripe.paymentIntents.create({
      amount: Math.round(amount * 100), // Convert to cents
      currency: currency || 'cad',
      metadata: {
        user_id: userId,
        purpose: purpose
      }
    });
    
    return paymentIntent;
  },
  
  async confirmPayment(paymentIntentId, paymentMethodId) {
    return await stripe.paymentIntents.confirm(paymentIntentId, {
      payment_method: paymentMethodId
    });
  }
};

// Payment processing endpoint
POST /api/payments/process
const processPayment = async (req, res) => {
  const { amount, purpose, payment_method_id } = req.body;
  
  try {
    // Create payment intent
    const paymentIntent = await stripeService.createPaymentIntent(
      amount, 'cad', purpose, req.userId
    );
    
    // Confirm payment
    const confirmedPayment = await stripeService.confirmPayment(
      paymentIntent.id, payment_method_id
    );
    
    // Record payment in database
    const payment = await Payment.create({
      user_id: req.userId,
      amount,
      purpose,
      stripe_transaction_id: confirmedPayment.id,
      status: 'completed'
    });
    
    return res.json({ payment, stripe_payment: confirmedPayment });
    
  } catch (error) {
    return res.status(400).json({ error: error.message });
  }
};
```

### 2. Trustee API Integration

#### Credit Report Generation
```javascript
// Trustee API service
const trusteeService = {
  async createInquiry(userData) {
    const response = await fetch('https://api.trustee.com/create-an-inquiry', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${process.env.TRUSTEE_API_KEY}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        first_name: userData.firstName,
        last_name: userData.lastName,
        date_of_birth: userData.dateOfBirth,
        address: userData.address,
        sin: userData.sin // Social Insurance Number
      })
    });
    
    return await response.json();
  }
};

// Credit report endpoint
POST /api/credit-reports/generate
const generateCreditReport = async (req, res) => {
  const { first_name, last_name, date_of_birth, address, sin } = req.body;
  
  try {
    // Check if user already has valid report
    const existingReport = await CreditReport.findOne({
      where: {
        user_id: req.userId,
        expires_at: { [Op.gt]: new Date() }
      }
    });
    
    if (existingReport) {
      return res.json({ 
        credit_report: existingReport,
        reused: true,
        message: 'Using existing valid report'
      });
    }
    
    // Generate new report via Trustee API
    const trusteeResponse = await trusteeService.createInquiry({
      firstName: first_name,
      lastName: last_name,
      dateOfBirth: date_of_birth,
      address,
      sin
    });
    
    // Store report reference
    const creditReport = await CreditReport.create({
      user_id: req.userId,
      trustee_reference: trusteeResponse.reference_id,
      report_url: trusteeResponse.report_url,
      expires_at: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // 30 days
    });
    
    return res.json({ credit_report: creditReport });
    
  } catch (error) {
    return res.status(400).json({ error: 'Failed to generate credit report' });
  }
};
```

### 3. N9 PDF Generation Service

#### Document Generation Flow
```javascript
// N9 form generation service
const pdfService = {
  async generateN9Form(formData) {
    const PDFDocument = require('pdfkit');
    const doc = new PDFDocument();
    
    // Fill N9 template with form data
    doc.fontSize(12)
       .text(`Tenant Name: ${formData.tenant_name}`, 50, 100)
       .text(`Landlord Name: ${formData.landlord_name}`, 50, 120)
       .text(`Property Address: ${formData.property_address}`, 50, 140)
       .text(`Termination Date: ${formData.termination_date}`, 50, 160)
       .text(`Reason for Termination: ${formData.reason}`, 50, 180);
    
    return doc;
  }
};

// N9 generation endpoint
POST /api/legal/n9-generate
const generateN9Notice = async (req, res) => {
  const formData = req.body;
  
  // Validate required fields
  const requiredFields = ['tenant_name', 'landlord_name', 'property_address', 'termination_date'];
  for (const field of requiredFields) {
    if (!formData[field]) {
      return res.status(400).json({ error: `${field} is required` });
    }
  }
  
  try {
    // Process payment first
    await processPayment(req.userId, 19.99, 'n9_form_generation');
    
    // Generate PDF
    const pdfDoc = await pdfService.generateN9Form(formData);
    const pdfBuffer = await pdfToBuffer(pdfDoc);
    
    // Store in secure location
    const fileName = `n9_${req.userId}_${Date.now()}.pdf`;
    const fileUrl = await uploadToSecureStorage(pdfBuffer, fileName);
    
    // Record the notice
    const tenantNotice = await TenantNotice.create({
      user_id: req.userId,
      notice_type: 'N9',
      pdf_url: fileUrl
    });
    
    return res.json({
      notice: tenantNotice,
      download_url: fileUrl,
      expires_at: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7 days
    });
    
  } catch (error) {
    return res.status(500).json({ error: 'Failed to generate N9 form' });
  }
};
```

### 4. Favorites & Recently Viewed System

#### User Activity Tracking
```javascript
// Recently viewed middleware
const trackPropertyView = async (req, res, next) => {
  if (req.userId && req.params.propertyId) {
    // Update or create recently viewed record
    await RecentlyViewed.upsert({
      user_id: req.userId,
      property_id: req.params.propertyId,
      viewed_at: new Date()
    });
    
    // Maintain only last 10 records
    const recentCount = await RecentlyViewed.count({
      where: { user_id: req.userId }
    });
    
    if (recentCount > 10) {
      const oldestRecords = await RecentlyViewed.findAll({
        where: { user_id: req.userId },
        order: [['viewed_at', 'ASC']],
        limit: recentCount - 10
      });
      
      await RecentlyViewed.destroy({
        where: { id: { [Op.in]: oldestRecords.map(r => r.id) } }
      });
    }
  }
  next();
};

// Favorites management
POST /api/favorites/toggle
const toggleFavorite = async (req, res) => {
  const { property_id } = req.body;
  
  const existing = await Favorite.findOne({
    where: { user_id: req.userId, property_id }
  });
  
  if (existing) {
    await existing.destroy();
    return res.json({ favorited: false, message: 'Removed from favorites' });
  } else {
    await Favorite.create({ user_id: req.userId, property_id });
    return res.json({ favorited: true, message: 'Added to favorites' });
  }
};
```

---

## Security & Authentication

### JWT Token Management
```javascript
// JWT service
const jwtService = {
  generateToken(user) {
    return jwt.sign(
      { 
        userId: user.id, 
        role: user.role,
        verified: user.verified 
      },
      process.env.JWT_SECRET,
      { expiresIn: '7d' }
    );
  },
  
  verifyToken(token) {
    return jwt.verify(token, process.env.JWT_SECRET);
  }
};

// Authentication middleware
const authenticateToken = async (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];
  
  if (!token) {
    return res.status(401).json({ error: 'Access token required' });
  }
  
  try {
    const decoded = jwtService.verifyToken(token);
    req.userId = decoded.userId;
    req.userRole = decoded.role;
    req.user = await User.findById(decoded.userId);
    next();
  } catch (error) {
    return res.status(403).json({ error: 'Invalid token' });
  }
};
```

### Role-Based Access Control
```javascript
// Role authorization middleware
const requireRole = (allowedRoles) => {
  return (req, res, next) => {
    if (!allowedRoles.includes(req.userRole)) {
      return res.status(403).json({ 
        error: 'Insufficient permissions',
        required_roles: allowedRoles,
        user_role: req.userRole
      });
    }
    next();
  };
};

// Usage examples:
// app.post('/api/properties', authenticateToken, requireRole(['landlord']), createProperty);
// app.post('/api/jobs', authenticateToken, requireRole(['hospital', 'recruiter']), createJob);
// app.get('/api/admin/*', authenticateToken, requireRole(['admin']), adminRoutes);
```

---

## Development Implementation Guide

### 1. Backend Project Structure
```
/backend
├── /src
│   ├── /controllers      # API endpoint handlers
│   │   ├── authController.js
│   │   ├── propertyController.js
│   │   ├── jobController.js
│   │   ├── marketplaceController.js
│   │   └── adminController.js
│   ├── /models          # Database models
│   │   ├── User.js
│   │   ├── Property.js
│   │   ├── Job.js
│   │   └── Payment.js
│   ├── /middleware      # Authentication, validation, etc.
│   │   ├── auth.js
│   │   ├── validation.js
│   │   └── rateLimit.js
│   ├── /services        # Business logic
│   │   ├── paymentService.js
│   │   ├── emailService.js
│   │   ├── pdfService.js
│   │   └── trusteeService.js
│   ├── /routes          # API route definitions
│   │   ├── auth.js
│   │   ├── properties.js
│   │   ├── jobs.js
│   │   └── marketplace.js
│   ├── /utils           # Helper functions
│   │   ├── validation.js
│   │   ├── fileUpload.js
│   │   └── pricing.js
│   └── /config          # Configuration files
│       ├── database.js
│       ├── stripe.js
│       └── email.js
├── /migrations          # Database migrations
├── /seeders            # Database seed data
└── /tests              # Test files

### 2. Database Migration Scripts

#### Initial Setup Migration
```sql
-- Migration: 001_create_users_table
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    role VARCHAR(20) NOT NULL CHECK (role IN ('tenant', 'landlord', 'hospital', 'recruiter', 'admin')),
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    phone VARCHAR(20),
    address TEXT,
    city VARCHAR(100),
    province_state VARCHAR(100),
    country VARCHAR(100) DEFAULT 'Canada',
    verified BOOLEAN DEFAULT FALSE,
    verification_documents JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_role ON users(role);
CREATE INDEX idx_users_verified ON users(verified);
```

#### Properties & Related Tables Migration
```sql
-- Migration: 002_create_properties_system
CREATE TABLE properties (
    id SERIAL PRIMARY KEY,
    landlord_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    address TEXT NOT NULL,
    city VARCHAR(100) NOT NULL,
    province_state VARCHAR(100) NOT NULL,
    country VARCHAR(100) DEFAULT 'Canada',
    price DECIMAL(10,2) NOT NULL,
    tier VARCHAR(20) NOT NULL CHECK (tier IN ('free', 'paid', 'featured')),
    duration_days INTEGER DEFAULT 30,
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'expired', 'draft')),
    expires_at TIMESTAMP,
    amenities JSONB, -- Store amenities as JSON array
    property_type VARCHAR(50),
    bedrooms INTEGER,
    bathrooms INTEGER,
    furnished BOOLEAN DEFAULT FALSE,
    available_from DATE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_properties_city ON properties(city);
CREATE INDEX idx_properties_price ON properties(price);
CREATE INDEX idx_properties_status ON properties(status);
CREATE INDEX idx_properties_tier ON properties(tier);
CREATE INDEX idx_properties_expires_at ON properties(expires_at);

CREATE TABLE property_photos (
    id SERIAL PRIMARY KEY,
    property_id INTEGER REFERENCES properties(id) ON DELETE CASCADE,
    file_path VARCHAR(500) NOT NULL,
    file_name VARCHAR(255),
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_property_photos_property_id ON property_photos(property_id);
```

### 3. Core API Controllers Implementation

#### Property Controller
```javascript
// propertyController.js
const propertyController = {
  // GET /api/properties - Search properties with filters
  async searchProperties(req, res) {
    try {
      const {
        page = 1,
        limit = 10,
        city,
        min_price,
        max_price,
        bedrooms,
        property_type,
        furnished,
        available_from,
        sort_by = 'created_at',
        sort_order = 'DESC'
      } = req.query;

      const whereClause = { status: 'active' };
      
      // Apply filters
      if (city) whereClause.city = { [Op.iLike]: `%${city}%` };
      if (min_price) whereClause.price = { [Op.gte]: min_price };
      if (max_price) whereClause.price = { ...whereClause.price, [Op.lte]: max_price };
      if (bedrooms) whereClause.bedrooms = bedrooms;
      if (property_type) whereClause.property_type = property_type;
      if (furnished !== undefined) whereClause.furnished = furnished === 'true';
      if (available_from) whereClause.available_from = { [Op.lte]: available_from };

      const properties = await Property.findAndCountAll({
        where: whereClause,
        include: [
          {
            model: PropertyPhoto,
            as: 'photos',
            order: [['sort_order', 'ASC']]
          },
          {
            model: User,
            as: 'landlord',
            attributes: ['name', 'phone', 'email']
          }
        ],
        order: [[sort_by, sort_order]],
        limit: parseInt(limit),
        offset: (parseInt(page) - 1) * parseInt(limit)
      });

      // Track search for analytics
      await SearchLog.create({
        user_id: req.userId || null,
        filters: req.query,
        result_count: properties.count
      });

      return res.json({
        properties: properties.rows,
        pagination: {
          current_page: parseInt(page),
          total_pages: Math.ceil(properties.count / limit),
          total_items: properties.count,
          items_per_page: parseInt(limit)
        }
      });

    } catch (error) {
      return res.status(500).json({ error: 'Failed to search properties' });
    }
  },

  // POST /api/properties - Create new property listing
  async createProperty(req, res) {
    try {
      const {
        title,
        description,
        address,
        city,
        province_state,
        price,
        tier,
        duration_days,
        amenities,
        property_type,
        bedrooms,
        bathrooms,
        furnished,
        available_from,
        photos
      } = req.body;

      // Verify landlord is verified
      if (!req.user.verified) {
        return res.status(403).json({
          error: 'Landlord verification required',
          verification_status: 'pending'
        });
      }

      // Check free listing limits
      if (tier === 'free') {
        const freeListingCount = await Property.count({
          where: {
            landlord_id: req.userId,
            tier: 'free',
            status: { [Op.in]: ['active', 'draft'] }
          }
        });
        
        if (freeListingCount >= 2) {
          return res.status(400).json({
            error: 'Free listing limit exceeded',
            limit: 2,
            current_count: freeListingCount
          });
        }
      }

      // Calculate listing fee
      const listingFee = calculateListingFee(tier, duration_days);
      
      // Process payment if required
      if (listingFee > 0) {
        const paymentResult = await processPayment(req.userId, listingFee, 'property_listing');
        if (!paymentResult.success) {
          return res.status(400).json({ error: 'Payment failed' });
        }
      }

      // Create property
      const property = await Property.create({
        landlord_id: req.userId,
        title,
        description,
        address,
        city,
        province_state,
        country: 'Canada',
        price,
        tier,
        duration_days,
        amenities,
        property_type,
        bedrooms,
        bathrooms,
        furnished,
        available_from,
        expires_at: new Date(Date.now() + duration_days * 24 * 60 * 60 * 1000),
        status: 'active'
      });

      // Handle photo uploads
      if (photos && photos.length > 0) {
        await processPropertyPhotos(property.id, photos);
      }

      return res.status(201).json({
        property,
        listing_fee: listingFee,
        message: 'Property listed successfully'
      });

    } catch (error) {
      return res.status(500).json({ error: 'Failed to create property listing' });
    }
  },

  // GET /api/properties/:id - Get property details
  async getPropertyDetails(req, res) {
    try {
      const propertyId = req.params.id;

      const property = await Property.findOne({
        where: { id: propertyId, status: 'active' },
        include: [
          {
            model: PropertyPhoto,
            as: 'photos',
            order: [['sort_order', 'ASC']]
          },
          {
            model: User,
            as: 'landlord',
            attributes: ['name', 'phone', 'email', 'verified']
          }
        ]
      });

      if (!property) {
        return res.status(404).json({ error: 'Property not found' });
      }

      // Track view if user is logged in
      if (req.userId) {
        await trackPropertyView(req, res, () => {});
      }

      // Get similar properties
      const similarProperties = await Property.findAll({
        where: {
          city: property.city,
          id: { [Op.ne]: property.id },
          status: 'active',
          price: {
            [Op.between]: [property.price * 0.8, property.price * 1.2]
          }
        },
        limit: 4,
        include: [{
          model: PropertyPhoto,
          as: 'photos',
          limit: 1
        }]
      });

      return res.json({
        property,
        similar_properties: similarProperties
      });

    } catch (error) {
      return res.status(500).json({ error: 'Failed to fetch property details' });
    }
  }
};
```

#### Job Controller
```javascript
// jobController.js
const jobController = {
  // POST /api/jobs - Create job posting
  async createJob(req, res) {
    try {
      const {
        title,
        description,
        type,
        department,
        salary_min,
        salary_max,
        requirements,
        application_deadline,
        contact_email,
        contact_phone
      } = req.body;

      // Auto-set location from user profile
      const location = req.user.address;

      // Calculate posting fee based on job type
      const postingFee = calculateJobPostingFee(type);

      // Process payment if required
      if (postingFee > 0) {
        const paymentResult = await processPayment(req.userId, postingFee, 'job_posting');
        if (!paymentResult.success) {
          return res.status(400).json({ error: 'Payment failed' });
        }
      }

      const job = await Job.create({
        poster_id: req.userId,
        title,
        description,
        location,
        type,
        department,
        salary_min,
        salary_max,
        requirements,
        contact_email,
        contact_phone,
        expires_at: application_deadline,
        status: 'active'
      });

      return res.status(201).json({
        job,
        posting_fee: postingFee,
        message: 'Job posted successfully'
      });

    } catch (error) {
      return res.status(500).json({ error: 'Failed to create job posting' });
    }
  },

  // GET /api/jobs/:id/applicants - Get job applicants (owner only)
  async getJobApplicants(req, res) {
    try {
      const jobId = req.params.id;

      // Verify job ownership
      const job = await Job.findOne({
        where: { id: jobId, poster_id: req.userId }
      });

      if (!job) {
        return res.status(404).json({ error: 'Job not found or access denied' });
      }

      const applicants = await JobApplicant.findAll({
        where: { job_id: jobId },
        order: [['created_at', 'DESC']]
      });

      return res.json({
        job,
        applicants,
        total_applicants: applicants.length
      });

    } catch (error) {
      return res.status(500).json({ error: 'Failed to fetch applicants' });
    }
  },

  // PUT /api/applicants/:id/status - Update applicant status
  async updateApplicantStatus(req, res) {
    try {
      const applicantId = req.params.id;
      const { status } = req.body;

      const applicant = await JobApplicant.findOne({
        where: { id: applicantId },
        include: [{
          model: Job,
          where: { poster_id: req.userId }
        }]
      });

      if (!applicant) {
        return res.status(404).json({ error: 'Applicant not found or access denied' });
      }

      await applicant.update({ status });

      // Send notification email to applicant
      await emailService.sendStatusUpdate(applicant.email, {
        job_title: applicant.Job.title,
        status: status,
        company: req.user.name
      });

      return res.json({
        applicant,
        message: 'Status updated successfully'
      });

    } catch (error) {
      return res.status(500).json({ error: 'Failed to update applicant status' });
    }
  }
};
```

### 4. Admin Portal Backend Logic

#### Admin Verification System
```javascript
// adminController.js
const adminController = {
  // GET /api/admin/pending-verifications - Get landlords awaiting verification
  async getPendingVerifications(req, res) {
    try {
      const pendingLandlords = await User.findAll({
        where: {
          role: 'landlord',
          verified: false,
          verification_documents: { [Op.ne]: null }
        },
        order: [['created_at', 'ASC']]
      });

      return res.json({ pending_verifications: pendingLandlords });

    } catch (error) {
      return res.status(500).json({ error: 'Failed to fetch pending verifications' });
    }
  },

  // PUT /api/admin/verify-landlord/:id - Approve/reject landlord verification
  async verifyLandlord(req, res) {
    try {
      const landlordId = req.params.id;
      const { approved, notes } = req.body;

      const landlord = await User.findById(landlordId);
      if (!landlord || landlord.role !== 'landlord') {
        return res.status(404).json({ error: 'Landlord not found' });
      }

      await landlord.update({ verified: approved });

      // Log admin action
      await AdminAction.create({
        admin_id: req.userId,
        action_type: approved ? 'landlord_approved' : 'landlord_rejected',
        target_id: landlordId,
        target_table: 'users',
        notes
      });

      // Send notification email
      await emailService.sendVerificationResult(landlord.email, {
        approved,
        notes,
        next_steps: approved ? 'You can now post properties' : 'Please resubmit documents'
      });

      return res.json({
        landlord,
        message: `Landlord ${approved ? 'approved' : 'rejected'} successfully`
      });

    } catch (error) {
      return res.status(500).json({ error: 'Failed to process verification' });
    }
  },

  // GET /api/admin/premium-sublets - Get premium sublet requests
  async getPremiumSublets(req, res) {
    try {
      const premiumSublets = await Sublet.findAll({
        where: {
          service_tier: 'premium',
          status: 'pending_admin_review'
        },
        include: [
          {
            model: Property,
            include: [{ model: User, as: 'landlord' }]
          },
          {
            model: User,
            as: 'tenant'
          }
        ],
        order: [['created_at', 'ASC']]
      });

      return res.json({ premium_sublets: premiumSublets });

    } catch (error) {
      return res.status(500).json({ error: 'Failed to fetch premium sublets' });
    }
  },

  // GET /api/admin/bulk-listings - Get bulk uploads pending review
  async getBulkListings(req, res) {
    try {
      const bulkUploads = await BulkUpload.findAll({
        where: { status: 'pending_admin_review' },
        include: [
          {
            model: User,
            as: 'uploader',
            attributes: ['name', 'email', 'role']
          },
          {
            model: BulkUploadItem,
            as: 'items'
          }
        ],
        order: [['created_at', 'ASC']]
      });

      return res.json({ bulk_uploads: bulkUploads });

    } catch (error) {
      return res.status(500).json({ error: 'Failed to fetch bulk listings' });
    }
  }
};
```

### 5. Email Service Implementation

#### Email Templates & Automation
```javascript
// emailService.js
const emailService = {
  async sendInquiryNotification(landlordEmail, inquiryData) {
    const template = {
      to: landlordEmail,
      subject: `New Inquiry: ${inquiryData.property.title}`,
      html: `
        <h2>New Property Inquiry</h2>
        <p><strong>Property:</strong> ${inquiryData.property.title}</p>
        <p><strong>From:</strong> ${inquiryData.name}</p>
        <p><strong>Email:</strong> ${inquiryData.email}</p>
        <p><strong>Phone:</strong> ${inquiryData.phone}</p>
        <p><strong>Message:</strong></p>
        <p>${inquiryData.message}</p>
        <hr>
        <p>Reply directly to this email to respond to the inquiry.</p>
      `
    };
    
    return await this.send(template);
  },

  async sendVerificationResult(userEmail, data) {
    const template = {
      to: userEmail,
      subject: data.approved ? 'ScrubHub Verification Approved' : 'ScrubHub Verification Update',
      html: data.approved ? `
        <h2>Congratulations! Your landlord verification has been approved.</h2>
        <p>You can now start posting properties on ScrubHub.</p>
        <p><a href="${process.env.FRONTEND_URL}/landlord/add-property">Post Your First Property</a></p>
      ` : `
        <h2>Verification Update Required</h2>
        <p>Your verification documents need attention:</p>
        <p><strong>Notes:</strong> ${data.notes}</p>
        <p><a href="${process.env.FRONTEND_URL}/profile/verification">Update Documents</a></p>
      `
    };
    
    return await this.send(template);
  },

  async sendStatusUpdate(applicantEmail, data) {
    const statusMessages = {
      interviewing: 'Your application is moving forward to the interview stage.',
      rejected: 'Thank you for your interest. We have decided to move forward with other candidates.',
      hired: 'Congratulations! We would like to extend an offer for this position.'
    };

    const template = {
      to: applicantEmail,
      subject: `Application Update: ${data.job_title}`,
      html: `
        <h2>Application Status Update</h2>
        <p><strong>Position:</strong> ${data.job_title}</p>
        <p><strong>Company:</strong> ${data.company}</p>
        <p><strong>Status:</strong> ${data.status}</p>
        <p>${statusMessages[data.status]}</p>
      `
    };
    
    return await this.send(template);
  }
};
```

### 6. File Upload & Storage Service

#### Photo & Document Handling
```javascript
// fileUploadService.js
const fileUploadService = {
  async uploadPropertyPhotos(propertyId, photoFiles) {
    const uploadedPhotos = [];
    
    for (let i = 0; i < photoFiles.length; i++) {
      const file = photoFiles[i];
      
      // Validate file type and size
      if (!this.isValidImageFile(file)) {
        throw new Error(`Invalid file type: ${file.originalname}`);
      }
      
      if (file.size > 5 * 1024 * 1024) { // 5MB limit
        throw new Error(`File too large: ${file.originalname}`);
      }
      
      // Upload to storage
      const fileName = `properties/${propertyId}/photo_${i}_${Date.now()}.jpg`;
      const uploadResult = await this.uploadToS3(file.buffer, fileName);
      
      // Save to database
      const photo = await PropertyPhoto.create({
        property_id: propertyId,
        file_path: uploadResult.url,
        file_name: file.originalname,
        sort_order: i
      });
      
      uploadedPhotos.push(photo);
    }
    
    return uploadedPhotos;
  },

  async uploadVerificationDocuments(userId, documents) {
    const uploadedDocs = [];
    
    for (const doc of documents) {
      const fileName = `verification/${userId}/${doc.type}_${Date.now()}.pdf`;
      const uploadResult = await this.uploadToS3(doc.buffer, fileName);
      uploadedDocs.push({
        type: doc.type,
        url: uploadResult.url,
        uploaded_at: new Date()
      });
    }
    
    // Update user verification documents
    await User.update(
      { verification_documents: uploadedDocs },
      { where: { id: userId } }
    );
    
    return uploadedDocs;
  },

  isValidImageFile(file) {
    const allowedTypes = ['image/jpeg', 'image/png', 'image/webp'];
    return allowedTypes.includes(file.mimetype);
  }
};
```

### 7. Search & Filtering Engine

#### Location-Based Search Implementation
```javascript
// searchService.js
const searchService = {
  async searchPropertiesNearHospitals(hospitalId, radius = 10) {
    // Get hospital coordinates
    const hospital = await Hospital.findById(hospitalId);
    
    const query = `
      SELECT p.*, 
             ( 6371 * acos( cos( radians(${hospital.latitude}) ) 
             * cos( radians( p.latitude ) ) 
             * cos( radians( p.longitude ) - radians(${hospital.longitude}) ) 
             + sin( radians(${hospital.latitude}) ) 
             * sin( radians( p.latitude ) ) ) ) AS distance
      FROM properties p
      WHERE p.status = 'active'
      HAVING distance < ${radius}
      ORDER BY distance, p.tier DESC, p.created_at DESC
    `;

    const properties = await sequelize.query(query, {
      type: QueryTypes.SELECT
    });

    return properties;
  },

  async searchWithAdvancedFilters(filters) {
    const {
      location,
      min_price,
      max_price,
      bedrooms,
      bathrooms,
      property_type,
      furnished,
      amenities,
      available_from,
      hospital_proximity
    } = filters;

    let whereClause = { status: 'active' };
    let includeClause = [];

    // Build dynamic where clause
    if (min_price) whereClause.price = { [Op.gte]: min_price };
    if (max_price) whereClause.price = { ...whereClause.price, [Op.lte]: max_price };
    if (bedrooms) whereClause.bedrooms = { [Op.gte]: bedrooms };
    if (bathrooms) whereClause.bathrooms = { [Op.gte]: bathrooms };
    if (property_type) whereClause.property_type = property_type;
    if (furnished !== undefined) whereClause.furnished = furnished;
    if (available_from) whereClause.available_from = { [Op.lte]: available_from };

    // Handle amenities filter
    if (amenities && amenities.length > 0) {
      whereClause.amenities = {
        [Op.contains]: amenities
      };
    }

    // Location-based search
    if (location) {
      whereClause[Op.or] = [
        { city: { [Op.iLike]: `%${location}%` } },
        { address: { [Op.iLike]: `%${location}%` } }
      ];
    }

    const properties = await Property.findAll({
      where: whereClause,
      include: [
        {
          model: PropertyPhoto,
          as: 'photos',
          limit: 1,
          order: [['sort_order', 'ASC']]
        }
      ],
      order: [
        ['tier', 'DESC'], // Featured first
        ['created_at', 'DESC']
      ]
    });

    return properties;
  }
};
```

---

## Advanced Backend Features

### 1. Automated Listing Expiry System

#### Cron Job Implementation
```javascript
// scheduledTasks.js
const scheduledTasks = {
  async expireListings() {
    // Find expired properties
    const expiredProperties = await Property.findAll({
      where: {
        expires_at: { [Op.lt]: new Date() },
        status: 'active'
      }
    });

    for (const property of expiredProperties) {
      await property.update({ status: 'expired' });
      
      // Notify landlord
      await emailService.sendExpiryNotification(property.landlord.email, {
        property_title: property.title,
        expired_at: property.expires_at,
        renewal_link: `${process.env.FRONTEND_URL}/properties/${property.id}/renew`
      });
    }

    // Expire sublets
    const expiredSublets = await Sublet.findAll({
      where: {
        end_date: { [Op.lt]: new Date() },
        status: 'active'
      }
    });

    await Sublet.update(
      { status: 'expired' },
      { where: { id: { [Op.in]: expiredSublets.map(s => s.id) } } }
    );

    console.log(`Expired ${expiredProperties.length} properties and ${expiredSublets.length} sublets`);
  },

  async cleanupOldData() {
    // Remove old recently viewed records (older than 30 days)
    await RecentlyViewed.destroy({
      where: {
        viewed_at: { [Op.lt]: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) }
      }
    });

    // Remove expired credit reports
    await CreditReport.destroy({
      where: {
        expires_at: { [Op.lt]: new Date() }
      }
    });
  }
};
```

### 2. Notification System

#### Real-time Notifications
```javascript
// notificationService.js
const notificationService = {
  async createNotification(userId, type, data) {
    const notification = await Notification.create({
      user_id: userId,
      type,
      title: this.getNotificationTitle(type, data),
      message: this.getNotificationMessage(type, data),
      data: JSON.stringify(data),
      read: false
    });

    // Send real-time notification if user is online
    if (this.isUserOnline(userId)) {
      this.sendRealTimeNotification(userId, notification);
    }

    return notification;
  },

  getNotificationTitle(type, data) {
    const titles = {
      new_inquiry: `New inquiry for ${data.property_title}`,
      payment_success: 'Payment processed successfully',
      listing_expired: `Listing expired: ${data.property_title}`,
      verification_approved: 'Account verification approved',
      application_status: `Application update: ${data.job_title}`
    };
    
    return titles[type] || 'ScrubHub Notification';
  },

  async sendBulkNotifications(userIds, type, data) {
    const notifications = userIds.map(userId => ({
      user_id: userId,
      type,
      title: this.getNotificationTitle(type, data),
      message: this.getNotificationMessage(type, data),
      data: JSON.stringify(data),
      read: false,
      created_at: new Date()
    }));

    await Notification.bulkCreate(notifications);
  }
};
```

### 3. Analytics & Reporting System

#### Usage Analytics
```javascript
// analyticsService.js
const analyticsService = {
  async trackUserActivity(userId, action, metadata = {}) {
    await UserActivity.create({
      user_id: userId,
      action,
      metadata: JSON.stringify(metadata),
      ip_address: req.ip,
      user_agent: req.get('User-Agent')
    });
  },

  async generatePropertyPerformanceReport(propertyId) {
    const property = await Property.findById(propertyId);
    
    const analytics = await Promise.all([
      // View count
      RecentlyViewed.count({ where: { property_id: propertyId } }),
      
      // Inquiry count
      Inquiry.count({ where: { property_id: propertyId } }),
      
      // Favorite count
      Favorite.count({ where: { property_id: propertyId } }),
      
      // Daily views in last 30 days
      this.getDailyViews(propertyId, 30)
    ]);

    return {
      property,
      metrics: {
        total_views: analytics[0],
        total_inquiries: analytics[1],
        total_favorites: analytics[2],
        daily_views: analytics[3],
        conversion_rate: analytics[1] / Math.max(analytics[0], 1) * 100
      }
    };
  },

  async getDashboardMetrics(userId, role) {
    const metrics = {};

    switch (role) {
      case 'landlord':
        metrics.total_properties = await Property.count({ where: { landlord_id: userId } });
        metrics.active_properties = await Property.count({ where: { landlord_id: userId, status: 'active' } });
        metrics.total_inquiries = await Inquiry.count({
          include: [{ model: Property, where: { landlord_id: userId } }]
        });
        metrics.new_inquiries = await Inquiry.count({
          where: { status: 'new' },
          include: [{ model: Property, where: { landlord_id: userId } }]
        });
        break;

      case 'hospital':
      case 'recruiter':
        metrics.total_jobs = await Job.count({ where: { poster_id: userId } });
        metrics.active_jobs = await Job.count({ where: { poster_id: userId, status: 'active' } });
        metrics.total_applicants = await JobApplicant.count({
          include: [{ model: Job, where: { poster_id: userId } }]
        });
        metrics.new_applicants = await JobApplicant.count({
          where: { status: 'applied' },
          include: [{ model: Job, where: { poster_id: userId } }]
        });
        break;
    }

    return metrics;
  }
};
```
