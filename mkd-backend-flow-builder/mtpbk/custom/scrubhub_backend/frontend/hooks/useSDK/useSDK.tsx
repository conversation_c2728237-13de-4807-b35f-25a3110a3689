


import { useMemo } from "react";
import { operations } from "@/utils";
import TreeSD<PERSON> from "@/utils/TreeSDK";
import MkdSDK from "@/utils/MkdSDK";
import SuperAdminSDK from "@/utils/SuperAdminSDK";
import <PERSON>SD<PERSON> from "@/utils/MemberSDK";
import Recruit<PERSON><PERSON><PERSON> from "@/utils/RecruiterSDK";
import HospitalSD<PERSON> from "@/utils/HospitalSDK";

interface SdkConfig {
  baseurl?: string;
  fe_baseurl?: string;
  project_id?: string;
  secret?: string;
  table?: string;
}

interface UseSDKReturnType {
  sdk: MkdSDK;
  superAdminSdk: SuperAdminSDK;
memberSdk: MemberSDK;
recruiterSdk: RecruiterSDK;
hospitalSdk: HospitalSDK;
  tdk: TreeSDK;
  projectId: string,
  operations: typeof operations;
}

const useSDK = (config: SdkConfig = {}): UseSDKReturnType => {
  const sdk = useMemo(() => {
    return new MkdSDK(config);
  }, [MkdSDK]);
  
  
    const superAdminSdk = useMemo(() => {
      return new SuperAdminSDK(config);
    }, [SuperAdminSDK]);
    

    const memberSdk = useMemo(() => {
      return new MemberSDK(config);
    }, [MemberSDK]);
    

    const recruiterSdk = useMemo(() => {
      return new RecruiterSDK(config);
    }, [RecruiterSDK]);
    

    const hospitalSdk = useMemo(() => {
      return new HospitalSDK(config);
    }, [HospitalSDK]);
    

  const tdk = useMemo(() => {
    return new TreeSDK(config);
  }, [TreeSDK]);

  const projectId = sdk.getProjectId()

  return { 
  sdk,
  tdk,
  projectId,
  operations, 
  superAdminSdk,
memberSdk,
recruiterSdk,
hospitalSdk
  };
};

export default useSDK;
