CREATE TABLE IF NOT EXISTS scrubhub_job (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `task` VARCHAR(512) NOT NULL,
  `arguments` TEXT,
  `time_interval` VARCHAR(512) DEFAULT 'once',
  `retries` INT DEFAULT '1',
  `status` INT DEFAULT 0,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS scrubhub_uploads (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `url` VARCHAR(512) NOT NULL,
  `caption` VARCHAR(512),
  `user_id` INT,
  `width` INT,
  `height` INT,
  `type` INT DEFAULT 0 NOT NULL,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS scrubhub_tokens (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `user_id` INT NOT NULL,
  `token` VARCHAR(512) NOT NULL,
  `code` VARCHAR(512) NOT NULL,
  `type` INT DEFAULT 0 NOT NULL,
  `data` TEXT,
  `status` INT DEFAULT 1 NOT NULL,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `expired_at` TIMESTAMP
);

CREATE TABLE IF NOT EXISTS scrubhub_preference (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `first_name` VARCHAR(512),
  `last_name` VARCHAR(512),
  `phone` VARCHAR(512),
  `photo` VARCHAR(512),
  `user_id` INT NOT NULL,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS scrubhub_user (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `email` VARCHAR(512) NOT NULL,
  `password` VARCHAR(100) NOT NULL,
  `login_type` INT DEFAULT 0 NOT NULL,
  `role_id` VARCHAR(512),
  `data` TEXT,
  `status` INT DEFAULT 0 NOT NULL,
  `verify` BOOLEAN DEFAULT '0' NOT NULL,
  `two_factor_authentication` BOOLEAN DEFAULT '0',
  `company_id` INT DEFAULT '0',
  `stripe_uid` VARCHAR(512),
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS scrubhub_stripe_product (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `created_at` DATE,
  `updated_at` DATETIME,
  `name` VARCHAR(512),
  `product_id` VARCHAR(512),
  `stripe_id` VARCHAR(512),
  `object` LONGTEXT,
  `status` INT
);

CREATE TABLE IF NOT EXISTS scrubhub_stripe_price (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `created_at` DATE,
  `updated_at` DATETIME,
  `name` VARCHAR(512),
  `product_id` VARCHAR(512),
  `stripe_id` VARCHAR(512),
  `is_usage_metered` INT,
  `usage_limit` INT,
  `object` MEDIUMTEXT,
  `amount` FLOAT,
  `trial_days` INT,
  `type` VARCHAR(512),
  `status` INT
);

CREATE TABLE IF NOT EXISTS scrubhub_stripe_subscription (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `stripe_id` VARCHAR(512),
  `price_id` VARCHAR(512),
  `user_id` INT,
  `object` TEXT,
  `status` VARCHAR(512),
  `is_lifetime` BOOLEAN,
  `created_at` DATE,
  `updated_at` DATETIME
);

CREATE TABLE IF NOT EXISTS scrubhub_stripe_checkout (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `user_id` INT,
  `stripe_id` VARCHAR(512),
  `object` TEXT,
  `created_at` DATE,
  `updated_at` DATETIME
);

CREATE TABLE IF NOT EXISTS scrubhub_stripe_webhook (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `stripe_id` VARCHAR(512),
  `idempotency_key` VARCHAR(512),
  `description` VARCHAR(512),
  `event_type` VARCHAR(512),
  `resource_type` VARCHAR(512),
  `object` TEXT,
  `is_handled` BOOLEAN,
  `created_at` DATE,
  `updated_at` DATETIME
);

CREATE TABLE IF NOT EXISTS scrubhub_stripe_setting (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `key` VARCHAR(512),
  `value` MEDIUMTEXT,
  `created_at` DATE,
  `updated_at` DATETIME
);

CREATE TABLE IF NOT EXISTS scrubhub_stripe_order (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `user_id` INT,
  `price_id` VARCHAR(512),
  `stripe_id` VARCHAR(512),
  `object` TEXT,
  `created_at` DATE,
  `updated_at` DATETIME
);

CREATE TABLE IF NOT EXISTS scrubhub_stripe_invoice (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `user_id` INT,
  `stripe_id` VARCHAR(512),
  `object` TEXT,
  `created_at` DATE,
  `updated_at` DATETIME
);

CREATE TABLE IF NOT EXISTS scrubhub_stripe_refund (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `user_id` INT,
  `stripe_id` VARCHAR(512),
  `object` TEXT,
  `charge_id` VARCHAR(512),
  `subscription_id` VARCHAR(512),
  `amount` VARCHAR(512),
  `currency` VARCHAR(512),
  `reason` VARCHAR(512),
  `status` VARCHAR(512),
  `created_at` DATE,
  `updated_at` DATETIME
);

CREATE TABLE IF NOT EXISTS scrubhub_stripe_dispute (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `user_id` INT,
  `stripe_id` VARCHAR(512),
  `subscription_id` VARCHAR(512),
  `object` TEXT,
  `amount` VARCHAR(512),
  `reason` VARCHAR(512),
  `reason_description` VARCHAR(512),
  `status` VARCHAR(512),
  `created_at` DATE,
  `updated_at` DATETIME
);

