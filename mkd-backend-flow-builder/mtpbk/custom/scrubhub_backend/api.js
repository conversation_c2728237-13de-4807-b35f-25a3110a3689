
const AuthService = require('../../baas/services/AuthService');
const TokenMiddleware = require('../../baas/middleware/TokenMiddleware');
const StripeService = require('../../baas/services/StripeService');
const { filterEmptyFields, sqlDateTimeFormat } = require('../../baas/services/UtilService');
const fetch = require('node-fetch');
const axios = require('axios');

module.exports = function(app) {
  const config = app.get('configuration');
  const stripe = new StripeService();

  // Helper function for role-based access
  const RoleMiddleware = (allowedRoles) => {
    return [
      TokenMiddleware(),
      (req, res, next) => {
        const userRole = req.role || req.user_role;
        if (!allowedRoles.includes(userRole)) {
          return res.status(403).json({
            error: true,
            message: `Access denied. Required roles: ${allowedRoles.join(', ')}`
          });
        }
        next();
      }
    ];
  };

  // Helper function for admin-only access
  const AdminMiddleware = () => {
    return RoleMiddleware(['admin', 'super_admin']);
  };

  // Helper function for landlord access
  const LandlordMiddleware = () => {
    return RoleMiddleware(['landlord', 'admin']);
  };

  // Helper function for hospital/recruiter access
  const HospitalRecruiterMiddleware = () => {
    return RoleMiddleware(['hospital', 'recruiter', 'admin']);
  };

  // Pricing calculation functions
  const PricingService = {
    calculateListingFee(tier, duration_days) {
      const pricing = {
        free: { cost: 0, duration: 30, limit: 2 },
        paid: {
          30: 4.99,
          60: 9.99,
          90: 14.99
        },
        featured: { additional: 19.99 }
      };

      if (tier === 'free') return pricing.free.cost;
      if (tier === 'featured') {
        return pricing.paid[duration_days] + pricing.featured.additional;
      }
      return pricing.paid[duration_days] || 0;
    },

    calculateSubletFee(service_tier) {
      const fees = {
        self_managed: 49.99,
        premium: 599.00
      };
      return fees[service_tier] || 0;
    },

    calculateMarketplaceFee(itemPrice) {
      if (itemPrice <= 100) return 0;
      if (itemPrice <= 500) return 9.99;
      if (itemPrice <= 2000) return 19.99;
      return 49.99;
    },

    calculateJobPostingFee(job_type, duration_days = 30) {
      const baseFees = {
        'full_time': 99.99,
        'part_time': 49.99,
        'permanent': 99.99,
        'fixed_term_contract': 79.99,
        'casual': 39.99,
        'seasonal': 59.99,
        'freelance': 29.99,
        'apprenticeship': 49.99,
        'internship': 29.99,
        'co_op': 29.99
      };

      const baseFee = baseFees[job_type] || 49.99;

      // Duration multiplier
      if (duration_days <= 30) return baseFee;
      if (duration_days <= 60) return baseFee * 1.5;
      if (duration_days <= 90) return baseFee * 2;
      return baseFee * 2.5; // 90+ days
    }
  };

  // Load lambda functions for authentication
  const memberRegisterLambda = require('./lambda/member_register');
  const memberLoginLambda = require('./lambda/member_login');
  const hospitalRegisterLambda = require('./lambda/hospital_register');
  const hospitalLoginLambda = require('./lambda/hospital_login');
  const recruiterRegisterLambda = require('./lambda/recruiter_register');
  const recruiterLoginLambda = require('./lambda/recruiter_login');
  const customerBillingLambda = require('./lambda/customer_billing');

  // Initialize lambda functions
  if (typeof memberRegisterLambda === 'function') memberRegisterLambda(app);
  if (typeof memberLoginLambda === 'function') memberLoginLambda(app);
  if (typeof hospitalRegisterLambda === 'function') hospitalRegisterLambda(app);
  if (typeof hospitalLoginLambda === 'function') hospitalLoginLambda(app);
  if (typeof recruiterRegisterLambda === 'function') recruiterRegisterLambda(app);
  if (typeof recruiterLoginLambda === 'function') recruiterLoginLambda(app);
  if (typeof customerBillingLambda === 'function') customerBillingLambda(app);

  // ==================== LOCATION & SEARCH APIs ====================

  // Get rentals near a specific location
  app.get('/v1/api/scrubhub/rentals/near/:location', async (req, res) => {
    try {
      const sdk = app.get('sdk');
      sdk.setProjectId('scrubhub');

      const location = req.params.location;
      const { page = 1, limit = 10, radius = 25 } = req.query; // radius in km
      const offset = (parseInt(page) - 1) * parseInt(limit);

      // Get location coordinates from our stored locations
      const locationQuery = `
        SELECT * FROM scrubhub_location
        WHERE name LIKE ? OR city LIKE ? OR province_state LIKE ?
        LIMIT 1
      `;
      const locations = await sdk.rawQuery(locationQuery, [`%${location}%`, `%${location}%`, `%${location}%`]);

      if (locations.length === 0) {
        return res.status(404).json({
          error: true,
          message: `Location "${location}" not found in our system`
        });
      }

      const targetLocation = locations[0];

      // Find properties near this location using stored coordinates
      const propertiesQuery = `
        SELECT
          p.*,
          u.name as landlord_name,
          u.verified as landlord_verified,
          l.name as location_name,
          l.distance_to_medical_schools,
          GROUP_CONCAT(pp.file_path ORDER BY pp.sort_order LIMIT 3) as photo_paths,
          SQRT(POW(69.1 * (p.latitude - ?), 2) + POW(69.1 * (? - p.longitude) * COS(p.latitude / 57.3), 2)) AS distance_km
        FROM scrubhub_property p
        LEFT JOIN scrubhub_user u ON p.landlord_id = u.id
        LEFT JOIN scrubhub_property_photo pp ON p.id = pp.property_id
        LEFT JOIN scrubhub_location l ON p.location_id = l.id
        WHERE p.status = 0
        AND SQRT(POW(69.1 * (p.latitude - ?), 2) + POW(69.1 * (? - p.longitude) * COS(p.latitude / 57.3), 2)) <= ?
        GROUP BY p.id
        ORDER BY distance_km ASC
        LIMIT ? OFFSET ?
      `;

      const properties = await sdk.rawQuery(propertiesQuery, [
        targetLocation.latitude, targetLocation.longitude,
        targetLocation.latitude, targetLocation.longitude,
        radius, parseInt(limit), offset
      ]);

      // Get total count
      const countQuery = `
        SELECT COUNT(*) as total FROM scrubhub_property p
        WHERE p.status = 0
        AND SQRT(POW(69.1 * (p.latitude - ?), 2) + POW(69.1 * (? - p.longitude) * COS(p.latitude / 57.3), 2)) <= ?
      `;
      const countResult = await sdk.rawQuery(countQuery, [
        targetLocation.latitude, targetLocation.longitude, radius
      ]);
      const total = countResult[0]?.total || 0;

      const formattedProperties = properties.map(property => ({
        ...property,
        photos: property.photo_paths ? property.photo_paths.split(',') : [],
        distance_km: parseFloat(property.distance_km).toFixed(2),
        near_medical_school: property.distance_to_medical_schools <= 5,
        // Ensure these key details are explicitly included
        bedrooms: property.bedrooms || 0,
        bathrooms: property.bathrooms || 0,
        price: parseFloat(property.price) || 0,
        formatted_price: property.price ? `$${parseFloat(property.price).toLocaleString()} CAD/month` : 'Price on request'
      }));

      res.status(200).json({
        error: false,
        location: targetLocation,
        list: formattedProperties,
        pagination: {
          total,
          totalPages: Math.ceil(total / parseInt(limit)),
          currentPage: parseInt(page),
          limit: parseInt(limit)
        }
      });
    } catch (error) {
      console.error('Location search error:', error);
      res.status(500).json({ error: true, message: error.message });
    }
  });

  // Get all available locations with enhanced search
  app.get('/v1/api/scrubhub/locations', async (req, res) => {
    try {
      const sdk = app.get('sdk');
      sdk.setProjectId('scrubhub');

      const {
        search,
        type = 'all',
        sort_by = 'popularity',
        limit = 50,
        include_nearby = true
      } = req.query;

      let results = {
        locations: [],
        universities: [],
        hospitals: [],
        medical_schools: [],
        popular_locations: [],
        total_count: 0
      };

      // Build search conditions
      let searchCondition = '';
      let searchParams = [];

      if (search) {
        searchCondition = 'WHERE name LIKE ? OR city LIKE ? OR province_state LIKE ?';
        const searchTerm = `%${search}%`;
        searchParams = [searchTerm, searchTerm, searchTerm];
      }

      // Get locations
      if (type === 'all' || type === 'locations') {
        const locationsQuery = `
          SELECT *, 'location' as result_type
          FROM scrubhub_location
          ${searchCondition}
          ORDER BY ${sort_by === 'popularity' ? 'popularity_score DESC, search_count DESC' : 'name ASC'}
          LIMIT ?
        `;
        const locations = await sdk.rawQuery(locationsQuery, [...searchParams, parseInt(limit)]);
        results.locations = locations;
      }

      // Get universities
      if (type === 'all' || type === 'universities') {
        const universitiesQuery = `
          SELECT *, 'university' as result_type
          FROM scrubhub_university
          ${searchCondition}
          ORDER BY ${sort_by === 'popularity' ? 'popularity_score DESC, search_count DESC' : 'name ASC'}
          LIMIT ?
        `;
        const universities = await sdk.rawQuery(universitiesQuery, [...searchParams, parseInt(limit)]);
        results.universities = universities;
      }

      // Get hospitals
      if (type === 'all' || type === 'hospitals') {
        const hospitalsQuery = `
          SELECT *, 'hospital' as result_type
          FROM scrubhub_hospital
          ${searchCondition}
          ORDER BY ${sort_by === 'popularity' ? 'popularity_score DESC, search_count DESC' : 'name ASC'}
          LIMIT ?
        `;
        const hospitals = await sdk.rawQuery(hospitalsQuery, [...searchParams, parseInt(limit)]);
        results.hospitals = hospitals;
      }

      // Get medical schools
      if (type === 'all' || type === 'medical_schools') {
        const medicalSchoolsQuery = `
          SELECT *, 'medical_school' as result_type
          FROM scrubhub_medical_school
          ${searchCondition}
          ORDER BY ${sort_by === 'popularity' ? 'popularity_score DESC, search_count DESC' : 'name ASC'}
          LIMIT ?
        `;
        const medicalSchools = await sdk.rawQuery(medicalSchoolsQuery, [...searchParams, parseInt(limit)]);
        results.medical_schools = medicalSchools;
      }

      // Get popular locations (top 10 most popular across all types)
      if (include_nearby === 'true' || include_nearby === true) {
        const popularQuery = `
          (SELECT name, city, province_state, latitude, longitude, popularity_score, 'location' as type FROM scrubhub_location ORDER BY popularity_score DESC LIMIT 5)
          UNION ALL
          (SELECT name, city, province_state, latitude, longitude, popularity_score, 'university' as type FROM scrubhub_university ORDER BY popularity_score DESC LIMIT 3)
          UNION ALL
          (SELECT name, city, province_state, latitude, longitude, popularity_score, 'hospital' as type FROM scrubhub_hospital ORDER BY popularity_score DESC LIMIT 2)
          ORDER BY popularity_score DESC
        `;
        const popularLocations = await sdk.rawQuery(popularQuery);
        results.popular_locations = popularLocations;
      }

      // Calculate total count
      results.total_count = results.locations.length + results.universities.length +
                           results.hospitals.length + results.medical_schools.length;

      // Update search counts for searched items (if search term provided)
      if (search) {
        // Update search counts for all found items
        const allResults = [...results.locations, ...results.universities, ...results.hospitals, ...results.medical_schools];
        for (const item of allResults) {
          if (item.result_type === 'location') {
            await sdk.rawQuery('UPDATE scrubhub_location SET search_count = search_count + 1 WHERE id = ?', [item.id]);
          } else if (item.result_type === 'university') {
            await sdk.rawQuery('UPDATE scrubhub_university SET search_count = search_count + 1 WHERE id = ?', [item.id]);
          } else if (item.result_type === 'hospital') {
            await sdk.rawQuery('UPDATE scrubhub_hospital SET search_count = search_count + 1 WHERE id = ?', [item.id]);
          } else if (item.result_type === 'medical_school') {
            await sdk.rawQuery('UPDATE scrubhub_medical_school SET search_count = search_count + 1 WHERE id = ?', [item.id]);
          }
        }
      }

      res.status(200).json({
        error: false,
        data: results,
        search_term: search || null,
        filters: { type, sort_by, limit }
      });
    } catch (error) {
      console.error('Get locations error:', error);
      res.status(500).json({ error: true, message: error.message });
    }
  });

  // Search properties by map bounds (for map view)
  app.get('/v1/api/scrubhub/properties/map', async (req, res) => {
    try {
      const sdk = app.get('sdk');
      sdk.setProjectId('scrubhub');

      const { north, south, east, west, limit = 100 } = req.query;

      if (!north || !south || !east || !west) {
        return res.status(400).json({
          error: true,
          message: 'Map bounds required: north, south, east, west'
        });
      }

      const propertiesQuery = `
        SELECT
          p.id, p.title, p.price, p.latitude, p.longitude, p.bedrooms, p.property_type,
          GROUP_CONCAT(pp.file_path ORDER BY pp.sort_order LIMIT 1) as first_photo
        FROM scrubhub_property p
        LEFT JOIN scrubhub_property_photo pp ON p.id = pp.property_id
        WHERE p.status = 0
        AND p.latitude BETWEEN ? AND ?
        AND p.longitude BETWEEN ? AND ?
        GROUP BY p.id
        LIMIT ?
      `;

      const properties = await sdk.rawQuery(propertiesQuery, [
        parseFloat(south), parseFloat(north),
        parseFloat(west), parseFloat(east),
        parseInt(limit)
      ]);

      res.status(200).json({
        error: false,
        list: properties.map(p => ({
          ...p,
          first_photo: p.first_photo
        }))
      });
    } catch (error) {
      console.error('Map search error:', error);
      res.status(500).json({ error: true, message: error.message });
    }
  });

  // ==================== MEDICAL SCHOOLS APIs ====================

  // Get all medical schools
  app.get('/v1/api/scrubhub/medical-schools', async (req, res) => {
    try {
      const sdk = app.get('sdk');
      sdk.setProjectId('scrubhub');

      const { province_state, city } = req.query;

      let whereConditions = [];
      let whereParams = [];

      if (province_state) {
        whereConditions.push('province_state = ?');
        whereParams.push(province_state);
      }

      if (city) {
        whereConditions.push('city LIKE ?');
        whereParams.push(`%${city}%`);
      }

      const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';

      const schoolsQuery = `
        SELECT * FROM scrubhub_medical_school
        ${whereClause}
        ORDER BY name ASC
      `;

      const schools = await sdk.rawQuery(schoolsQuery, whereParams);

      res.status(200).json({
        error: false,
        list: schools
      });
    } catch (error) {
      console.error('Medical schools error:', error);
      res.status(500).json({ error: true, message: error.message });
    }
  });

  // Get medical school details with nearby properties
  app.get('/v1/api/scrubhub/medical-schools/:id', async (req, res) => {
    try {
      const sdk = app.get('sdk');
      sdk.setProjectId('scrubhub');

      const schoolId = req.params.id;
      const { radius = 25, limit = 20 } = req.query;

      const school = await sdk.findById('medical_school', schoolId);
      if (!school) {
        return res.status(404).json({ error: true, message: 'Medical school not found' });
      }

      // Get nearby properties
      const propertiesQuery = `
        SELECT
          p.*,
          u.name as landlord_name,
          u.verified as landlord_verified,
          GROUP_CONCAT(pp.file_path ORDER BY pp.sort_order LIMIT 3) as photo_paths,
          SQRT(POW(69.1 * (p.latitude - ?), 2) + POW(69.1 * (? - p.longitude) * COS(p.latitude / 57.3), 2)) AS distance_km
        FROM scrubhub_property p
        LEFT JOIN scrubhub_user u ON p.landlord_id = u.id
        LEFT JOIN scrubhub_property_photo pp ON p.id = pp.property_id
        WHERE p.status = 0
        AND SQRT(POW(69.1 * (p.latitude - ?), 2) + POW(69.1 * (? - p.longitude) * COS(p.latitude / 57.3), 2)) <= ?
        GROUP BY p.id
        ORDER BY distance_km ASC
        LIMIT ?
      `;

      const nearbyProperties = await sdk.rawQuery(propertiesQuery, [
        school.latitude, school.longitude,
        school.latitude, school.longitude,
        radius, parseInt(limit)
      ]);

      const formattedProperties = nearbyProperties.map(property => ({
        ...property,
        photos: property.photo_paths ? property.photo_paths.split(',') : [],
        distance_km: parseFloat(property.distance_km).toFixed(2)
      }));

      res.status(200).json({
        error: false,
        school: school,
        nearby_properties: formattedProperties,
        search_radius: parseFloat(radius)
      });
    } catch (error) {
      console.error('Medical school details error:', error);
      res.status(500).json({ error: true, message: error.message });
    }
  });

  // Get properties near a specific location (alias for rentals/near)
  // app.get('/v1/api/scrubhub/properties/near/:location', async (req, res) => {
  //   try {
  //     const sdk = app.get('sdk');
  //     sdk.setProjectId('scrubhub');

  //     const location = req.params.location;
  //     const { page = 1, limit = 10, radius = 25 } = req.query; // radius in km
  //     const offset = (parseInt(page) - 1) * parseInt(limit);

  //     // Get location coordinates from our stored locations
  //     const locationQuery = `
  //       SELECT * FROM scrubhub_location
  //       WHERE name LIKE ? OR city LIKE ? OR province_state LIKE ?
  //       LIMIT 1
  //     `;
  //     const locations = await sdk.rawQuery(locationQuery, [`%${location}%`, `%${location}%`, `%${location}%`]);

  //     if (locations.length === 0) {
  //       return res.status(404).json({
  //         error: true,
  //         message: 'Location not found'
  //       });
  //     }

  //     const targetLocation = locations[0];

  //     // Find properties near this location using stored coordinates
  //     const propertiesQuery = `
  //       SELECT
  //         p.*,
  //         u.name as landlord_name,
  //         u.verified as landlord_verified,
  //         l.name as location_name,
  //         l.distance_to_medical_schools,
  //         GROUP_CONCAT(pp.file_path ORDER BY pp.sort_order LIMIT 3) as photo_paths,
  //         SQRT(POW(69.1 * (p.latitude - ?), 2) + POW(69.1 * (? - p.longitude) * COS(p.latitude / 57.3), 2)) AS distance_km
  //       FROM scrubhub_property p
  //       LEFT JOIN scrubhub_user u ON p.landlord_id = u.id
  //       LEFT JOIN scrubhub_property_photo pp ON p.id = pp.property_id
  //       LEFT JOIN scrubhub_location l ON p.location_id = l.id
  //       WHERE p.status = 0
  //       AND SQRT(POW(69.1 * (p.latitude - ?), 2) + POW(69.1 * (? - p.longitude) * COS(p.latitude / 57.3), 2)) <= ?
  //       GROUP BY p.id
  //       ORDER BY distance_km ASC
  //       LIMIT ? OFFSET ?
  //     `;

  //     const properties = await sdk.rawQuery(propertiesQuery, [
  //       targetLocation.latitude, targetLocation.longitude,
  //       targetLocation.latitude, targetLocation.longitude,
  //       radius, parseInt(limit), offset
  //     ]);

  //     // Get total count
  //     const countQuery = `
  //       SELECT COUNT(*) as total FROM scrubhub_property p
  //       WHERE p.status = 0
  //       AND SQRT(POW(69.1 * (p.latitude - ?), 2) + POW(69.1 * (? - p.longitude) * COS(p.latitude / 57.3), 2)) <= ?
  //     `;
  //     const countResult = await sdk.rawQuery(countQuery, [
  //       targetLocation.latitude, targetLocation.longitude, radius
  //     ]);
  //     const total = countResult[0]?.total || 0;

  //     const formattedProperties = properties.map(property => ({
  //       ...property,
  //       photos: property.photo_paths ? property.photo_paths.split(',') : [],
  //       distance_km: parseFloat(property.distance_km).toFixed(2),
  //       near_medical_school: property.distance_to_medical_schools <= 5,
  //       // Ensure these key details are explicitly included
  //       bedrooms: property.bedrooms || 0,
  //       bathrooms: property.bathrooms || 0,
  //       price: parseFloat(property.price) || 0,
  //       formatted_price: property.price ? `$${parseFloat(property.price).toLocaleString()} CAD/month` : 'Price on request'
  //     }));

  //     res.status(200).json({
  //       error: false,
  //       location: targetLocation,
  //       list: formattedProperties,
  //       pagination: {
  //         total,
  //         totalPages: Math.ceil(total / parseInt(limit)),
  //         currentPage: parseInt(page),
  //         limit: parseInt(limit)
  //       }
  //     });
  //   } catch (error) {
  //     console.error('Properties near location error:', error);
  //     res.status(500).json({ error: true, message: error.message });
  //   }
  // });

  // ==================== FAVORITES & RECENTLY VIEWED APIs ====================

  // Get user favorites
  app.get('/v1/api/scrubhub/favorites', [TokenMiddleware()], async (req, res) => {
    try {
      const sdk = app.get('sdk');
      sdk.setProjectId('scrubhub');

      const { page = 1, limit = 10 } = req.query;
      const offset = (parseInt(page) - 1) * parseInt(limit);

      const favoritesQuery = `
        SELECT
          p.*,
          f.created_at as favorited_at,
          u.name as landlord_name,
          u.verified as landlord_verified,
          GROUP_CONCAT(pp.file_path ORDER BY pp.sort_order LIMIT 3) as photo_paths
        FROM scrubhub_favorite f
        JOIN scrubhub_property p ON f.property_id = p.id
        LEFT JOIN scrubhub_user u ON p.landlord_id = u.id
        LEFT JOIN scrubhub_property_photo pp ON p.id = pp.property_id
        WHERE f.user_id = ? AND p.status = 0
        GROUP BY p.id
        ORDER BY f.created_at DESC
        LIMIT ? OFFSET ?
      `;

      const favorites = await sdk.rawQuery(favoritesQuery, [req.user_id, parseInt(limit), offset]);

      // Get total count
      const total = await sdk.count('favorite', { user_id: req.user_id });

      const formattedFavorites = favorites.map(property => ({
        ...property,
        photos: property.photo_paths ? property.photo_paths.split(',') : []
      }));

      res.status(200).json({
        error: false,
        list: formattedFavorites,
        pagination: {
          total,
          totalPages: Math.ceil(total / parseInt(limit)),
          currentPage: parseInt(page),
          limit: parseInt(limit)
        }
      });
    } catch (error) {
      console.error('Get favorites error:', error);
      res.status(500).json({ error: true, message: error.message });
    }
  });

  // Add property to favorites
  app.post('/v1/api/scrubhub/favorites', [TokenMiddleware()], async (req, res) => {
    try {
      const sdk = app.get('sdk');
      sdk.setProjectId('scrubhub');

      const { property_id } = req.body;

      if (!property_id) {
        return res.status(400).json({ error: true, message: 'Property ID is required' });
      }

      // Check if already favorited
      const existing = await sdk.findOne('favorite', {
        user_id: req.user_id,
        property_id: property_id
      });

      if (existing) {
        return res.status(400).json({ error: true, message: 'Property already in favorites' });
      }

      // Verify property exists
      const property = await sdk.findById('property', property_id);
      if (!property) {
        return res.status(404).json({ error: true, message: 'Property not found' });
      }

      await sdk.create('favorite', {
        user_id: req.user_id,
        property_id: property_id,
        created_at: sqlDateTimeFormat(new Date())
      });

      res.status(200).json({
        error: false,
        message: 'Property added to favorites'
      });
    } catch (error) {
      console.error('Add favorite error:', error);
      res.status(500).json({ error: true, message: error.message });
    }
  });

  // Remove property from favorites
  app.delete('/v1/api/scrubhub/favorites/:property_id', [TokenMiddleware()], async (req, res) => {
    try {
      const sdk = app.get('sdk');
      sdk.setProjectId('scrubhub');

      const property_id = req.params.property_id;

      const deleted = await sdk.delete('favorite', {
        user_id: req.user_id,
        property_id: property_id
      });

      if (deleted === 0) {
        return res.status(404).json({ error: true, message: 'Favorite not found' });
      }

      res.status(200).json({
        error: false,
        message: 'Property removed from favorites'
      });
    } catch (error) {
      console.error('Remove favorite error:', error);
      res.status(500).json({ error: true, message: error.message });
    }
  });

  // Get recently viewed properties
  app.get('/v1/api/scrubhub/recently-viewed', [TokenMiddleware()], async (req, res) => {
    try {
      const sdk = app.get('sdk');
      sdk.setProjectId('scrubhub');

      const recentlyViewedQuery = `
        SELECT
          p.*,
          rv.viewed_at,
          u.name as landlord_name,
          u.verified as landlord_verified,
          GROUP_CONCAT(pp.file_path ORDER BY pp.sort_order LIMIT 3) as photo_paths
        FROM scrubhub_recently_viewed rv
        JOIN scrubhub_property p ON rv.property_id = p.id
        LEFT JOIN scrubhub_user u ON p.landlord_id = u.id
        LEFT JOIN scrubhub_property_photo pp ON p.id = pp.property_id
        WHERE rv.user_id = ? AND p.status = 0
        GROUP BY p.id
        ORDER BY rv.viewed_at DESC
        LIMIT 10
      `;

      const recentlyViewed = await sdk.rawQuery(recentlyViewedQuery, [req.user_id]);

      const formattedProperties = recentlyViewed.map(property => ({
        ...property,
        photos: property.photo_paths ? property.photo_paths.split(',') : []
      }));

      res.status(200).json({
        error: false,
        list: formattedProperties
      });
    } catch (error) {
      console.error('Get recently viewed error:', error);
      res.status(500).json({ error: true, message: error.message });
    }
  });

  // Add property to recently viewed
  app.post('/v1/api/scrubhub/recently-viewed', [TokenMiddleware()], async (req, res) => {
    try {
      const sdk = app.get('sdk');
      sdk.setProjectId('scrubhub');

      const { property_id } = req.body;

      if (!property_id) {
        return res.status(400).json({ error: true, message: 'Property ID is required' });
      }

      // Remove existing entry for this property
      await sdk.delete('recently_viewed', {
        user_id: req.user_id,
        property_id: property_id
      });

      // Add new entry
      await sdk.create('recently_viewed', {
        user_id: req.user_id,
        property_id: property_id,
        viewed_at: sqlDateTimeFormat(new Date())
      });

      // Keep only last 10 entries
      const recentEntries = await sdk.find('recently_viewed',
        { user_id: req.user_id },
        { orderBy: 'viewed_at', direction: 'DESC', limit: 10 }
      );

      if (recentEntries.length === 10) {
        // Delete older entries
        await sdk.rawQuery(`
          DELETE FROM scrubhub_recently_viewed
          WHERE user_id = ? AND viewed_at < ?
        `, [req.user_id, recentEntries[9].viewed_at]);
      }

      res.status(200).json({
        error: false,
        message: 'Property added to recently viewed'
      });
    } catch (error) {
      console.error('Add recently viewed error:', error);
      res.status(500).json({ error: true, message: error.message });
    }
  });

  // ==================== PROPERTY MANAGEMENT APIs ====================

  // Get all properties with search and filters (Enhanced with location-based search)
  app.get('/v1/api/scrubhub/properties', async (req, res) => {
    try {
      const sdk = app.get('sdk');
      sdk.setProjectId('scrubhub');

      const {
        page = 1,
        limit = 10,
        city,
        location, // New: for "rentals near {location}" functionality
        medical_school_id, // New: filter by specific medical school
        radius = 25, // km radius for location-based search
        min_price,
        max_price,
        bedrooms,
        property_type,
        furnished,
        available_from,
        near_medical_school, // New: filter for properties near medical schools
        include_sublets = true, // New: include sublets in search
        search_type = 'all', // New: 'properties', 'sublets', 'all'
        sort_by = 'popularity', // New: default to popularity
        sort_order = 'DESC'
      } = req.query;

      const offset = (parseInt(page) - 1) * parseInt(limit);

      // Build where conditions
      let whereConditions = ['p.status = 0']; // 0 = active
      let whereParams = [];
      let joinClauses = '';
      let selectFields = `
        p.*,
        u.name as landlord_name,
        u.phone as landlord_phone,
        u.email as landlord_email,
        u.verified as landlord_verified,
        GROUP_CONCAT(pp.file_path ORDER BY pp.sort_order) as photo_paths
      `;

      // Location-based search using stored locations
      if (location) {
        // Get location coordinates from our stored locations
        const locationQuery = `
          SELECT * FROM scrubhub_location
          WHERE name LIKE ? OR city LIKE ? OR province_state LIKE ?
          LIMIT 1
        `;
        const locations = await sdk.rawQuery(locationQuery, [`%${location}%`, `%${location}%`, `%${location}%`]);

        if (locations.length > 0) {
          const targetLocation = locations[0];
          selectFields += `,
            SQRT(POW(69.1 * (p.latitude - ?), 2) + POW(69.1 * (? - p.longitude) * COS(p.latitude / 57.3), 2)) AS distance_km`;
          whereParams.unshift(targetLocation.latitude, targetLocation.longitude);

          whereConditions.push(`
            SQRT(POW(69.1 * (p.latitude - ?), 2) + POW(69.1 * (? - p.longitude) * COS(p.latitude / 57.3), 2)) <= ?
          `);
          whereParams.push(targetLocation.latitude, targetLocation.longitude, parseFloat(radius));
        }
      }

      // Medical school specific search
      if (medical_school_id) {
        const medicalSchool = await sdk.findById('medical_school', medical_school_id);
        if (medicalSchool) {
          selectFields += `,
            SQRT(POW(69.1 * (p.latitude - ?), 2) + POW(69.1 * (? - p.longitude) * COS(p.latitude / 57.3), 2)) AS distance_to_school`;
          whereParams.unshift(medicalSchool.latitude, medicalSchool.longitude);

          whereConditions.push(`
            SQRT(POW(69.1 * (p.latitude - ?), 2) + POW(69.1 * (? - p.longitude) * COS(p.latitude / 57.3), 2)) <= ?
          `);
          whereParams.push(medicalSchool.latitude, medicalSchool.longitude, parseFloat(radius));
        }
      }

      if (city) {
        whereConditions.push('p.city LIKE ?');
        whereParams.push(`%${city}%`);
      }

      if (min_price) {
        whereConditions.push('p.price >= ?');
        whereParams.push(min_price);
      }

      if (max_price) {
        whereConditions.push('p.price <= ?');
        whereParams.push(max_price);
      }

      if (bedrooms) {
        whereConditions.push('p.bedrooms = ?');
        whereParams.push(bedrooms);
      }

      if (property_type) {
        whereConditions.push('p.property_type = ?');
        whereParams.push(property_type);
      }

      if (furnished !== undefined) {
        whereConditions.push('p.furnished = ?');
        whereParams.push(furnished === 'true' ? 1 : 0);
      }

      if (available_from) {
        whereConditions.push('p.available_from <= ?');
        whereParams.push(available_from);
      }

      // Medical school proximity filter (general)
      if (near_medical_school === 'true') {
        joinClauses += `
          LEFT JOIN scrubhub_medical_school ms ON
          SQRT(POW(69.1 * (p.latitude - ms.latitude), 2) + POW(69.1 * (ms.longitude - p.longitude) * COS(p.latitude / 57.3), 2)) <= 5
        `;
        whereConditions.push('ms.id IS NOT NULL');
      }

      const whereClause = whereConditions.join(' AND ');

      let results = {
        properties: [],
        sublets: [],
        total_count: 0,
        popular_properties: []
      };

      // Get properties
      if (search_type === 'all' || search_type === 'properties') {
        // Get total count for properties
        const countQuery = `
          SELECT COUNT(DISTINCT p.id) as total
          FROM scrubhub_property p
          LEFT JOIN scrubhub_user u ON p.landlord_id = u.id
          ${joinClauses}
          WHERE ${whereClause}
        `;
        const countResult = await sdk.rawQuery(countQuery, whereParams);
        const propertyCount = countResult[0]?.total || 0;

        // Get properties with photos and landlord info
        const propertiesQuery = `
          SELECT ${selectFields}, 'property' as listing_type,
            (SELECT COUNT(*) FROM scrubhub_inquiry WHERE property_id = p.id) as inquiry_count,
            (SELECT COUNT(*) FROM scrubhub_favorite WHERE property_id = p.id) as favorite_count
          FROM scrubhub_property p
          LEFT JOIN scrubhub_user u ON p.landlord_id = u.id
          LEFT JOIN scrubhub_property_photo pp ON p.id = pp.property_id
          ${joinClauses}
          WHERE ${whereClause}
          GROUP BY p.id
          ORDER BY ${location || medical_school_id ? 'distance_km ASC, ' : ''}
            ${sort_by === 'popularity' ? '(inquiry_count + favorite_count * 2) DESC, ' : ''}
            p.${sort_by === 'popularity' ? 'created_at' : sort_by} ${sort_order}
          LIMIT ? OFFSET ?
        `;

        const properties = await sdk.rawQuery(propertiesQuery, [...whereParams, parseInt(limit), offset]);
        results.properties = properties;
        results.total_count += propertyCount;
      }

      // Get sublets if requested
      if ((search_type === 'all' || search_type === 'sublets') && include_sublets === 'true') {
        // Build sublet query conditions
        let subletWhereConditions = ['s.status = 0', 's.end_date >= CURDATE()']; // active and not expired
        let subletWhereParams = [];
        let subletJoinClauses = '';

        // Apply similar filters to sublets
        if (location) {
          const locationQuery = `
            SELECT * FROM scrubhub_location
            WHERE name LIKE ? OR city LIKE ? OR province_state LIKE ?
            LIMIT 1
          `;
          const locations = await sdk.rawQuery(locationQuery, [`%${location}%`, `%${location}%`, `%${location}%`]);

          if (locations.length > 0) {
            const targetLocation = locations[0];
            subletWhereConditions.push(`
              SQRT(POW(69.1 * (p.latitude - ?), 2) + POW(69.1 * (? - p.longitude) * COS(p.latitude / 57.3), 2)) <= ?
            `);
            subletWhereParams.push(targetLocation.latitude, targetLocation.longitude, parseFloat(radius));
          }
        }

        if (city) {
          subletWhereConditions.push('p.city LIKE ?');
          subletWhereParams.push(`%${city}%`);
        }

        if (min_price) {
          subletWhereConditions.push('s.price >= ?');
          subletWhereParams.push(min_price);
        }

        if (max_price) {
          subletWhereConditions.push('s.price <= ?');
          subletWhereParams.push(max_price);
        }

        const subletWhereClause = subletWhereConditions.join(' AND ');

        // Get sublets count
        const subletCountQuery = `
          SELECT COUNT(DISTINCT s.id) as total
          FROM scrubhub_sublet s
          LEFT JOIN scrubhub_property p ON s.property_id = p.id
          LEFT JOIN scrubhub_user u ON s.tenant_id = u.id
          WHERE ${subletWhereClause}
        `;
        const subletCountResult = await sdk.rawQuery(subletCountQuery, subletWhereParams);
        const subletCount = subletCountResult[0]?.total || 0;

        // Get sublets
        const subletsQuery = `
          SELECT s.*, p.title as property_title, p.address, p.city, p.province_state,
                 p.latitude, p.longitude, p.bedrooms, p.bathrooms, p.property_type,
                 u.name as tenant_name, u.phone as tenant_phone, u.email as tenant_email,
                 'sublet' as listing_type,
                 s.view_count, s.inquiry_count
          FROM scrubhub_sublet s
          LEFT JOIN scrubhub_property p ON s.property_id = p.id
          LEFT JOIN scrubhub_user u ON s.tenant_id = u.id
          WHERE ${subletWhereClause}
          ORDER BY ${sort_by === 'popularity' ? 's.popularity_score DESC, s.view_count DESC, ' : ''}
                   s.${sort_by === 'popularity' ? 'created_at' : sort_by} ${sort_order}
          LIMIT ? OFFSET ?
        `;

        const sublets = await sdk.rawQuery(subletsQuery, [...subletWhereParams, parseInt(limit), offset]);
        results.sublets = sublets;
        results.total_count += subletCount;
      }

      // Get popular properties (top 5 most popular in the area)
      if (city || location) {
        const popularQuery = `
          SELECT p.*, 'property' as listing_type,
            (SELECT COUNT(*) FROM scrubhub_inquiry WHERE property_id = p.id) as inquiry_count,
            (SELECT COUNT(*) FROM scrubhub_favorite WHERE property_id = p.id) as favorite_count
          FROM scrubhub_property p
          WHERE p.status = 0 ${city ? 'AND p.city LIKE ?' : ''}
          ORDER BY (inquiry_count + favorite_count * 2) DESC, p.created_at DESC
          LIMIT 5
        `;
        const popularParams = city ? [`%${city}%`] : [];
        const popularProperties = await sdk.rawQuery(popularQuery, popularParams);
        results.popular_properties = popularProperties;
      }

      // Combine and format all results
      const allListings = [...results.properties, ...results.sublets];
      const properties = allListings;

      // Format properties
      const formattedProperties = results.properties.map(property => ({
        ...property,
        photos: property.photo_paths ? property.photo_paths.split(',') : [],
        distance_km: property.distance_km ? parseFloat(property.distance_km).toFixed(2) : null,
        distance_to_school: property.distance_to_school ? parseFloat(property.distance_to_school).toFixed(2) : null,
        popularity_score: (property.inquiry_count || 0) + (property.favorite_count || 0) * 2,
        is_popular: ((property.inquiry_count || 0) + (property.favorite_count || 0) * 2) > 5,
        landlord: {
          name: property.landlord_name,
          phone: property.landlord_phone,
          email: property.landlord_email,
          verified: property.landlord_verified
        }
      }));

      // Format sublets
      const formattedSublets = results.sublets.map(sublet => ({
        ...sublet,
        distance_km: sublet.distance_km ? parseFloat(sublet.distance_km).toFixed(2) : null,
        popularity_score: sublet.popularity_score || 0,
        is_popular: (sublet.popularity_score || 0) > 10,
        tenant: {
          name: sublet.tenant_name,
          phone: sublet.tenant_phone,
          email: sublet.tenant_email
        }
      }));

      // Format popular properties
      const formattedPopularProperties = results.popular_properties.map(property => ({
        ...property,
        popularity_score: (property.inquiry_count || 0) + (property.favorite_count || 0) * 2,
        is_popular: true
      }));

      const totalPages = Math.ceil(results.total_count / parseInt(limit));

      res.status(200).json({
        error: false,
        data: {
          properties: formattedProperties,
          sublets: formattedSublets,
          popular_properties: formattedPopularProperties,
          combined_list: [...formattedProperties, ...formattedSublets] // For backward compatibility
        },
        list: [...formattedProperties, ...formattedSublets], // For backward compatibility
        pagination: {
          total: results.total_count,
          totalPages,
          currentPage: parseInt(page),
          limit: parseInt(limit)
        },
        search_params: {
          location: location,
          medical_school_id: medical_school_id,
          radius: parseFloat(radius),
          near_medical_school: near_medical_school === 'true',
          include_sublets: include_sublets === 'true',
          search_type: search_type,
          sort_by: sort_by
        },
        popularity_info: {
          popular_threshold: 5,
          sublet_popular_threshold: 10,
          popularity_factors: ['inquiries', 'favorites', 'views']
        }
      });
    } catch (error) {
      console.error('Properties search error:', error);
      res.status(500).json({ error: true, message: error.message });
    }
  });

  // Get single property details
  app.get('/v1/api/scrubhub/properties/:id', async (req, res) => {
    try {
      const sdk = app.get('sdk');
      sdk.setProjectId('scrubhub');
      const propertyId = req.params.id;

      // Get property with landlord and photos
      const propertyQuery = `
        SELECT
          p.*,
          u.name as landlord_name,
          u.phone as landlord_phone,
          u.email as landlord_email,
          u.verified as landlord_verified
        FROM scrubhub_property p
        LEFT JOIN scrubhub_user u ON p.landlord_id = u.id
        WHERE p.id = ? AND p.status = 0
      `;

      const properties = await sdk.rawQuery(propertyQuery, [propertyId]);

      if (properties.length === 0) {
        return res.status(404).json({ error: true, message: 'Property not found' });
      }

      const property = properties[0];

      // Get property photos
      const photos = await sdk.find('property_photo', { property_id: propertyId }, { orderBy: 'sort_order', direction: 'ASC' });

      // Track view if user is logged in
      if (req.user_id) {
        // Check if already viewed recently
        const existingView = await sdk.findOne('recently_viewed', {
          user_id: req.user_id,
          property_id: propertyId
        });

        if (existingView) {
          await sdk.updateById('recently_viewed', existingView.id, {
            viewed_at: sqlDateTimeFormat(new Date())
          });
        } else {
          await sdk.create('recently_viewed', {
            user_id: req.user_id,
            property_id: propertyId,
            viewed_at: sqlDateTimeFormat(new Date())
          });

          // Keep only last 10 views
          const recentViews = await sdk.find('recently_viewed', { user_id: req.user_id }, {
            orderBy: 'viewed_at',
            direction: 'DESC'
          });

          if (recentViews.length > 10) {
            const toDelete = recentViews.slice(10);
            for (const view of toDelete) {
              await sdk.deleteById('recently_viewed', view.id);
            }
          }
        }
      }

      // Get similar properties
      const similarQuery = `
        SELECT p.*, GROUP_CONCAT(pp.file_path ORDER BY pp.sort_order LIMIT 1) as first_photo
        FROM scrubhub_property p
        LEFT JOIN scrubhub_property_photo pp ON p.id = pp.property_id
        WHERE p.city = ? AND p.id != ? AND p.status = 0
        AND p.price BETWEEN ? AND ?
        GROUP BY p.id
        ORDER BY p.created_at DESC
        LIMIT 4
      `;

      const similarProperties = await sdk.rawQuery(similarQuery, [
        property.city,
        propertyId,
        property.price * 0.8,
        property.price * 1.2
      ]);

      res.status(200).json({
        error: false,
        model: {
          ...property,
          photos: photos.map(p => p.file_path),
          landlord: {
            name: property.landlord_name,
            phone: property.landlord_phone,
            email: property.landlord_email,
            verified: property.landlord_verified
          },
          similar_properties: similarProperties
        }
      });
    } catch (error) {
      console.error('Property details error:', error);
      res.status(500).json({ error: true, message: error.message });
    }
  });

  // Create new property listing (landlords only)
  app.post('/v1/api/scrubhub/properties', LandlordMiddleware(), async (req, res) => {
    try {
      const sdk = app.get('sdk');
      sdk.setProjectId('scrubhub');

      // Check if landlord is verified
      const user = await sdk.findById('user', req.user_id);
      if (!user.verified) {
        return res.status(403).json({
          error: true,
          message: 'Landlord verification required before listing properties'
        });
      }

      const {
        title,
        description,
        address,
        city,
        province_state,
        price,
        tier = 'free',
        duration_days = 30,
        amenities,
        property_type,
        bedrooms,
        bathrooms,
        furnished = false,
        available_from,
        photos = []
      } = req.body;

      // Validate required fields
      if (!title || !address || !city || !province_state || !price) {
        return res.status(400).json({
          error: true,
          message: 'Missing required fields: title, address, city, province_state, price'
        });
      }

      // Check free listing limits
      if (tier === 'free') {
        const freeListingCount = await sdk.count('property', {
          landlord_id: req.user_id,
          tier: 0, // 0 = free
          status: [0, 3] // active or draft
        });

        if (freeListingCount >= 2) {
          return res.status(400).json({
            error: true,
            message: 'Free listing limit exceeded. Maximum 2 free listings allowed.',
            limit: 2,
            current_count: freeListingCount
          });
        }
      }

      // Calculate listing fee
      const listingFee = PricingService.calculateListingFee(tier, duration_days);

      // Process payment if required
      if (listingFee > 0) {
        // Create Stripe payment intent
        const paymentIntent = await stripe.createPaymentIntentManual({
          amount: Math.round(listingFee * 100), // Convert to cents
          currency: 'cad',
          metadata: {
            user_id: req.user_id,
            purpose: 'property_listing',
            tier: tier,
            duration_days: duration_days
          }
        });

        // Store payment record
        await sdk.create('payment', {
          user_id: req.user_id,
          amount: listingFee,
          currency: 'CAD',
          purpose: 'property_listing',
          stripe_transaction_id: paymentIntent.id,
          status: 0, // pending
          metadata: JSON.stringify({ tier, duration_days }),
          created_at: sqlDateTimeFormat(new Date())
        });
      }

      // Create property
      const tierMapping = { 'free': 0, 'paid': 1, 'featured': 2 };
      const expiresAt = new Date(Date.now() + duration_days * 24 * 60 * 60 * 1000);

      const property = await sdk.create('property', {
        landlord_id: req.user_id,
        title,
        description,
        address,
        city,
        province_state,
        country: 'Canada',
        price,
        tier: tierMapping[tier] || 0,
        duration_days,
        status: 0, // active
        amenities: JSON.stringify(amenities || []),
        property_type,
        bedrooms,
        bathrooms,
        furnished: furnished ? 1 : 0,
        available_from,
        expires_at: sqlDateTimeFormat(expiresAt),
        created_at: sqlDateTimeFormat(new Date()),
        updated_at: sqlDateTimeFormat(new Date())
      });

      // Handle photo uploads
      if (photos && photos.length > 0) {
        for (let i = 0; i < photos.length; i++) {
          await sdk.create('property_photo', {
            property_id: property.id,
            file_path: photos[i].file_path,
            file_name: photos[i].file_name,
            sort_order: i,
            created_at: sqlDateTimeFormat(new Date())
          });
        }
      }

      res.status(201).json({
        error: false,
        model: property,
        listing_fee: listingFee,
        message: 'Property listed successfully'
      });
    } catch (error) {
      console.error('Property creation error:', error);
      res.status(500).json({ error: true, message: error.message });
    }
  });

  // Update property (landlords only)
  app.put('/v1/api/scrubhub/properties/:id', LandlordMiddleware(), async (req, res) => {
    try {
      const sdk = app.get('sdk');
      sdk.setProjectId('scrubhub');
      const propertyId = req.params.id;

      // Verify property ownership
      const property = await sdk.findOne('property', { id: propertyId, landlord_id: req.user_id });
      if (!property) {
        return res.status(404).json({ error: true, message: 'Property not found or access denied' });
      }

      const updateData = filterEmptyFields(req.body);
      updateData.updated_at = sqlDateTimeFormat(new Date());

      await sdk.updateById('property', propertyId, updateData);

      res.status(200).json({ error: false, message: 'Property updated successfully' });
    } catch (error) {
      console.error('Property update error:', error);
      res.status(500).json({ error: true, message: error.message });
    }
  });

  // Delete property (landlords only)
  app.delete('/v1/api/scrubhub/properties/:id', LandlordMiddleware(), async (req, res) => {
    try {
      const sdk = app.get('sdk');
      sdk.setProjectId('scrubhub');
      const propertyId = req.params.id;

      // Verify property ownership
      const property = await sdk.findOne('property', { id: propertyId, landlord_id: req.user_id });
      if (!property) {
        return res.status(404).json({ error: true, message: 'Property not found or access denied' });
      }

      // Soft delete by updating status
      await sdk.updateById('property', propertyId, {
        status: 2, // inactive
        updated_at: sqlDateTimeFormat(new Date())
      });

      res.status(200).json({ error: false, message: 'Property deleted successfully' });
    } catch (error) {
      console.error('Property deletion error:', error);
      res.status(500).json({ error: true, message: error.message });
    }
  });

  // Get landlord's properties
  app.get('/v1/api/scrubhub/landlord/properties', LandlordMiddleware(), async (req, res) => {
    try {
      const sdk = app.get('sdk');
      sdk.setProjectId('scrubhub');

      const { page = 1, limit = 10, status } = req.query;
      const offset = (parseInt(page) - 1) * parseInt(limit);

      let whereConditions = ['landlord_id = ?'];
      let whereParams = [req.user_id];

      if (status !== undefined) {
        whereConditions.push('status = ?');
        whereParams.push(status);
      }

      const whereClause = whereConditions.join(' AND ');

      // Get total count
      const countQuery = `SELECT COUNT(*) as total FROM scrubhub_property WHERE ${whereClause}`;
      const countResult = await sdk.rawQuery(countQuery, whereParams);
      const total = countResult[0]?.total || 0;

      // Get properties with inquiry counts
      const propertiesQuery = `
        SELECT
          p.*,
          COUNT(i.id) as inquiry_count,
          GROUP_CONCAT(pp.file_path ORDER BY pp.sort_order LIMIT 1) as first_photo
        FROM scrubhub_property p
        LEFT JOIN scrubhub_inquiry i ON p.id = i.property_id
        LEFT JOIN scrubhub_property_photo pp ON p.id = pp.property_id
        WHERE ${whereClause}
        GROUP BY p.id
        ORDER BY p.created_at DESC
        LIMIT ? OFFSET ?
      `;

      const properties = await sdk.rawQuery(propertiesQuery, [...whereParams, parseInt(limit), offset]);

      const totalPages = Math.ceil(total / parseInt(limit));

      res.status(200).json({
        error: false,
        list: properties,
        pagination: {
          total,
          totalPages,
          currentPage: parseInt(page),
          limit: parseInt(limit)
        }
      });
    } catch (error) {
      console.error('Landlord properties error:', error);
      res.status(500).json({ error: true, message: error.message });
    }
  });

  // ==================== INQUIRY MANAGEMENT APIs ====================

  // Submit property inquiry
  app.post('/v1/api/scrubhub/properties/:id/inquire', async (req, res) => {
    try {
      const sdk = app.get('sdk');
      sdk.setProjectId('scrubhub');
      const propertyId = req.params.id;

      const { name, email, phone, message } = req.body;

      // Validate required fields
      if (!name || !email) {
        return res.status(400).json({
          error: true,
          message: 'Name and email are required'
        });
      }

      // Check if property exists and is active
      const property = await sdk.findOne('property', { id: propertyId, status: 0 });
      if (!property) {
        return res.status(404).json({ error: true, message: 'Property not found or inactive' });
      }

      // Create inquiry
      const inquiry = await sdk.create('inquiry', {
        property_id: propertyId,
        name,
        email,
        phone,
        message,
        status: 0, // new
        created_at: sqlDateTimeFormat(new Date())
      });

      // Get landlord email for notification
      const landlord = await sdk.findById('user', property.landlord_id);

      // TODO: Send email notification to landlord
      // await emailService.send({
      //   to: landlord.email,
      //   template: 'new_inquiry',
      //   data: { inquiry, property, landlord }
      // });

      res.status(201).json({
        error: false,
        model: inquiry,
        message: 'Inquiry submitted successfully'
      });
    } catch (error) {
      console.error('Inquiry submission error:', error);
      res.status(500).json({ error: true, message: error.message });
    }
  });

  // Get property inquiries (landlords only)
  app.get('/v1/api/scrubhub/properties/:id/inquiries', LandlordMiddleware(), async (req, res) => {
    try {
      const sdk = app.get('sdk');
      sdk.setProjectId('scrubhub');
      const propertyId = req.params.id;

      // Verify property ownership
      const property = await sdk.findOne('property', { id: propertyId, landlord_id: req.user_id });
      if (!property) {
        return res.status(404).json({ error: true, message: 'Property not found or access denied' });
      }

      const { status, page = 1, limit = 10 } = req.query;
      const offset = (parseInt(page) - 1) * parseInt(limit);

      let whereConditions = ['property_id = ?'];
      let whereParams = [propertyId];

      if (status !== undefined) {
        whereConditions.push('status = ?');
        whereParams.push(status);
      }

      const whereClause = whereConditions.join(' AND ');

      // Get total count
      const countQuery = `SELECT COUNT(*) as total FROM scrubhub_inquiry WHERE ${whereClause}`;
      const countResult = await sdk.rawQuery(countQuery, whereParams);
      const total = countResult[0]?.total || 0;

      // Get inquiries
      const inquiriesQuery = `
        SELECT * FROM scrubhub_inquiry
        WHERE ${whereClause}
        ORDER BY created_at DESC
        LIMIT ? OFFSET ?
      `;

      const inquiries = await sdk.rawQuery(inquiriesQuery, [...whereParams, parseInt(limit), offset]);

      const totalPages = Math.ceil(total / parseInt(limit));

      res.status(200).json({
        error: false,
        list: inquiries,
        pagination: {
          total,
          totalPages,
          currentPage: parseInt(page),
          limit: parseInt(limit)
        }
      });
    } catch (error) {
      console.error('Property inquiries error:', error);
      res.status(500).json({ error: true, message: error.message });
    }
  });

  // Update inquiry status (landlords only)
  app.put('/v1/api/scrubhub/inquiries/:id/status', LandlordMiddleware(), async (req, res) => {
    try {
      const sdk = app.get('sdk');
      sdk.setProjectId('scrubhub');
      const inquiryId = req.params.id;
      const { status } = req.body;

      // Get inquiry with property info to verify ownership
      const inquiryQuery = `
        SELECT i.*, p.landlord_id
        FROM scrubhub_inquiry i
        JOIN scrubhub_property p ON i.property_id = p.id
        WHERE i.id = ?
      `;

      const inquiries = await sdk.rawQuery(inquiryQuery, [inquiryId]);

      if (inquiries.length === 0) {
        return res.status(404).json({ error: true, message: 'Inquiry not found' });
      }

      const inquiry = inquiries[0];

      if (inquiry.landlord_id !== req.user_id) {
        return res.status(403).json({ error: true, message: 'Access denied' });
      }

      await sdk.updateById('inquiry', inquiryId, {
        status: status === 'responded' ? 1 : 0
      });

      res.status(200).json({ error: false, message: 'Inquiry status updated' });
    } catch (error) {
      console.error('Inquiry status update error:', error);
      res.status(500).json({ error: true, message: error.message });
    }
  });

  // ==================== FAVORITES & RECENTLY VIEWED APIs ====================

  // Toggle favorite property
  app.post('/v1/api/scrubhub/favorites/toggle', [TokenMiddleware()], async (req, res) => {
    try {
      const sdk = app.get('sdk');
      sdk.setProjectId('scrubhub');
      const { property_id } = req.body;

      if (!property_id) {
        return res.status(400).json({ error: true, message: 'Property ID is required' });
      }

      // Check if already favorited
      const existing = await sdk.findOne('favorite', {
        user_id: req.user_id,
        property_id: property_id
      });

      if (existing) {
        await sdk.deleteById('favorite', existing.id);
        res.status(200).json({
          error: false,
          favorited: false,
          message: 'Removed from favorites'
        });
      } else {
        await sdk.create('favorite', {
          user_id: req.user_id,
          property_id: property_id,
          created_at: sqlDateTimeFormat(new Date())
        });
        res.status(200).json({
          error: false,
          favorited: true,
          message: 'Added to favorites'
        });
      }
    } catch (error) {
      console.error('Toggle favorite error:', error);
      res.status(500).json({ error: true, message: error.message });
    }
  });

  // Get user's favorite properties
  app.get('/v1/api/scrubhub/favorites', [TokenMiddleware()], async (req, res) => {
    try {
      const sdk = app.get('sdk');
      sdk.setProjectId('scrubhub');

      const { page = 1, limit = 10 } = req.query;
      const offset = (parseInt(page) - 1) * parseInt(limit);

      // Get favorites with property details
      const favoritesQuery = `
        SELECT
          p.*,
          f.created_at as favorited_at,
          GROUP_CONCAT(pp.file_path ORDER BY pp.sort_order LIMIT 1) as first_photo
        FROM scrubhub_favorite f
        JOIN scrubhub_property p ON f.property_id = p.id
        LEFT JOIN scrubhub_property_photo pp ON p.id = pp.property_id
        WHERE f.user_id = ? AND p.status = 0
        GROUP BY p.id
        ORDER BY f.created_at DESC
        LIMIT ? OFFSET ?
      `;

      const favorites = await sdk.rawQuery(favoritesQuery, [req.user_id, parseInt(limit), offset]);

      // Get total count
      const countQuery = `
        SELECT COUNT(*) as total
        FROM scrubhub_favorite f
        JOIN scrubhub_property p ON f.property_id = p.id
        WHERE f.user_id = ? AND p.status = 0
      `;
      const countResult = await sdk.rawQuery(countQuery, [req.user_id]);
      const total = countResult[0]?.total || 0;

      const totalPages = Math.ceil(total / parseInt(limit));

      res.status(200).json({
        error: false,
        list: favorites,
        pagination: {
          total,
          totalPages,
          currentPage: parseInt(page),
          limit: parseInt(limit)
        }
      });
    } catch (error) {
      console.error('Get favorites error:', error);
      res.status(500).json({ error: true, message: error.message });
    }
  });

  // Get recently viewed properties
  app.get('/v1/api/scrubhub/recently-viewed', [TokenMiddleware()], async (req, res) => {
    try {
      const sdk = app.get('sdk');
      sdk.setProjectId('scrubhub');

      const recentlyViewedQuery = `
        SELECT
          p.*,
          rv.viewed_at,
          GROUP_CONCAT(pp.file_path ORDER BY pp.sort_order LIMIT 1) as first_photo
        FROM scrubhub_recently_viewed rv
        JOIN scrubhub_property p ON rv.property_id = p.id
        LEFT JOIN scrubhub_property_photo pp ON p.id = pp.property_id
        WHERE rv.user_id = ? AND p.status = 0
        GROUP BY p.id
        ORDER BY rv.viewed_at DESC
        LIMIT 10
      `;

      const recentlyViewed = await sdk.rawQuery(recentlyViewedQuery, [req.user_id]);

      res.status(200).json({
        error: false,
        list: recentlyViewed
      });
    } catch (error) {
      console.error('Get recently viewed error:', error);
      res.status(500).json({ error: true, message: error.message });
    }
  });

  // ==================== SUBLET MANAGEMENT APIs ====================

  // Create sublet request
  app.post('/v1/api/scrubhub/sublets', [TokenMiddleware()], async (req, res) => {
    try {
      const sdk = app.get('sdk');
      sdk.setProjectId('scrubhub');

      const {
        property_id,
        price,
        start_date,
        end_date,
        service_tier = 'self_managed'
      } = req.body;

      // Validate required fields
      if (!property_id || !price || !start_date || !end_date) {
        return res.status(400).json({
          error: true,
          message: 'Missing required fields: property_id, price, start_date, end_date'
        });
      }

      // Check if property exists
      const property = await sdk.findOne('property', { id: property_id, status: 0 });
      if (!property) {
        return res.status(404).json({ error: true, message: 'Property not found or inactive' });
      }

      // Calculate service fee
      const serviceFee = PricingService.calculateSubletFee(service_tier);

      // Determine status based on service tier
      const tierMapping = { 'self_managed': 0, 'premium': 1 };
      const statusMapping = {
        'self_managed': 1, // active
        'premium': 4 // pending_admin_review
      };

      // Create sublet
      const sublet = await sdk.create('sublet', {
        property_id,
        tenant_id: req.user_id,
        price,
        start_date,
        end_date,
        service_tier: tierMapping[service_tier],
        status: statusMapping[service_tier],
        created_at: sqlDateTimeFormat(new Date())
      });

      // Process payment
      if (serviceFee > 0) {
        const paymentIntent = await stripe.createPaymentIntentManual({
          amount: Math.round(serviceFee * 100),
          currency: 'cad',
          metadata: {
            user_id: req.user_id,
            purpose: 'sublet_service',
            sublet_id: sublet.id,
            service_tier: service_tier
          }
        });

        await sdk.create('payment', {
          user_id: req.user_id,
          amount: serviceFee,
          currency: 'CAD',
          purpose: 'sublet_service',
          stripe_transaction_id: paymentIntent.id,
          status: 0, // pending
          metadata: JSON.stringify({ sublet_id: sublet.id, service_tier }),
          created_at: sqlDateTimeFormat(new Date())
        });
      }

      // If premium, create admin action for review
      if (service_tier === 'premium') {
        await sdk.create('admin_action', {
          admin_id: null,
          action_type: 'premium_sublet_review',
          target_id: sublet.id,
          target_table: 'sublet',
          notes: 'Premium sublet request pending review',
          metadata: JSON.stringify({ service_fee: serviceFee }),
          created_at: sqlDateTimeFormat(new Date())
        });
      }

      res.status(201).json({
        error: false,
        model: sublet,
        service_fee: serviceFee,
        message: service_tier === 'premium' ?
          'Premium sublet request submitted for admin review' :
          'Sublet request created successfully'
      });
    } catch (error) {
      console.error('Sublet creation error:', error);
      res.status(500).json({ error: true, message: error.message });
    }
  });

  // Get user's sublets
  app.get('/v1/api/scrubhub/sublets', [TokenMiddleware()], async (req, res) => {
    try {
      const sdk = app.get('sdk');
      sdk.setProjectId('scrubhub');

      const subletsQuery = `
        SELECT
          s.*,
          p.title as property_title,
          p.address as property_address,
          p.city as property_city
        FROM scrubhub_sublet s
        JOIN scrubhub_property p ON s.property_id = p.id
        WHERE s.tenant_id = ?
        ORDER BY s.created_at DESC
      `;

      const sublets = await sdk.rawQuery(subletsQuery, [req.user_id]);

      res.status(200).json({
        error: false,
        list: sublets
      });
    } catch (error) {
      console.error('Get sublets error:', error);
      res.status(500).json({ error: true, message: error.message });
    }
  });

  // ==================== JOB MANAGEMENT APIs ====================

  // Get all job postings
  app.get('/v1/api/scrubhub/jobs', async (req, res) => {
    try {
      const sdk = app.get('sdk');
      sdk.setProjectId('scrubhub');

      const {
        page = 1,
        limit = 10,
        location,
        type,
        department,
        min_salary,
        max_salary,
        sort_by = 'created_at',
        sort_order = 'DESC'
      } = req.query;

      const offset = (parseInt(page) - 1) * parseInt(limit);

      let whereConditions = ['j.status = 0']; // 0 = active, use table alias to avoid ambiguity
      let whereParams = [];

      if (location) {
        whereConditions.push('j.location LIKE ?');
        whereParams.push(`%${location}%`);
      }

      if (type) {
        whereConditions.push('j.type = ?');
        whereParams.push(type);
      }

      if (department) {
        whereConditions.push('j.department = ?');
        whereParams.push(department);
      }

      if (min_salary) {
        whereConditions.push('j.salary_min >= ?');
        whereParams.push(min_salary);
      }

      if (max_salary) {
        whereConditions.push('j.salary_max <= ?');
        whereParams.push(max_salary);
      }

      const whereClause = whereConditions.join(' AND ');

      // Get total count
      const countQuery = `SELECT COUNT(*) as total FROM scrubhub_job j WHERE ${whereClause}`;
      const countResult = await sdk.rawQuery(countQuery, whereParams);
      const total = countResult[0]?.total || 0;

      // Get jobs with poster info
      const jobsQuery = `
        SELECT
          j.*,
          u.name as poster_name,
          u.verified as poster_verified
        FROM scrubhub_job j
        LEFT JOIN scrubhub_user u ON j.poster_id = u.id
        WHERE ${whereClause}
        ORDER BY j.${sort_by} ${sort_order}
        LIMIT ? OFFSET ?
      `;

      const jobs = await sdk.rawQuery(jobsQuery, [...whereParams, parseInt(limit), offset]);

      const totalPages = Math.ceil(total / parseInt(limit));

      res.status(200).json({
        error: false,
        list: jobs,
        pagination: {
          total,
          totalPages,
          currentPage: parseInt(page),
          limit: parseInt(limit)
        }
      });
    } catch (error) {
      console.error('Jobs search error:', error);
      res.status(500).json({ error: true, message: error.message });
    }
  });

  // Get single job details
  app.get('/v1/api/scrubhub/jobs/:id', async (req, res) => {
    try {
      const sdk = app.get('sdk');
      sdk.setProjectId('scrubhub');
      const jobId = req.params.id;

      const jobQuery = `
        SELECT
          j.*,
          u.name as poster_name,
          u.verified as poster_verified
        FROM scrubhub_job j
        LEFT JOIN scrubhub_user u ON j.poster_id = u.id
        WHERE j.id = ? AND j.status = 0
      `;

      const jobs = await sdk.rawQuery(jobQuery, [jobId]);

      if (jobs.length === 0) {
        return res.status(404).json({ error: true, message: 'Job not found' });
      }

      const job = jobs[0];

      res.status(200).json({
        error: false,
        model: job
      });
    } catch (error) {
      console.error('Job details error:', error);
      res.status(500).json({ error: true, message: error.message });
    }
  });

  // Create job posting (hospitals/recruiters only)
  app.post('/v1/api/scrubhub/jobs', HospitalRecruiterMiddleware(), async (req, res) => {
    try {
      const sdk = app.get('sdk');
      sdk.setProjectId('scrubhub');

      const {
        title,
        description,
        location,
        type,
        department,
        salary_min,
        salary_max,
        requirements,
        contact_email,
        contact_phone,
        posting_fee
      } = req.body;

      // Validate required fields
      if (!title || !description || !location || !type) {
        return res.status(400).json({
          error: true,
          message: 'Missing required fields: title, description, location, type'
        });
      }

      // Handle posting fee
      let postingFeeAmount = 0;
      let postingFeeRate = 'standard';

      if (posting_fee && typeof posting_fee === 'object') {
        postingFeeAmount = posting_fee.amount || 0;
        postingFeeRate = posting_fee.rate || 'standard';
      }

      // Process payment if required
      if (postingFeeAmount > 0) {
        // Note: For direct charges, you would need payment_method_id from the request
        // This is a placeholder - in practice, you'd get the payment method from the user
        const { payment_method_id } = req.body;

        if (!payment_method_id) {
          return res.status(400).json({
            error: true,
            message: 'Payment method required for paid job postings'
          });
        }

        // Get user's Stripe customer ID
        const user = await sdk.findById('user', req.user_id);
        let customerId = user.stripe_customer_id;

        if (!customerId) {
          return res.status(400).json({
            error: true,
            message: 'User must have a Stripe customer account for payments'
          });
        }

        const charge = await stripe.createStripeCharge({
          amount: Math.round(postingFeeAmount * 100),
          currency: 'cad',
          customer: customerId,
          source: payment_method_id,
          description: `ScrubHub job posting fee - ${title}`,
          metadata: {
            user_id: req.user_id,
            purpose: 'job_posting',
            job_type: type,
            posting_fee_rate: postingFeeRate
          }
        });

        if (charge.error) {
          return res.status(400).json({
            error: true,
            message: charge.message || 'Payment failed'
          });
        }

        await sdk.create('payment', {
          user_id: req.user_id,
          amount: postingFeeAmount,
          currency: 'CAD',
          purpose: 'job_posting',
          stripe_transaction_id: charge.id,
          status: charge.status === 'succeeded' ? 1 : 0, // 1=completed, 0=pending
          metadata: JSON.stringify({
            job_type: type,
            posting_fee_rate: postingFeeRate,
            charge_id: charge.id
          }),
          created_at: sqlDateTimeFormat(new Date())
        });
      }

      // Create job
      const expiresAt = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000); // 30 days

      const job = await sdk.create('job', {
        poster_id: req.user_id,
        title,
        description,
        location,
        type,
        department,
        salary_min,
        salary_max,
        requirements,
        contact_email,
        contact_phone,
        posting_fee: postingFeeAmount,
        posting_fee_rate: postingFeeRate,
        status: 0, // active
        expires_at: sqlDateTimeFormat(expiresAt),
        created_at: sqlDateTimeFormat(new Date()),
        updated_at: sqlDateTimeFormat(new Date())
      });

      res.status(201).json({
        error: false,
        model: job,
        posting_fee: postingFeeAmount,
        message: 'Job posted successfully'
      });
    } catch (error) {
      console.error('Job creation error:', error);
      res.status(500).json({ error: true, message: error.message });
    }
  });

  // Apply for job
  app.post('/v1/api/scrubhub/jobs/:id/apply', async (req, res) => {
    try {
      const sdk = app.get('sdk');
      sdk.setProjectId('scrubhub');
      const jobId = req.params.id;

      const {
        applicant_name,
        email,
        phone,
        cover_letter,
        resume_url
      } = req.body;

      // Validate required fields
      if (!applicant_name || !email) {
        return res.status(400).json({
          error: true,
          message: 'Name and email are required'
        });
      }

      // Check if job exists and is active
      const job = await sdk.findOne('job', { id: jobId, status: 0 });
      if (!job) {
        return res.status(404).json({ error: true, message: 'Job not found or inactive' });
      }

      // Check if already applied
      const existingApplication = await sdk.findOne('job_applicant', {
        job_id: jobId,
        email: email
      });

      if (existingApplication) {
        return res.status(400).json({
          error: true,
          message: 'You have already applied for this job'
        });
      }

      // Create application
      const application = await sdk.create('job_applicant', {
        job_id: jobId,
        applicant_name,
        email,
        phone,
        cover_letter,
        resume_url,
        status: 0, // applied
        created_at: sqlDateTimeFormat(new Date())
      });

      res.status(201).json({
        error: false,
        model: application,
        message: 'Application submitted successfully'
      });
    } catch (error) {
      console.error('Job application error:', error);
      res.status(500).json({ error: true, message: error.message });
    }
  });

  // Get job applications (poster only)
  app.get('/v1/api/scrubhub/jobs/:id/applications', HospitalRecruiterMiddleware(), async (req, res) => {
    try {
      const sdk = app.get('sdk');
      sdk.setProjectId('scrubhub');
      const jobId = req.params.id;

      // Verify job ownership
      const job = await sdk.findOne('job', { id: jobId, poster_id: req.user_id });
      if (!job) {
        return res.status(404).json({ error: true, message: 'Job not found or access denied' });
      }

      const { status, page = 1, limit = 10 } = req.query;
      const offset = (parseInt(page) - 1) * parseInt(limit);

      let whereConditions = ['job_id = ?'];
      let whereParams = [jobId];

      if (status !== undefined) {
        whereConditions.push('status = ?');
        whereParams.push(status);
      }

      const whereClause = whereConditions.join(' AND ');

      // Get total count
      const countQuery = `SELECT COUNT(*) as total FROM scrubhub_job_applicant WHERE ${whereClause}`;
      const countResult = await sdk.rawQuery(countQuery, whereParams);
      const total = countResult[0]?.total || 0;

      // Get applications
      const applicationsQuery = `
        SELECT * FROM scrubhub_job_applicant
        WHERE ${whereClause}
        ORDER BY created_at DESC
        LIMIT ? OFFSET ?
      `;

      const applications = await sdk.rawQuery(applicationsQuery, [...whereParams, parseInt(limit), offset]);

      const totalPages = Math.ceil(total / parseInt(limit));

      res.status(200).json({
        error: false,
        list: applications,
        pagination: {
          total,
          totalPages,
          currentPage: parseInt(page),
          limit: parseInt(limit)
        }
      });
    } catch (error) {
      console.error('Job applications error:', error);
      res.status(500).json({ error: true, message: error.message });
    }
  });

  // ==================== INQUIRY MANAGEMENT APIs ====================

  // Submit property inquiry
  app.post('/v1/api/scrubhub/inquiries', async (req, res) => {
    try {
      const sdk = app.get('sdk');
      sdk.setProjectId('scrubhub');

      const { property_id, name, email, phone, message } = req.body;

      if (!property_id || !name || !email) {
        return res.status(400).json({
          error: true,
          message: 'Property ID, name, and email are required'
        });
      }

      // Verify property exists
      const property = await sdk.findById('property', property_id);
      if (!property) {
        return res.status(404).json({ error: true, message: 'Property not found' });
      }

      const inquiry = await sdk.create('inquiry', {
        property_id: property_id,
        name: name,
        email: email,
        phone: phone || null,
        message: message || null,
        status: 0, // new
        created_at: sqlDateTimeFormat(new Date())
      });

      res.status(200).json({
        error: false,
        message: 'Inquiry submitted successfully',
        inquiry_id: inquiry.id
      });
    } catch (error) {
      console.error('Submit inquiry error:', error);
      res.status(500).json({ error: true, message: error.message });
    }
  });

  // Get inquiries for landlord's properties
  app.get('/v1/api/scrubhub/inquiries', [TokenMiddleware()], async (req, res) => {
    try {
      const sdk = app.get('sdk');
      sdk.setProjectId('scrubhub');

      const { status = 'all', page = 1, limit = 20 } = req.query;
      const offset = (parseInt(page) - 1) * parseInt(limit);

      let whereConditions = ['p.landlord_id = ?'];
      let whereParams = [req.user_id];

      if (status !== 'all') {
        whereConditions.push('i.status = ?');
        whereParams.push(status === 'new' ? 0 : 1);
      }

      const whereClause = whereConditions.join(' AND ');

      const inquiriesQuery = `
        SELECT
          i.*,
          p.title as property_title,
          p.address as property_address
        FROM scrubhub_inquiry i
        JOIN scrubhub_property p ON i.property_id = p.id
        WHERE ${whereClause}
        ORDER BY i.created_at DESC
        LIMIT ? OFFSET ?
      `;

      const inquiries = await sdk.rawQuery(inquiriesQuery, [...whereParams, parseInt(limit), offset]);

      // Get total count
      const countQuery = `
        SELECT COUNT(*) as total
        FROM scrubhub_inquiry i
        JOIN scrubhub_property p ON i.property_id = p.id
        WHERE ${whereClause}
      `;
      const countResult = await sdk.rawQuery(countQuery, whereParams);
      const total = countResult[0]?.total || 0;

      res.status(200).json({
        error: false,
        list: inquiries,
        pagination: {
          total,
          totalPages: Math.ceil(total / parseInt(limit)),
          currentPage: parseInt(page),
          limit: parseInt(limit)
        }
      });
    } catch (error) {
      console.error('Get inquiries error:', error);
      res.status(500).json({ error: true, message: error.message });
    }
  });

  // Mark inquiry as responded
  app.put('/v1/api/scrubhub/inquiries/:id/respond', [TokenMiddleware()], async (req, res) => {
    try {
      const sdk = app.get('sdk');
      sdk.setProjectId('scrubhub');

      const inquiryId = req.params.id;

      // Verify inquiry belongs to user's property
      const inquiry = await sdk.rawQuery(`
        SELECT i.* FROM scrubhub_inquiry i
        JOIN scrubhub_property p ON i.property_id = p.id
        WHERE i.id = ? AND p.landlord_id = ?
      `, [inquiryId, req.user_id]);

      if (inquiry.length === 0) {
        return res.status(404).json({ error: true, message: 'Inquiry not found' });
      }

      await sdk.updateById('inquiry', inquiryId, {
        status: 1, // responded
        responded_at: sqlDateTimeFormat(new Date())
      });

      res.status(200).json({
        error: false,
        message: 'Inquiry marked as responded'
      });
    } catch (error) {
      console.error('Mark inquiry responded error:', error);
      res.status(500).json({ error: true, message: error.message });
    }
  });

  // ==================== SUBLET MANAGEMENT APIs ====================

  // Create sublet request
  app.post('/v1/api/scrubhub/sublets', [TokenMiddleware()], async (req, res) => {
    try {
      const sdk = app.get('sdk');
      sdk.setProjectId('scrubhub');

      const {
        property_id,
        start_date,
        end_date,
        price,
        description,
        service_tier = 'self_managed' // self_managed or premium
      } = req.body;

      if (!property_id || !start_date || !end_date || !price) {
        return res.status(400).json({
          error: true,
          message: 'Property ID, start date, end date, and price are required'
        });
      }

      // Verify property exists and user has access
      const property = await sdk.findById('property', property_id);
      if (!property) {
        return res.status(404).json({ error: true, message: 'Property not found' });
      }

      const serviceFees = {
        self_managed: 49.99,
        premium: 599.00
      };

      const fee = serviceFees[service_tier];
      if (!fee) {
        return res.status(400).json({ error: true, message: 'Invalid service tier' });
      }

      // Create sublet record
      const sublet = await sdk.create('sublet', {
        property_id: property_id,
        tenant_id: req.user_id,
        start_date: start_date,
        end_date: end_date,
        price: price,
        description: description,
        service_tier: service_tier,
        status: service_tier === 'premium' ? 2 : 0, // 0=pending payment, 1=active, 2=pending admin review
        created_at: sqlDateTimeFormat(new Date())
      });

      // Create payment intent
      const paymentIntent = await stripe.createPaymentIntentManual({
        amount: Math.round(fee * 100),
        currency: 'cad',
        metadata: {
          user_id: req.user_id,
          purpose: 'sublet_fee',
          sublet_id: sublet.id,
          service_tier: service_tier
        }
      });

      // Create payment record
      await sdk.create('payment', {
        user_id: req.user_id,
        amount: fee,
        currency: 'CAD',
        purpose: 'sublet_fee',
        stripe_transaction_id: paymentIntent.id,
        status: 0, // pending
        metadata: JSON.stringify({ sublet_id: sublet.id, service_tier }),
        created_at: sqlDateTimeFormat(new Date())
      });

      res.status(200).json({
        error: false,
        sublet_id: sublet.id,
        payment_intent: paymentIntent.client_secret,
        fee: fee,
        message: service_tier === 'premium'
          ? 'Premium sublet request created. Complete payment and await admin review.'
          : 'Sublet request created. Complete payment to activate listing.'
      });
    } catch (error) {
      console.error('Create sublet error:', error);
      res.status(500).json({ error: true, message: error.message });
    }
  });

  // Get user's sublets
  app.get('/v1/api/scrubhub/sublets', [TokenMiddleware()], async (req, res) => {
    try {
      const sdk = app.get('sdk');
      sdk.setProjectId('scrubhub');

      const { page = 1, limit = 10 } = req.query;
      const offset = (parseInt(page) - 1) * parseInt(limit);

      const subletsQuery = `
        SELECT
          s.*,
          p.title as property_title,
          p.address as property_address
        FROM scrubhub_sublet s
        JOIN scrubhub_property p ON s.property_id = p.id
        WHERE s.tenant_id = ?
        ORDER BY s.created_at DESC
        LIMIT ? OFFSET ?
      `;

      const sublets = await sdk.rawQuery(subletsQuery, [req.user_id, parseInt(limit), offset]);

      // Get total count
      const total = await sdk.count('sublet', { tenant_id: req.user_id });

      res.status(200).json({
        error: false,
        list: sublets,
        pagination: {
          total,
          totalPages: Math.ceil(total / parseInt(limit)),
          currentPage: parseInt(page),
          limit: parseInt(limit)
        }
      });
    } catch (error) {
      console.error('Get sublets error:', error);
      res.status(500).json({ error: true, message: error.message });
    }
  });

  // ==================== N9 FORM GENERATION API ====================

  // Generate N9 form
  app.post('/v1/api/scrubhub/n9-form', [TokenMiddleware()], async (req, res) => {
    try {
      const sdk = app.get('sdk');
      sdk.setProjectId('scrubhub');

      const {
        tenant_name,
        tenant_address,
        landlord_name,
        landlord_address,
        rental_address,
        termination_date,
        reason
      } = req.body;

      if (!tenant_name || !landlord_name || !rental_address || !termination_date) {
        return res.status(400).json({
          error: true,
          message: 'Tenant name, landlord name, rental address, and termination date are required'
        });
      }

      const fee = 19.99; // N9 form generation fee

      // Create payment intent
      const paymentIntent = await stripe.createPaymentIntentManual({
        amount: Math.round(fee * 100),
        currency: 'cad',
        metadata: {
          user_id: req.user_id,
          purpose: 'n9_form',
          tenant_name: tenant_name,
          landlord_name: landlord_name
        }
      });

      // Create tenant notice record
      const notice = await sdk.create('tenant_notice', {
        user_id: req.user_id,
        notice_type: 'N9',
        tenant_name: tenant_name,
        tenant_address: tenant_address,
        landlord_name: landlord_name,
        landlord_address: landlord_address,
        rental_address: rental_address,
        termination_date: termination_date,
        reason: reason,
        status: 0, // pending payment
        created_at: sqlDateTimeFormat(new Date())
      });

      // Create payment record
      await sdk.create('payment', {
        user_id: req.user_id,
        amount: fee,
        currency: 'CAD',
        purpose: 'n9_form',
        stripe_transaction_id: paymentIntent.id,
        status: 0, // pending
        metadata: JSON.stringify({ notice_id: notice.id }),
        created_at: sqlDateTimeFormat(new Date())
      });

      res.status(200).json({
        error: false,
        notice_id: notice.id,
        payment_intent: paymentIntent.client_secret,
        fee: fee,
        message: 'Complete payment to generate and download N9 form'
      });
    } catch (error) {
      console.error('N9 form generation error:', error);
      res.status(500).json({ error: true, message: error.message });
    }
  });

  // Download N9 form (after payment)
  app.get('/v1/api/scrubhub/n9-form/:id/download', [TokenMiddleware()], async (req, res) => {
    try {
      const sdk = app.get('sdk');
      sdk.setProjectId('scrubhub');

      const noticeId = req.params.id;

      const notice = await sdk.findOne('tenant_notice', {
        id: noticeId,
        user_id: req.user_id,
        status: 1 // paid
      });

      if (!notice) {
        return res.status(404).json({
          error: true,
          message: 'N9 form not found or payment not completed'
        });
      }

      if (!notice.pdf_url) {
        return res.status(400).json({
          error: true,
          message: 'PDF not yet generated. Please try again in a few moments.'
        });
      }

      res.status(200).json({
        error: false,
        download_url: notice.pdf_url,
        notice: notice
      });
    } catch (error) {
      console.error('N9 download error:', error);
      res.status(500).json({ error: true, message: error.message });
    }
  });

  // ==================== DUPLICATE JOB MANAGEMENT APIs (REMOVED) ====================

  // REMOVED: Duplicate jobs endpoint - using the one above instead
  // This duplicate endpoint has been removed - using the enhanced version above

  // Create job posting
  app.post('/v1/api/scrubhub/jobs', [TokenMiddleware()], async (req, res) => {
    try {
      const sdk = app.get('sdk');
      sdk.setProjectId('scrubhub');

      const {
        title,
        description,
        location,
        location_type = 'in_person', // in_person, remote, hybrid
        job_type = 'full_time', // full_time, part_time, permanent, fixed_term_contract, casual, seasonal, freelance, apprenticeship, internship, co_op
        type, // legacy field for backward compatibility
        has_start_date = false,
        start_date = null,
        payment_type = 'yearly', // hourly, daily, weekly, monthly, yearly, per_shift, commission, contract
        show_pay_by = 'range', // range, starting_amount, maximum_amount, exact_amount
        salary_min,
        salary_max,
        requirements,
        benefits,
        daily_updates_email = null,
        resume_required = true,
        allow_email_contact = false,
        has_application_deadline = false,
        application_deadline = null,
        posting_duration_days = 30,
        is_paid_posting = false
      } = req.body;

      if (!title || !description || !location) {
        return res.status(400).json({
          error: true,
          message: 'Title, description, and location are required'
        });
      }

      // Validate enum values
      const validLocationTypes = ['in_person', 'remote', 'hybrid'];
      const validJobTypes = ['full_time', 'part_time', 'permanent', 'fixed_term_contract', 'casual', 'seasonal', 'freelance', 'apprenticeship', 'internship', 'co_op'];
      const validPaymentTypes = ['hourly', 'daily', 'weekly', 'monthly', 'yearly', 'per_shift', 'commission', 'contract'];
      const validShowPayBy = ['range', 'starting_amount', 'maximum_amount', 'exact_amount'];

      if (!validLocationTypes.includes(location_type)) {
        return res.status(400).json({
          error: true,
          message: `Invalid location_type. Must be one of: ${validLocationTypes.join(', ')}`
        });
      }

      if (!validJobTypes.includes(job_type)) {
        return res.status(400).json({
          error: true,
          message: `Invalid job_type. Must be one of: ${validJobTypes.join(', ')}`
        });
      }

      if (!validPaymentTypes.includes(payment_type)) {
        return res.status(400).json({
          error: true,
          message: `Invalid payment_type. Must be one of: ${validPaymentTypes.join(', ')}`
        });
      }

      if (!validShowPayBy.includes(show_pay_by)) {
        return res.status(400).json({
          error: true,
          message: `Invalid show_pay_by. Must be one of: ${validShowPayBy.join(', ')}`
        });
      }

      // Validate start date if provided
      if (has_start_date && !start_date) {
        return res.status(400).json({
          error: true,
          message: 'start_date is required when has_start_date is true'
        });
      }

      // Validate application deadline if provided
      if (has_application_deadline && !application_deadline) {
        return res.status(400).json({
          error: true,
          message: 'application_deadline is required when has_application_deadline is true'
        });
      }

      // Verify user is hospital or recruiter
      const user = await sdk.findById('user', req.user_id);
      if (![2, 3].includes(user.role)) { // 2=hospital, 3=recruiter
        return res.status(403).json({
          error: true,
          message: 'Only hospitals and recruiters can post jobs'
        });
      }

      let jobData = {
        poster_id: req.user_id,
        title: title,
        description: description,
        location: location,
        location_type: location_type,
        job_type: job_type,
        type: type || job_type, // Keep legacy field for backward compatibility
        has_start_date: has_start_date ? 1 : 0,
        start_date: has_start_date && start_date ? start_date : null,
        payment_type: payment_type,
        show_pay_by: show_pay_by,
        salary_min: salary_min || null,
        salary_max: salary_max || null,
        requirements: requirements || null,
        benefits: benefits || null,
        daily_updates_email: daily_updates_email,
        resume_required: resume_required ? 1 : 0,
        allow_email_contact: allow_email_contact ? 1 : 0,
        has_application_deadline: has_application_deadline ? 1 : 0,
        application_deadline: has_application_deadline && application_deadline ? application_deadline : null,
        posting_duration_days: posting_duration_days || 30,
        status: is_paid_posting ? 2 : 0, // 0=active, 1=inactive, 2=pending payment
        created_at: sqlDateTimeFormat(new Date())
      };

      if (is_paid_posting) {
        const fee = PricingService.calculateJobPostingFee(job_type, posting_duration_days);

        // Create payment intent
        const paymentIntent = await stripe.createPaymentIntentManual({
          amount: Math.round(fee * 100),
          currency: 'cad',
          metadata: {
            user_id: req.user_id,
            purpose: 'job_posting',
            title: title,
            job_type: job_type,
            location_type: location_type,
            posting_duration_days: posting_duration_days.toString()
          }
        });

        const job = await sdk.create('job', jobData);

        // Create payment record
        await sdk.create('payment', {
          user_id: req.user_id,
          amount: fee,
          currency: 'CAD',
          purpose: 'job_posting',
          stripe_transaction_id: paymentIntent.id,
          status: 0, // pending
          metadata: JSON.stringify({
            job_id: job.id,
            job_type: job_type,
            location_type: location_type,
            posting_duration_days: posting_duration_days,
            title: title
          }),
          created_at: sqlDateTimeFormat(new Date())
        });

        res.status(200).json({
          error: false,
          job_id: job.id,
          payment_intent: paymentIntent.client_secret,
          fee: fee,
          message: 'Complete payment to activate job posting'
        });
      } else {
        const job = await sdk.create('job', jobData);

        res.status(200).json({
          error: false,
          job: job,
          message: 'Job posted successfully'
        });
      }
    } catch (error) {
      console.error('Create job error:', error);
      res.status(500).json({ error: true, message: error.message });
    }
  });

  // Get job applicants (for job poster)
  app.get('/v1/api/scrubhub/jobs/:id/applicants', [TokenMiddleware()], async (req, res) => {
    try {
      const sdk = app.get('sdk');
      sdk.setProjectId('scrubhub');

      const jobId = req.params.id;

      // Verify job belongs to user
      const job = await sdk.findOne('job', { id: jobId, poster_id: req.user_id });
      if (!job) {
        return res.status(404).json({ error: true, message: 'Job not found' });
      }

      const applicants = await sdk.find('job_applicant',
        { job_id: jobId },
        { orderBy: 'created_at', direction: 'DESC' }
      );

      res.status(200).json({
        error: false,
        list: applicants
      });
    } catch (error) {
      console.error('Get applicants error:', error);
      res.status(500).json({ error: true, message: error.message });
    }
  });

  // Apply to job
  app.post('/v1/api/scrubhub/jobs/:id/apply', async (req, res) => {
    try {
      const sdk = app.get('sdk');
      sdk.setProjectId('scrubhub');

      const jobId = req.params.id;
      const { applicant_name, email, resume_url, cover_letter } = req.body;

      if (!applicant_name || !email) {
        return res.status(400).json({
          error: true,
          message: 'Applicant name and email are required'
        });
      }

      // Verify job exists
      const job = await sdk.findById('job', jobId);
      if (!job || job.status !== 0) {
        return res.status(404).json({ error: true, message: 'Job not found or inactive' });
      }

      // Check for duplicate application
      const existingApplication = await sdk.findOne('job_applicant', {
        job_id: jobId,
        email: email
      });

      if (existingApplication) {
        return res.status(400).json({
          error: true,
          message: 'You have already applied to this job'
        });
      }

      const application = await sdk.create('job_applicant', {
        job_id: jobId,
        applicant_name: applicant_name,
        email: email,
        resume_url: resume_url || null,
        cover_letter: cover_letter || null,
        status: 0, // new
        created_at: sqlDateTimeFormat(new Date())
      });

      res.status(200).json({
        error: false,
        application: application,
        message: 'Application submitted successfully'
      });
    } catch (error) {
      console.error('Apply to job error:', error);
      res.status(500).json({ error: true, message: error.message });
    }
  });

  // Update applicant status
  app.put('/v1/api/scrubhub/applicants/:id/status', [TokenMiddleware()], async (req, res) => {
    try {
      const sdk = app.get('sdk');
      sdk.setProjectId('scrubhub');

      const applicantId = req.params.id;
      const { status } = req.body; // 'interviewing', 'rejected', 'hired'

      const statusMapping = {
        'interviewing': 1,
        'rejected': 2,
        'hired': 3
      };

      if (!statusMapping.hasOwnProperty(status)) {
        return res.status(400).json({
          error: true,
          message: 'Invalid status. Use: interviewing, rejected, or hired'
        });
      }

      // Verify applicant belongs to user's job
      const applicant = await sdk.rawQuery(`
        SELECT ja.* FROM scrubhub_job_applicant ja
        JOIN scrubhub_job j ON ja.job_id = j.id
        WHERE ja.id = ? AND j.poster_id = ?
      `, [applicantId, req.user_id]);

      if (applicant.length === 0) {
        return res.status(404).json({ error: true, message: 'Applicant not found' });
      }

      await sdk.updateById('job_applicant', applicantId, {
        status: statusMapping[status],
        updated_at: sqlDateTimeFormat(new Date())
      });

      res.status(200).json({
        error: false,
        message: `Applicant status updated to ${status}`
      });
    } catch (error) {
      console.error('Update applicant status error:', error);
      res.status(500).json({ error: true, message: error.message });
    }
  });

  // ==================== MARKETPLACE APIs ====================

  // Get marketplace items
  app.get('/v1/api/scrubhub/marketplace', async (req, res) => {
    try {
      const sdk = app.get('sdk');
      sdk.setProjectId('scrubhub');

      const {
        page = 1,
        limit = 10,
        category,
        item_condition,
        min_price,
        max_price,
        search,
        sort_by = 'created_at',
        sort_order = 'DESC'
      } = req.query;

      const offset = (parseInt(page) - 1) * parseInt(limit);

      let whereConditions = ['m.status = 0']; // 0 = active, use table alias to avoid ambiguity
      let whereParams = [];

      if (category) {
        whereConditions.push('m.category = ?');
        whereParams.push(category);
      }

      if (item_condition) {
        whereConditions.push('m.item_condition = ?');
        whereParams.push(item_condition);
      }

      if (min_price) {
        whereConditions.push('m.price >= ?');
        whereParams.push(min_price);
      }

      if (max_price) {
        whereConditions.push('m.price <= ?');
        whereParams.push(max_price);
      }

      if (search) {
        whereConditions.push('(m.title LIKE ? OR m.description LIKE ?)');
        whereParams.push(`%${search}%`, `%${search}%`);
      }

      const whereClause = whereConditions.join(' AND ');

      // Get total count
      const countQuery = `
        SELECT COUNT(*) as total
        FROM scrubhub_marketplace_item m
        LEFT JOIN scrubhub_user u ON m.seller_id = u.id
        WHERE ${whereClause}
      `;
      const countResult = await sdk.rawQuery(countQuery, whereParams);
      const total = countResult[0]?.total || 0;

      // Get items with seller info
      const itemsQuery = `
        SELECT
          m.*,
          u.name as seller_name,
          u.verified as seller_verified,
          CASE WHEN m.phone_revealed = 1 THEN u.phone ELSE 'Contact for phone' END as seller_phone
        FROM scrubhub_marketplace_item m
        LEFT JOIN scrubhub_user u ON m.seller_id = u.id
        WHERE ${whereClause}
        ORDER BY m.${sort_by} ${sort_order}
        LIMIT ? OFFSET ?
      `;

      const items = await sdk.rawQuery(itemsQuery, [...whereParams, parseInt(limit), offset]);

      // Add phone reveal fee calculation
      const formattedItems = items.map(item => ({
        ...item,
        phone_reveal_fee: item.phone_revealed ? 0 : calculatePhoneRevealFee(item.price),
        can_reveal_phone: item.phone_revealed === 0
      }));

      const totalPages = Math.ceil(total / parseInt(limit));

      res.status(200).json({
        error: false,
        list: formattedItems,
        pagination: {
          total,
          totalPages,
          currentPage: parseInt(page),
          limit: parseInt(limit)
        }
      });
    } catch (error) {
      console.error('Marketplace search error:', error);
      res.status(500).json({ error: true, message: error.message });
    }
  });

  // Helper function to calculate phone reveal fee based on BRD pricing logic
  function calculatePhoneRevealFee(itemPrice) {
    if (itemPrice >= 1000) return 19.99;
    if (itemPrice >= 500) return 9.99;
    if (itemPrice >= 100) return 4.99;
    return 2.99;
  }

  // Helper function for job status text
  function getJobStatusText(status) {
    const statusMap = {
      0: 'Active',
      1: 'Inactive',
      2: 'Expired',
      3: 'Draft',
      4: 'Filled'
    };
    return statusMap[status] || 'Unknown';
  }

  // Helper function for job type text
  function getJobTypeText(type) {
    const typeMap = {
      'residency': 'Residency',
      'fellowship': 'Fellowship',
      'staff': 'Staff Position',
      'locum': 'Locum Tenens',
      'part_time': 'Part Time',
      'full_time': 'Full Time',
      'contract': 'Contract'
    };
    return typeMap[type] || type || 'Not specified';
  }

  // Helper function for salary formatting
  function formatSalaryRange(min, max) {
    if (!min && !max) return 'Salary not specified';
    if (min && max) return `$${parseInt(min).toLocaleString()} - $${parseInt(max).toLocaleString()}`;
    if (min) return `From $${parseInt(min).toLocaleString()}`;
    if (max) return `Up to $${parseInt(max).toLocaleString()}`;
    return 'Salary not specified';
  }

  // Reveal phone number for marketplace item
  app.post('/v1/api/scrubhub/marketplace/:id/reveal-phone', [TokenMiddleware()], async (req, res) => {
    try {
      const sdk = app.get('sdk');
      sdk.setProjectId('scrubhub');

      const itemId = req.params.id;

      const item = await sdk.findById('marketplace_item', itemId);
      if (!item || item.status !== 0) {
        return res.status(404).json({ error: true, message: 'Item not found' });
      }

      if (item.phone_revealed === 1) {
        // Phone already revealed, get seller info
        const seller = await sdk.findById('user', item.seller_id);
        return res.status(200).json({
          error: false,
          phone: seller.phone,
          message: 'Phone number already revealed'
        });
      }

      const fee = calculatePhoneRevealFee(item.price);

      // Create payment intent
      const paymentIntent = await stripe.createPaymentIntentManual({
        amount: Math.round(fee * 100),
        currency: 'cad',
        metadata: {
          user_id: req.user_id,
          purpose: 'phone_reveal',
          item_id: itemId
        }
      });

      // Create payment record
      await sdk.create('payment', {
        user_id: req.user_id,
        amount: fee,
        currency: 'CAD',
        purpose: 'phone_reveal',
        stripe_transaction_id: paymentIntent.id,
        status: 0, // pending
        metadata: JSON.stringify({ item_id: itemId }),
        created_at: sqlDateTimeFormat(new Date())
      });

      res.status(200).json({
        error: false,
        payment_intent: paymentIntent.client_secret,
        fee: fee,
        message: 'Complete payment to reveal phone number'
      });
    } catch (error) {
      console.error('Phone reveal error:', error);
      res.status(500).json({ error: true, message: error.message });
    }
  });

  // Create marketplace item
  app.post('/v1/api/scrubhub/marketplace', [TokenMiddleware()], async (req, res) => {
    try {
      const sdk = app.get('sdk');
      sdk.setProjectId('scrubhub');

      const {
        title,
        description,
        price,
        item_condition,
        category,
        images = []
      } = req.body;

      // Validate required fields
      if (!title || !price) {
        return res.status(400).json({
          error: true,
          message: 'Title and price are required'
        });
      }

      // Validate images array
      if (images && !Array.isArray(images)) {
        return res.status(400).json({
          error: true,
          message: 'Images must be an array'
        });
      }

      // Limit number of images
      if (images.length > 10) {
        return res.status(400).json({
          error: true,
          message: 'Maximum 10 images allowed'
        });
      }

      // Calculate listing fee
      const listingFee = PricingService.calculateMarketplaceFee(parseFloat(price));

      // Create item
      const item = await sdk.create('marketplace_item', {
        seller_id: req.user_id,
        title,
        description,
        price,
        item_condition,
        category,
        status: listingFee > 0 ? 0 : 0, // pending_approval if fee required, active if free
        phone_revealed: 0,
        listing_fee: listingFee,
        created_at: sqlDateTimeFormat(new Date())
      });

      // Process payment if required
      if (listingFee > 0) {
        const paymentIntent = await stripe.createPaymentIntentManual({
          amount: Math.round(listingFee * 100),
          currency: 'cad',
          metadata: {
            user_id: req.user_id,
            purpose: 'marketplace_listing',
            item_id: item.id
          }
        });

        await sdk.create('payment', {
          user_id: req.user_id,
          amount: listingFee,
          currency: 'CAD',
          purpose: 'marketplace_listing',
          stripe_transaction_id: paymentIntent.id,
          status: 0, // pending
          metadata: JSON.stringify({ item_id: item.id }),
          created_at: sqlDateTimeFormat(new Date())
        });
      }

      // Add images if provided
      if (images.length > 0) {
        for (let i = 0; i < images.length; i++) {
          const imagePath = images[i];
          if (imagePath && typeof imagePath === 'string') {
            await sdk.create('marketplace_item_photo', {
              item_id: item.id,
              file_path: imagePath,
              sort_order: i,
              created_at: sqlDateTimeFormat(new Date())
            });
          }
        }
      }

      // Get item with photos for response
      let itemWithPhotos = item;
      if (images.length > 0) {
        const itemQuery = await sdk.rawQuery(`
          SELECT
            mi.*,
            JSON_ARRAYAGG(
              CASE WHEN mip.file_path IS NOT NULL
              THEN JSON_OBJECT('id', mip.id, 'file_path', mip.file_path, 'sort_order', mip.sort_order)
              ELSE NULL END
            ) as photos
          FROM scrubhub_marketplace_item mi
          LEFT JOIN scrubhub_marketplace_item_photo mip ON mi.id = mip.item_id
          WHERE mi.id = ?
          GROUP BY mi.id
        `, [item.id]);

        itemWithPhotos = itemQuery[0];
        if (itemWithPhotos.photos && itemWithPhotos.photos[0] === null) {
          itemWithPhotos.photos = [];
        }
      }

      res.status(201).json({
        error: false,
        model: itemWithPhotos,
        listing_fee: listingFee,
        message: listingFee > 0 ? 'Item submitted for approval after payment' : 'Item listed successfully'
      });
    } catch (error) {
      console.error('Marketplace item creation error:', error);
      res.status(500).json({ error: true, message: error.message });
    }
  });

  // Reveal phone number (requires payment)
  app.post('/v1/api/scrubhub/marketplace/:id/reveal-phone', [TokenMiddleware()], async (req, res) => {
    try {
      const sdk = app.get('sdk');
      sdk.setProjectId('scrubhub');
      const itemId = req.params.id;

      // Get item
      const item = await sdk.findOne('marketplace_item', { id: itemId, status: 0 });
      if (!item) {
        return res.status(404).json({ error: true, message: 'Item not found or inactive' });
      }

      // Check if phone already revealed for this user
      const existingPayment = await sdk.findOne('payment', {
        user_id: req.user_id,
        purpose: 'phone_reveal',
        status: 1, // completed
        metadata: JSON.stringify({ item_id: itemId })
      });

      if (existingPayment) {
        // Get seller phone
        const seller = await sdk.findById('user', item.seller_id);
        return res.status(200).json({
          error: false,
          phone: seller.phone,
          message: 'Phone number already revealed'
        });
      }

      // Create payment for phone reveal
      const revealFee = 2.99;
      const paymentIntent = await stripe.createPaymentIntentManual({
        amount: Math.round(revealFee * 100),
        currency: 'cad',
        metadata: {
          user_id: req.user_id,
          purpose: 'phone_reveal',
          item_id: itemId
        }
      });

      await sdk.create('payment', {
        user_id: req.user_id,
        amount: revealFee,
        currency: 'CAD',
        purpose: 'phone_reveal',
        stripe_transaction_id: paymentIntent.id,
        status: 0, // pending
        metadata: JSON.stringify({ item_id: itemId }),
        created_at: sqlDateTimeFormat(new Date())
      });

      res.status(200).json({
        error: false,
        payment_intent: paymentIntent.client_secret,
        fee: revealFee,
        message: 'Complete payment to reveal phone number'
      });
    } catch (error) {
      console.error('Phone reveal error:', error);
      res.status(500).json({ error: true, message: error.message });
    }
  });

  // ==================== CREDIT REPORT APIs ====================

  // Request credit report
  app.post('/v1/api/scrubhub/credit-report', [TokenMiddleware()], async (req, res) => {
    try {
      const sdk = app.get('sdk');
      sdk.setProjectId('scrubhub');

      const { personal_info } = req.body;

      // Validate required fields
      if (!personal_info || !personal_info.first_name || !personal_info.last_name) {
        return res.status(400).json({
          error: true,
          message: 'Personal information is required'
        });
      }

      // Check if user already has a valid credit report
      const existingReport = await sdk.findOne('credit_report', {
        user_id: req.user_id,
        expires_at: { '>=': sqlDateTimeFormat(new Date()) }
      });

      if (existingReport) {
        return res.status(200).json({
          error: false,
          model: existingReport,
          message: 'Valid credit report already exists'
        });
      }

      // Call Trustee API
      try {
        const trusteeResponse = await axios.post(config.trustee_api_url, {
          first_name: personal_info.first_name,
          last_name: personal_info.last_name,
          date_of_birth: personal_info.date_of_birth,
          sin: personal_info.sin,
          address: personal_info.address
        }, {
          headers: {
            'Authorization': `Bearer ${config.trustee_api_key}`,
            'Content-Type': 'application/json'
          }
        });

        const trusteeData = trusteeResponse.data;
        const expiresAt = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000); // 30 days

        // Create credit report record
        const creditReport = await sdk.create('credit_report', {
          user_id: req.user_id,
          trustee_reference: trusteeData.reference_id,
          report_url: trusteeData.report_url,
          expires_at: sqlDateTimeFormat(expiresAt),
          created_at: sqlDateTimeFormat(new Date())
        });

        res.status(201).json({
          error: false,
          model: creditReport,
          message: 'Credit report generated successfully'
        });
      } catch (trusteeError) {
        console.error('Trustee API error:', trusteeError);
        res.status(500).json({
          error: true,
          message: 'Failed to generate credit report. Please try again later.'
        });
      }
    } catch (error) {
      console.error('Credit report error:', error);
      res.status(500).json({ error: true, message: error.message });
    }
  });

  // ==================== N9 NOTICE APIs ====================

  // Generate N9 notice
  app.post('/v1/api/scrubhub/n9-notice', [TokenMiddleware()], async (req, res) => {
    try {
      const sdk = app.get('sdk');
      sdk.setProjectId('scrubhub');

      const { form_data, notice_type = 'N9' } = req.body;

      // Validate required fields
      if (!form_data) {
        return res.status(400).json({
          error: true,
          message: 'Form data is required'
        });
      }

      // Validate signature section fields
      const { signature_section } = form_data;
      if (!signature_section) {
        return res.status(400).json({
          error: true,
          message: 'Signature section is required'
        });
      }

      const requiredSignatureFields = [
        'signed_by', // 'tenant' or 'representative'
        'first_name',
        'last_name',
        'phone_number',
        'signature' // typed full name
      ];

      const missingFields = requiredSignatureFields.filter(field =>
        !signature_section[field] || signature_section[field].trim() === ''
      );

      if (missingFields.length > 0) {
        return res.status(400).json({
          error: true,
          message: `Missing required signature fields: ${missingFields.join(', ')}`,
          missing_fields: missingFields
        });
      }

      // Validate signed_by field
      if (!['tenant', 'representative'].includes(signature_section.signed_by)) {
        return res.status(400).json({
          error: true,
          message: 'signed_by must be either "tenant" or "representative"'
        });
      }

      // Validate phone number format (basic validation)
      const phoneRegex = /^\+?1?[-.\s]?\(?[0-9]{3}\)?[-.\s]?[0-9]{3}[-.\s]?[0-9]{4}$/;
      if (!phoneRegex.test(signature_section.phone_number)) {
        return res.status(400).json({
          error: true,
          message: 'Invalid phone number format'
        });
      }

      // Add timestamp and user info to form data
      const enrichedFormData = {
        ...form_data,
        signature_section: {
          ...signature_section,
          signed_at: new Date().toISOString(),
          ip_address: req.ip || req.connection.remoteAddress,
          user_agent: req.get('User-Agent')
        },
        submission_info: {
          user_id: req.user_id,
          submitted_at: new Date().toISOString(),
          notice_type
        }
      };

      // Call N9 PDF generation service
      try {
        const pdfResponse = await axios.post(config.n9_service_url, {
          notice_type,
          form_data: enrichedFormData
        }, {
          headers: {
            'Authorization': `Bearer ${config.n9_service_key}`,
            'Content-Type': 'application/json'
          }
        });

        const pdfData = pdfResponse.data;

        // Create tenant notice record
        const tenantNotice = await sdk.create('tenant_notice', {
          user_id: req.user_id,
          notice_type,
          pdf_url: pdfData.pdf_url,
          form_data: JSON.stringify(enrichedFormData),
          created_at: sqlDateTimeFormat(new Date())
        });

        res.status(201).json({
          error: false,
          model: tenantNotice,
          pdf_url: pdfData.pdf_url,
          signature_info: {
            signed_by: signature_section.signed_by,
            signer_name: `${signature_section.first_name} ${signature_section.last_name}`,
            signed_at: enrichedFormData.signature_section.signed_at
          },
          message: 'N9 notice generated successfully with electronic signature'
        });
      } catch (pdfError) {
        console.error('N9 PDF generation error:', pdfError);
        res.status(500).json({
          error: true,
          message: 'Failed to generate N9 notice. Please try again later.'
        });
      }
    } catch (error) {
      console.error('N9 notice error:', error);
      res.status(500).json({ error: true, message: error.message });
    }
  });

  // Get user's notices
  app.get('/v1/api/scrubhub/notices', [TokenMiddleware()], async (req, res) => {
    try {
      const sdk = app.get('sdk');
      sdk.setProjectId('scrubhub');

      const notices = await sdk.find('tenant_notice', { user_id: req.user_id }, {
        orderBy: 'created_at',
        direction: 'DESC'
      });

      res.status(200).json({
        error: false,
        list: notices
      });
    } catch (error) {
      console.error('Get notices error:', error);
      res.status(500).json({ error: true, message: error.message });
    }
  });

  // ==================== USER JOB MANAGEMENT APIs ====================

  // Get user's created jobs (for all authenticated users)
  app.get('/v1/api/scrubhub/user/jobs', [TokenMiddleware()], async (req, res) => {
    try {
      const sdk = app.get('sdk');
      sdk.setProjectId('scrubhub');

      const {
        page = 1,
        limit = 10,
        status,
        type,
        sort_by = 'created_at',
        sort_order = 'DESC'
      } = req.query;

      const offset = (parseInt(page) - 1) * parseInt(limit);

      let whereConditions = ['j.poster_id = ?'];
      let whereParams = [req.user_id];

      if (status !== undefined) {
        whereConditions.push('j.status = ?');
        whereParams.push(status);
      }

      if (type) {
        whereConditions.push('(j.type = ? OR j.job_type = ?)');
        whereParams.push(type, type);
      }

      const whereClause = whereConditions.join(' AND ');

      // Get total count
      const countQuery = `
        SELECT COUNT(*) as total
        FROM scrubhub_job j
        WHERE ${whereClause}
      `;
      const countResult = await sdk.rawQuery(countQuery, whereParams);
      const total = countResult[0]?.total || 0;

      // Get jobs with additional details
      const jobsQuery = `
        SELECT
          j.*,
          u.name as poster_name,
          u.verified as poster_verified,
          u.role as poster_role,
          (SELECT COUNT(*) FROM scrubhub_job_applicant ja WHERE ja.job_id = j.id) as applicant_count,
          (SELECT COUNT(*) FROM scrubhub_job_applicant ja WHERE ja.job_id = j.id AND ja.status = 'pending') as pending_applications,
          (SELECT COUNT(*) FROM scrubhub_job_applicant ja WHERE ja.job_id = j.id AND ja.status = 'hired') as hired_count
        FROM scrubhub_job j
        LEFT JOIN scrubhub_user u ON j.poster_id = u.id
        WHERE ${whereClause}
        ORDER BY j.${sort_by} ${sort_order}
        LIMIT ? OFFSET ?
      `;

      const jobs = await sdk.rawQuery(jobsQuery, [...whereParams, parseInt(limit), offset]);

      // Format jobs with status mapping
      const formattedJobs = jobs.map(job => ({
        ...job,
        status_text: getJobStatusText(job.status),
        type_text: getJobTypeText(job.type || job.job_type),
        formatted_salary: formatSalaryRange(job.salary_min, job.salary_max),
        is_expired: job.expires_at && new Date(job.expires_at) < new Date(),
        days_remaining: job.expires_at ? Math.max(0, Math.ceil((new Date(job.expires_at) - new Date()) / (1000 * 60 * 60 * 24))) : null
      }));

      res.status(200).json({
        error: false,
        list: formattedJobs,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / parseInt(limit))
        },
        summary: {
          total_jobs: total,
          active_jobs: formattedJobs.filter(j => j.status === 0).length,
          expired_jobs: formattedJobs.filter(j => j.is_expired).length,
          total_applications: formattedJobs.reduce((sum, j) => sum + (j.applicant_count || 0), 0)
        }
      });
    } catch (error) {
      console.error('Get user jobs error:', error);
      res.status(500).json({ error: true, message: error.message });
    }
  });

  // ==================== JOB CRUD APIs FOR HOSPITALS/RECRUITERS ====================

  // Get my posted jobs (hospital/recruiter only) - LEGACY ENDPOINT
  app.get('/v1/api/scrubhub/my-jobs', HospitalRecruiterMiddleware(), async (req, res) => {
    try {
      const sdk = app.get('sdk');
      sdk.setProjectId('scrubhub');

      const {
        page = 1,
        limit = 10,
        status,
        type,
        sort_by = 'created_at',
        sort_order = 'DESC'
      } = req.query;

      const offset = (parseInt(page) - 1) * parseInt(limit);

      let whereConditions = ['poster_id = ?'];
      let whereParams = [req.user_id];

      if (status !== undefined) {
        whereConditions.push('status = ?');
        whereParams.push(status);
      }

      if (type) {
        whereConditions.push('type = ?');
        whereParams.push(type);
      }

      const whereClause = whereConditions.join(' AND ');

      // Get total count
      const countQuery = `SELECT COUNT(*) as total FROM scrubhub_job WHERE ${whereClause}`;
      const countResult = await sdk.rawQuery(countQuery, whereParams);
      const total = countResult[0]?.total || 0;

      // Get jobs
      const jobsQuery = `
        SELECT
          j.*,
          (SELECT COUNT(*) FROM scrubhub_job_applicant WHERE job_id = j.id) as applicant_count
        FROM scrubhub_job j
        WHERE ${whereClause}
        ORDER BY j.${sort_by} ${sort_order}
        LIMIT ? OFFSET ?
      `;

      const jobs = await sdk.rawQuery(jobsQuery, [...whereParams, parseInt(limit), offset]);

      res.status(200).json({
        error: false,
        list: jobs,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / parseInt(limit))
        }
      });
    } catch (error) {
      console.error('Get my jobs error:', error);
      res.status(500).json({ error: true, message: error.message });
    }
  });

  // Get single job details (for editing)
  app.get('/v1/api/scrubhub/my-jobs/:id', HospitalRecruiterMiddleware(), async (req, res) => {
    try {
      const sdk = app.get('sdk');
      sdk.setProjectId('scrubhub');
      const jobId = req.params.id;

      const job = await sdk.findOne('job', {
        id: jobId,
        poster_id: req.user_id
      });

      if (!job) {
        return res.status(404).json({
          error: true,
          message: 'Job not found or access denied'
        });
      }

      res.status(200).json({
        error: false,
        model: job
      });
    } catch (error) {
      console.error('Get job details error:', error);
      res.status(500).json({ error: true, message: error.message });
    }
  });

  // Update job
  app.put('/v1/api/scrubhub/my-jobs/:id', HospitalRecruiterMiddleware(), async (req, res) => {
    try {
      const sdk = app.get('sdk');
      sdk.setProjectId('scrubhub');
      const jobId = req.params.id;

      // Verify job ownership
      const existingJob = await sdk.findOne('job', {
        id: jobId,
        poster_id: req.user_id
      });

      if (!existingJob) {
        return res.status(404).json({
          error: true,
          message: 'Job not found or access denied'
        });
      }

      const {
        title,
        description,
        location,
        type,
        department,
        salary_min,
        salary_max,
        requirements,
        contact_email,
        contact_phone,
        posting_fee,
        status
      } = req.body;

      // Handle posting fee
      let postingFeeAmount = existingJob.posting_fee || 0;
      let postingFeeRate = existingJob.posting_fee_rate || 'standard';

      if (posting_fee && typeof posting_fee === 'object') {
        postingFeeAmount = posting_fee.amount || postingFeeAmount;
        postingFeeRate = posting_fee.rate || postingFeeRate;
      }

      const updateData = filterEmptyFields({
        title,
        description,
        location,
        type,
        department,
        salary_min,
        salary_max,
        requirements,
        contact_email,
        contact_phone,
        posting_fee: postingFeeAmount,
        posting_fee_rate: postingFeeRate,
        status,
        updated_at: sqlDateTimeFormat(new Date())
      });

      await sdk.updateById('job', jobId, updateData);

      // Get updated job
      const updatedJob = await sdk.findById('job', jobId);

      res.status(200).json({
        error: false,
        model: updatedJob,
        message: 'Job updated successfully'
      });
    } catch (error) {
      console.error('Update job error:', error);
      res.status(500).json({ error: true, message: error.message });
    }
  });

  // Delete job
  app.delete('/v1/api/scrubhub/my-jobs/:id', HospitalRecruiterMiddleware(), async (req, res) => {
    try {
      const sdk = app.get('sdk');
      sdk.setProjectId('scrubhub');
      const jobId = req.params.id;

      // Verify job ownership
      const job = await sdk.findOne('job', {
        id: jobId,
        poster_id: req.user_id
      });

      if (!job) {
        return res.status(404).json({
          error: true,
          message: 'Job not found or access denied'
        });
      }

      // Check if job has applications
      const applications = await sdk.find('job_applicant', { job_id: jobId });

      if (applications.length > 0) {
        // Don't actually delete, just mark as inactive
        await sdk.updateById('job', jobId, {
          status: 1, // inactive
          updated_at: sqlDateTimeFormat(new Date())
        });

        return res.status(200).json({
          error: false,
          message: 'Job marked as inactive due to existing applications'
        });
      }

      // Safe to delete if no applications
      await sdk.deleteById('job', jobId);

      res.status(200).json({
        error: false,
        message: 'Job deleted successfully'
      });
    } catch (error) {
      console.error('Delete job error:', error);
      res.status(500).json({ error: true, message: error.message });
    }
  });

  // ==================== ENTERPRISE BULK LISTING APIs ====================

  // Submit Enterprise Bulk Listing Application
  app.post('/v1/api/scrubhub/enterprise/apply', HospitalRecruiterMiddleware(), async (req, res) => {
    try {
      const sdk = app.get('sdk');
      sdk.setProjectId('scrubhub');
      // /v1/api/scrubhub/enterprise/apply
      const {
        agency_name,
        contact_name,
        contact_email,
        contact_phone,
        agency_address,
        agency_website,
        monthly_job_postings,
        client_companies_served,
        industry_specialty,
        additional_requirements
      } = req.body;

      // Validate required fields
      if (!agency_name || !contact_name || !contact_email || !monthly_job_postings || !client_companies_served) {
        return res.status(400).json({
          error: true,
          message: 'Missing required fields: agency_name, contact_name, contact_email, monthly_job_postings, client_companies_served'
        });
      }

      // Check if user already has a pending or approved application
      const existingApplication = await sdk.findOne('enterprise_application', {
        user_id: req.user_id,
        status: [0, 1] // pending or approved
      });

      if (existingApplication) {
        return res.status(400).json({
          error: true,
          message: 'You already have a pending or approved enterprise application'
        });
      }

      // Create application
      const application = await sdk.create('enterprise_application', {
        user_id: req.user_id,
        agency_name,
        contact_name,
        contact_email,
        contact_phone,
        agency_address,
        agency_website,
        monthly_job_postings,
        client_companies_served,
        industry_specialty,
        additional_requirements,
        status: 0, // pending
        created_at: sqlDateTimeFormat(new Date()),
        updated_at: sqlDateTimeFormat(new Date())
      });

      res.status(201).json({
        error: false,
        model: application,
        message: 'Enterprise application submitted successfully'
      });
    } catch (error) {
      console.error('Enterprise application error:', error);
      res.status(500).json({ error: true, message: error.message });
    }
  });

  // Get user's enterprise applications
  app.get('/v1/api/scrubhub/enterprise/applications', HospitalRecruiterMiddleware(), async (req, res) => {
    try {
      const sdk = app.get('sdk');
      sdk.setProjectId('scrubhub');

      const applications = await sdk.find('enterprise_application',
        { user_id: req.user_id },
        { orderBy: 'created_at', direction: 'DESC' }
      );

      res.status(200).json({
        error: false,
        list: applications
      });
    } catch (error) {
      console.error('Get enterprise applications error:', error);
      res.status(500).json({ error: true, message: error.message });
    }
  });

  // ==================== ADMIN APIs ====================

  // Get admin dashboard stats
  app.get('/v1/api/scrubhub/admin/dashboard', AdminMiddleware(), async (req, res) => {
    try {
      const sdk = app.get('sdk');
      sdk.setProjectId('scrubhub');

      // Get various counts
      const [
        totalUsers,
        totalProperties,
        totalJobs,
        totalMarketplaceItems,
        pendingVerifications,
        pendingSublets,
        totalRevenue
      ] = await Promise.all([
        sdk.count('user'),
        sdk.count('property'),
        sdk.count('job'),
        sdk.count('marketplace_item'),
        sdk.count('user', { verified: 0 }),
        sdk.count('sublet', { status: 4 }), // pending_admin_review
        sdk.rawQuery('SELECT SUM(amount) as total FROM scrubhub_payment WHERE status = 1')
      ]);

      res.status(200).json({
        error: false,
        stats: {
          total_users: totalUsers,
          total_properties: totalProperties,
          total_jobs: totalJobs,
          total_marketplace_items: totalMarketplaceItems,
          pending_verifications: pendingVerifications,
          pending_sublets: pendingSublets,
          total_revenue: totalRevenue[0]?.total || 0
        }
      });
    } catch (error) {
      console.error('Admin dashboard error:', error);
      res.status(500).json({ error: true, message: error.message });
    }
  });

  // Verify user
  app.post('/v1/api/scrubhub/admin/verify-user/:id', AdminMiddleware(), async (req, res) => {
    try {
      const sdk = app.get('sdk');
      sdk.setProjectId('scrubhub');
      const userId = req.params.id;

      await sdk.updateById('user', userId, {
        verified: 1,
        updated_at: sqlDateTimeFormat(new Date())
      });

      // Log admin action
      await sdk.create('admin_action', {
        admin_id: req.user_id,
        action_type: 'user_verification',
        target_id: userId,
        target_table: 'user',
        notes: 'User verified by admin',
        created_at: sqlDateTimeFormat(new Date())
      });

      res.status(200).json({
        error: false,
        message: 'User verified successfully'
      });
    } catch (error) {
      console.error('User verification error:', error);
      res.status(500).json({ error: true, message: error.message });
    }
  });

  // Approve premium sublet
  app.post('/v1/api/scrubhub/admin/approve-sublet/:id', AdminMiddleware(), async (req, res) => {
    try {
      const sdk = app.get('sdk');
      sdk.setProjectId('scrubhub');
      const subletId = req.params.id;
      const { approved } = req.body;

      const newStatus = approved ? 1 : 3; // active or cancelled

      await sdk.updateById('sublet', subletId, {
        status: newStatus
      });

      // Log admin action
      await sdk.create('admin_action', {
        admin_id: req.user_id,
        action_type: 'sublet_approval',
        target_id: subletId,
        target_table: 'sublet',
        notes: approved ? 'Premium sublet approved' : 'Premium sublet rejected',
        metadata: JSON.stringify({ approved }),
        created_at: sqlDateTimeFormat(new Date())
      });

      res.status(200).json({
        error: false,
        message: approved ? 'Sublet approved successfully' : 'Sublet rejected'
      });
    } catch (error) {
      console.error('Sublet approval error:', error);
      res.status(500).json({ error: true, message: error.message });
    }
  });

  // Get all enterprise applications (admin only)
  app.get('/v1/api/scrubhub/admin/enterprise-applications', AdminMiddleware(), async (req, res) => {
    try {
      const sdk = app.get('sdk');
      sdk.setProjectId('scrubhub');

      const { page = 1, limit = 20, status } = req.query;
      const offset = (parseInt(page) - 1) * parseInt(limit);

      let whereClause = '1=1';
      let whereParams = [];

      if (status !== undefined) {
        whereClause += ' AND ea.status = ?';
        whereParams.push(status);
      }

      // Get total count
      const countQuery = `SELECT COUNT(*) as total FROM scrubhub_enterprise_application ea WHERE ${whereClause}`;
      const countResult = await sdk.rawQuery(countQuery, whereParams);
      const total = countResult[0]?.total || 0;

      // Get applications with user info
      const applicationsQuery = `
        SELECT
          ea.*,
          u.name as user_name,
          u.email as user_email,
          u.role as user_role,
          admin.name as reviewed_by_name
        FROM scrubhub_enterprise_application ea
        LEFT JOIN scrubhub_user u ON ea.user_id = u.id
        LEFT JOIN scrubhub_user admin ON ea.reviewed_by = admin.id
        WHERE ${whereClause}
        ORDER BY ea.created_at DESC
        LIMIT ? OFFSET ?
      `;

      const applications = await sdk.rawQuery(applicationsQuery, [...whereParams, parseInt(limit), offset]);

      res.status(200).json({
        error: false,
        list: applications,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / parseInt(limit))
        }
      });
    } catch (error) {
      console.error('Get enterprise applications error:', error);
      res.status(500).json({ error: true, message: error.message });
    }
  });

  // Review enterprise application (admin only)
  app.put('/v1/api/scrubhub/admin/enterprise-applications/:id', AdminMiddleware(), async (req, res) => {
    try {
      const sdk = app.get('sdk');
      sdk.setProjectId('scrubhub');

      const applicationId = req.params.id;
      const { status, admin_notes } = req.body;

      // Validate status
      if (![1, 2, 3].includes(parseInt(status))) {
        return res.status(400).json({
          error: true,
          message: 'Invalid status. Use 1=approved, 2=rejected, 3=under_review'
        });
      }

      // Update application
      await sdk.updateById('enterprise_application', applicationId, {
        status: parseInt(status),
        admin_notes,
        reviewed_by: req.user_id,
        reviewed_at: sqlDateTimeFormat(new Date()),
        updated_at: sqlDateTimeFormat(new Date())
      });

      // Log admin action
      await sdk.create('admin_action', {
        admin_id: req.user_id,
        action_type: 'review_enterprise_application',
        target_id: applicationId,
        details: JSON.stringify({ status, admin_notes }),
        created_at: sqlDateTimeFormat(new Date())
      });

      res.status(200).json({
        error: false,
        message: 'Enterprise application reviewed successfully'
      });
    } catch (error) {
      console.error('Review enterprise application error:', error);
      res.status(500).json({ error: true, message: error.message });
    }
  });

  // ==================== CREDIT CHECK & TRUSTEE API INTEGRATION ====================

  // Request credit check
  app.post('/v1/api/scrubhub/credit-check', [TokenMiddleware()], async (req, res) => {
    try {
      const sdk = app.get('sdk');
      sdk.setProjectId('scrubhub');

      const {
        first_name,
        last_name,
        date_of_birth,
        social_insurance_number,
        address,
        city,
        province_state,
        postal_code
      } = req.body;

      if (!first_name || !last_name || !date_of_birth || !social_insurance_number) {
        return res.status(400).json({
          error: true,
          message: 'First name, last name, date of birth, and SIN are required'
        });
      }

      // Check if user already has a valid credit report (within 30 days)
      const existingReport = await sdk.rawQuery(`
        SELECT * FROM scrubhub_credit_report
        WHERE user_id = ? AND created_at > DATE_SUB(NOW(), INTERVAL 30 DAY)
        ORDER BY created_at DESC LIMIT 1
      `, [req.user_id]);

      if (existingReport.length > 0) {
        return res.status(200).json({
          error: false,
          credit_report: existingReport[0],
          message: 'Using existing credit report (reusable for 30 days)'
        });
      }

      const fee = 29.99; // Credit check fee

      // Create payment intent
      const paymentIntent = await stripe.createPaymentIntentManual({
        amount: Math.round(fee * 100),
        currency: 'cad',
        metadata: {
          user_id: req.user_id,
          purpose: 'credit_check',
          first_name: first_name,
          last_name: last_name
        }
      });

      // Store credit check request
      const creditRequest = await sdk.create('credit_report', {
        user_id: req.user_id,
        first_name: first_name,
        last_name: last_name,
        date_of_birth: date_of_birth,
        social_insurance_number: social_insurance_number, // This should be encrypted in production
        address: address,
        city: city,
        province_state: province_state,
        postal_code: postal_code,
        status: 0, // pending payment
        created_at: sqlDateTimeFormat(new Date())
      });

      // Create payment record
      await sdk.create('payment', {
        user_id: req.user_id,
        amount: fee,
        currency: 'CAD',
        purpose: 'credit_check',
        stripe_transaction_id: paymentIntent.id,
        status: 0, // pending
        metadata: JSON.stringify({ credit_request_id: creditRequest.id }),
        created_at: sqlDateTimeFormat(new Date())
      });

      res.status(200).json({
        error: false,
        credit_request_id: creditRequest.id,
        payment_intent: paymentIntent.client_secret,
        fee: fee,
        message: 'Complete payment to process credit check via Trustee API'
      });
    } catch (error) {
      console.error('Credit check request error:', error);
      res.status(500).json({ error: true, message: error.message });
    }
  });

  // Get user's credit reports
  app.get('/v1/api/scrubhub/credit-reports', [TokenMiddleware()], async (req, res) => {
    try {
      const sdk = app.get('sdk');
      sdk.setProjectId('scrubhub');

      const reports = await sdk.find('credit_report',
        { user_id: req.user_id },
        { orderBy: 'created_at', direction: 'DESC' }
      );

      // Remove sensitive data from response
      const sanitizedReports = reports.map(report => ({
        id: report.id,
        status: report.status,
        trustee_reference: report.trustee_reference,
        report_url: report.report_url,
        created_at: report.created_at,
        expires_at: report.expires_at
      }));

      res.status(200).json({
        error: false,
        list: sanitizedReports
      });
    } catch (error) {
      console.error('Get credit reports error:', error);
      res.status(500).json({ error: true, message: error.message });
    }
  });

  // Download credit report (after payment)
  app.get('/v1/api/scrubhub/credit-reports/:id/download', [TokenMiddleware()], async (req, res) => {
    try {
      const sdk = app.get('sdk');
      sdk.setProjectId('scrubhub');

      const reportId = req.params.id;

      const report = await sdk.findOne('credit_report', {
        id: reportId,
        user_id: req.user_id,
        status: 1 // completed
      });

      if (!report) {
        return res.status(404).json({
          error: true,
          message: 'Credit report not found or not yet processed'
        });
      }

      if (!report.report_url) {
        return res.status(400).json({
          error: true,
          message: 'Report not yet generated. Please try again in a few moments.'
        });
      }

      res.status(200).json({
        error: false,
        download_url: report.report_url,
        trustee_reference: report.trustee_reference,
        expires_at: report.expires_at
      });
    } catch (error) {
      console.error('Credit report download error:', error);
      res.status(500).json({ error: true, message: error.message });
    }
  });

  // ==================== USER PROFILE & DASHBOARD APIs ====================

  // Get user profile
  app.get('/v1/api/scrubhub/profile', [TokenMiddleware()], async (req, res) => {
    try {
      const sdk = app.get('sdk');
      sdk.setProjectId('scrubhub');

      const user = await sdk.findById('user', req.user_id);
      if (!user) {
        return res.status(404).json({ error: true, message: 'User not found' });
      }

      // Remove sensitive data
      delete user.password;

      // Map role number to string
      const roleMapping = { 0: 'tenant', 1: 'landlord', 2: 'hospital', 3: 'recruiter', 4: 'admin' };
      user.role_name = roleMapping[user.role] || 'tenant';

      res.status(200).json({
        error: false,
        model: user
      });
    } catch (error) {
      console.error('Get profile error:', error);
      res.status(500).json({ error: true, message: error.message });
    }
  });

  // Update user profile
  app.put('/v1/api/scrubhub/profile', [TokenMiddleware()], async (req, res) => {
    try {
      const sdk = app.get('sdk');
      sdk.setProjectId('scrubhub');

      const updateData = filterEmptyFields(req.body);

      // Remove fields that shouldn't be updated via this endpoint
      delete updateData.id;
      delete updateData.email;
      delete updateData.password;
      delete updateData.role;
      delete updateData.verified;
      delete updateData.created_at;

      updateData.updated_at = sqlDateTimeFormat(new Date());

      await sdk.updateById('user', req.user_id, updateData);

      res.status(200).json({
        error: false,
        message: 'Profile updated successfully'
      });
    } catch (error) {
      console.error('Update profile error:', error);
      res.status(500).json({ error: true, message: error.message });
    }
  });

  // Get user dashboard data
  app.get('/v1/api/scrubhub/dashboard', [TokenMiddleware()], async (req, res) => {
    try {
      const sdk = app.get('sdk');
      sdk.setProjectId('scrubhub');

      const user = await sdk.findById('user', req.user_id);
      const roleMapping = { 0: 'tenant', 1: 'landlord', 2: 'hospital', 3: 'recruiter', 4: 'admin' };
      const userRole = roleMapping[user.role] || 'tenant';

      let dashboardData = {
        user: {
          name: user.name,
          email: user.email,
          role: userRole,
          verified: user.verified
        }
      };

      // Role-specific dashboard data
      if (userRole === 'landlord') {
        // Landlord dashboard data
        const [properties, inquiries, recentPayments] = await Promise.all([
          sdk.find('property', { landlord_id: req.user_id }, { orderBy: 'created_at', direction: 'DESC', limit: 5 }),
          sdk.rawQuery(`
            SELECT i.*, p.title as property_title
            FROM scrubhub_inquiry i
            JOIN scrubhub_property p ON i.property_id = p.id
            WHERE p.landlord_id = ? AND i.status = 0
            ORDER BY i.created_at DESC LIMIT 10
          `, [req.user_id]),
          sdk.find('payment', { user_id: req.user_id }, { orderBy: 'created_at', direction: 'DESC', limit: 5 })
        ]);

        dashboardData.stats = {
          total_properties: await sdk.count('property', { landlord_id: req.user_id }),
          active_properties: await sdk.count('property', { landlord_id: req.user_id, status: 0 }),
          new_inquiries: await sdk.rawQuery(`
            SELECT COUNT(*) as count FROM scrubhub_inquiry i
            JOIN scrubhub_property p ON i.property_id = p.id
            WHERE p.landlord_id = ? AND i.status = 0
          `, [req.user_id]).then(r => r[0]?.count || 0),
          total_revenue: await sdk.rawQuery(`
            SELECT SUM(amount) as total FROM scrubhub_payment
            WHERE user_id = ? AND status = 1
          `, [req.user_id]).then(r => r[0]?.total || 0)
        };

        dashboardData.recent_properties = properties;
        dashboardData.new_inquiries = inquiries;
        dashboardData.recent_payments = recentPayments;

      } else if (userRole === 'hospital') {
        // Hospital dashboard data
        const [jobs, applications] = await Promise.all([
          sdk.find('job', { poster_id: req.user_id }, { orderBy: 'created_at', direction: 'DESC', limit: 5 }),
          sdk.rawQuery(`
            SELECT ja.*, j.title as job_title
            FROM scrubhub_job_applicant ja
            JOIN scrubhub_job j ON ja.job_id = j.id
            WHERE j.poster_id = ? AND ja.status = 0
            ORDER BY ja.created_at DESC LIMIT 10
          `, [req.user_id])
        ]);

        dashboardData.stats = {
          total_jobs: await sdk.count('job', { poster_id: req.user_id }),
          active_jobs: await sdk.count('job', { poster_id: req.user_id, status: 0 }),
          new_applications: await sdk.rawQuery(`
            SELECT COUNT(*) as count FROM scrubhub_job_applicant ja
            JOIN scrubhub_job j ON ja.job_id = j.id
            WHERE j.poster_id = ? AND ja.status = 0
          `, [req.user_id]).then(r => r[0]?.count || 0)
        };

        dashboardData.recent_jobs = jobs;
        dashboardData.new_applications = applications;

      } else if (userRole === 'tenant') {
        // Tenant dashboard data
        const [favorites, recentlyViewed, sublets] = await Promise.all([
          sdk.rawQuery(`
            SELECT p.*, f.created_at as favorited_at
            FROM scrubhub_favorite f
            JOIN scrubhub_property p ON f.property_id = p.id
            WHERE f.user_id = ? AND p.status = 0
            ORDER BY f.created_at DESC LIMIT 5
          `, [req.user_id]),
          sdk.rawQuery(`
            SELECT p.*, rv.viewed_at
            FROM scrubhub_recently_viewed rv
            JOIN scrubhub_property p ON rv.property_id = p.id
            WHERE rv.user_id = ? AND p.status = 0
            ORDER BY rv.viewed_at DESC LIMIT 5
          `, [req.user_id]),
          sdk.find('sublet', { tenant_id: req.user_id }, { orderBy: 'created_at', direction: 'DESC', limit: 5 })
        ]);

        dashboardData.stats = {
          total_favorites: await sdk.count('favorite', { user_id: req.user_id }),
          total_sublets: await sdk.count('sublet', { tenant_id: req.user_id }),
          active_sublets: await sdk.count('sublet', { tenant_id: req.user_id, status: 1 })
        };

        dashboardData.recent_favorites = favorites;
        dashboardData.recently_viewed = recentlyViewed;
        dashboardData.recent_sublets = sublets;
      }

      res.status(200).json({
        error: false,
        dashboard: dashboardData
      });
    } catch (error) {
      console.error('Dashboard error:', error);
      res.status(500).json({ error: true, message: error.message });
    }
  });

  // ==================== SUBSCRIPTION MANAGEMENT APIs ====================

//   // Get available subscription plans
//   app.get('/v1/api/scrubhub/subscription/plans', async (req, res) => {
//     try {
//       const sdk = app.get('sdk');
//       sdk.setProjectId('scrubhub');

//       const { target_audience } = req.query;

//       let whereConditions = ['active = 1'];
//       let whereParams = [];

//       if (target_audience) {
//         whereConditions.push('target_audience = ?');
//         whereParams.push(target_audience);
//       }

//       const whereClause = whereConditions.join(' AND ');

//       const plans = await sdk.rawQuery(`
//         SELECT * FROM scrubhub_subscription_plan
//         WHERE ${whereClause}
//         ORDER BY price ASC
//       `, whereParams);

//       // Parse features JSON
//       const formattedPlans = plans.map(plan => ({
//         ...plan,
//         features: typeof plan.features === 'string' ? JSON.parse(plan.features) : plan.features
//       }));

//       res.status(200).json({
//         error: false,
//         list: formattedPlans
//       });
//     } catch (error) {
//       console.error('Get subscription plans error:', error);
//       res.status(500).json({ error: true, message: error.message });
//     }
//   });

//   // Get user subscription status
//   app.get('/v1/api/scrubhub/subscription', [TokenMiddleware()], async (req, res) => {
//     try {
//       const sdk = app.get('sdk');
//       sdk.setProjectId('scrubhub');

//       const subscriptionQuery = `
//         SELECT
//           s.*,
//           sp.name as plan_name,
//           sp.description as plan_description,
//           sp.features,
//           sp.target_audience
//         FROM scrubhub_subscription s
//         LEFT JOIN scrubhub_subscription_plan sp ON s.plan_id = sp.id
//         WHERE s.user_id = ? AND s.status IN ('active', 'trialing')
//         ORDER BY s.created_at DESC
//         LIMIT 1
//       `;

//       const subscriptions = await sdk.rawQuery(subscriptionQuery, [req.user_id]);

//       if (subscriptions.length === 0) {
//         return res.status(200).json({
//           error: false,
//           subscription: {
//             plan: 'free',
//             status: 'active',
//             features: {
//               property_listings: 2,
//               listing_duration: 30,
//               featured_listings: false,
//               priority_support: false,
//               early_access: false,
//               enhanced_visibility: false
//             }
//           }
//         });
//       }

//       const subscription = subscriptions[0];
//       const features = typeof subscription.features === 'string'
//         ? JSON.parse(subscription.features)
//         : subscription.features;

//       res.status(200).json({
//         error: false,
//         subscription: {
//           ...subscription,
//           features: features
//         }
//       });
//     } catch (error) {
//       console.error('Get subscription error:', error);
//       res.status(500).json({ error: true, message: error.message });
//     }
//   });

//   // Create subscription
//   app.post('/v1/api/scrubhub/subscription', [TokenMiddleware()], async (req, res) => {
//     try {
//       const sdk = app.get('sdk');
//       sdk.setProjectId('scrubhub');

//       const { plan_id } = req.body;

//       if (!plan_id) {
//         return res.status(400).json({
//           error: true,
//           message: 'Plan ID is required'
//         });
//       }

//       // Get plan details
//       const plan = await sdk.findById('subscription_plan', plan_id);
//       if (!plan || !plan.active) {
//         return res.status(404).json({
//           error: true,
//           message: 'Subscription plan not found or inactive'
//         });
//       }

//       // Check if user already has an active subscription
//       const existingSubscription = await sdk.findOne('subscription', {
//         user_id: req.user_id,
//         status: ['active', 'trialing']
//       });

//       if (existingSubscription) {
//         return res.status(400).json({
//           error: true,
//           message: 'User already has an active subscription'
//         });
//       }

//       // Get user details for Stripe customer
//       const user = await sdk.findById('user', req.user_id);
//       if (!user) {
//         return res.status(404).json({ error: true, message: 'User not found' });
//       }

//       // Create Stripe customer if not exists
//       let stripeCustomerId = user.stripe_customer_id;
//       if (!stripeCustomerId) {
//         const customer = await stripe.createStripeCustomer({
//           email: user.email,
//           name: user.name,
//           metadata: {
//             user_id: req.user_id,
//             platform: 'scrubhub'
//           }
//         });

//         if (customer.error) {
//           throw new Error(`Failed to create Stripe customer: ${customer.message}`);
//         }

//         stripeCustomerId = customer.id;

//         // Update user with Stripe customer ID
//         await sdk.updateById('user', req.user_id, {
//           stripe_customer_id: stripeCustomerId
//         });
//       }

//       // Create Stripe subscription
//       const stripeSubscription = await stripe.createStripeSubscription({
//         customer: stripeCustomerId,
//         items: [{ price: plan.stripe_price_id }],
//         trial_period_days: plan.trial_days,
//         metadata: {
//           user_id: req.user_id,
//           plan_id: plan_id,
//           platform: 'scrubhub'
//         }
//       });

//       if (stripeSubscription.error) {
//         throw new Error(`Failed to create Stripe subscription: ${stripeSubscription.message}`);
//       }

//       // Create subscription record
//       const subscription = await sdk.create('subscription', {
//         user_id: req.user_id,
//         plan_id: plan_id,
//         stripe_subscription_id: stripeSubscription.id,
//         stripe_customer_id: stripeCustomerId,
//         status: stripeSubscription.status,
//         current_period_start: sqlDateTimeFormat(new Date(stripeSubscription.current_period_start * 1000)),
//         current_period_end: sqlDateTimeFormat(new Date(stripeSubscription.current_period_end * 1000)),
//         trial_start: stripeSubscription.trial_start ? sqlDateTimeFormat(new Date(stripeSubscription.trial_start * 1000)) : null,
//         trial_end: stripeSubscription.trial_end ? sqlDateTimeFormat(new Date(stripeSubscription.trial_end * 1000)) : null,
//         created_at: sqlDateTimeFormat(new Date())
//       });

//       res.status(200).json({
//         error: false,
//         subscription: subscription,
//         plan: plan,
//         stripe_subscription_id: stripeSubscription.id,
//         message: plan.trial_days > 0
//           ? `Subscription created with ${plan.trial_days}-day trial`
//           : 'Subscription created successfully'
//       });
//     } catch (error) {
//       console.error('Create subscription error:', error);
//       res.status(500).json({ error: true, message: error.message });
//     }
//   });

//   // Cancel subscription
//   app.delete('/v1/api/scrubhub/subscription', [TokenMiddleware()], async (req, res) => {
//     try {
//       const sdk = app.get('sdk');
//       sdk.setProjectId('scrubhub');

//       const subscription = await sdk.findOne('subscription', {
//         user_id: req.user_id,
//         status: ['active', 'trialing']
//       });

//       if (!subscription) {
//         return res.status(404).json({
//           error: true,
//           message: 'No active subscription found'
//         });
//       }

//       // Cancel Stripe subscription
//       if (subscription.stripe_subscription_id) {
//         const canceledSubscription = await stripe.cancelStripeSubscription(subscription.stripe_subscription_id);

//         if (canceledSubscription.error) {
//           throw new Error(`Failed to cancel Stripe subscription: ${canceledSubscription.message}`);
//         }
//       }

//       // Update subscription status
//       await sdk.updateById('subscription', subscription.id, {
//         status: 'canceled',
//         canceled_at: sqlDateTimeFormat(new Date()),
//         updated_at: sqlDateTimeFormat(new Date())
//       });

//       res.status(200).json({
//         error: false,
//         message: 'Subscription canceled successfully'
//       });
//     } catch (error) {
//       console.error('Cancel subscription error:', error);
//       res.status(500).json({ error: true, message: error.message });
//     }
//   });

  // ==================== PAYMENT WEBHOOK ====================

  // Stripe webhook handler
  app.post('/v1/api/scrubhub/webhook/stripe', async (req, res) => {
    try {
      const sdk = app.get('sdk');
      sdk.setProjectId('scrubhub');

      const sig = req.headers['stripe-signature'];
      const event = stripe.constructEvent(req.body, sig, config.stripe_webhook_secret);

      if (event.type === 'payment_intent.succeeded') {
        const paymentIntent = event.data.object;

        // Update payment status
        await sdk.update('payment',
          { stripe_transaction_id: paymentIntent.id },
          { status: 1 } // completed
        );

        // Handle specific payment purposes
        const metadata = paymentIntent.metadata;

        if (metadata.purpose === 'marketplace_listing' && metadata.item_id) {
          // Activate marketplace item
          await sdk.updateById('marketplace_item', metadata.item_id, {
            status: 0 // active
          });
        } else if (metadata.purpose === 'subscription' && metadata.plan) {
          // Activate subscription
          await sdk.update('subscription',
            { user_id: metadata.user_id },
            {
              status: 'active',
              activated_at: sqlDateTimeFormat(new Date())
            }
          );
        } else if (metadata.purpose === 'phone_reveal' && metadata.item_id) {
          // Mark phone as revealed for this user
          await sdk.updateById('marketplace_item', metadata.item_id, {
            phone_revealed: 1
          });
        } else if (metadata.purpose === 'sublet_fee') {
          // Activate sublet listing
          const payment = await sdk.findOne('payment', { stripe_transaction_id: paymentIntent.id });
          if (payment && payment.metadata) {
            const paymentMeta = JSON.parse(payment.metadata);
            if (paymentMeta.sublet_id) {
              await sdk.updateById('sublet', paymentMeta.sublet_id, {
                status: paymentMeta.service_tier === 'premium' ? 2 : 1 // 1=active, 2=pending admin review
              });
            }
          }
        } else if (metadata.purpose === 'job_posting') {
          // Activate job posting
          const payment = await sdk.findOne('payment', { stripe_transaction_id: paymentIntent.id });
          if (payment && payment.metadata) {
            const paymentMeta = JSON.parse(payment.metadata);
            if (paymentMeta.job_id) {
              await sdk.updateById('job', paymentMeta.job_id, {
                status: 0 // active
              });
            }
          }
        } else if (metadata.purpose === 'credit_check') {
          // Process credit check via Trustee API
          const payment = await sdk.findOne('payment', { stripe_transaction_id: paymentIntent.id });
          if (payment && payment.metadata) {
            const paymentMeta = JSON.parse(payment.metadata);
            if (paymentMeta.credit_request_id) {
              await sdk.updateById('credit_report', paymentMeta.credit_request_id, {
                status: 1, // processing
                payment_completed_at: sqlDateTimeFormat(new Date())
              });

              // TODO: Integrate with actual Trustee API here
              // For now, we'll simulate the process
              setTimeout(async () => {
                try {
                  await sdk.updateById('credit_report', paymentMeta.credit_request_id, {
                    status: 2, // completed
                    trustee_reference: `TR-${Date.now()}`,
                    report_url: `/reports/credit-${paymentMeta.credit_request_id}.pdf`,
                    expires_at: sqlDateTimeFormat(new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)) // 30 days
                  });
                } catch (error) {
                  console.error('Credit report update error:', error);
                }
              }, 5000); // Simulate 5 second processing time
            }
          }
        } else if (metadata.purpose === 'n9_form') {
          // Generate N9 form PDF
          const payment = await sdk.findOne('payment', { stripe_transaction_id: paymentIntent.id });
          if (payment && payment.metadata) {
            const paymentMeta = JSON.parse(payment.metadata);
            if (paymentMeta.notice_id) {
              await sdk.updateById('tenant_notice', paymentMeta.notice_id, {
                status: 1, // paid
                payment_completed_at: sqlDateTimeFormat(new Date())
              });

              // TODO: Generate actual PDF here
              // For now, we'll simulate the process
              setTimeout(async () => {
                try {
                  await sdk.updateById('tenant_notice', paymentMeta.notice_id, {
                    pdf_url: `/forms/n9-${paymentMeta.notice_id}.pdf`,
                    generated_at: sqlDateTimeFormat(new Date())
                  });
                } catch (error) {
                  console.error('N9 form generation error:', error);
                }
              }, 3000); // Simulate 3 second generation time
            }
          }
        } else if (metadata.purpose === 'property_listing') {
          // Activate property listing
          const payment = await sdk.findOne('payment', { stripe_transaction_id: paymentIntent.id });
          if (payment && payment.metadata) {
            const paymentMeta = JSON.parse(payment.metadata);
            if (paymentMeta.property_id) {
              await sdk.updateById('property', paymentMeta.property_id, {
                status: 0, // active
                tier: paymentMeta.tier || 'paid',
                featured: paymentMeta.featured || false,
                expires_at: sqlDateTimeFormat(new Date(Date.now() + (paymentMeta.duration || 30) * 24 * 60 * 60 * 1000))
              });
            }
          }
        }
      }

      res.status(200).json({ received: true });
    } catch (error) {
      console.error('Webhook error:', error);
      res.status(400).json({ error: true, message: error.message });
    }
  });

};