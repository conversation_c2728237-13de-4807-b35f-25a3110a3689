const { sqlDateTimeFormat } = require('../../../baas/services/UtilService');

class PropertyService {
  constructor(sdk) {
    this.sdk = sdk;
    this.sdk.setProjectId('scrubhub');
  }

  // Calculate listing fees based on tier and duration
  calculateListingFee(tier, duration_days) {
    const pricing = {
      free: { cost: 0, duration: 30, limit: 2 },
      paid: {
        30: 4.99,
        60: 9.99,
        90: 14.99
      },
      featured: { additional: 19.99 }
    };
    
    if (tier === 'free') return pricing.free.cost;
    if (tier === 'featured') {
      return pricing.paid[duration_days] + pricing.featured.additional;
    }
    return pricing.paid[duration_days] || 0;
  }

  // Check if landlord can post free listings
  async canPostFreeListing(landlordId) {
    const freeListingCount = await this.sdk.count('property', {
      landlord_id: landlordId,
      tier: 0, // 0 = free
      status: [0, 3] // active or draft
    });
    
    return freeListingCount < 2;
  }

  // Get property with all related data
  async getPropertyDetails(propertyId) {
    const propertyQuery = `
      SELECT 
        p.*,
        u.name as landlord_name,
        u.phone as landlord_phone,
        u.email as landlord_email,
        u.verified as landlord_verified
      FROM scrubhub_property p
      LEFT JOIN scrubhub_user u ON p.landlord_id = u.id
      WHERE p.id = ? AND p.status = 0
    `;
    
    const properties = await this.sdk.rawQuery(propertyQuery, [propertyId]);
    
    if (properties.length === 0) {
      return null;
    }

    const property = properties[0];

    // Get property photos
    const photos = await this.sdk.find('property_photo', 
      { property_id: propertyId }, 
      { orderBy: 'sort_order', direction: 'ASC' }
    );

    return {
      ...property,
      photos: photos.map(p => p.file_path),
      landlord: {
        name: property.landlord_name,
        phone: property.landlord_phone,
        email: property.landlord_email,
        verified: property.landlord_verified
      }
    };
  }

  // Search properties with filters
  async searchProperties(filters, pagination) {
    const {
      city,
      min_price,
      max_price,
      bedrooms,
      property_type,
      furnished,
      available_from,
      sort_by = 'created_at',
      sort_order = 'DESC'
    } = filters;

    const { page = 1, limit = 10 } = pagination;
    const offset = (parseInt(page) - 1) * parseInt(limit);
    
    // Build where conditions
    let whereConditions = ['status = 0']; // 0 = active
    let whereParams = [];
    
    if (city) {
      whereConditions.push('city LIKE ?');
      whereParams.push(`%${city}%`);
    }
    
    if (min_price) {
      whereConditions.push('price >= ?');
      whereParams.push(min_price);
    }
    
    if (max_price) {
      whereConditions.push('price <= ?');
      whereParams.push(max_price);
    }
    
    if (bedrooms) {
      whereConditions.push('bedrooms = ?');
      whereParams.push(bedrooms);
    }
    
    if (property_type) {
      whereConditions.push('property_type = ?');
      whereParams.push(property_type);
    }
    
    if (furnished !== undefined) {
      whereConditions.push('furnished = ?');
      whereParams.push(furnished === 'true' ? 1 : 0);
    }
    
    if (available_from) {
      whereConditions.push('available_from <= ?');
      whereParams.push(available_from);
    }

    const whereClause = whereConditions.join(' AND ');
    
    // Get total count
    const countQuery = `SELECT COUNT(*) as total FROM scrubhub_property WHERE ${whereClause}`;
    const countResult = await this.sdk.rawQuery(countQuery, whereParams);
    const total = countResult[0]?.total || 0;
    
    // Get properties with photos and landlord info
    const propertiesQuery = `
      SELECT 
        p.*,
        u.name as landlord_name,
        u.phone as landlord_phone,
        u.email as landlord_email,
        u.verified as landlord_verified,
        GROUP_CONCAT(pp.file_path ORDER BY pp.sort_order) as photo_paths
      FROM scrubhub_property p
      LEFT JOIN scrubhub_user u ON p.landlord_id = u.id
      LEFT JOIN scrubhub_property_photo pp ON p.id = pp.property_id
      WHERE ${whereClause}
      GROUP BY p.id
      ORDER BY p.${sort_by} ${sort_order}
      LIMIT ? OFFSET ?
    `;
    
    const properties = await this.sdk.rawQuery(propertiesQuery, [...whereParams, parseInt(limit), offset]);
    
    // Format properties
    const formattedProperties = properties.map(property => ({
      ...property,
      photos: property.photo_paths ? property.photo_paths.split(',') : [],
      landlord: {
        name: property.landlord_name,
        phone: property.landlord_phone,
        email: property.landlord_email,
        verified: property.landlord_verified
      }
    }));

    const totalPages = Math.ceil(total / parseInt(limit));
    
    return {
      properties: formattedProperties,
      pagination: {
        total,
        totalPages,
        currentPage: parseInt(page),
        limit: parseInt(limit)
      }
    };
  }

  // Track property view
  async trackView(userId, propertyId) {
    if (!userId) return;

    // Check if already viewed recently
    const existingView = await this.sdk.findOne('recently_viewed', { 
      user_id: userId, 
      property_id: propertyId 
    });

    if (existingView) {
      await this.sdk.updateById('recently_viewed', existingView.id, {
        viewed_at: sqlDateTimeFormat(new Date())
      });
    } else {
      await this.sdk.create('recently_viewed', {
        user_id: userId,
        property_id: propertyId,
        viewed_at: sqlDateTimeFormat(new Date())
      });

      // Keep only last 10 views
      const recentViews = await this.sdk.find('recently_viewed', { user_id: userId }, { 
        orderBy: 'viewed_at', 
        direction: 'DESC' 
      });
      
      if (recentViews.length > 10) {
        const toDelete = recentViews.slice(10);
        for (const view of toDelete) {
          await this.sdk.deleteById('recently_viewed', view.id);
        }
      }
    }
  }

  // Get similar properties
  async getSimilarProperties(propertyId, city, price, limit = 4) {
    const similarQuery = `
      SELECT p.*, GROUP_CONCAT(pp.file_path ORDER BY pp.sort_order LIMIT 1) as first_photo
      FROM scrubhub_property p
      LEFT JOIN scrubhub_property_photo pp ON p.id = pp.property_id
      WHERE p.city = ? AND p.id != ? AND p.status = 0
      AND p.price BETWEEN ? AND ?
      GROUP BY p.id
      ORDER BY p.created_at DESC
      LIMIT ?
    `;
    
    return await this.sdk.rawQuery(similarQuery, [
      city,
      propertyId,
      price * 0.8,
      price * 1.2,
      limit
    ]);
  }

  // Expire listings that have passed their expiration date
  async expireListings() {
    const now = sqlDateTimeFormat(new Date());
    
    await this.sdk.update('property', 
      { 
        expires_at: { '<=': now },
        status: 0 // currently active
      },
      { 
        status: 2, // expired
        updated_at: now
      }
    );
  }
}

module.exports = PropertyService;
