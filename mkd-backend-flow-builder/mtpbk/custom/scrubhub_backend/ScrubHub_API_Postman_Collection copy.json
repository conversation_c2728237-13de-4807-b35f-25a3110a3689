{"info": {"name": "ScrubHub API Collection", "description": "Complete API collection for ScrubHub medical housing and recruitment platform", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{auth_token}}", "type": "string"}]}, "variable": [{"key": "base_url", "value": "http://localhost:5172", "type": "string"}, {"key": "auth_token", "value": "", "type": "string"}], "item": [{"name": "Authentication", "item": [{"name": "Member Register", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"first_name\": \"<PERSON>\",\n  \"last_name\": \"<PERSON><PERSON>\",\n  \"password\": \"password123\",\n  \"phone\": \"******-555-0123\",\n  \"address\": \"123 Main St\",\n  \"city\": \"Toronto\",\n  \"province_state\": \"ON\",\n  \"country\": \"Canada\",\n  \"is_refresh\": true\n}"}, "url": {"raw": "{{base_url}}/v1/api/scrubhub/member/auth/register", "host": ["{{base_url}}"], "path": ["v1", "api", "scrubhub", "member", "auth", "register"]}}}, {"name": "Member <PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"password123\",\n  \"is_refresh\": true\n}"}, "url": {"raw": "{{base_url}}/v1/api/scrubhub/member/auth/login", "host": ["{{base_url}}"], "path": ["v1", "api", "scrubhub", "member", "auth", "login"]}}}, {"name": "Hospital Register", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"hospital_name\": \"Toronto General Hospital\",\n  \"contact_person\": \"Dr<PERSON>\",\n  \"password\": \"password123\",\n  \"phone\": \"******-555-0456\",\n  \"address\": \"200 Elizabeth St\",\n  \"city\": \"Toronto\",\n  \"province_state\": \"ON\",\n  \"country\": \"Canada\",\n  \"is_refresh\": true\n}"}, "url": {"raw": "{{base_url}}/v1/api/scrubhub/hospital/auth/register", "host": ["{{base_url}}"], "path": ["v1", "api", "scrubhub", "hospital", "auth", "register"]}}}, {"name": "Hospital Login", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"password123\",\n  \"is_refresh\": true\n}"}, "url": {"raw": "{{base_url}}/v1/api/scrubhub/hospital/auth/login", "host": ["{{base_url}}"], "path": ["v1", "api", "scrubhub", "hospital", "auth", "login"]}}}, {"name": "Recruiter Register", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"company_name\": \"MedStaff Solutions\",\n  \"contact_person\": \"<PERSON>\",\n  \"first_name\": \"<PERSON>\",\n  \"last_name\": \"<PERSON>\",\n  \"password\": \"password123\",\n  \"phone\": \"******-555-0789\",\n  \"address\": \"456 Bay St\",\n  \"city\": \"Toronto\",\n  \"province_state\": \"ON\",\n  \"country\": \"Canada\",\n  \"is_refresh\": true\n}"}, "url": {"raw": "{{base_url}}/v1/api/scrubhub/recruiter/auth/register", "host": ["{{base_url}}"], "path": ["v1", "api", "scrubhub", "recruiter", "auth", "register"]}}}, {"name": "Re<PERSON><PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"password123\",\n  \"is_refresh\": true\n}"}, "url": {"raw": "{{base_url}}/v1/api/scrubhub/recruiter/auth/login", "host": ["{{base_url}}"], "path": ["v1", "api", "scrubhub", "recruiter", "auth", "login"]}}}]}, {"name": "Locations & Search", "item": [{"name": "Get All Locations", "request": {"method": "GET", "url": {"raw": "{{base_url}}/v1/api/scrubhub/locations", "host": ["{{base_url}}"], "path": ["v1", "api", "scrubhub", "locations"]}}}, {"name": "Get Rentals Near Location", "request": {"method": "GET", "url": {"raw": "{{base_url}}/v1/api/scrubhub/rentals/near/Toronto?page=1&limit=10&radius=25", "host": ["{{base_url}}"], "path": ["v1", "api", "scrubhub", "rentals", "near", "Toronto"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}, {"key": "radius", "value": "25"}]}}}, {"name": "Search Properties by Map Bounds", "request": {"method": "GET", "url": {"raw": "{{base_url}}/v1/api/scrubhub/properties/map?north=43.7&south=43.6&east=-79.3&west=-79.5&limit=50", "host": ["{{base_url}}"], "path": ["v1", "api", "scrubhub", "properties", "map"], "query": [{"key": "north", "value": "43.7"}, {"key": "south", "value": "43.6"}, {"key": "east", "value": "-79.3"}, {"key": "west", "value": "-79.5"}, {"key": "limit", "value": "50"}]}}}]}, {"name": "Medical Schools", "item": [{"name": "Get All Medical Schools", "request": {"method": "GET", "url": {"raw": "{{base_url}}/v1/api/scrubhub/medical-schools?province_state=ON", "host": ["{{base_url}}"], "path": ["v1", "api", "scrubhub", "medical-schools"], "query": [{"key": "province_state", "value": "ON"}]}}}, {"name": "Get Medical School with Nearby Properties", "request": {"method": "GET", "url": {"raw": "{{base_url}}/v1/api/scrubhub/medical-schools/1?radius=25&limit=20", "host": ["{{base_url}}"], "path": ["v1", "api", "scrubhub", "medical-schools", "1"], "query": [{"key": "radius", "value": "25"}, {"key": "limit", "value": "20"}]}}}]}, {"name": "Properties", "item": [{"name": "Search Properties", "request": {"method": "GET", "url": {"raw": "{{base_url}}/v1/api/scrubhub/properties?page=1&limit=10&city=Toronto&min_price=1000&max_price=3000&bedrooms=2&property_type=apartment&furnished=true&near_medical_school=true", "host": ["{{base_url}}"], "path": ["v1", "api", "scrubhub", "properties"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}, {"key": "city", "value": "Toronto"}, {"key": "min_price", "value": "1000"}, {"key": "max_price", "value": "3000"}, {"key": "bedrooms", "value": "2"}, {"key": "property_type", "value": "apartment"}, {"key": "furnished", "value": "true"}, {"key": "near_medical_school", "value": "true"}]}}}, {"name": "Get Property Details", "request": {"method": "GET", "url": {"raw": "{{base_url}}/v1/api/scrubhub/properties/1", "host": ["{{base_url}}"], "path": ["v1", "api", "scrubhub", "properties", "1"]}}}, {"name": "Create Property", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"title\": \"Beautiful 2BR Apartment Near UofT\",\n  \"description\": \"Spacious 2-bedroom apartment perfect for medical students. Walking distance to University of Toronto and major hospitals.\",\n  \"address\": \"123 College Street\",\n  \"city\": \"Toronto\",\n  \"province_state\": \"ON\",\n  \"country\": \"Canada\",\n  \"postal_code\": \"M5S 1A1\",\n  \"latitude\": 43.6629,\n  \"longitude\": -79.3957,\n  \"price\": 2500,\n  \"bedrooms\": 2,\n  \"bathrooms\": 1,\n  \"property_type\": \"apartment\",\n  \"furnished\": true,\n  \"available_from\": \"2024-09-01\",\n  \"lease_duration\": 12,\n  \"utilities_included\": true,\n  \"parking_available\": false,\n  \"pets_allowed\": false,\n  \"tier\": \"paid\",\n  \"duration\": 30\n}"}, "url": {"raw": "{{base_url}}/v1/api/scrubhub/properties", "host": ["{{base_url}}"], "path": ["v1", "api", "scrubhub", "properties"]}}}, {"name": "Update Property", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"title\": \"Updated Beautiful 2BR Apartment Near UofT\",\n  \"price\": 2600,\n  \"description\": \"Updated description with new amenities\"\n}"}, "url": {"raw": "{{base_url}}/v1/api/scrubhub/properties/1", "host": ["{{base_url}}"], "path": ["v1", "api", "scrubhub", "properties", "1"]}}}, {"name": "Delete Property", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/v1/api/scrubhub/properties/1", "host": ["{{base_url}}"], "path": ["v1", "api", "scrubhub", "properties", "1"]}}}, {"name": "Get My Properties", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/v1/api/scrubhub/landlord/properties?page=1&limit=10&status=0", "host": ["{{base_url}}"], "path": ["v1", "api", "scrubhub", "landlord", "properties"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}, {"key": "status", "value": "0"}]}}}]}, {"name": "Favorites & Recently Viewed", "item": [{"name": "Get Favorites", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/v1/api/scrubhub/favorites?page=1&limit=10", "host": ["{{base_url}}"], "path": ["v1", "api", "scrubhub", "favorites"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}}}, {"name": "Add to Favorites", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"property_id\": 1\n}"}, "url": {"raw": "{{base_url}}/v1/api/scrubhub/favorites", "host": ["{{base_url}}"], "path": ["v1", "api", "scrubhub", "favorites"]}}}, {"name": "Remove from Favorites", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/v1/api/scrubhub/favorites/1", "host": ["{{base_url}}"], "path": ["v1", "api", "scrubhub", "favorites", "1"]}}}, {"name": "Get Recently Viewed", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/v1/api/scrubhub/recently-viewed", "host": ["{{base_url}}"], "path": ["v1", "api", "scrubhub", "recently-viewed"]}}}, {"name": "Add to Recently Viewed", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"property_id\": 1\n}"}, "url": {"raw": "{{base_url}}/v1/api/scrubhub/recently-viewed", "host": ["{{base_url}}"], "path": ["v1", "api", "scrubhub", "recently-viewed"]}}}]}, {"name": "Inquiries", "item": [{"name": "Submit Property Inquiry", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"property_id\": 1,\n  \"name\": \"<PERSON>\",\n  \"email\": \"<EMAIL>\",\n  \"phone\": \"******-555-0987\",\n  \"message\": \"Hi, I'm interested in this property for my medical residency. Is it available from September?\"\n}"}, "url": {"raw": "{{base_url}}/v1/api/scrubhub/inquiries", "host": ["{{base_url}}"], "path": ["v1", "api", "scrubhub", "inquiries"]}}}, {"name": "Inquire About Specific Property", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"<PERSON>\",\n  \"email\": \"<EMAIL>\",\n  \"phone\": \"******-555-0987\",\n  \"message\": \"Hi, I'm interested in this property for my medical residency. Is it available from September?\"\n}"}, "url": {"raw": "{{base_url}}/v1/api/scrubhub/properties/1/inquire", "host": ["{{base_url}}"], "path": ["v1", "api", "scrubhub", "properties", "1", "inquire"]}}}, {"name": "Get Property Inquiries (Landlord)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/v1/api/scrubhub/properties/1/inquiries", "host": ["{{base_url}}"], "path": ["v1", "api", "scrubhub", "properties", "1", "inquiries"]}}}, {"name": "Update Inquiry Status", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"status\": 1\n}"}, "url": {"raw": "{{base_url}}/v1/api/scrubhub/inquiries/1/status", "host": ["{{base_url}}"], "path": ["v1", "api", "scrubhub", "inquiries", "1", "status"]}}}, {"name": "Get My Inquiries (Landlord)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/v1/api/scrubhub/inquiries?status=new&page=1&limit=20", "host": ["{{base_url}}"], "path": ["v1", "api", "scrubhub", "inquiries"], "query": [{"key": "status", "value": "new"}, {"key": "page", "value": "1"}, {"key": "limit", "value": "20"}]}}}, {"name": "Mark <PERSON> as Responded", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/v1/api/scrubhub/inquiries/1/respond", "host": ["{{base_url}}"], "path": ["v1", "api", "scrubhub", "inquiries", "1", "respond"]}}}]}, {"name": "Sublets", "item": [{"name": "Create Sublet Request", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"property_id\": 1,\n  \"start_date\": \"2024-06-01\",\n  \"end_date\": \"2024-08-31\",\n  \"price\": 2000,\n  \"description\": \"Summer sublet available for medical students. Fully furnished with all utilities included.\",\n  \"service_tier\": \"self_managed\"\n}"}, "url": {"raw": "{{base_url}}/v1/api/scrubhub/sublets", "host": ["{{base_url}}"], "path": ["v1", "api", "scrubhub", "sublets"]}}}, {"name": "Create Premium Sublet Request", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"property_id\": 1,\n  \"start_date\": \"2024-06-01\",\n  \"end_date\": \"2024-08-31\",\n  \"price\": 3500,\n  \"description\": \"Premium luxury sublet with concierge service. Perfect for visiting medical professionals.\",\n  \"service_tier\": \"premium\"\n}"}, "url": {"raw": "{{base_url}}/v1/api/scrubhub/sublets", "host": ["{{base_url}}"], "path": ["v1", "api", "scrubhub", "sublets"]}}}, {"name": "Get My Sublets", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/v1/api/scrubhub/sublets?page=1&limit=10", "host": ["{{base_url}}"], "path": ["v1", "api", "scrubhub", "sublets"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}}}]}, {"name": "Jobs", "item": [{"name": "Search Jobs", "request": {"method": "GET", "url": {"raw": "{{base_url}}/v1/api/scrubhub/jobs?page=1&limit=10&location=Toronto&type=residency&salary_min=50000&salary_max=80000", "host": ["{{base_url}}"], "path": ["v1", "api", "scrubhub", "jobs"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}, {"key": "location", "value": "Toronto"}, {"key": "type", "value": "residency"}, {"key": "salary_min", "value": "50000"}, {"key": "salary_max", "value": "80000"}]}}}, {"name": "Create Job Posting", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"title\": \"Internal Medicine Resident Position\",\n  \"description\": \"We are seeking a dedicated Internal Medicine resident to join our team at Toronto General Hospital. This position offers excellent training opportunities and mentorship.\",\n  \"location\": \"Toronto, ON\",\n  \"type\": \"residency\",\n  \"salary_min\": 55000,\n  \"salary_max\": 65000,\n  \"requirements\": \"MD degree, MCCEE certification, strong clinical skills\",\n  \"benefits\": \"Health insurance, dental coverage, continuing education allowance\",\n  \"is_paid_posting\": false\n}"}, "url": {"raw": "{{base_url}}/v1/api/scrubhub/jobs", "host": ["{{base_url}}"], "path": ["v1", "api", "scrubhub", "jobs"]}}}, {"name": "Create Paid <PERSON> Posting", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"title\": \"Senior Cardiologist Position\",\n  \"description\": \"Prestigious cardiology position at leading medical center. Seeking experienced cardiologist for full-time staff position.\",\n  \"location\": \"Toronto, ON\",\n  \"type\": \"staff\",\n  \"salary_min\": 250000,\n  \"salary_max\": 350000,\n  \"requirements\": \"Board certification in Cardiology, 5+ years experience\",\n  \"benefits\": \"Comprehensive benefits package, research opportunities, leadership roles\",\n  \"is_paid_posting\": true\n}"}, "url": {"raw": "{{base_url}}/v1/api/scrubhub/jobs", "host": ["{{base_url}}"], "path": ["v1", "api", "scrubhub", "jobs"]}}}, {"name": "Apply to Job", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"applicant_name\": \"Dr. <PERSON>\",\n  \"email\": \"<EMAIL>\",\n  \"resume_url\": \"https://example.com/resumes/sarah-johnson.pdf\",\n  \"cover_letter\": \"I am very interested in this Internal Medicine residency position. My clinical experience and research background make me an ideal candidate.\"\n}"}, "url": {"raw": "{{base_url}}/v1/api/scrubhub/jobs/1/apply", "host": ["{{base_url}}"], "path": ["v1", "api", "scrubhub", "jobs", "1", "apply"]}}}, {"name": "Get Job Applications", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/v1/api/scrubhub/jobs/1/applications", "host": ["{{base_url}}"], "path": ["v1", "api", "scrubhub", "jobs", "1", "applications"]}}}, {"name": "Get Job Applicants", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/v1/api/scrubhub/jobs/1/applicants", "host": ["{{base_url}}"], "path": ["v1", "api", "scrubhub", "jobs", "1", "applicants"]}}}, {"name": "Update Applicant Status", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"status\": \"interviewing\"\n}"}, "url": {"raw": "{{base_url}}/v1/api/scrubhub/applicants/1/status", "host": ["{{base_url}}"], "path": ["v1", "api", "scrubhub", "applicants", "1", "status"]}}}]}, {"name": "Marketplace", "item": [{"name": "Search Marketplace Items", "request": {"method": "GET", "url": {"raw": "{{base_url}}/v1/api/scrubhub/marketplace?page=1&limit=10&category=medical_equipment&item_condition=new&min_price=100&max_price=5000&search=stethoscope", "host": ["{{base_url}}"], "path": ["v1", "api", "scrubhub", "marketplace"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}, {"key": "category", "value": "medical_equipment"}, {"key": "item_condition", "value": "new"}, {"key": "min_price", "value": "100"}, {"key": "max_price", "value": "5000"}, {"key": "search", "value": "stethoscope"}]}}}, {"name": "Create Marketplace Item", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"title\": \"Littmann Cardiology IV Stethoscope\",\n  \"description\": \"Excellent condition Li<PERSON><PERSON> stethoscope, barely used. Perfect for medical students and residents.\",\n  \"price\": 250,\n  \"item_condition\": \"like_new\",\n  \"category\": \"medical_equipment\"\n}"}, "url": {"raw": "{{base_url}}/v1/api/scrubhub/marketplace", "host": ["{{base_url}}"], "path": ["v1", "api", "scrubhub", "marketplace"]}}}, {"name": "Create High-Value Item (Requires Payment)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"title\": \"Ultrasound Machine - GE LOGIQ e\",\n  \"description\": \"Professional ultrasound machine in excellent working condition. Includes all probes and accessories.\",\n  \"price\": 15000,\n  \"item_condition\": \"used\",\n  \"category\": \"medical_equipment\"\n}"}, "url": {"raw": "{{base_url}}/v1/api/scrubhub/marketplace", "host": ["{{base_url}}"], "path": ["v1", "api", "scrubhub", "marketplace"]}}}, {"name": "Reveal Phone Number", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/v1/api/scrubhub/marketplace/1/reveal-phone", "host": ["{{base_url}}"], "path": ["v1", "api", "scrubhub", "marketplace", "1", "reveal-phone"]}}}]}, {"name": "Credit Check & Legal", "item": [{"name": "Request Credit Check", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"first_name\": \"<PERSON>\",\n  \"last_name\": \"<PERSON><PERSON>\",\n  \"date_of_birth\": \"1995-05-15\",\n  \"social_insurance_number\": \"123-456-789\",\n  \"address\": \"123 Main Street\",\n  \"city\": \"Toronto\",\n  \"province_state\": \"ON\",\n  \"postal_code\": \"M5V 3A8\"\n}"}, "url": {"raw": "{{base_url}}/v1/api/scrubhub/credit-check", "host": ["{{base_url}}"], "path": ["v1", "api", "scrubhub", "credit-check"]}}}, {"name": "Get Credit Reports", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/v1/api/scrubhub/credit-reports", "host": ["{{base_url}}"], "path": ["v1", "api", "scrubhub", "credit-reports"]}}}, {"name": "Download Credit Report", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/v1/api/scrubhub/credit-reports/1/download", "host": ["{{base_url}}"], "path": ["v1", "api", "scrubhub", "credit-reports", "1", "download"]}}}, {"name": "Generate N9 Form", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"tenant_name\": \"<PERSON>\",\n  \"tenant_address\": \"123 Main Street, Toronto, ON M5V 3A8\",\n  \"landlord_name\": \"<PERSON>\",\n  \"landlord_address\": \"456 Oak Avenue, Toronto, ON M4W 1A1\",\n  \"rental_address\": \"789 College Street, Toronto, ON M5S 1A1\",\n  \"termination_date\": \"2024-12-31\",\n  \"reason\": \"End of lease term\"\n}"}, "url": {"raw": "{{base_url}}/v1/api/scrubhub/n9-form", "host": ["{{base_url}}"], "path": ["v1", "api", "scrubhub", "n9-form"]}}}, {"name": "Download N9 Form", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/v1/api/scrubhub/n9-form/1/download", "host": ["{{base_url}}"], "path": ["v1", "api", "scrubhub", "n9-form", "1", "download"]}}}]}, {"name": "User Profile & Dashboard", "item": [{"name": "Get Profile", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/v1/api/scrubhub/profile", "host": ["{{base_url}}"], "path": ["v1", "api", "scrubhub", "profile"]}}}, {"name": "Update Profile", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"<PERSON>\",\n  \"phone\": \"******-555-9999\",\n  \"address\": \"456 New Address Street\",\n  \"city\": \"Toronto\",\n  \"province_state\": \"ON\"\n}"}, "url": {"raw": "{{base_url}}/v1/api/scrubhub/profile", "host": ["{{base_url}}"], "path": ["v1", "api", "scrubhub", "profile"]}}}, {"name": "Get Dashboard", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/v1/api/scrubhub/dashboard", "host": ["{{base_url}}"], "path": ["v1", "api", "scrubhub", "dashboard"]}}}]}, {"name": "Subscriptions & Billing", "item": [{"name": "Get Billing Info", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/v1/api/scrubhub/customer/lambda/billing-info", "host": ["{{base_url}}"], "path": ["v1", "api", "scrubhub", "customer", "lambda", "billing-info"]}}}, {"name": "Get Available Plans", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/v1/api/scrubhub/customer/lambda/available-plans", "host": ["{{base_url}}"], "path": ["v1", "api", "scrubhub", "customer", "lambda", "available-plans"]}}}, {"name": "Create Card Token", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"card_number\": \"****************\",\n  \"exp_month\": 12,\n  \"exp_year\": 2025,\n  \"cvc\": \"123\"\n}"}, "url": {"raw": "{{base_url}}/v1/api/scrubhub/customer/lambda/create-card-token", "host": ["{{base_url}}"], "path": ["v1", "api", "scrubhub", "customer", "lambda", "create-card-token"]}}}, {"name": "Create Stripe Customer", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/v1/api/scrubhub/customer/lambda/create-stripe-customer", "host": ["{{base_url}}"], "path": ["v1", "api", "scrubhub", "customer", "lambda", "create-stripe-customer"]}}}, {"name": "Add Payment Method", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"card_token\": \"tok_visa\"\n}"}, "url": {"raw": "{{base_url}}/v1/api/scrubhub/customer/lambda/add-payment-method", "host": ["{{base_url}}"], "path": ["v1", "api", "scrubhub", "customer", "lambda", "add-payment-method"]}}}, {"name": "Create Subscription", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"price_id\": \"price_1234567890\",\n  \"payment_method_id\": \"pm_1234567890\"\n}"}, "url": {"raw": "{{base_url}}/v1/api/scrubhub/customer/lambda/create-subscription", "host": ["{{base_url}}"], "path": ["v1", "api", "scrubhub", "customer", "lambda", "create-subscription"]}}}, {"name": "Cancel Subscription", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/v1/api/scrubhub/customer/lambda/cancel-subscription", "host": ["{{base_url}}"], "path": ["v1", "api", "scrubhub", "customer", "lambda", "cancel-subscription"]}}}, {"name": "Get Available Plans (Legacy)", "request": {"method": "GET", "url": {"raw": "{{base_url}}/v1/api/scrubhub/subscription/plans?target_audience=renters", "host": ["{{base_url}}"], "path": ["v1", "api", "scrubhub", "subscription", "plans"], "query": [{"key": "target_audience", "value": "renters"}]}}}, {"name": "Get Available Plans for Landlords (Legacy)", "request": {"method": "GET", "url": {"raw": "{{base_url}}/v1/api/scrubhub/subscription/plans?target_audience=landlords", "host": ["{{base_url}}"], "path": ["v1", "api", "scrubhub", "subscription", "plans"], "query": [{"key": "target_audience", "value": "landlords"}]}}}, {"name": "Get Subscription Status (Legacy)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/v1/api/scrubhub/subscription", "host": ["{{base_url}}"], "path": ["v1", "api", "scrubhub", "subscription"]}}}, {"name": "Subscribe to Pro Plan (Legacy)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"plan_id\": 1\n}"}, "url": {"raw": "{{base_url}}/v1/api/scrubhub/subscription", "host": ["{{base_url}}"], "path": ["v1", "api", "scrubhub", "subscription"]}}}, {"name": "Subscribe to Landlord Plan (Legacy)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"plan_id\": 2\n}"}, "url": {"raw": "{{base_url}}/v1/api/scrubhub/subscription", "host": ["{{base_url}}"], "path": ["v1", "api", "scrubhub", "subscription"]}}}, {"name": "Cancel Subscription (Legacy)", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/v1/api/scrubhub/subscription", "host": ["{{base_url}}"], "path": ["v1", "api", "scrubhub", "subscription"]}}}]}, {"name": "Admin", "item": [{"name": "Get Admin Dashboard", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/v1/api/scrubhub/admin/dashboard", "host": ["{{base_url}}"], "path": ["v1", "api", "scrubhub", "admin", "dashboard"]}}}, {"name": "Verify User", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/v1/api/scrubhub/admin/verify-user/1", "host": ["{{base_url}}"], "path": ["v1", "api", "scrubhub", "admin", "verify-user", "1"]}}}, {"name": "Approve Sublet", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/v1/api/scrubhub/admin/approve-sublet/1", "host": ["{{base_url}}"], "path": ["v1", "api", "scrubhub", "admin", "approve-sublet", "1"]}}}]}, {"name": "Webhooks", "item": [{"name": "Stripe Webhook", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "stripe-signature", "value": "test_signature"}], "body": {"mode": "raw", "raw": "{\n  \"type\": \"payment_intent.succeeded\",\n  \"data\": {\n    \"object\": {\n      \"id\": \"pi_test_123\",\n      \"amount\": 4999,\n      \"currency\": \"cad\",\n      \"metadata\": {\n        \"user_id\": \"1\",\n        \"purpose\": \"property_listing\",\n        \"property_id\": \"1\"\n      }\n    }\n  }\n}"}, "url": {"raw": "{{base_url}}/v1/api/scrubhub/webhook/stripe", "host": ["{{base_url}}"], "path": ["v1", "api", "scrubhub", "webhook", "stripe"]}}}]}]}