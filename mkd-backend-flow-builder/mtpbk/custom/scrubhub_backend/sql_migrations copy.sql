-- ScrubHub Backend Database Migrations
-- This file contains all SQL statements to update the existing database schema

-- ==================== UPDATE USER TABLE ====================
-- Add new columns to existing user table
ALTER TABLE scrubhub_user
ADD COLUMN IF NOT EXISTS role TINYINT DEFAULT 0 COMMENT '0:tenant,1:landlord,2:hospital,3:recruiter,4:admin',
ADD COLUMN IF NOT EXISTS name VARCHAR(255) NULL,
ADD COLUMN IF NOT EXISTS phone VARCHAR(20) NULL,
ADD COLUMN IF NOT EXISTS address TEXT NULL,
ADD COLUMN IF NOT EXISTS city VARCHAR(100) NULL,
ADD COLUMN IF NOT EXISTS province_state VARCHAR(100) NULL,
ADD COLUMN IF NOT EXISTS country VARCHAR(100) DEFAULT 'Canada',
ADD COLUMN IF NOT EXISTS verified BOOLEAN DEFAULT 0,
ADD COLUMN IF NOT EXISTS verification_documents JSON NULL,
ADD COLUMN IF NOT EXISTS stripe_customer_id VARCHAR(255) NULL COMMENT 'Stripe customer ID for billing',
ADD COLUMN IF NOT EXISTS profile_photo VARCHAR(500) NULL COMMENT 'Profile photo file path';

-- ==================== CREATE NEW TABLES ====================

-- Locations table (for stored location coordinates)
CREATE TABLE IF NOT EXISTS scrubhub_location (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    city VARCHAR(100) NOT NULL,
    province_state VARCHAR(100) NOT NULL,
    country VARCHAR(100) DEFAULT 'Canada',
    latitude DECIMAL(10,8) NOT NULL,
    longitude DECIMAL(11,8) NOT NULL,
    distance_to_medical_schools DECIMAL(5,2) DEFAULT 0 COMMENT 'Average distance to nearby medical schools in km',
    popularity_score INT DEFAULT 0 COMMENT 'Popularity score based on searches and activity',
    search_count INT DEFAULT 0 COMMENT 'Number of times this location was searched',
    property_count INT DEFAULT 0 COMMENT 'Number of properties in this location',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_city (city),
    INDEX idx_province_state (province_state),
    INDEX idx_coordinates (latitude, longitude),
    INDEX idx_popularity (popularity_score),
    INDEX idx_search_count (search_count),
    UNIQUE KEY unique_location (name, city, province_state)
);

-- Medical Schools table
CREATE TABLE IF NOT EXISTS scrubhub_medical_school (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    short_name VARCHAR(100),
    address TEXT,
    city VARCHAR(100) NOT NULL,
    province_state VARCHAR(100) NOT NULL,
    country VARCHAR(100) DEFAULT 'Canada',
    latitude DECIMAL(10,8) NOT NULL,
    longitude DECIMAL(11,8) NOT NULL,
    website VARCHAR(255),
    phone VARCHAR(20),
    email VARCHAR(255),
    programs JSON COMMENT 'Available medical programs',
    popularity_score INT DEFAULT 0 COMMENT 'Popularity score based on searches and proximity searches',
    search_count INT DEFAULT 0 COMMENT 'Number of times this school was searched',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_city (city),
    INDEX idx_province_state (province_state),
    INDEX idx_coordinates (latitude, longitude),
    INDEX idx_popularity (popularity_score),
    INDEX idx_search_count (search_count)
);

-- Universities table
CREATE TABLE IF NOT EXISTS scrubhub_university (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    short_name VARCHAR(100),
    address TEXT,
    city VARCHAR(100) NOT NULL,
    province_state VARCHAR(100) NOT NULL,
    country VARCHAR(100) DEFAULT 'Canada',
    latitude DECIMAL(10,8) NOT NULL,
    longitude DECIMAL(11,8) NOT NULL,
    website VARCHAR(255),
    phone VARCHAR(20),
    email VARCHAR(255),
    type ENUM('university', 'college', 'community_college', 'technical_institute') DEFAULT 'university',
    student_population INT DEFAULT 0,
    programs JSON COMMENT 'Available programs and faculties',
    popularity_score INT DEFAULT 0 COMMENT 'Popularity score based on searches and proximity searches',
    search_count INT DEFAULT 0 COMMENT 'Number of times this university was searched',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_city (city),
    INDEX idx_province_state (province_state),
    INDEX idx_coordinates (latitude, longitude),
    INDEX idx_type (type),
    INDEX idx_popularity (popularity_score),
    INDEX idx_search_count (search_count)
);

-- Hospitals table
CREATE TABLE IF NOT EXISTS scrubhub_hospital (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    short_name VARCHAR(100),
    address TEXT,
    city VARCHAR(100) NOT NULL,
    province_state VARCHAR(100) NOT NULL,
    country VARCHAR(100) DEFAULT 'Canada',
    latitude DECIMAL(10,8) NOT NULL,
    longitude DECIMAL(11,8) NOT NULL,
    website VARCHAR(255),
    phone VARCHAR(20),
    email VARCHAR(255),
    type ENUM('general', 'specialty', 'teaching', 'research', 'community') DEFAULT 'general',
    bed_count INT DEFAULT 0,
    specialties JSON COMMENT 'Medical specialties and departments',
    popularity_score INT DEFAULT 0 COMMENT 'Popularity score based on searches and job postings',
    search_count INT DEFAULT 0 COMMENT 'Number of times this hospital was searched',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_city (city),
    INDEX idx_province_state (province_state),
    INDEX idx_coordinates (latitude, longitude),
    INDEX idx_type (type),
    INDEX idx_popularity (popularity_score),
    INDEX idx_search_count (search_count)
);

-- Properties table
CREATE TABLE IF NOT EXISTS scrubhub_property (
    id INT AUTO_INCREMENT PRIMARY KEY,
    landlord_id INT NOT NULL,
    location_id INT NULL COMMENT 'Reference to scrubhub_location table',
    title VARCHAR(255) NOT NULL,
    description TEXT,
    address TEXT NOT NULL,
    city VARCHAR(100) NOT NULL,
    province_state VARCHAR(100) NOT NULL,
    country VARCHAR(100) DEFAULT 'Canada',
    latitude DECIMAL(10,8) NULL COMMENT 'Property coordinates for distance calculations',
    longitude DECIMAL(11,8) NULL,
    price DECIMAL(10,2) NOT NULL,
    tier TINYINT DEFAULT 0 COMMENT '0:free,1:paid,2:featured',
    duration_days INT DEFAULT 30,
    status TINYINT DEFAULT 0 COMMENT '0:active,1:inactive,2:expired,3:draft',
    amenities JSON,
    property_type VARCHAR(50),
    bedrooms INT,
    bathrooms INT,
    furnished BOOLEAN DEFAULT 0,
    available_from DATE,
    expires_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_landlord_id (landlord_id),
    INDEX idx_location_id (location_id),
    INDEX idx_city (city),
    INDEX idx_status (status),
    INDEX idx_tier (tier),
    INDEX idx_price (price),
    INDEX idx_coordinates (latitude, longitude),
    FOREIGN KEY (landlord_id) REFERENCES scrubhub_user(id) ON DELETE CASCADE,
    FOREIGN KEY (location_id) REFERENCES scrubhub_location(id) ON DELETE SET NULL
);

-- Property photos table
CREATE TABLE IF NOT EXISTS scrubhub_property_photo (
    id INT AUTO_INCREMENT PRIMARY KEY,
    property_id INT NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_name VARCHAR(255),
    sort_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_property_id (property_id),
    INDEX idx_sort_order (sort_order),
    FOREIGN KEY (property_id) REFERENCES scrubhub_property(id) ON DELETE CASCADE
);

-- Favorites table
CREATE TABLE IF NOT EXISTS scrubhub_favorite (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    property_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY unique_favorite (user_id, property_id),
    INDEX idx_user_id (user_id),
    INDEX idx_property_id (property_id),
    FOREIGN KEY (user_id) REFERENCES scrubhub_user(id) ON DELETE CASCADE,
    FOREIGN KEY (property_id) REFERENCES scrubhub_property(id) ON DELETE CASCADE
);

-- Recently Viewed table
CREATE TABLE IF NOT EXISTS scrubhub_recently_viewed (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    property_id INT NOT NULL,
    viewed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id),
    INDEX idx_property_id (property_id),
    INDEX idx_viewed_at (viewed_at),
    FOREIGN KEY (user_id) REFERENCES scrubhub_user(id) ON DELETE CASCADE,
    FOREIGN KEY (property_id) REFERENCES scrubhub_property(id) ON DELETE CASCADE
);

-- Inquiries table
CREATE TABLE IF NOT EXISTS scrubhub_inquiry (
    id INT AUTO_INCREMENT PRIMARY KEY,
    property_id INT NOT NULL,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) NOT NULL,
    phone VARCHAR(20),
    message TEXT,
    status TINYINT DEFAULT 0 COMMENT '0=new, 1=responded',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_property_id (property_id),
    INDEX idx_email (email),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at),
    FOREIGN KEY (property_id) REFERENCES scrubhub_property(id) ON DELETE CASCADE
);

-- Sublets table
CREATE TABLE IF NOT EXISTS scrubhub_sublet (
    id INT AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT NOT NULL,
    property_id INT NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    price DECIMAL(10,2) NOT NULL,
    description TEXT,
    service_tier ENUM('self_managed', 'premium') DEFAULT 'self_managed',
    status TINYINT DEFAULT 0 COMMENT '0=active, 1=inactive, 2=expired, 3=draft, 4=pending_admin_review',
    popularity_score INT DEFAULT 0 COMMENT 'Popularity score based on views and inquiries',
    view_count INT DEFAULT 0 COMMENT 'Number of times this sublet was viewed',
    inquiry_count INT DEFAULT 0 COMMENT 'Number of inquiries received',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_tenant_id (tenant_id),
    INDEX idx_property_id (property_id),
    INDEX idx_status (status),
    INDEX idx_dates (start_date, end_date),
    INDEX idx_popularity (popularity_score),
    INDEX idx_view_count (view_count),
    FOREIGN KEY (tenant_id) REFERENCES scrubhub_user(id) ON DELETE CASCADE,
    FOREIGN KEY (property_id) REFERENCES scrubhub_property(id) ON DELETE CASCADE
);

-- Favorites table
CREATE TABLE IF NOT EXISTS scrubhub_favorite (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    property_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY unique_favorite (user_id, property_id),
    INDEX idx_user_id (user_id),
    INDEX idx_property_id (property_id),
    FOREIGN KEY (user_id) REFERENCES scrubhub_user(id) ON DELETE CASCADE,
    FOREIGN KEY (property_id) REFERENCES scrubhub_property(id) ON DELETE CASCADE
);

-- Recently viewed table
CREATE TABLE IF NOT EXISTS scrubhub_recently_viewed (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    property_id INT NOT NULL,
    viewed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY unique_recent_view (user_id, property_id),
    INDEX idx_user_id (user_id),
    INDEX idx_property_id (property_id),
    INDEX idx_viewed_at (viewed_at),
    FOREIGN KEY (user_id) REFERENCES scrubhub_user(id) ON DELETE CASCADE,
    FOREIGN KEY (property_id) REFERENCES scrubhub_property(id) ON DELETE CASCADE
);

-- Credit reports table
CREATE TABLE IF NOT EXISTS scrubhub_credit_report (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    trustee_reference VARCHAR(255) NOT NULL,
    report_url VARCHAR(500),
    expires_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id),
    INDEX idx_expires_at (expires_at),
    FOREIGN KEY (user_id) REFERENCES scrubhub_user(id) ON DELETE CASCADE
);

-- Tenant notices table
CREATE TABLE IF NOT EXISTS scrubhub_tenant_notice (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    notice_type VARCHAR(50) NOT NULL,
    pdf_url VARCHAR(500) NOT NULL,
    form_data JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id),
    INDEX idx_notice_type (notice_type),
    FOREIGN KEY (user_id) REFERENCES scrubhub_user(id) ON DELETE CASCADE
);

-- Marketplace items table
CREATE TABLE IF NOT EXISTS scrubhub_marketplace_item (
    id INT AUTO_INCREMENT PRIMARY KEY,
    seller_id INT NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    price DECIMAL(10,2) NOT NULL,
    item_condition VARCHAR(50),
    category VARCHAR(100),
    status TINYINT DEFAULT 0 COMMENT '0:active,1:sold,2:removed,3:pending_approval',
    phone_revealed BOOLEAN DEFAULT 0,
    listing_fee DECIMAL(10,2) DEFAULT 0.00,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_seller_id (seller_id),
    INDEX idx_status (status),
    INDEX idx_category (category),
    INDEX idx_price (price),
    FOREIGN KEY (seller_id) REFERENCES scrubhub_user(id) ON DELETE CASCADE
);

-- Marketplace Item Photos table
CREATE TABLE IF NOT EXISTS scrubhub_marketplace_item_photo (
    id INT AUTO_INCREMENT PRIMARY KEY,
    item_id INT NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    sort_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_item_id (item_id),
    INDEX idx_sort_order (sort_order),
    FOREIGN KEY (item_id) REFERENCES scrubhub_marketplace_item(id) ON DELETE CASCADE
);

-- Update existing job table structure
ALTER TABLE scrubhub_job 
DROP COLUMN IF EXISTS task,
DROP COLUMN IF EXISTS arguments,
DROP COLUMN IF EXISTS time_interval,
DROP COLUMN IF EXISTS retries,
ADD COLUMN IF NOT EXISTS poster_id INT NOT NULL AFTER id,
ADD COLUMN IF NOT EXISTS title VARCHAR(255) NOT NULL AFTER poster_id,
ADD COLUMN IF NOT EXISTS description TEXT NOT NULL AFTER title,
ADD COLUMN IF NOT EXISTS location VARCHAR(255) NOT NULL AFTER description,
ADD COLUMN IF NOT EXISTS type VARCHAR(50) NOT NULL AFTER location,
ADD COLUMN IF NOT EXISTS department VARCHAR(100) AFTER type,
ADD COLUMN IF NOT EXISTS salary_min DECIMAL(10,2) AFTER department,
ADD COLUMN IF NOT EXISTS salary_max DECIMAL(10,2) AFTER salary_min,
ADD COLUMN IF NOT EXISTS requirements TEXT AFTER salary_max,
ADD COLUMN IF NOT EXISTS contact_email VARCHAR(255) AFTER requirements,
ADD COLUMN IF NOT EXISTS contact_phone VARCHAR(20) AFTER contact_email,
ADD COLUMN IF NOT EXISTS posting_fee DECIMAL(10,2) DEFAULT 0.00 AFTER contact_phone,
ADD COLUMN IF NOT EXISTS posting_fee_rate VARCHAR(50) DEFAULT 'standard' AFTER posting_fee,
ADD COLUMN IF NOT EXISTS expires_at TIMESTAMP AFTER status,
MODIFY COLUMN status TINYINT DEFAULT 0 COMMENT '0:active,1:closed,2:draft';

-- Add foreign key to job table
ALTER TABLE scrubhub_job 
ADD CONSTRAINT fk_job_poster 
FOREIGN KEY (poster_id) REFERENCES scrubhub_user(id) ON DELETE CASCADE;

-- Job applicants table
CREATE TABLE IF NOT EXISTS scrubhub_job_applicant (
    id INT AUTO_INCREMENT PRIMARY KEY,
    job_id INT NOT NULL,
    applicant_name VARCHAR(255) NOT NULL,
    resume_url VARCHAR(500),
    email VARCHAR(255) NOT NULL,
    phone VARCHAR(20),
    cover_letter TEXT,
    status TINYINT DEFAULT 0 COMMENT '0:applied,1:interviewing,2:rejected,3:hired',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_job_id (job_id),
    INDEX idx_status (status),
    INDEX idx_email (email),
    FOREIGN KEY (job_id) REFERENCES scrubhub_job(id) ON DELETE CASCADE
);

-- Payments table
CREATE TABLE IF NOT EXISTS scrubhub_payment (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'CAD',
    purpose VARCHAR(100) NOT NULL COMMENT 'property_listing,sublet_fee,job_posting,marketplace_listing,phone_reveal,credit_check,n9_form,subscription',
    stripe_transaction_id VARCHAR(255),
    status TINYINT DEFAULT 0 COMMENT '0:pending,1:completed,2:failed,3:refunded',
    metadata JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id),
    INDEX idx_status (status),
    INDEX idx_purpose (purpose),
    INDEX idx_stripe_transaction_id (stripe_transaction_id),
    FOREIGN KEY (user_id) REFERENCES scrubhub_user(id) ON DELETE CASCADE
);

-- Admin actions table
CREATE TABLE IF NOT EXISTS scrubhub_admin_action (
    id INT AUTO_INCREMENT PRIMARY KEY,
    admin_id INT,
    action_type VARCHAR(100) NOT NULL,
    target_id INT,
    target_table VARCHAR(50),
    notes TEXT,
    metadata JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_admin_id (admin_id),
    INDEX idx_action_type (action_type),
    INDEX idx_target (target_table, target_id),
    FOREIGN KEY (admin_id) REFERENCES scrubhub_user(id) ON DELETE SET NULL
);

-- ==================== CREATE INDEXES FOR PERFORMANCE ====================

-- Additional indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_property_search ON scrubhub_property (city, price, bedrooms, status);
CREATE INDEX IF NOT EXISTS idx_job_search ON scrubhub_job (location, type, status);
CREATE INDEX IF NOT EXISTS idx_marketplace_search ON scrubhub_marketplace_item (category, price, status);

-- ==================== SUBSCRIPTION SYSTEM TABLES ====================

-- Stripe Products table
CREATE TABLE IF NOT EXISTS scrubhub_stripe_product (
    id INT AUTO_INCREMENT PRIMARY KEY,
    stripe_product_id VARCHAR(255) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    active TINYINT DEFAULT 1,
    metadata JSON,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_stripe_product_id (stripe_product_id),
    INDEX idx_product_active (active)
);

-- Stripe Prices table
CREATE TABLE IF NOT EXISTS scrubhub_stripe_price (
    id INT AUTO_INCREMENT PRIMARY KEY,
    stripe_price_id VARCHAR(255) UNIQUE NOT NULL,
    stripe_product_id VARCHAR(255) NOT NULL,
    unit_amount INT NOT NULL COMMENT 'Amount in cents',
    currency VARCHAR(3) DEFAULT 'CAD',
    recurring_interval VARCHAR(20) COMMENT 'month, year, etc.',
    recurring_interval_count INT DEFAULT 1,
    nickname VARCHAR(255),
    active TINYINT DEFAULT 1,
    trial_period_days INT DEFAULT 0,
    metadata JSON,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_stripe_price_id (stripe_price_id),
    INDEX idx_price_product (stripe_product_id),
    INDEX idx_price_active (active)
);

-- Subscription Plans table
CREATE TABLE IF NOT EXISTS scrubhub_subscription_plan (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    price DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'CAD',
    stripe_price_id VARCHAR(255),
    stripe_product_id VARCHAR(255),
    target_audience ENUM('renters', 'landlords', 'hospitals', 'recruiters') NOT NULL,
    features JSON,
    trial_days INT DEFAULT 0,
    active TINYINT DEFAULT 1,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_plan_target (target_audience),
    INDEX idx_plan_active (active)
);

-- User Subscriptions table (updated to match new structure)
CREATE TABLE IF NOT EXISTS scrubhub_subscription (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    plan_id INT,
    stripe_subscription_id VARCHAR(255),
    stripe_customer_id VARCHAR(255),
    payment_type ENUM('subscription', 'onetime') DEFAULT 'subscription',
    status ENUM('active', 'canceled', 'past_due', 'unpaid', 'trialing') DEFAULT 'trialing',
    current_period_start DATETIME,
    current_period_end DATETIME,
    trial_start DATETIME,
    trial_end DATETIME,
    canceled_at DATETIME,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES scrubhub_user(id) ON DELETE CASCADE,
    FOREIGN KEY (plan_id) REFERENCES scrubhub_subscription_plan(id) ON DELETE SET NULL,
    INDEX idx_user_subscription (user_id),
    INDEX idx_subscription_status (status),
    INDEX idx_stripe_subscription (stripe_subscription_id),
    INDEX idx_stripe_customer (stripe_customer_id),
    INDEX idx_payment_type (payment_type)
);

-- Enterprise Bulk Listing Applications table
CREATE TABLE IF NOT EXISTS scrubhub_enterprise_application (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL COMMENT 'Recruiter or hospital user ID',
    agency_name VARCHAR(255) NOT NULL,
    contact_name VARCHAR(255) NOT NULL,
    contact_email VARCHAR(255) NOT NULL,
    contact_phone VARCHAR(20),
    agency_address TEXT,
    agency_website VARCHAR(255),
    monthly_job_postings VARCHAR(50) NOT NULL COMMENT 'Range like 1-10, 11-50, 51-100, 100+',
    client_companies_served VARCHAR(50) NOT NULL COMMENT 'Range like 1-5, 6-15, 16-50, 50+',
    industry_specialty VARCHAR(100) COMMENT 'Primary industry focus',
    additional_requirements TEXT,
    status TINYINT DEFAULT 0 COMMENT '0=pending, 1=approved, 2=rejected, 3=under_review',
    admin_notes TEXT COMMENT 'Admin notes for review',
    reviewed_by INT NULL COMMENT 'Admin user who reviewed',
    reviewed_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id),
    INDEX idx_status (status),
    INDEX idx_contact_email (contact_email),
    INDEX idx_reviewed_by (reviewed_by),
    INDEX idx_created_at (created_at),
    FOREIGN KEY (user_id) REFERENCES scrubhub_user(id) ON DELETE CASCADE,
    FOREIGN KEY (reviewed_by) REFERENCES scrubhub_user(id) ON DELETE SET NULL
);

-- User Preferences table
CREATE TABLE IF NOT EXISTS scrubhub_preference (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    first_name VARCHAR(100),
    last_name VARCHAR(100),
    phone VARCHAR(20),
    address TEXT,
    city VARCHAR(100),
    province_state VARCHAR(100),
    country VARCHAR(100) DEFAULT 'Canada',
    hospital_name VARCHAR(255),
    contact_person VARCHAR(255),
    company_name VARCHAR(255),
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES scrubhub_user(id) ON DELETE CASCADE,
    INDEX idx_user_preference (user_id)
);

-- Tokens table for refresh tokens and verification
CREATE TABLE IF NOT EXISTS scrubhub_tokens (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    token TEXT NOT NULL,
    code TEXT,
    type TINYINT DEFAULT 1 COMMENT '1=refresh_token, 2=verification, 3=password_reset',
    data JSON,
    expired_at DATETIME,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES scrubhub_user(id) ON DELETE CASCADE,
    INDEX idx_user_token (user_id),
    INDEX idx_token_type (type),
    INDEX idx_token_expired (expired_at)
);

-- ==================== INSERT DEFAULT DATA ====================

-- Insert default admin user (password: admin123)
INSERT IGNORE INTO scrubhub_user (email, password, name, role, verified, status, created_at, updated_at)
VALUES ('<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Admin User', 4, 1, 0, NOW(), NOW());

-- ==================== SAMPLE DATA FOR TESTING ====================

-- Sample locations (major Canadian cities with medical schools)
INSERT IGNORE INTO scrubhub_location (name, city, province_state, country, latitude, longitude, distance_to_medical_schools) VALUES
('Toronto Downtown', 'Toronto', 'ON', 'Canada', 43.6532, -79.3832, 2.5),
('Vancouver West End', 'Vancouver', 'BC', 'Canada', 49.2827, -123.1207, 3.2),
('Montreal Plateau', 'Montreal', 'QC', 'Canada', 45.5017, -73.5673, 1.8),
('Calgary Beltline', 'Calgary', 'AB', 'Canada', 51.0447, -114.0719, 4.1),
('Ottawa Centretown', 'Ottawa', 'ON', 'Canada', 45.4215, -75.6972, 2.9),
('Halifax Downtown', 'Halifax', 'NS', 'Canada', 44.6488, -63.5752, 1.5),
('Winnipeg Exchange District', 'Winnipeg', 'MB', 'Canada', 49.8951, -97.1384, 3.8),
('Saskatoon University Area', 'Saskatoon', 'SK', 'Canada', 52.1332, -106.6700, 2.1),
('Edmonton University Area', 'Edmonton', 'AB', 'Canada', 53.5461, -113.4938, 1.9),
('Quebec City Old Town', 'Quebec City', 'QC', 'Canada', 46.8139, -71.2080, 2.7);

-- Sample medical schools
INSERT IGNORE INTO scrubhub_medical_school (name, short_name, address, city, province_state, country, latitude, longitude, website, phone, programs, popularity_score) VALUES
('University of Toronto Faculty of Medicine', 'U of T Medicine', '1 King\'s College Circle', 'Toronto', 'ON', 'Canada', 43.6629, -79.3957, 'https://medicine.utoronto.ca', '******-978-2011', '["MD Program", "MD/PhD Program", "Residency Programs"]', 95),
('McGill University Faculty of Medicine', 'McGill Medicine', '3655 Promenade Sir William Osler', 'Montreal', 'QC', 'Canada', 45.5048, -73.5772, 'https://www.mcgill.ca/medicine', '******-398-3515', '["MD Program", "MD/PhD Program", "Residency Programs"]', 92),
('University of British Columbia Faculty of Medicine', 'UBC Medicine', '317-2194 Health Sciences Mall', 'Vancouver', 'BC', 'Canada', 49.2606, -123.2460, 'https://med.ubc.ca', '******-822-2421', '["MD Program", "MD/PhD Program", "Residency Programs"]', 88),
('University of Calgary Cumming School of Medicine', 'UCalgary Medicine', '3330 Hospital Drive NW', 'Calgary', 'AB', 'Canada', 51.0775, -114.1293, 'https://cumming.ucalgary.ca', '******-220-4262', '["MD Program", "MD/PhD Program", "Residency Programs"]', 82),
('University of Alberta Faculty of Medicine & Dentistry', 'UAlberta Medicine', '2J2.00 WC Mackenzie Health Sciences Centre', 'Edmonton', 'AB', 'Canada', 53.5232, -113.5263, 'https://www.ualberta.ca/medicine', '+1-************', '["MD Program", "MD/PhD Program", "Residency Programs"]', 79),
('Dalhousie University Faculty of Medicine', 'Dalhousie Medicine', '5849 University Avenue', 'Halifax', 'NS', 'Canada', 44.6369, -63.5906, 'https://medicine.dal.ca', '+1-************', '["MD Program", "MD/PhD Program", "Residency Programs"]', 75),
('University of Ottawa Faculty of Medicine', 'uOttawa Medicine', '451 Smyth Road', 'Ottawa', 'ON', 'Canada', 45.3839, -75.6445, 'https://med.uottawa.ca', '+1-************', '["MD Program", "MD/PhD Program", "Residency Programs"]', 73),
('University of Manitoba Max Rady College of Medicine', 'UManitoba Medicine', '753 McDermot Avenue', 'Winnipeg', 'MB', 'Canada', 49.9139, -97.1451, 'https://umanitoba.ca/medicine', '+1-************', '["MD Program", "MD/PhD Program", "Residency Programs"]', 68),
('University of Saskatchewan College of Medicine', 'USask Medicine', '107 Wiggins Road', 'Saskatoon', 'SK', 'Canada', 52.1304, -106.6336, 'https://medicine.usask.ca', '******-966-8554', '["MD Program", "MD/PhD Program", "Residency Programs"]', 65),
('Université Laval Faculty of Medicine', 'Laval Medicine', '1050 Avenue de la Médecine', 'Quebec City', 'QC', 'Canada', 46.7799, -71.2756, 'https://www.fmed.ulaval.ca', '******-656-2131', '["MD Program", "MD/PhD Program", "Residency Programs"]', 70);

-- Sample universities
INSERT IGNORE INTO scrubhub_university (name, short_name, address, city, province_state, country, latitude, longitude, website, phone, type, student_population, programs, popularity_score) VALUES
('University of Toronto', 'U of T', '27 King\'s College Circle', 'Toronto', 'ON', 'Canada', 43.6629, -79.3957, 'https://www.utoronto.ca', '******-978-2011', 'university', 97000, '["Medicine", "Engineering", "Business", "Arts & Science", "Law"]', 98),
('McGill University', 'McGill', '845 Sherbrooke Street West', 'Montreal', 'QC', 'Canada', 45.5048, -73.5772, 'https://www.mcgill.ca', '******-398-4455', 'university', 40000, '["Medicine", "Engineering", "Management", "Arts", "Science"]', 94),
('University of British Columbia', 'UBC', '2329 West Mall', 'Vancouver', 'BC', 'Canada', 49.2606, -123.2460, 'https://www.ubc.ca', '******-822-2211', 'university', 65000, '["Medicine", "Engineering", "Business", "Arts", "Science"]', 91),
('University of Waterloo', 'Waterloo', '200 University Avenue West', 'Waterloo', 'ON', 'Canada', 43.4723, -80.5449, 'https://uwaterloo.ca', '******-888-4567', 'university', 42000, '["Engineering", "Computer Science", "Mathematics", "Business"]', 89),
('University of Alberta', 'UAlberta', '116 Street & 85 Avenue', 'Edmonton', 'AB', 'Canada', 53.5232, -113.5263, 'https://www.ualberta.ca', '+1-************', 'university', 38000, '["Medicine", "Engineering", "Business", "Arts", "Science"]', 85),
('York University', 'York', '4700 Keele Street', 'Toronto', 'ON', 'Canada', 43.7735, -79.5019, 'https://www.yorku.ca', '******-736-2100', 'university', 53000, '["Business", "Liberal Arts", "Science", "Health", "Education"]', 78),
('Ryerson University', 'Ryerson', '350 Victoria Street', 'Toronto', 'ON', 'Canada', 43.6577, -79.3788, 'https://www.ryerson.ca', '******-979-5000', 'university', 45000, '["Engineering", "Business", "Media", "Science", "Arts"]', 76),
('University of Calgary', 'UCalgary', '2500 University Drive NW', 'Calgary', 'AB', 'Canada', 51.0775, -114.1293, 'https://www.ucalgary.ca', '******-220-5110', 'university', 33000, '["Medicine", "Engineering", "Business", "Arts", "Science"]', 82),
('Dalhousie University', 'Dalhousie', '6299 South Street', 'Halifax', 'NS', 'Canada', 44.6369, -63.5906, 'https://www.dal.ca', '+1-************', 'university', 20000, '["Medicine", "Engineering", "Management", "Arts", "Science"]', 74),
('University of Ottawa', 'uOttawa', '75 Laurier Avenue East', 'Ottawa', 'ON', 'Canada', 45.4215, -75.6839, 'https://www.uottawa.ca', '+1-************', 'university', 42000, '["Medicine", "Engineering", "Management", "Arts", "Science"]', 80);

-- Sample hospitals
INSERT IGNORE INTO scrubhub_hospital (name, short_name, address, city, province_state, country, latitude, longitude, website, phone, type, bed_count, specialties, popularity_score) VALUES
('Toronto General Hospital', 'TGH', '200 Elizabeth Street', 'Toronto', 'ON', 'Canada', 43.6591, -79.3890, 'https://www.uhn.ca', '******-340-4800', 'teaching', 727, '["Cardiology", "Oncology", "Transplant", "Emergency", "Surgery"]', 96),
('Montreal General Hospital', 'MGH', '1650 Cedar Avenue', 'Montreal', 'QC', 'Canada', 45.4995, -73.5848, 'https://www.muhc.ca', '******-934-1934', 'teaching', 832, '["Emergency", "Surgery", "Internal Medicine", "Pediatrics"]', 89),
('Vancouver General Hospital', 'VGH', '899 West 12th Avenue', 'Vancouver', 'BC', 'Canada', 49.2627, -123.1207, 'https://www.vch.ca', '+1-************', 'teaching', 950, '["Emergency", "Trauma", "Cardiology", "Oncology", "Surgery"]', 92),
('Calgary Foothills Medical Centre', 'FMC', '1403 29 Street NW', 'Calgary', 'AB', 'Canada', 51.0918, -114.1285, 'https://www.albertahealthservices.ca', '+1-************', 'teaching', 1000, '["Emergency", "Trauma", "Neurology", "Surgery", "Pediatrics"]', 85),
('University of Alberta Hospital', 'UAH', '8440 112 Street NW', 'Edmonton', 'AB', 'Canada', 53.5232, -113.5263, 'https://www.albertahealthservices.ca', '+1-************', 'teaching', 766, '["Emergency", "Surgery", "Internal Medicine", "Oncology"]', 83),
('Ottawa Hospital - Civic Campus', 'TOH Civic', '1053 Carling Avenue', 'Ottawa', 'ON', 'Canada', 45.3839, -75.7572, 'https://www.ottawahospital.on.ca', '+1-************', 'teaching', 1200, '["Emergency", "Trauma", "Surgery", "Internal Medicine"]', 81),
('Halifax Infirmary', 'QEII', '1796 Summer Street', 'Halifax', 'NS', 'Canada', 44.6369, -63.5906, 'https://www.nshealth.ca', '+1-************', 'teaching', 650, '["Emergency", "Surgery", "Internal Medicine", "Cardiology"]', 78),
('Health Sciences Centre Winnipeg', 'HSC', '820 Sherbrook Street', 'Winnipeg', 'MB', 'Canada', 49.9139, -97.1451, 'https://www.hsc.mb.ca', '+1-************', 'teaching', 700, '["Emergency", "Trauma", "Surgery", "Pediatrics"]', 76),
('Royal University Hospital', 'RUH', '103 Hospital Drive', 'Saskatoon', 'SK', 'Canada', 52.1304, -106.6336, 'https://www.saskatoonhealthregion.ca', '+1-************', 'teaching', 500, '["Emergency", "Surgery", "Internal Medicine", "Oncology"]', 72),
('CHU de Québec', 'CHU Quebec', '1401 18e Rue', 'Quebec City', 'QC', 'Canada', 46.7799, -71.2756, 'https://www.chudequebec.ca', '+1-************', 'teaching', 1350, '["Emergency", "Surgery", "Internal Medicine", "Pediatrics"]', 79);

-- ==================== NOTES ====================
-- 1. All tables use the scrubhub_ prefix to avoid conflicts
-- 2. Foreign key constraints ensure data integrity
-- 3. Indexes are created for commonly queried columns
-- 4. JSON columns are used for flexible data storage (amenities, metadata, etc.)
-- 5. TIMESTAMP columns use CURRENT_TIMESTAMP for automatic date handling
-- 6. Status fields use TINYINT with comments for clarity
-- 7. The existing job table is modified to match the new requirements
-- 8. All price fields use DECIMAL(10,2) for accurate currency handling
