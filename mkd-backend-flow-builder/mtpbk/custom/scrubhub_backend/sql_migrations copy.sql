1. Job Posting API Fields - POST /v1/api/scrubhub/jobs
Required Fields:

title - Job title (string)
description - Job description (string)
location - Job location address (string)
Job Location Type:

location_type - 'in_person' | 'remote' | 'hybrid' (default: 'in_person')
Job Type:

job_type - 'full_time' | 'part_time' | 'permanent' | 'fixed_term_contract' | 'casual' | 'seasonal' | 'freelance' | 'apprenticeship' | 'internship' | 'co_op' (default: 'full_time')
Start Date:

has_start_date - <PERSON>olean (default: false)
start_date - Date string YYYY-MM-DD (required if has_start_date is true)
Payment Type:

payment_type - 'hourly' | 'daily' | 'weekly' | 'monthly' | 'yearly' | 'per_shift' | 'commission' | 'contract' (default: 'yearly')
show_pay_by - 'range' | 'starting_amount' | 'maximum_amount' | 'exact_amount' (default: 'range')
salary_min - Number (optional)
salary_max - Number (optional)
Communication & Application Preferences:

daily_updates_email - Email string (optional)
resume_required - Boolean (default: true)
allow_email_contact - <PERSON><PERSON><PERSON> (default: false)
Application Deadline:

has_application_deadline - <PERSON><PERSON><PERSON> (default: false)
application_deadline - Date string YYYY-MM-DD (required if has_application_deadline is true)
Posting Duration:

posting_duration_days - Number (default: 30)


Get Invoices:

GET /v1/api/scrubhub/customer/lambda/get-invoices
Query parameters: page, limit, type
Download Invoice:

GET /v1/api/scrubhub/customer/lambda/download-invoice/:invoice_id?type=stripe|local
Add Payment Method:

POST /v1/api/scrubhub/customer/lambda/add-payment-method
Body: { "card_token": "tok_..." }

DELETE /v1/api/scrubhub/customer/lambda/remove-payment-method/:payment_method_id

{
  "title": "Registered Nurse - ICU",
  "description": "Full-time RN position in our state-of-the-art ICU department",
  "location": "123 Hospital St, Toronto, ON M5V 3A8",
  "location_type": "in_person",
  "job_type": "full_time",
  "has_start_date": true,
  "start_date": "2025-09-01",
  "payment_type": "yearly",
  "show_pay_by": "range",
  "salary_min": 75000,
  "salary_max": 95000,
  "daily_updates_email": "<EMAIL>",
  "resume_required": true,
  "allow_email_contact": false,
  "has_application_deadline": true,
  "application_deadline": "2025-08-31",
  "posting_duration_days": 60,
  "requirements": "BSN required, 2+ years ICU experience preferred",
  "benefits": "Health insurance, dental, vision, 401k matching, PTO",
  "is_paid_posting": true
}