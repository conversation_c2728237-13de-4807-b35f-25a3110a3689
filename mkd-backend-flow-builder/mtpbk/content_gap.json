{"settings": {"globalKey": "key_1743437153368_zhdrwo6im", "databaseType": "mysql", "authType": "session", "timezone": "UTC", "dbHost": "localhost", "dbPort": "3306", "dbUser": "root", "dbPassword": "root", "dbName": "database_2025-03-31", "id": "project_1743437153368_yep8uufll", "isPWA": false, "isMultiTenant": false, "model_namespace": "namespace_1743437153368_v42glp5d6", "payment_option": "subscription"}, "models": [{"id": "model_1743437160252_l9wzyeucx", "name": "company", "fields": [{"name": "id", "type": "primary key", "defaultValue": "", "validation": ""}, {"name": "name", "type": "string", "defaultValue": "", "validation": "required"}, {"name": "website", "type": "string", "defaultValue": "", "validation": ""}, {"name": "logo", "type": "string", "defaultValue": "", "validation": ""}, {"name": "owner_id", "type": "integer", "defaultValue": "", "validation": ""}, {"name": "address", "type": "string", "defaultValue": "", "validation": ""}, {"name": "city", "type": "string", "defaultValue": "", "validation": ""}, {"name": "state", "type": "string", "defaultValue": "", "validation": ""}, {"name": "zip", "type": "integer", "defaultValue": "", "validation": ""}, {"name": "country", "type": "string", "defaultValue": "", "validation": ""}, {"name": "phone", "type": "string", "defaultValue": "", "validation": "phone"}, {"name": "status", "type": "mapping", "mapping": "0:Active,1:Inactive,2:Suspend", "defaultValue": "0", "validation": ""}, {"name": "created_at", "type": "timestamp", "defaultValue": "CURRENT_TIMESTAMP", "validation": "date"}]}, {"id": "model_1743437160252_z661ygt7m", "name": "user", "fields": [{"name": "id", "type": "primary key", "defaultValue": "", "validation": ""}, {"name": "email", "type": "string", "defaultValue": "", "validation": "required,email"}, {"name": "company_id", "type": "foreign key", "defaultValue": "", "validation": "required"}, {"name": "password", "type": "password", "defaultValue": "", "validation": "required"}, {"name": "login_type", "type": "mapping", "mapping": "0:<PERSON>,1:Google,2:Microsoft,3:Apple,4:Twitter,5:Facebook", "defaultValue": "0", "validation": "required,enum:0,1,2,3,4,5"}, {"name": "role_id", "type": "string", "defaultValue": "", "validation": ""}, {"name": "data", "type": "json", "defaultValue": "", "validation": ""}, {"name": "status", "type": "mapping", "mapping": "0:Active,1:Inactive,2:Suspend", "defaultValue": "0", "validation": "required,enum:0,1,2"}, {"name": "verify", "type": "boolean", "defaultValue": "0", "validation": "required"}, {"name": "two_factor_authentication", "type": "boolean", "defaultValue": "0", "validation": ""}, {"name": "company_id", "type": "integer", "defaultValue": "0", "validation": ""}, {"name": "stripe_uid", "type": "string", "defaultValue": "", "validation": ""}, {"name": "created_at", "type": "timestamp", "defaultValue": "CURRENT_TIMESTAMP", "validation": "date"}]}, {"id": "model_1743437160252_73213ifcw", "name": "company_user", "fields": [{"name": "id", "type": "primary key", "defaultValue": "", "validation": ""}, {"name": "company_id", "type": "integer", "defaultValue": "", "validation": "required"}, {"name": "user_id", "type": "integer", "defaultValue": "", "validation": "required"}, {"name": "role", "type": "string", "defaultValue": "", "validation": "required"}, {"name": "email", "type": "string", "defaultValue": "", "validation": "required"}, {"name": "status", "type": "mapping", "mapping": "0:Active,1:Inactive,2:Suspend", "defaultValue": "0", "validation": "required,enum:0,1,2"}, {"name": "created_at", "type": "timestamp", "defaultValue": "CURRENT_TIMESTAMP", "validation": "date"}]}, {"id": "model_1743437160252_xpxxvvukt", "name": "company_admin", "fields": [{"name": "id", "type": "primary key", "defaultValue": "", "validation": ""}, {"name": "company_id", "type": "integer", "defaultValue": "", "validation": "required"}, {"name": "user_id", "type": "integer", "defaultValue": "", "validation": "required"}, {"name": "created_at", "type": "timestamp", "defaultValue": "CURRENT_TIMESTAMP", "validation": "date"}]}, {"id": "model_1743437160252_29s5i4sb5", "name": "company_employee_subscription", "fields": [{"name": "id", "type": "primary key", "defaultValue": "", "validation": ""}, {"name": "company_id", "type": "integer", "defaultValue": "", "validation": "required"}, {"name": "user_id", "type": "integer", "defaultValue": "", "validation": "required"}, {"name": "stripe_subscription_id", "type": "string", "defaultValue": "", "validation": "required"}, {"name": "status", "type": "mapping", "mapping": "0:Active,1:Inactive,2:Suspend", "defaultValue": "0", "validation": "required,enum:0,1,2"}, {"name": "created_at", "type": "timestamp", "defaultValue": "CURRENT_TIMESTAMP", "validation": "date"}]}, {"id": "model_1743437160252_zyegv40vf", "name": "company_usage", "fields": [{"name": "id", "type": "primary key", "defaultValue": "", "validation": ""}, {"name": "company_id", "type": "integer", "defaultValue": "", "validation": "required"}, {"name": "created_at", "type": "timestamp", "defaultValue": "CURRENT_TIMESTAMP", "validation": "date"}]}, {"id": "model_1743437218651_r33p58ugr", "name": "website_summary", "fields": [{"name": "id", "type": "primary key", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "created_at", "type": "datetime", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "updated_at", "type": "datetime", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "url", "type": "string", "defaultValue": "", "validation": "required", "validationOptions": {}, "relation": ""}], "relations": []}, {"id": "model_1743437392777_a8xl0a8u4", "name": "stripe_product", "fields": [{"name": "id", "type": "primary key", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "created_at", "type": "date", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "updated_at", "type": "datetime", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "name", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "product_id", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "stripe_id", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "object", "type": "long text", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "status", "type": "integer", "defaultValue": "", "validation": "", "validationOptions": {}}]}, {"id": "model_1743437392777_9uw05zf67", "name": "stripe_price", "fields": [{"name": "id", "type": "primary key", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "created_at", "type": "date", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "updated_at", "type": "datetime", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "name", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "product_id", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "stripe_id", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "is_usage_metered", "type": "integer", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "usage_limit", "type": "integer", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "object", "type": "medium text", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "amount", "type": "float", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "trial_days", "type": "integer", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "type", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "status", "type": "integer", "defaultValue": "", "validation": "", "validationOptions": {}}]}, {"id": "model_1743437392777_muix1kk5k", "name": "stripe_subscription", "fields": [{"name": "id", "type": "primary key", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "stripe_id", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "price_id", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "user_id", "type": "integer", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "object", "type": "json", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "status", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "is_lifetime", "type": "boolean", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "created_at", "type": "date", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "updated_at", "type": "datetime", "defaultValue": "", "validation": "", "validationOptions": {}}]}, {"id": "model_1743437392777_8n0k6u33j", "name": "stripe_checkout", "fields": [{"name": "id", "type": "primary key", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "user_id", "type": "integer", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "stripe_id", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "object", "type": "json", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "created_at", "type": "date", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "updated_at", "type": "datetime", "defaultValue": "", "validation": "", "validationOptions": {}}]}, {"id": "model_1743437392777_gxpkt8cfh", "name": "stripe_webhook", "fields": [{"name": "id", "type": "primary key", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "stripe_id", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "idempotency_key", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "description", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "event_type", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "resource_type", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "object", "type": "json", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "is_handled", "type": "boolean", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "created_at", "type": "date", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "updated_at", "type": "datetime", "defaultValue": "", "validation": "", "validationOptions": {}}]}, {"id": "model_1743437392777_53tyzm4bt", "name": "stripe_setting", "fields": [{"name": "id", "type": "primary key", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "key", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "value", "type": "medium text", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "created_at", "type": "date", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "updated_at", "type": "datetime", "defaultValue": "", "validation": "", "validationOptions": {}}]}, {"id": "model_1743437392777_o5f7uptgs", "name": "stripe_order", "fields": [{"name": "id", "type": "primary key", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "user_id", "type": "integer", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "price_id", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "stripe_id", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "object", "type": "json", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "created_at", "type": "date", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "updated_at", "type": "datetime", "defaultValue": "", "validation": "", "validationOptions": {}}]}, {"id": "model_1743437392777_mjgvzg5md", "name": "stripe_invoice", "fields": [{"name": "id", "type": "primary key", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "user_id", "type": "integer", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "stripe_id", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "object", "type": "json", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "created_at", "type": "date", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "updated_at", "type": "datetime", "defaultValue": "", "validation": "", "validationOptions": {}}]}, {"id": "model_1743437392777_truj4b89v", "name": "stripe_refund", "fields": [{"name": "id", "type": "primary key", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "user_id", "type": "integer", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "stripe_id", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "object", "type": "json", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "charge_id", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "subscription_id", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "amount", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "currency", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "reason", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "status", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "created_at", "type": "date", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "updated_at", "type": "datetime", "defaultValue": "", "validation": "", "validationOptions": {}}]}, {"id": "model_1743437392777_7mo7ju52y", "name": "stripe_dispute", "fields": [{"name": "id", "type": "primary key", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "user_id", "type": "integer", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "stripe_id", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "subscription_id", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "object", "type": "json", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "amount", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "reason", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "reason_description", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "status", "type": "string", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "created_at", "type": "date", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "updated_at", "type": "datetime", "defaultValue": "", "validation": "", "validationOptions": {}}]}], "routes": [{"id": "route_1743437247066_9fjeczh4a", "name": "Get All website_summary", "method": "GET", "url": "/api/website_summary", "flowData": {"nodes": [{"id": "url_node_1743437247066", "type": "url", "position": {"x": 100, "y": 100}, "data": {"label": "Url", "apiname": "Get All website_summary", "path": "/api/website_summary", "method": "GET"}}, {"id": "auth_node_1743437247066", "type": "auth", "position": {"x": 100, "y": 200}, "data": {"label": "<PERSON><PERSON>", "authType": "none"}}, {"id": "db_find_node_1743437247066", "type": "db-find", "position": {"x": 100, "y": 300}, "data": {"label": "Database Find", "model": "website_summary", "operation": "find<PERSON>any", "query": "SELECT * FROM website_summary", "resultVar": "website_summaryResult"}}, {"id": "logic_node_1743437247066", "type": "logic", "position": {"x": 100, "y": 400}, "data": {"label": "Logic", "fields": [], "queryFields": [], "code": "//commented logic"}}, {"id": "output_node_1743437247066", "type": "outputs", "position": {"x": 100, "y": 500}, "data": {"label": "Response", "outputType": "json", "fields": [{"name": "id", "type": "number"}, {"name": "created_at", "type": "datetime"}, {"name": "updated_at", "type": "datetime"}, {"name": "url", "type": "string"}], "resultVar": "website_summaryResult", "statusCode": 200}}], "edges": [{"id": "url-to-auth_1743437247066", "source": "url_node_1743437247066", "target": "auth_node_1743437247066"}, {"id": "auth-to-db_1743437247066", "source": "auth_node_1743437247066", "target": "db_find_node_1743437247066"}, {"id": "db-to-logic_1743437247066", "source": "db_find_node_1743437247066", "target": "logic_node_1743437247066"}, {"id": "logic-to-output_1743437247066", "source": "logic_node_1743437247066", "target": "output_node_1743437247066"}]}}, {"id": "route_1743437247066_0whflgzv7", "name": "Get One website_summary", "method": "GET", "url": "/api/website_summary/:id", "flowData": {"nodes": [{"id": "url_node_1743437247066_1", "type": "url", "position": {"x": 100, "y": 100}, "data": {"label": "Url", "apiname": "Get One website_summary", "path": "/api/website_summary/:id", "method": "GET", "queryFields": [{"name": "id", "type": "number", "validation": "required"}]}}, {"id": "auth_node_1743437247066_1", "type": "auth", "position": {"x": 100, "y": 200}, "data": {"label": "<PERSON><PERSON>", "authType": "none"}}, {"id": "db_query_node_1743437247066_1", "type": "db-query", "position": {"x": 100, "y": 300}, "data": {"label": "Database Find", "model": "website_summary", "operation": "findOne", "query": "SELECT * FROM website_summary WHERE id=id", "resultVar": "website_summaryOneResult"}}, {"id": "logic_node_1743437247066_1", "type": "logic", "position": {"x": 100, "y": 400}, "data": {"label": "Logic", "fields": [], "queryFields": [], "code": "//commented logic"}}, {"id": "output_node_1743437247066_1", "type": "outputs", "position": {"x": 100, "y": 500}, "data": {"label": "Response", "outputType": "json", "fields": [{"name": "id", "type": "number"}, {"name": "created_at", "type": "datetime"}, {"name": "updated_at", "type": "datetime"}, {"name": "url", "type": "string"}, {"name": "error", "type": "boolean"}], "resultVar": "website_summaryOneResult", "statusCode": 200}}], "edges": [{"id": "url-to-auth_1743437247066_1", "source": "url_node_1743437247066_1", "target": "auth_node_1743437247066_1"}, {"id": "auth-to-db_1743437247066_1", "source": "auth_node_1743437247066_1", "target": "db_query_node_1743437247066_1"}, {"id": "db-to-logic_1743437247066_1", "source": "db_query_node_1743437247066_1", "target": "logic_node_1743437247066_1"}, {"id": "logic-to-output_1743437247066_1", "source": "logic_node_1743437247066_1", "target": "output_node_1743437247066_1"}]}}, {"id": "route_1743437247066_xxoxxfcwu", "name": "Create website_summary", "method": "POST", "url": "/api/website_summary", "flowData": {"nodes": [{"id": "url_node_1743437247066_3", "type": "url", "position": {"x": 100, "y": 100}, "data": {"label": "Url", "apiname": "Create website_summary", "path": "/api/website_summary", "method": "POST", "fields": [{"name": "created_at", "type": "datetime", "validation": ""}, {"name": "updated_at", "type": "datetime", "validation": ""}, {"name": "url", "type": "string", "validation": ""}]}}, {"id": "auth_node_1743437247066_3", "type": "auth", "position": {"x": 100, "y": 200}, "data": {"label": "<PERSON><PERSON>", "authType": "none"}}, {"id": "db_insert_node_1743437247066_3", "type": "db-insert", "position": {"x": 100, "y": 300}, "data": {"label": "Database Insert", "model": "website_summary", "operation": "create", "query": "INSERT INTO website_summary (created_at, updated_at, url)\n                      VALUES (:created_at, :updated_at, :url)", "resultVar": "website_summaryCreateResult"}}, {"id": "logic_node_1743437247066_3", "type": "logic", "position": {"x": 100, "y": 400}, "data": {"label": "Logic", "fields": [], "queryFields": [], "code": "//commented logic"}}, {"id": "output_node_1743437247066_3", "type": "outputs", "position": {"x": 100, "y": 500}, "data": {"label": "Response", "outputType": "json", "fields": [{"name": "error", "type": "boolean"}, {"name": "id", "type": "number"}], "resultVar": "website_summaryCreateResult", "statusCode": 200}}], "edges": [{"id": "url-to-auth_1743437247066_3", "source": "url_node_1743437247066_3", "target": "auth_node_1743437247066_3"}, {"id": "auth-to-db_1743437247066_3", "source": "auth_node_1743437247066_3", "target": "db_insert_node_1743437247066_3"}, {"id": "db-to-logic_1743437247066_3", "source": "db_insert_node_1743437247066_3", "target": "logic_node_1743437247066_3"}, {"id": "logic-to-output_1743437247066_3", "source": "logic_node_1743437247066_3", "target": "output_node_1743437247066_3"}]}}, {"id": "route_1743437247066_0eclq3gan", "name": "Update website_summary", "method": "PUT", "url": "/api/website_summary/:id", "flowData": {"nodes": [{"id": "url_node_1743437247066_4", "type": "url", "position": {"x": 100, "y": 100}, "data": {"label": "Url", "apiname": "Update website_summary", "path": "/api/website_summary/:id", "method": "PUT", "queryFields": [{"name": "id", "type": "number", "validation": "required"}], "fields": [{"name": "created_at", "type": "datetime", "validation": ""}, {"name": "updated_at", "type": "datetime", "validation": ""}, {"name": "url", "type": "string", "validation": "required"}]}}, {"id": "auth_node_1743437247066_4", "type": "auth", "position": {"x": 100, "y": 200}, "data": {"label": "<PERSON><PERSON>", "authType": "none"}}, {"id": "db_update_node_1743437247066_4", "type": "db-update", "position": {"x": 100, "y": 300}, "data": {"label": "Database Update", "model": "website_summary", "operation": "update", "idField": "id", "query": "UPDATE website_summary SET created_at=:created_at, updated_at=:updated_at, url=:url WHERE id=:id", "resultVar": "website_summaryUpdateResult"}}, {"id": "logic_node_1743437247066_4", "type": "logic", "position": {"x": 100, "y": 400}, "data": {"label": "Logic", "fields": [], "queryFields": [], "code": "//commented logic"}}, {"id": "output_node_1743437247066_4", "type": "outputs", "position": {"x": 100, "y": 500}, "data": {"label": "Response", "outputType": "json", "fields": [{"name": "error", "type": "boolean"}, {"name": "id", "type": "number"}], "resultVar": "website_summaryUpdateResult", "statusCode": 200}}], "edges": [{"id": "url-to-auth_1743437247066_4", "source": "url_node_1743437247066_4", "target": "auth_node_1743437247066_4"}, {"id": "auth-to-db_1743437247066_4", "source": "auth_node_1743437247066_4", "target": "db_update_node_1743437247066_4"}, {"id": "db-to-logic_1743437247066_4", "source": "db_update_node_1743437247066_4", "target": "logic_node_1743437247066_4"}, {"id": "logic-to-output_1743437247066_4", "source": "logic_node_1743437247066_4", "target": "output_node_1743437247066_4"}]}}, {"id": "route_1743437247066_auhkslfdc", "name": "Delete One website_summary", "method": "DELETE", "url": "/api/website_summary/:id", "flowData": {"nodes": [{"id": "url_node_1743437247066_2", "type": "url", "position": {"x": 100, "y": 100}, "data": {"label": "Url", "apiname": "Delete One website_summary", "path": "/api/website_summary/:id", "method": "DELETE", "queryFields": [{"name": "id", "type": "number", "validation": "required"}]}}, {"id": "auth_node_1743437247066_2", "type": "auth", "position": {"x": 100, "y": 200}, "data": {"label": "<PERSON><PERSON>", "authType": "none"}}, {"id": "db_delete_node_1743437247066_2", "type": "db-delete", "position": {"x": 100, "y": 300}, "data": {"label": "Database Delete", "model": "website_summary", "operation": "delete", "query": "DELETE FROM website_summary WHERE id=id", "resultVar": "website_summaryDeleteResult"}}, {"id": "logic_node_1743437247066_2", "type": "logic", "position": {"x": 100, "y": 400}, "data": {"label": "Logic", "fields": [], "queryFields": [], "code": "//commented logic"}}, {"id": "output_node_1743437247066_2", "type": "outputs", "position": {"x": 100, "y": 500}, "data": {"label": "Response", "outputType": "json", "fields": [{"name": "error", "type": "boolean"}, {"name": "id", "type": "integer"}], "statusCode": 200, "resultVar": "website_summaryDeleteResult"}}], "edges": [{"id": "url-to-auth_1743437247066_2", "source": "url_node_1743437247066_2", "target": "auth_node_1743437247066_2"}, {"id": "auth-to-db_1743437247066_2", "source": "auth_node_1743437247066_2", "target": "db_delete_node_1743437247066_2"}, {"id": "db-to-logic_1743437247066_2", "source": "db_delete_node_1743437247066_2", "target": "logic_node_1743437247066_2"}, {"id": "logic-to-output_1743437247066_2", "source": "logic_node_1743437247066_2", "target": "output_node_1743437247066_2"}]}}, {"id": "route_1743437277749_l3us1va2p", "name": "All purpose #", "flowData": {"nodes": [{"id": "url_node_1743437284126", "type": "mock-api", "position": {"x": 100, "y": 100}, "data": {"label": "Mock API", "apiname": "All purpose #", "path": "/v1/api/", "method": "GET", "description": "", "fields": [], "queryFields": [], "responseFields": [], "authType": "none", "outputType": "json", "statusCode": 200}}], "edges": []}}], "roles": [{"id": "role_admin_1743437160252", "name": "Super Admin", "slug": "super_admin", "permissions": {"routes": [], "canCreateUsers": true, "canEditUsers": true, "canDeleteUsers": true, "canManageRoles": true, "canUpdateOtherUsers": true}}, {"id": "role_company_admin_1743437160252", "name": "Company Admin", "slug": "company_admin", "permissions": {"routes": [], "canCreateUsers": true, "canEditUsers": true, "canDeleteUsers": false, "canManageRoles": false, "canUpdateOtherUsers": true, "companyScoped": true}}, {"id": "role_member_1743437160252", "name": "Member", "slug": "member", "permissions": {"routes": ["route_1743437247066_9fjeczh4a", "route_1743437247066_0whflgzv7", "route_1743437247066_xxoxxfcwu", "route_1743437247066_0eclq3gan", "route_1743437247066_auhkslfdc"], "canCreateUsers": false, "canEditUsers": false, "canDeleteUsers": false, "canManageRoles": false, "canLogin": false, "canRegister": false, "canForgot": false, "canReset": false, "canGoogleLogin": false, "canAppleLogin": false, "canMicrosoftLogin": false, "canMagicLinkLogin": false, "canTwitterLogin": false, "needs2FA": false, "canSetPermissions": false, "canPreference": false, "canVerifyEmail": false, "canUpload": false, "canStripe": false, "canStripeWebhook": false, "canRealTime": false, "canAI": false, "canUpdateEmail": false, "canUpdatePassword": false, "canUpdateOtherUsers": false, "treeql": {"enabled": false, "models": {}}, "companyScoped": true}}]}