{"info": {"name": "Dealmaker Community APIs", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Communities", "item": [{"name": "List Communities", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{base_url}}/v1/api/dealmaker/user/community/communities?search=test&industry=technology", "host": ["{{base_url}}"], "path": ["v1", "api", "dealmaker", "user", "community", "communities"], "query": [{"key": "search", "value": "test", "description": "Search term (required)"}, {"key": "industry", "value": "technology", "description": "Industry filter (optional)"}]}}}, {"name": "Get Community Details", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{base_url}}/v1/api/dealmaker/user/community/communities/{{community_id}}", "host": ["{{base_url}}"], "path": ["v1", "api", "dealmaker", "user", "community", "communities", "{{community_id}}"]}}}, {"name": "Create Community", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"title\": \"Test Community\",\n    \"description\": \"A test community description\",\n    \"guidelines\": \"Community guidelines here\",\n    \"privacy\": \"public\",\n    \"industry_id\": 1\n}"}, "url": {"raw": "{{base_url}}/v1/api/dealmaker/user/community/communities", "host": ["{{base_url}}"], "path": ["v1", "api", "dealmaker", "user", "community", "communities"]}}}, {"name": "Update Community", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"title\": \"Updated Title\",\n    \"description\": \"Updated description\",\n    \"guidelines\": \"Updated guidelines\",\n    \"privacy\": \"private\"\n}"}, "url": {"raw": "{{base_url}}/v1/api/dealmaker/user/community/update-communities/{{community_id}}", "host": ["{{base_url}}"], "path": ["v1", "api", "dealmaker", "user", "community", "update-communities", "{{community_id}}"]}}}]}, {"name": "Community Membership", "item": [{"name": "List Community Members", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{base_url}}/v1/api/dealmaker/user/community/communities/{{community_id}}/members", "host": ["{{base_url}}"], "path": ["v1", "api", "dealmaker", "user", "community", "communities", "{{community_id}}", "members"]}}}, {"name": "Join Community", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{base_url}}/v1/api/dealmaker/user/community/communities/{{community_id}}/join", "host": ["{{base_url}}"], "path": ["v1", "api", "dealmaker", "user", "community", "communities", "{{community_id}}", "join"]}}}, {"name": "Leave Community", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"reason\": \"Optional reason for leaving\"\n}"}, "url": {"raw": "{{base_url}}/v1/api/dealmaker/user/community/communities/{{community_id}}/leave", "host": ["{{base_url}}"], "path": ["v1", "api", "dealmaker", "user", "community", "communities", "{{community_id}}", "leave"]}}}, {"name": "Handle Join Request", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"user_id\": 123,\n    \"status\": \"approve\"\n}"}, "url": {"raw": "{{base_url}}/v1/api/dealmaker/user/community/communities/{{community_id}}/requests", "host": ["{{base_url}}"], "path": ["v1", "api", "dealmaker", "user", "community", "communities", "{{community_id}}", "requests"]}}}, {"name": "Invite Member", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"message\": \"Please join our community!\"\n}"}, "url": {"raw": "{{base_url}}/v1/api/dealmaker/user/community/communities/{{community_id}}/invite", "host": ["{{base_url}}"], "path": ["v1", "api", "dealmaker", "user", "community", "communities", "{{community_id}}", "invite"]}}}, {"name": "Remove Member", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{base_url}}/v1/api/dealmaker/user/community/communities/{{community_id}}/members/{{member_id}}", "host": ["{{base_url}}"], "path": ["v1", "api", "dealmaker", "user", "community", "communities", "{{community_id}}", "members", "{{member_id}}"]}}}]}, {"name": "Referrals", "item": [{"name": "List Referrals", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{base_url}}/v1/api/dealmaker/user/referral/referrals?status=active&industry=technology", "host": ["{{base_url}}"], "path": ["v1", "api", "dealmaker", "user", "referral", "referrals"], "query": [{"key": "status", "value": "active", "description": "Status filter (required)"}, {"key": "industry", "value": "technology", "description": "Industry filter (optional)"}]}}}, {"name": "Get Referral Details", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{base_url}}/v1/api/dealmaker/referral/referrals/{{referral_id}}", "host": ["{{base_url}}"], "path": ["v1", "api", "dealmaker", "referral", "referrals", "{{referral_id}}"]}}}, {"name": "Create Referral", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"job_title\": \"Software Engineer\",\n    \"description\": \"Looking for a senior engineer\",\n    \"pay\": \"$150k-$200k\",\n    \"industry_id\": 1\n}"}, "url": {"raw": "{{base_url}}/v1/api/dealmaker/referral/referrals", "host": ["{{base_url}}"], "path": ["v1", "api", "dealmaker", "referral", "referrals"]}}}]}], "variable": [{"key": "base_url", "value": "http://localhost:3000"}, {"key": "token", "value": "your_auth_token_here"}, {"key": "community_id", "value": "1"}, {"key": "member_id", "value": "1"}, {"key": "referral_id", "value": "1"}]}