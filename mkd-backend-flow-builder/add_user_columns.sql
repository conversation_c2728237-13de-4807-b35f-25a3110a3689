-- Add missing columns to user table for profile management
ALTER TABLE scrubhub_user ADD COLUMN IF NOT EXISTS first_name VARCHAR(255) NULL COMMENT 'User first name';
ALTER TABLE scrubhub_user ADD COLUMN IF NOT EXISTS last_name VA<PERSON><PERSON><PERSON>(255) NULL COMMENT 'User last name';
ALTER TABLE scrubhub_user ADD COLUMN IF NOT EXISTS phone VARCHAR(20) NULL COMMENT 'User phone number';
ALTER TABLE scrubhub_user ADD COLUMN IF NOT EXISTS profile_image VARCHAR(512) NULL COMMENT 'User profile image URL';
ALTER TABLE scrubhub_user ADD COLUMN IF NOT EXISTS name VARCHAR(512) NULL COMMENT 'User full name';
ALTER TABLE scrubhub_user ADD COLUMN IF NOT EXISTS role INT DEFAULT 0 COMMENT 'User role: 0=tenant, 1=landlord, 2=hospital, 3=recruiter, 4=admin';
ALTER TABLE scrubhub_user ADD COLUMN IF NOT EXISTS verified TINYINT(1) DEFAULT 0 COMMENT 'Whether user is verified';
ALTER TABLE scrubhub_user ADD COLUMN IF NOT EXISTS stripe_customer_id VARCHAR(255) NULL COMMENT 'Stripe customer ID';

-- Add indexes for user table
ALTER TABLE scrubhub_user ADD INDEX IF NOT EXISTS idx_first_name (first_name);
ALTER TABLE scrubhub_user ADD INDEX IF NOT EXISTS idx_last_name (last_name);
ALTER TABLE scrubhub_user ADD INDEX IF NOT EXISTS idx_role (role);
ALTER TABLE scrubhub_user ADD INDEX IF NOT EXISTS idx_verified (verified);

-- Show the updated table structure
DESCRIBE scrubhub_user;
