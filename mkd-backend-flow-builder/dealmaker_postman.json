{"info": {"name": "Dealmaker API", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Communities", "item": [{"name": "List Communities", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{base_url}}/v1/api/dealmaker/user/community/communities?search={{search}}&industry={{industry}}", "host": ["{{base_url}}"], "path": ["v1", "api", "dealmaker", "user", "community", "communities"], "query": [{"key": "search", "value": "{{search}}", "description": "Search term (required)"}, {"key": "industry", "value": "{{industry}}", "description": "Industry filter (optional)"}]}}}, {"name": "Get Community Details", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{base_url}}/v1/api/dealmaker/user/community/communities/{{community_id}}", "host": ["{{base_url}}"], "path": ["v1", "api", "dealmaker", "user", "community", "communities", "{{community_id}}"]}}}, {"name": "Join Community", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{base_url}}/v1/api/dealmaker/user/community/communities/{{community_id}}/join", "host": ["{{base_url}}"], "path": ["v1", "api", "dealmaker", "user", "community", "communities", "{{community_id}}", "join"]}}}, {"name": "Leave Community", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"reason\": \"{{reason}}\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/v1/api/dealmaker/user/community/communities/{{community_id}}/leave", "host": ["{{base_url}}"], "path": ["v1", "api", "dealmaker", "user", "community", "communities", "{{community_id}}", "leave"]}}}, {"name": "Invite to Community", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"{{email}}\",\n  \"user_id\": \"{{user_id}}\",\n  \"message\": \"{{message}}\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/v1/api/dealmaker/user/community/communities/{{community_id}}/invite", "host": ["{{base_url}}"], "path": ["v1", "api", "dealmaker", "user", "community", "communities", "{{community_id}}", "invite"]}}}, {"name": "Remove Member", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{base_url}}/v1/api/dealmaker/user/community/communities/{{community_id}}/members/{{member_id}}", "host": ["{{base_url}}"], "path": ["v1", "api", "dealmaker", "user", "community", "communities", "{{community_id}}", "members", "{{member_id}}"]}}}, {"name": "Handle Join Request", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"status\": \"approve|deny\",\n  \"user_id\": \"{{user_id}}\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/v1/api/dealmaker/user/community/communities/{{community_id}}/requests", "host": ["{{base_url}}"], "path": ["v1", "api", "dealmaker", "user", "community", "communities", "{{community_id}}", "requests"]}}}]}, {"name": "Referrals", "item": [{"name": "Create Referral", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"job_title\": \"{{job_title}}\",\n  \"description\": \"{{description}}\",\n  \"pay\": \"{{pay}}\",\n  \"industry_id\": {{industry_id}}\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/v1/api/dealmaker/referral/referrals", "host": ["{{base_url}}"], "path": ["v1", "api", "dealmaker", "referral", "referrals"]}}}, {"name": "Update Referral", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"job_title\": \"{{job_title}}\",\n  \"description\": \"{{description}}\",\n  \"pay\": \"{{pay}}\",\n  \"industry_id\": \"{{industry_id}}\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/v1/api/dealmaker/user/update-referral/referrals/{{referral_id}}", "host": ["{{base_url}}"], "path": ["v1", "api", "dealmaker", "user", "update-referral", "referrals", "{{referral_id}}"]}}}, {"name": "Delete Referral", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{base_url}}/v1/api/dealmaker/user/referral/referrals/{{referral_id}}", "host": ["{{base_url}}"], "path": ["v1", "api", "dealmaker", "user", "referral", "referrals", "{{referral_id}}"]}}}, {"name": "Repost Referral", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"community_id\": {{community_id}}\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/v1/api/dealmaker/user/referral/referrals/{{referral_id}}/repost", "host": ["{{base_url}}"], "path": ["v1", "api", "dealmaker", "user", "referral", "referrals", "{{referral_id}}", "repost"]}}}, {"name": "Archive Referral", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"reason\": \"{{reason}}\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/v1/api/dealmaker/user/referral/referrals/{{referral_id}}/archive", "host": ["{{base_url}}"], "path": ["v1", "api", "dealmaker", "user", "referral", "referrals", "{{referral_id}}", "archive"]}}}, {"name": "Unarchive Referral", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{base_url}}/v1/api/dealmaker/user/referral/referrals/{{referral_id}}/unarchive", "host": ["{{base_url}}"], "path": ["v1", "api", "dealmaker", "user", "referral", "referrals", "{{referral_id}}", "unarchive"]}}}]}, {"name": "Feed", "item": [{"name": "Get User Feed", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{base_url}}/v1/api/dealmaker/user/feed/feed?limit={{limit}}&page={{page}}&community_id={{community_id}}", "host": ["{{base_url}}"], "path": ["v1", "api", "dealmaker", "user", "feed", "feed"], "query": [{"key": "limit", "value": "{{limit}}", "description": "Items per page (default: 20)"}, {"key": "page", "value": "{{page}}", "description": "Page number"}, {"key": "community_id", "value": "{{community_id}}", "description": "Filter by community"}]}}}, {"name": "Get Feed Event Details", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{base_url}}/v1/api/dealmaker/user/feed/feed/{{event_id}}", "host": ["{{base_url}}"], "path": ["v1", "api", "dealmaker", "user", "feed", "feed", "{{event_id}}"]}}}]}, {"name": "Meetings", "item": [{"name": "List Meetings", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{base_url}}/v1/api/dealmaker/user/meeting/meetings?start_date={{start_date}}&end_date={{end_date}}&community_id={{community_id}}&user_id={{user_id}}&status={{status}}&page={{page}}&limit={{limit}}", "host": ["{{base_url}}"], "path": ["v1", "api", "dealmaker", "user", "meeting", "meetings"], "query": [{"key": "start_date", "value": "{{start_date}}", "description": "Filter by start date"}, {"key": "end_date", "value": "{{end_date}}", "description": "Filter by end date"}, {"key": "community_id", "value": "{{community_id}}", "description": "Filter by community"}, {"key": "user_id", "value": "{{user_id}}", "description": "Filter by user"}, {"key": "status", "value": "{{status}}", "description": "Filter by status"}, {"key": "page", "value": "{{page}}", "description": "Page number"}, {"key": "limit", "value": "{{limit}}", "description": "Items per page"}]}}}, {"name": "Get Meeting Details", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{base_url}}/v1/api/dealmaker/user/meeting/meetings/{{meeting_id}}", "host": ["{{base_url}}"], "path": ["v1", "api", "dealmaker", "user", "meeting", "meetings", "{{meeting_id}}"]}}}]}], "variable": [{"key": "base_url", "value": "http://localhost:5172"}]}