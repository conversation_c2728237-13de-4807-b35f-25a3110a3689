﻿Business Requirements Document (BRD)
Project Name: ScrubHub
Version: 1.0
Date: July 30, 2025
Prepared by: [To be filled]
1. Executive Summary
ScrubHub is a multi-role property and recruitment platform tailored for the medical housing ecosystem. It connects Renters/Landlords, Hospitals, and Recruiters through role-specific dashboards, offering features for rental property management, subletting, job posting, applicant tracking, device marketplace listings, and legal form generation. Landlords and tenants share a unified interface, while Hospitals and Recruiters have dedicated portals for job management and applicant processing. The system integrates payment processing, automated PDF generation, API connections for credit checks, and location-based search results. Admin tools allow moderation of premium services, bulk job listings, and equipment sales, ensuring smooth operation and compliance with listing rules.
2. ScrubHub – Release Schedule


Module
	Expected Completion
	Notes
	Backend API Development
	Week 1
	Implement property listing tiers & pricing logic, subletting fee handling, premium status workflow, credit check integration (Trustee API), N9 PDF generation, device marketplace pricing logic.
	Payment Processing Integration
	Week 1
	Implement Stripe for listing payments, subletting fees, job postings, and N9 form payments.
	Availability & Inquiry Handling
	Week 1
	Backend logic for inquiries, mark-as-responded flow, email/phone triggers.
	Job Posting Backend
	Week 1
	Hospital & recruiter posting workflows, location-based filtering, applicant status updates, bulk listing admin review logic.
	Credit Rating Service
	Week 1
	Trustee API integration for generating and reusing credit reports.
	N9 Form Generator
	Week 1
	PDF generation service with pre-fill from form fields and secure download after payment.
	Device Marketplace Backend
	Week 1
	Pricing rules, phone number masking logic, admin approval for high-value or bulk listings.
	Admin Portal
	Week 2
	Moderation tools for premium sublets, bulk job/device listings, billing management, user verification approval, and role management.
	User Verification Workflow
	Week 2
	Backend handling of landlord document verification before property publishing.
	

3. ScrubHub – Business Requirements
Functional Requirement
ID
	Functionality
	Requirement Description
	FR-01
	User Roles & Authentication
	Landlords/Renters share one interface; Recruiters and Hospitals have separate role-based portals. Secure login with session management.
	FR-02
	Property Listing Rules
	Users must log in to post rentals. Landlords require admin verification via two uploaded documents before publishing. Multiple photo uploads supported with drag-and-drop reordering.
	FR-03
	Listing Tiers & Pricing
	Free: 2 listings, 30 days each (limited availability) ; Paid: $4.99 (30 days), $9.99 (60 days), $14.99 (90 days), Feature Lsitings +$19.99; Featured listings appear at top of search results.
	FR-04
	Availability & Inquiry Management
	Inquiries appear in “My Listing”. Email opens email client; phone opens dialer. Marking as “responded” moves the item from new to responded.
	FR-05
	Subletting Services
	One-time fee; $49.99 charge to sublet a property. Premium requests ($599 +) stored separately for manual admin handling.
	FR-06
	Favorites & Recently Viewed
	Users can favorite/unfavorite listings; recently viewed stores the last 10 properties.
	FR-07
	Credit Rating Integration
	Users submit required details and payment. Trustee API generates credit report and background check, reusable for multiple applications.
	FR-08
	T9/N9 Tenant Notice Tool
	System fills N9 PDF form; user downloads after payment.
	FR-09
	Device Marketplace
	Users can list devices; pricing logic applies (e.g., $19.99 fee for $1,000 item). Phone numbers hidden until “Show Phone Number” is clicked.
	FR-10
	Hospital Job Posting
	Hospitals can post paid job listings, search positions, and manage applicants (view resume, email, mark as “interviewing” or “rejected”).
	FR-11
	Hospital Bulk Listing
	Bulk job listings stored for admin review before posting.
	FR-12
	Hospital Marketplace
	Hospitals can list/sell equipment; bulk listing requires admin approval.
	FR-13
	Recruiter Job Posting
	Recruiters can post jobs, search listings, and manage applicants similar to hospitals.
	FR-14
	Profile Management
	All roles can update profile details.
	FR-15
	Tenant Insurance
	Tenant insurance information accessible from tenant dashboard.
	FR-16
	Hospital Directory
	Displays all hospitals in Canada; clicking opens details with nearby rentals.
	FR-17
	Payment Processing
	All fees (listings, sublets, N9, job postings) processed through Stripe.
	FR-18
	Admin Portal Functions
	Admin can verify landlord documents, moderate premium sublets, bulk listings, device marketplace items, manage billing methods, and approve/reject content.
	Non-Functional Requirements


ID
	Category
	Description
	NFR-01
	Performance
	System should return search and listing results within 2 seconds under normal load.
	NFR-02
	Scalability
	Must support 10,000 concurrent users during peak usage periods.
	NFR-03
	Availability
	99.9% uptime excluding scheduled maintenance.
	NFR-04
	Security
	All sensitive fields encrypted at rest; HTTPS enforced on all connections; secure session tokens with HTTPOnly and Secure flags.
	NFR-05
	Maintainability
	Codebase should be modular to support role-specific feature updates without breaking other roles.
	NFR-06
	Localization
	Support English and French, including date/time formats, currency, and tax calculations per location.
	NFR-07
	Accessibility
	Meet WCAG 2.1 AA standards; ensure screen reader compatibility and keyboard navigability.
	NFR-08
	Backup & Recovery
	Automated daily database backups with 14-day retention; restore time target ≤ 2 hours.
	

4. Key Stakeholders
Name
	Email
	Phone Number
	Role
	[To be filled]
	[To be filled]
	[To be filled]
	[To be filled]
	5. Project Constraints
* Compliance with HIPAA and GDPR is mandatory.
* System must handle spikes during medical school rotation seasons.
* Secure against phishing, injection, and other common web attacks.
* Support only modern browsers (Chrome, Safari, Firefox, Edge).
 6. QA Checklist
* Test Stripe payments for all pricing tiers, fees, and failure/refund scenarios.

* Ensure Trustee API returns valid credit reports and securely stores results.

   * Confirm landlord verification blocks property listing until admin approval.

   * Ensure premium sublet requests are routed to admin portal for manual handling.

   * Verify hospital/recruiter job search respects geographic restrictions.

   * Test device marketplace fee calculation matches pricing logic.

      * Confirm admin can approve/reject landlord verification, bulk listings, premium sublets, and device listings.

      * Validate billing and payout reports for accuracy.

      * Verify audit logs record all admin actions with timestamps and user IDs.

General Functionality
         * Confirm email and phone triggers open respective apps (mail client/dialer).

         * Validate recently viewed section only displays latest 10 properties.

         * Ensure favorites update instantly when added/removed.

         * Test N9 PDF generation with correct field population and download availability after payment.

         * Validate search, filter, and pagination accuracy for all modules.

         * Check accessibility compliance (WCAG 2.1 AA).

         * Test all role-based access flows (Tenant/Landlord, Hospital, Recruiter, Admin).

         * Verify language switching (English/French) works across the platform.
7. Database Schema 
Users: id (PK), role, name, email (unique), password_hash, phone, address, country, province/state, verified (bool), created_at, updated_at
Properties: id (PK), landlord_id (FK → Users), title, description, address, city, province/state, country, price, tier, status, created_at, updated_at
Property_Photos: id (PK), property_id (FK → Properties), file_path, sort_order
Inquiries: id (PK), property_id (FK → Properties), name, email, phone, status, created_at
Sublets: id (PK), property_id (FK → Properties), status, created_at
Favorites: id (PK), user_id (FK → Users), property_id (FK → Properties), created_at
Recently_Viewed: id (PK), user_id (FK → Users), property_id (FK → Properties), viewed_at
Credit_Reports: id (PK), user_id (FK → Users), trustee_reference, report_url, created_at
Tenant_Notices: id (PK), user_id (FK → Users), notice_type, pdf_url, created_at
Marketplace_Items: id (PK), seller_id (FK → Users), title, description, price, status, created_at
Jobs: id (PK), poster_id (FK → Users), title, description, location, type, status, created_at
Job_Applicants: id (PK), job_id (FK → Jobs), applicant_name, resume_url, email, status, created_at
Payments: id (PK), user_id (FK → Users), amount, currency, purpose, stripe_transaction_id, status, created_at
Admin_Actions: id (PK), admin_id (FK → Users), action_type, target_id, target_table, notes, created_at








8. Screen-by-Screen Explanation with User Flow Diagrams


8.1 Homepage    (homepage.png)


  

The ScrubHub Homepage is the public-facing entry point for the platform and the first interaction point for most users. It is fully accessible without login and is designed to inform, engage, and guide tenants, landlords, hospital representatives, and recruiters to relevant sections of the platform. It promotes trust through professional design, showcases housing opportunities, and provides clear navigation to role-specific features.
Components:
            * Header Navigation (Sticky, Responsive)
  
               * Dropdown for Medical Schools: links to individual schools and “All Medical Schools” page.

               * Dropdown for Tenants: includes access to legal tools like T9/N9 Notice and tenant-specific resources.

               * Hospitals: links to hospital directory with rentals near each hospital.

               * Contact Us: navigates to inquiry form.

               * Login / Sign Up: role-specific login redirects; sign up starts with role selection.

               * Adapts to mobile view with hamburger menu.

                  * Hero Section

                     * Core messaging such as “Find Trusted Housing Near Your Next Hospital Rotation.”

                     * Prominent call-to-action (e.g., “Browse Rentals Near You”).

                     * High-quality banner image or illustration with optional animation.

                        * Featured Rentals Section

                           * Previews of rental properties (by default, near Toronto or user’s nearest major hospital).

                           * Each preview includes image, address, price, and quick action button.



                              * FAQ Section

                                 * Short list of common questions with link to full FAQ page.

                                    * Footer

                                       * Persistent navigation links, legal pages, and language selector.
Functionality Summary
                                       * Publicly accessible without login.

                                       * Navigation links dynamically adjust based on screen size.

                                       * “Login” and “Sign Up” direct users to role-specific authentication flows.

                                       * Featured rentals pull dynamically from database using location-based filters.

                                       * FAQ and Email Subscription sections are interactive and validate user input.

                                       * Language selector updates content in English or French instantly.



🧪 QA Expectations
QA Item
	Validation
	Responsive Layout
	Viewport breakpoints for mobile/tablet/desktop; test nav and rentals layout
	Nav Dropdown Behavior
	Hover and tap interactions tested on all screen sizes
	Hero CTA
	Works across devices and redirects correctly to listings or sign-up
	Preview Rentals Load
	Confirm listings load dynamically and display fallback on slow networks
	Language Support
	Confirm English and French content appears based on language selector
	Accessibility
	All nav links, buttons, and cards accessible via keyboard & screen readers
	













8.2 Hospitals  (Hospitals.png)


  

Overview
The Hospitals page is a public-facing directory ofall the  hospitals across Canada. It serves both informational and functional purposes, enabling medical students, tenants, landlords, and hospital administrators to locate and interact with hospital profiles. Each hospital entry provides essential details and can link to nearby rental listings. Logged-in hospitals have the ability to manage their own public profile, while unauthenticated users can browse and filter the directory.
Components
                                          * Section Title: Clear heading “Hospitals” displayed at the top of the page.
                                          * Hospital Cards/List: Each entry includes hospital name, location (city/province)
                                          * Filter Bar: Allows filtering by location/province
                                          * Search Box: Keyword-based search for quick navigation.
                                          * Responsive Layout: Grid adjusts to desktop, tablet, and mobile views.
Functionality Summary
                                          * Publicly accessible to all users without login.
                                          * Clicking on a hospital transitions user to map showing the hospital and nearby rental properties
                                          * Filter and search functions operate in real time without page reload.
                                          * “View Details” navigates to hospital profile with integrated rental listings.
                                          * Logged-in hospital representatives can update their public profile.
                                          * Directory pulls from the complete national hospital database.
                                          * Nearby rentals shown are automatically location-matched to hospital address.






8.3 Contact (get in touch.png)
  

Contact Us / Get in Touch – ScrubHub
The Contact Us page provides a centralized communication channel for users to reach ScrubHub’s support or administrative team. It is accessible to all visitors, regardless of login status, and is designed to handle inquiries from tenants, landlords, recruiters, and hospital representatives. The form captures essential details, ensures proper validation, and routes submissions to the appropriate internal team.
Components
                                          * Page Title: “Contact Us” or “Get in Touch” prominently displayed.

                                          * Form Fields:

                                             * Name (required)
                                             * Email (required, validated format)
                                             * Message/Inquiry (required, multiline input)

                                                * Submit Button: Triggers form validation and submission.
                                                * Validation Feedback: Inline error messages for missing or invalid input.
                                                * Success/Failure Notification: Displays confirmation or error message after submission.
                                                * Spam Protection: CAPTCHA or equivalent anti-bot mechanism.
                                                * Responsive Layout: Fully functional across desktop, tablet, and mobile.







Functionality Summary
                                                   * Accessible to all users without authentication.

                                                   * All fields validated on both client and server side.

                                                   * Email addresses validated to prevent incorrect formatting.

                                                   * Form submission stores inquiry in backend and sends notification to support team.

                                                   * CAPTCHA or equivalent tool prevents automated spam.

                                                   * Success message confirms submission; error messages prompt corrections.

                                                   * Mobile-friendly design ensures accessibility from all devices.
QA Expectations
Form Validation Scenarios
Test ID
	Description
	FORM-01
	Submitting an empty form triggers required field error messages
	FORM-02
	Invalid email format shows “Invalid email address” message
	FORM-03
	Successful submission displays confirmation message or thank-you screen
	FORM-04
	Special characters (e.g., <script>) are safely sanitized
	FORM-05
	Messages over 500 characters wrap correctly and are submitted without truncation
	Field-Level Tests
Test ID
	Validation Focus
	FIELD-01
	Name field accepts alphanumeric and special characters like hyphens, apostrophes
	FIELD-02
	Email field uses type="email" and launches proper keyboard on mobile devices
	FIELD-03
	Message box supports multiline input and resizes properly in text-heavy cases
	Security & Anti-Spam Tests
Test ID
	Description
	SEC-01
	HTML/JS injection is neutralized before submission and not echoed on the page
	SEC-02
	Server-side rate limiting triggers on repeated, rapid form submissions
	SEC-03
	Duplicate message attempts are logged and rate-limited
	SEC-04
	Email header injection is blocked (e.g., CRLF in email fields)
	Accessibility (A11Y) Tests
Test ID
	Validation Scenario
	A11Y-01
	All form fields accessible via keyboard (Tab, Shift+Tab)
	A11Y-02
	Error messages are announced via ARIA live region for screen readers
	A11Y-03
	Submit button uses accessible name (“Submit Message” or “Send Inquiry”)
	





8.4 Sign Up (create account.png)
  

The Sign Up page enables new users to create a ScrubHub account and select their role — Tenant/Landlord, Hospital, or Recruiter. After successful registration, each role is directed to an onboarding process before accessing their dashboard.
Components
                                                      * Page Title: “Create Your Account”

                                                      * Form Fields:

                                                         * Full Name (required)

                                                         * Email Address (required, validated format)

                                                         * Password (required, with strength rules and show/hide toggle)

                                                         * Role Selection (Tenant/Landlord, Hospital, Recruiter)

                                                            * Password Strength Indicator: Visual cue for password compliance.

                                                            * Submit Button: Disabled until all mandatory fields are correctly filled.

                                                            * Validation Feedback: Inline error messages for missing or invalid data.

                                                            * Redirect After Success: Directs users to role-specific onboarding screen.

                                                            * Responsive Layout: Optimized for mobile and desktop.

Functionality Summary
                                                               * Supports three primary roles with distinct post-registration flows.

                                                               * Password field masked by default, with optional toggle for visibility.

                                                               * Prevents duplicate registration using email already in the system.

                                                               * Enforces password rules (length, complexity).

                                                               * Browser autofill supported for faster registration.

                                                               * Role selection determines which modules are available after login.

                                                               * Redirects to onboarding screen immediately after successful registration.


QA Expectations
UI & Input Validation
Test ID
	Validation Scenario
	FORM-01
	Submitting empty fields triggers “Required” error messages
	FORM-02
	Password under 8 characters triggers “Password too short” warning
	FORM-03
	Skipping role selection triggers “Please select a role” message
	FORM-04
	Invalid email input displays standard validation error
	FORM-05
	Password show/hide toggle switches input visibility correctly across devices
	

Functional Behavior
Test ID
	Validation Scenario
	FUNC-01
	Valid form submission creates account and redirects to correct role dashboard
	FUNC-02
	Attempting to reuse registered email shows “Email already in use” message
	FUNC-03
	Password input is masked by default on load
	FUNC-04
	Chrome/Safari autofill fills name and email from saved credentials
	FUNC-05
	Submit button remains disabled until all form validations are passed
	

Security Tests
Test ID
	Security Concern
	SEC-01
	Password field is securely rendered; not exposed in page DOM or console logs
	SEC-02
	Passwords are hashed and encrypted server-side; never stored in plain text
	SEC-03
	All inputs are escaped/sanitized to prevent XSS or SQL injection attempts
	SEC-04
	Signup rate-limited on server to prevent brute force or bot registration abuse
	SEC-05
	CAPTCHA (if enabled) correctly blocks spam/bot attempts without harming UX
	



8.5 Login Page (login roles.png)
  

The Login page is the secure access point for all registered ScrubHub users, including Tenants/Landlords, Hospitals, and Recruiters. It is designed for speed, security, and device compatibility, providing persistent sessions with automatic secure expiration. The system routes each authenticated user to their role-specific dashboard.
Components
                                                                  * Page Title: “Login to Your Account”

                                                                  * Form Fields:

                                                                     * Email Address (required, validated format)

                                                                     * Password (required, masked input with show/hide toggle)

                                                                        * Forgot Password Link: Redirects to password reset flow.

                                                                        * Submit/Login Button: Disabled until all mandatory fields are filled.

                                                                        * Validation Feedback: Inline errors for incorrect credentials or missing fields.

                                                                        * Session Management: Supports “Remember Me” functionality with secure token handling.

                                                                        * Responsive Layout: Optimized for both mobile and desktop screens.







Functionality Summary
                                                                           * Supports authentication for all three primary roles.

                                                                           * Secure login with encrypted password verification.

                                                                           * Session persistence with automatic secure logout after inactivity.

                                                                           * Incorrect login attempts trigger CAPTCHA or rate limiting.

                                                                           * Forgot Password flow sends a secure reset link to the registered email.

                                                                           * Role detection ensures correct dashboard routing after login.

                                                                           * Password input protected from DOM/script-based access.
QA Expectations
Field Validation Tests
Test ID
	Validation Scenario
	FORM-01
	Correct email/password logs in user and redirects to role-specific dashboard
	FORM-02
	Submitting with empty fields triggers “Required” validation messages
	FORM-03
	Invalid email format (e.g., missing “@”) shows inline error and prevents submission
	FORM-04
	Incorrect password shows “Invalid login credentials” message
	FORM-05
	Clicking “Forgot Password” navigates to the reset password entry screen
	Security Tests
Test ID
	Security Control
	SEC-01
	Password field is masked and hidden from screen readers and DOM inspection tools
	SEC-02
	After 5 failed attempts, CAPTCHA or rate-limiting logic is activated
	SEC-03
	Successful login generates a secure session or token (JWT, session ID) with HTTPOnly and Secure cookie flags
	SEC-04
	Backend authentication checks role before granting access to restricted dashboards
	SEC-05
	Inputs are escaped to prevent login-related injection attacks
	

8.6 Landlord Portal (landlord side menu.png)
(Side Menu)
The Landlord Portal Side Menu is the central navigation panel for landlords (and renters with landlord permissions) to access all property management and related ScrubHub services. It is persistent across all landlord dashboard pages, providing quick access to listing management, subletting, credit checks, legal tools, marketplace, and profile management. The menu dynamically highlights the current active section for clarity.
Components
                                                                              * Add my Property – Enables verified landlords to create and publish rental listings on ScrubHub.

                                                                              * My Listings – View and manage posted properties, including availability inquiries.

                                                                              * Sublet – Create and manage subletting requests, with premium service options.

                                                                              * Favorites – View saved/favorited rental properties.

                                                                              * Recently Viewed – Quick access to up to 10 most recently viewed properties.

                                                                              * Credit Rating – Access to Trustee API-powered credit report generation.

                                                                              * T9/N9 Tenant Notice Tool – Fill and purchase downloadable N9 PDF for lease termination.

                                                                              * Device Marketplace – List and manage equipment or devices for sale.

                                                                              * Profile – Update account information and verification documents.
Functionality Summary
                                                                                 * Provides persistent role-specific navigation for landlord features.

                                                                                 * Highlights the active menu item for clear user orientation.

                                                                                 * Integrates with backend permissions to hide or disable menu options when a user lacks access rights.

                                                                                 * Supports responsive design for mobile side drawer or collapsible sidebar on desktop.
QA Expectations
UI/UX
                                                                                    * UI-01: Sidebar collapses and expands smoothly on toggle.

                                                                                    * UI-02: Sidebar icons are vertically aligned with consistent spacing.

                                                                                    * UI-03: Active section is visually indicated with highlight or bold styling.

                                                                                    * UI-04: Hovering over icons displays tooltips matching section names.

Interactivity
                                                                                       * INTER-01: Clicking an icon navigates to the correct section.

                                                                                       * INTER-02: Arrow keys or Tab enable keyboard focus on icons.

                                                                                       * INTER-03: Keyboard shortcut (e.g., Alt/Ctrl + M) toggles sidebar if implemented.

                                                                                       * INTER-04: Rapid toggling does not break layout or cause animation glitches.

Accessibility
                                                                                          * A11Y-01: Icons have ARIA labels or titles read by screen readers.

                                                                                          * A11Y-02: Sidebar element uses semantic role="navigation".

                                                                                          * A11Y-03: Focus ring is clearly visible on selected or focused icons.

 Responsive Behavior
                                                                                             * RESP-01: Sidebar remains functional and aligned on tablet viewports.

                                                                                             * RESP-02: On mobile, sidebar collapses into a drawer or overlay menu.




8.6.2  Add My Property (Add Property.png)
  

The Add My Property screen enables verified landlords to create and publish rental listings on ScrubHub. Landlords must complete verification through the admin portal by uploading two valid documents before their listing can go live. The page is designed for quick, step-by-step data entry and supports high-quality media uploads to attract potential tenants. Listing tier selection (Free, Paid, Featured) determines the listing’s visibility and duration.
Components
                                                                                                * Property Details Form – Collects essential rental information including title, address, rental price, availability dates, property type, and description.

                                                                                                * Photo Upload Area – Supports multiple photo uploads with drag-and-drop functionality and image reordering.

                                                                                                * Document Verification Prompt – Displays if the landlord has not completed verification, linking to the verification upload page.

                                                                                                * Listing Tier Selection –

                                                                                                   * Free: Two lifetime listings, each active for 30 days.

                                                                                                   * Paid: $4.99 (30 days), $66.99 (60 days), $90 (90 days).

                                                                                                   * Featured: Boosted visibility, pinned at top of search results.

                                                                                                      * Publish/Save as Draft Buttons – Allows the landlord to publish immediately (if verified) or save for later.









Functionality Summary
                                                                                                         * Restricts listing creation to authenticated, verified landlords.

                                                                                                         * Integrates with the admin portal to check and confirm landlord verification before publishing.

                                                                                                         * Supports multiple high-resolution image uploads and drag-and-drop rearranging.

                                                                                                         * Applies listing rules and pricing logic automatically based on the tier selected.

                                                                                                         * Publishes listings instantly upon payment confirmation for paid tiers.
QA Expectations
Form Validation
                                                                                                            * ADD-01: Submitting an empty form triggers required field errors.
                                                                                                            * ADD-02: Invalid price input (e.g., negative numbers, non-numeric) prompts an error.
                                                                                                            * ADD-03: Missing address triggers validation error and shows Google Maps red border.
                                                                                                            * ADD-04: Max image upload limit (e.g., 10 photos) enforced.
                                                                                                            * ADD-05: Description over 1,000 characters shows length warning.
                                                                                                            * ADD-06: Uploading unsupported image formats (.exe, .svg) is blocked.
                                                                                                            * ADD-07: Submitting with past availability dates is rejected.
                                                                                                            * ADD-08: Duplicate listings (same title, address, contact) prompt deduplication warning.

Field-Level Tests
                                                                                                               * FIELD-01: Title field supports special characters and 100+ char strings.
                                                                                                               * FIELD-02: Address uses autocomplete and validates postal format.
                                                                                                               * FIELD-03: Description box auto-expands for long entries.
                                                                                                               * FIELD-04: Checkbox amenities are selectable and persist on reload.
                                                                                                               * FIELD-05: File uploader allows drag & drop, and preview of thumbnails.
Security & Anti-Spam
                                                                                                               * SEC-01: XSS prevention in title and description inputs.
                                                                                                               * SEC-02: Uploaded files scanned for malware server-side.
                                                                                                               * SEC-03: Rate-limiting on submissions to prevent spam uploads.
                                                                                                               * SEC-04: CAPTCHA or reCAPTCHA v3 on form submission.
                                                                                                               * SEC-05: Contact info obfuscation to prevent scraping bots.

Accessibility (A11Y)
                                                                                                                  * A11Y-01: All form elements reachable via Tab key.
                                                                                                                  * A11Y-02: Descriptive aria-labels on each input.
                                                                                                                  * A11Y-03: Error messages read out by screen readers.
                                                                                                                  * A11Y-04: Sufficient color contrast on input focus and errors.
                                                                                                                  * A11Y-05: Image upload buttons are labeled with alt text for screen readers.




________________


8.6.3 Sublet Service (Sublet service.png)
  

The Sublet Service screen enables authenticated tenants with an active rental listing to create a short-term sublet post. This feature supports two service tiers—Self-Managed and Premium Full-Service—and ensures premium requests are handled by the admin team. Listings automatically expire and are removed from public view once the rental period ends.
Components
                                                                                                                  * Sublet Form – Captures sublet details: property address, rental period, price, and restrictions.

                                                                                                                  * Photo Upload Section – Optional images to enhance listing appeal.

                                                                                                                  * Service Tier Options:

                                                                                                                     * Self-Managed: $49.99 processing fee per sublet; tenant manages inquiries directly.

                                                                                                                     * Premium Full-Service: Starts at $599; request is stored with a “premium” status in the admin portal for team-managed handling, enhanced visibility, and concierge service.

                                                                                                                        * Verification Check – Confirms that only authenticated tenants with active rental listings can post a sublet.

                                                                                                                        * Publish Button – Publishes listing after payment for Self-Managed tier; Premium tier pending admin review.

                                                                                                                        * Auto-Expiry – Automatically removes sublet from listings once the end date passes.
Functionality Summary
                                                                                                                           * Only authenticated tenants with active rentals can create sublet posts.

                                                                                                                           * Self-Managed tier: $49.99 fee, tenant handles all communications.

                                                                                                                           * Premium Full-Service tier: $599+, admin-managed listing, higher exposure, and concierge handling.

                                                                                                                           * Premium requests stored in admin portal for review before publishing.

                                                                                                                           * Automatic removal of sublet listing after lease period ends.

                                                                                                                           * Optional photo uploads supported.

                                                                                                                           * All listing data stored and displayed according to tier rules.

QA Expectations
Form Validation
                                                                                                                              * SUBLET-01: Missing address/start/end date triggers required field errors.

                                                                                                                              * SUBLET-02: End date before start date is blocked.

                                                                                                                              * SUBLET-03: Missing verification file prompts alert.

                                                                                                                              * SUBLET-04: Invalid price entry triggers error.

                                                                                                                              * SUBLET-05: Description >1,000 chars triggers warning.

                                                                                                                              * SUBLET-06: Not accepting terms blocks submission.

                                                                                                                              * SUBLET-07: Multiple rapid submissions are rate-limited.

Field-Level Tests
                                                                                                                                 * FIELD-01: Address autocomplete with Google Maps.

                                                                                                                                 * FIELD-02: Date pickers prevent invalid manual input.

                                                                                                                                 * FIELD-03: Verification file accepts PDF, JPG, PNG only.

                                                                                                                                 * FIELD-04: Photo upload supports drag/drop + preview.

                                                                                                                                 * FIELD-05: Tooltip help text visible on hover.

Security & Anti-Spam
                                                                                                                                    * SEC-01: Inputs sanitized for XSS/SQL injection.

                                                                                                                                    * SEC-02: Verification files encrypted at rest.

                                                                                                                                    * SEC-03: CAPTCHA/human check prevents spam.

                                                                                                                                    * SEC-04: Contact info hidden until tenant verified.

                                                                                                                                    * SEC-05: Duplicate sublet listings flagged.

Accessibility (A11Y)
                                                                                                                                       * A11Y-01: Date picker keyboard accessible with screen reader support.

                                                                                                                                       * A11Y-02: Labels linked to inputs using for and aria-label.

                                                                                                                                       * A11Y-03: Error summaries announced to screen readers.

                                                                                                                                       * A11Y-04: Upload buttons have descriptive alt text and correct roles.

                                                                                                                                       * A11Y-05: “Sublet Policy” modal closable via Esc with ARIA labels.
                                                                                                                                          * 









8.6.4  My Listings ( My Listings.png)
  

The My Listings screen serves as the landlord or tenant’s central hub for managing all active, expired, and draft rental or sublet listings. It consolidates property management actions, availability inquiries, and listing status updates into one streamlined interface.
Components
                                                                                                                                          * Listings Table / Card View – Displays each property with its title, location, pricing, status (Active, Expired, Draft), and key actions.

                                                                                                                                          * Availability Requests Panel –

                                                                                                                                             * Shows all inquiries submitted via the availability form on the property details page.

                                                                                                                                             * Clicking Email opens the user’s default email client with the requester’s address pre-filled.

                                                                                                                                             * Clicking Phone opens the dialer with the requester’s number.

                                                                                                                                             * Marking as Responded moves the inquiry from the “New” section to the “Responded” section.

                                                                                                                                                * Search & Filter Tools – Allows filtering listings by status or searching by property title.
                                                                                                                                                * Edit Listing Action – Opens the listing form for changes.
                                                                                                                                                * Upgrade Option – Provides a direct path to upgrade to featured listing tier.
Functionality Summary
                                                                                                                                                * Displays all user-owned rental and sublet listings with quick access to edit or upgrade options.

                                                                                                                                                * Allows landlords to manage inquiries efficiently with email/phone triggers and response tracking.

                                                                                                                                                * Search and filter capabilities for quick navigation of multiple listings.

                                                                                                                                                * Keeps an organized separation of “New” and “Responded” availability requests.
QA Expectations
Listing Management
                                                                                                                                                   * LIST-01: All listings submitted by the user appear with accurate details.
                                                                                                                                                   * LIST-02: Edit action loads correct pre-populated form.
                                                                                                                                                   * LIST-03: Delete prompts confirmation and removes the listing only after confirm.
                                                                                                                                                   * LIST-04: Duplicate action clones the listing and opens it in edit mode.
                                                                                                                                                   * LIST-05: Clicking a listing title opens the public view or preview mode.
                                                                                                                                                   * LIST-06: Status tags (e.g., Published, Expired) are color-coded and consistent with backend status.
Sorting & Filtering
                                                                                                                                                   * FILTER-01: Selecting a status filter updates the list correctly.
                                                                                                                                                   * FILTER-02: Sort dropdown updates order of listings by selected criteria.
                                                                                                                                                   * FILTER-03: Search input filters listings in real-time or on submit.
                                                                                                                                                   * FILTER-04: Pagination (if any) loads correct number of listings per page.
Security & Data Integrity
                                                                                                                                                   * SEC-01: Listings belong only to the logged-in user; no data leak to other users.
                                                                                                                                                   * SEC-02: Deleted listings cannot be accessed directly via URL.
                                                                                                                                                   * SEC-03: Backend sanitizes edited listing inputs on save.
                                                                                                                                                   * SEC-04: All backend operations (delete/edit) require auth tokens.

Accessibility (A11Y)
                                                                                                                                                      * A11Y-01: All listing actions (edit/delete/duplicate) are reachable by keyboard.
                                                                                                                                                      * A11Y-02: ARIA roles and labels on status chips (e.g., “Status: Published”).
                                                                                                                                                      * A11Y-03: Focus state clearly visible on each card.
                                                                                                                                                      * A11Y-04: Screen reader reads listing metadata in logical order (title, status, date, price).








8.6.5 My Profile (my profile.png)
  

The My Profile screen provides each user with a personal space to manage and update their account details, preferences, and identity. It serves as the centralized profile hub, supporting editing, privacy settings, and even identity verification processes — crucial for building trust in the ScrubHub community.
Key Components
                                                                                                                                                      1. Profile Summary Section

                                                                                                                                                         * Full Name
                                                                                                                                                         * Email Address (editable)
                                                                                                                                                         * Phone Number
                                                                                                                                                         * Password

                                                                                                                                                            2. Editable Details

                                                                                                                                                               * Contact Info
                                                                                                                                                               * Password Change Section
QA Expectations
Data Editing & Validation
                                                                                                                                                               * PROFILE-01: All fields show current user data on page load.
                                                                                                                                                               * PROFILE-02: Editing a name or phone number and saving persists changes to the backend.
                                                                                                                                                               * PROFILE-03: Invalid phone numbers or email formats trigger validation errors.
                                                                                                                                                               * PROFILE-04: Saving profile changes triggers a success message.
                                                                                                                                                               * PROFILE-05: Changing password validates old password before allowing new one.
Security & Privacy
                                                                                                                                                               * SEC-01: Email and phone are not editable without re-authentication (e.g., password prompt).
                                                                                                                                                               * SEC-02: Password change triggers confirmation email.
                                                                                                                                                               * SEC-04: Deleting an account triggers multiple confirmation steps.
                                                                                                                                                               * SEC-05: Toggling 2FA sends a confirmation and sets auth method securely.
Notification Settings
                                                                                                                                                               * NOTIFY-01: Checking/unchecking notification toggles updates preferences in backend.
                                                                                                                                                               * NOTIFY-02: All toggles persist state after page reload.
                                                                                                                                                               * NOTIFY-03: Email and push preference combinations are enforced (e.g., push required for emergency updates).
Accessibility (A11Y)
                                                                                                                                                               * A11Y-01: All input fields are properly labeled and grouped under semantic headings.
                                                                                                                                                               * A11Y-02: Success/error messages are announced using ARIA live regions.
                                                                                                                                                               * A11Y-04: Toggling 2FA and notification switches are keyboard- and screen-reader friendly.
                                                                                                                                                               * A11Y-05: Modal overlays (e.g., password change) are fully accessible and trap focus.


8.6.6 Favorites (Favorites.png)
  

The Favorites screen displays all rental listings that a user has marked as favorites. It provides a quick-access space for users to revisit properties they are most interested in without having to repeat searches. Listings remain in this section until the user manually removes them.


Components
                                                                                                                                                               * Favorites List/Grid View – Displays saved listings with:

                                                                                                                                                                  * Thumbnail image

                                                                                                                                                                  * Property title and location

                                                                                                                                                                  * Monthly rent

                                                                                                                                                                  * Quick action buttons (View Details, Remove from Favorites)

                                                                                                                                                                     * Remove Favorite Action – Clicking the favorite icon again removes the property from the list instantly.

                                                                                                                                                                     * Empty State – Message encouraging users to browse and save listings if none are present.

Functionality Summary
                                                                                                                                                                        * Centralized access to all favorited properties.
                                                                                                                                                                        * Instant removal from favorites without page reload.
                                                                                                                                                                        * Supports role-agnostic usage—available to all authenticated users.
QA Expectations
Interaction Tests
                                                                                                                                                                        * FAV-01: Tapping the heart icon on a property from the search screen adds it to this screen.
                                                                                                                                                                        * FAV-02: Removing a property from favorites immediately reflects in the UI.
                                                                                                                                                                        * FAV-03: Clicking a listing card opens the full property detail screen.
Data Integrity & Sync
                                                                                                                                                                        * FAV-06: Favorites persist across sessions and devices when logged in.
                                                                                                                                                                        * FAV-07: If a favorited listing is removed by its owner, it disappears from this screen.
                                                                                                                                                                        * FAV-08: Sorting or reloading does not reset the user’s position in the list.
                                                                                                                                                                        * FAV-09: Offline mode still shows cached favorite properties.
Security & Privacy
                                                                                                                                                                        * SEC-01: User’s list of favorites is private and not visible to other users.
                                                                                                                                                                        * SEC-02: All favorite/unfavorite actions are authenticated and rate-limited to prevent abuse.
                                                                                                                                                                        * SEC-03: Links to property detail pages validate access permissions and status.
Accessibility (A11Y)
                                                                                                                                                                        * A11Y-01: Favorite/unfavorite buttons are labeled with aria-pressed and change appropriately.
                                                                                                                                                                        * A11Y-02: Property cards are fully navigable by keyboard.
                                                                                                                                                                        * A11Y-03: Screen reader users hear property summary including title, rent, and availability.
                                                                                                                                                                        * A11Y-04: Empty state CTA is keyboard-focusable and screen reader-compatible.
                                                                                                                                                                        * A11Y-05: “Inquire Now” buttons include aria-label="Inquire about [Property Name]".
8.6.7 Property Details (rental details.png)
  

The Property Details page provides a comprehensive view of an individual rental listing. It contains all relevant property information, media, and interactive actions to help users evaluate and initiate contact with the landlord or property owner. This is the primary conversion point for inquiries, favorites, and availability checks.
Components
                                                                                                                                                                        * Image Gallery – Multiple property photos with slideshow navigation and zoom functionality.

                                                                                                                                                                        * Property Summary – Title, location, monthly rent, and listing status (Free / Paid / Featured).

                                                                                                                                                                        * Detailed Description – Landlord-provided description of the property, including features and nearby amenities.

                                                                                                                                                                        * Key Details Section – Structured fields such as:

                                                                                                                                                                           * Property type

                                                                                                                                                                           * Number of rooms/bathrooms

                                                                                                                                                                           * Furnished/unfurnished status

                                                                                                                                                                           * Availability date

                                                                                                                                                                              * Landlord Contact Actions –

                                                                                                                                                                                 * “Email” button opens the user’s email client.

                                                                                                                                                                                 * “Phone” button reveals the landlord’s phone number upon click.

                                                                                                                                                                                 * “Request Availability” form to send an inquiry.

                                                                                                                                                                                    * Favorite Button – Adds or removes property from favorites list.
                                                                                                                                                                                    * Map Embed – Displays location via interactive map.
                                                                                                                                                                                    * Similar Listings – Suggested related properties to encourage further browsing.

Functionality Summary
                                                                                                                                                                                       * Enables detailed review of rental property before making contact.

                                                                                                                                                                                       * Supports direct inquiry through email or phone.

                                                                                                                                                                                       * Integrates with favorites and recently viewed modules.

                                                                                                                                                                                       * Displays related listings for better discovery.
QA Expectations
Functional Tests
                                                                                                                                                                                          * DETAIL-01: All listing fields render with correct and up-to-date information.
                                                                                                                                                                                          * DETAIL-02: “Ask for Availability” opens the inquiry form or popup.
                                                                                                                                                                                          * DETAIL-03: Tapping “Favorite” icon toggles state and updates user’s saved listings.
                                                                                                                                                                                          * DETAIL-04: Amenity tags display based on backend configuration.
Data Integrity Tests
                                                                                                                                                                                          * DETAIL-08: Property status (Active, Unavailable, Expired) is enforced — expired listings are read-only and inquiry buttons disabled.
                                                                                                                                                                                          * DETAIL-09: Rent price and availability data reflect backend source of truth.
                                                                                                                                                                                          * DETAIL-10: If listing is removed or flagged, show appropriate message or redirect.
                                                                                                                                                                                          * DETAIL-11: Review section only loads if reviews exist; placeholder text otherwise.
Security & Privacy
                                                                                                                                                                                          * SEC-01: User cannot access property detail page of listings they’re not authorized to view (e.g., blocked, private, expired).
                                                                                                                                                                                          * SEC-02: Contact form obfuscates owner’s email and phone.
                                                                                                                                                                                          * SEC-03: “Report Listing” triggers abuse-handling flow and server-side validation.
                                                                                                                                                                                          * SEC-04: All user-generated content (descriptions, reviews) is sanitized on render.
Accessibility (A11Y)
                                                                                                                                                                                          * A11Y-01: All text and interactive elements have sufficient contrast.
                                                                                                                                                                                          * A11Y-02: Carousel controls are keyboard-navigable and include screen reader labels like “Next image” or “Previous.”
                                                                                                                                                                                          * A11Y-03: Amenities list is announced as a group with associated icons labeled.
                                                                                                                                                                                          * A11Y-04: All inquiry buttons are labeled for screen readers with property name context.
                                                                                                                                                                                          * A11Y-05: Report button includes a confirmation prompt that is accessible and ARIA-compliant.









8.6.8  Ask for availability (Ask for availability.png)
  

The Ask for Availability feature allows prospective tenants to directly inquire about the availability of a specific property from its Property Details page. This interaction captures user interest, logs the inquiry in the landlord’s My Listings dashboard, and initiates a direct communication channel between tenant and landlord.
Components
                                                                                                                                                                                             * Trigger Button – “Ask for Availability” button prominently placed on the Property Details page.

                                                                                                                                                                                             * Inquiry Form –

                                                                                                                                                                                                * Name (required)

                                                                                                                                                                                                * Email (required)

                                                                                                                                                                                                * Phone number (optional)

                                                                                                                                                                                                * Message box (required)

                                                                                                                                                                                                   * Submit Action – On submission:

                                                                                                                                                                                                      * Inquiry is stored in the property owner’s My Listings → New Inquiries section.

                                                                                                                                                                                                      * Email/phone buttons allow landlord to respond directly.

                                                                                                                                                                                                         * Status Tracking – Landlord can mark inquiries as Responded, moving them from “New” to “Responded” list.





Functionality Summary
                                                                                                                                                                                                            * Provides a direct inquiry workflow from property page to landlord dashboard.

                                                                                                                                                                                                            * Integrates with landlord’s availability management tools.

                                                                                                                                                                                                            * Supports multiple contact methods (email, phone).

                                                                                                                                                                                                            * Tracks inquiry status for follow-up management.
QA Expectations
Functional Tests
                                                                                                                                                                                                               * AVAIL-02: Empty date fields trigger “Required” validation.
                                                                                                                                                                                                               * AVAIL-04: Submitting form shows confirmation toast or screen.
                                                                                                                                                                                                               * AVAIL-05: Message input accepts multiline entries and handles 500+ characters.
Security & Privacy
                                                                                                                                                                                                               * SEC-01: All user input is sanitized to prevent XSS or HTML injection.
                                                                                                                                                                                                               * SEC-02: Message form cannot be used to spam property owners — CAPTCHA required after 3 attempts.
                                                                                                                                                                                                               * SEC-03: Landlord contact information is protected until the user is verified or approved.
                                                                                                                                                                                                               * SEC-04: Users cannot spoof property references (IDs are locked at form level).
                                                                                                                                                                                                               * SEC-05: Emails sent to landlords do not expose tenant's full contact details unless opted in.
Accessibility (A11Y)
                                                                                                                                                                                                               * A11Y-01: Form is fully keyboard-navigable and responsive.
                                                                                                                                                                                                               * A11Y-02: Form fields include screen reader-friendly labels and ARIA roles.
                                                                                                                                                                                                               * A11Y-03: Error messages are announced automatically on validation failure.
                                                                                                                                                                                                               * A11Y-04: Calendar pickers are accessible with alternative text and keyboard controls.
                                                                                                                                                                                                               * A11Y-05: “Send Request” button has a clear label and responds to Enter key.




















8.6.9 Recently Viewed (Recently viewed.png)
  

The Recently Viewed feature provides users with quick access to properties they have recently visited on ScrubHub. This helps tenants and landlords easily revisit listings of interest without performing a fresh search. The list is dynamic and updates automatically as the user browses properties.
Components
                                                                                                                                                                                                               * Recently Viewed Section – Accessible from the user dashboard or via a dedicated menu link.

                                                                                                                                                                                                               * Property Cards –

                                                                                                                                                                                                                  * Thumbnail image

                                                                                                                                                                                                                  * Property title/address

                                                                                                                                                                                                                  * Monthly rent

                                                                                                                                                                                                                  * Link to full Property Details page

                                                                                                                                                                                                                     * Storage & Ordering –

                                                                                                                                                                                                                        * Displays a maximum of the 10 most recently viewed properties.

                                                                                                                                                                                                                        * Automatically removes the oldest entry when a new one is added beyond the limit.

                                                                                                                                                                                                                           * Cross-Device Sync – Viewing history tied to the user’s account so it persists across devices.



Functionality Summary
                                                                                                                                                                                                                              * Automatically logs properties the user views.

                                                                                                                                                                                                                              * Stores up to 10 most recent entries with newest at the top.

                                                                                                                                                                                                                              * One-click access to revisit a property’s full details.

                                                                                                                                                                                                                              * Fully integrated with the user’s authenticated session for cross-device continuity.

QA Expectations
Functional Tests
                                                                                                                                                                                                                                 * RECENT-01: Properties opened from search or favorites are automatically added to Recently Viewed.
                                                                                                                                                                                                                                 * RECENT-02: Cards display in reverse chronological order.
                                                                                                                                                                                                                                 * RECENT-03: Time since last view is accurate and updates in real time or refresh.
                                                                                                                                                                                                                                 * RECENT-04: Removing a property from the list works instantly without full reload.
                                                                                                                                                                                                                                 * RECENT-05: “Clear All” shows confirmation modal before clearing history.
                                                                                                                                                                                                                                 * RECENT-06: Clicking “View Details” loads full property page.
Persistence & State
                                                                                                                                                                                                                                 * RECENT-07: Logged-in users see the same list across devices.
                                                                                                                                                                                                                                 * RECENT-08: Guests’ recent views are retained locally across browser sessions.
                                                                                                                                                                                                                                 * RECENT-09: Clearing browser cookies resets history for guest users.
                                                                                                                                                                                                                                 * RECENT-10: Listings no longer available are greyed out or marked “Unavailable.”
Security & Privacy
                                                                                                                                                                                                                                 * SEC-01: Only the current user can view their Recently Viewed history.
                                                                                                                                                                                                                                 * SEC-02: Backend queries for logged-in users are scoped to user ID.
                                                                                                                                                                                                                                 * SEC-03: If a previously viewed property was removed or flagged, access is restricted with a message.
                                                                                                                                                                                                                                 * SEC-04: “Clear History” actions require confirmation and cannot be undone.

Accessibility (A11Y)
                                                                                                                                                                                                                                    * A11Y-01: All listing cards are structured using semantic HTML for list/grid.
                                                                                                                                                                                                                                    * A11Y-02: Buttons have descriptive labels (e.g., “View [Property Name] again”).
                                                                                                                                                                                                                                    * A11Y-03: Focus is moved to the first property on load for keyboard users.
                                                                                                                                                                                                                                    * A11Y-04: Time stamps are readable aloud by screen readers (e.g., “Viewed 1 hour ago”).
                                                                                                                                                                                                                                    * A11Y-05: “Clear All” modal includes accessible roles, focus trap, and Escape to close.








8.6.10  Search by Map (search by map.png)
  The Search by Map feature enables users to visually explore available rental properties on an interactive map interface. This tool enhances location-based discovery by allowing tenants and landlords to identify listings in specific geographic areas, particularly near hospitals or medical schools.
Components
                                                                                                                                                                                                                                    * Interactive Map Interface –

                                                                                                                                                                                                                                       * Pan, zoom, and drag navigation.

                                                                                                                                                                                                                                       * Map pins representing available properties.

                                                                                                                                                                                                                                          * Map Pins & Property Cards –

                                                                                                                                                                                                                                             * Pin click opens a quick preview card with thumbnail, address, rent, and “View Details” link.

                                                                                                                                                                                                                                                * List & Map View Toggle – Switch between traditional listing view and map-based view.

                                                                                                                                                                                                                                                * Geolocation Support – Option to center the map on the user’s current location.





Functionality Summary
                                                                                                                                                                                                                                                   * Allows property browsing via interactive map with real-time filtering.

                                                                                                                                                                                                                                                   * Integrates with hospital and medical school location data to highlight nearby rentals.

                                                                                                                                                                                                                                                   * Supports combined map and list views for improved navigation.

                                                                                                                                                                                                                                                   * Dynamically updates search results when the map position or filters change.
QA Expectations
Map Functionality Tests
                                                                                                                                                                                                                                                      * MAP-01: Map loads with all pins representing available properties.
                                                                                                                                                                                                                                                      * MAP-02: Clicking a pin shows listing summary with correct details.
                                                                                                                                                                                                                                                      * MAP-03: Zooming in reveals individual pins from clusters.
                                                                                                                                                                                                                                                      * MAP-04: Panning the map updates sidebar/list view in real time.
                                                                                                                                                                                                                                                      * MAP-05: Clicking “Search This Area” refreshes listings within current bounds.
                                                                                                                                                                                                                                                      * MAP-06: Listings outside current bounds are hidden unless explicitly searched for.
Filtering & Search
                                                                                                                                                                                                                                                      * MAP-07: Filters (price, bed count, etc.) update both map pins and sidebar.
                                                                                                                                                                                                                                                      * MAP-08: Distance-based filter from hospital address works accurately with geolocation radius.
                                                                                                                                                                                                                                                      * MAP-09: Sidebar results sort correctly by price or distance.
                                                                                                                                                                                                                                                      * MAP-10: Clearing filters resets the map and repopulates all pins.
Location-Awareness & Responsiveness
                                                                                                                                                                                                                                                      * MAP-11: Clicking “My Location” centers map on user’s current geolocation.
                                                                                                                                                                                                                                                      * MAP-12: Responsive layout adapts map and sidebar on mobile/tablet.
                                                                                                                                                                                                                                                      * MAP-13: Device orientation change preserves map state and zoom.
                                                                                                                                                                                                                                                      * MAP-14: Errors in loading map tiles or listings gracefully display fallback messages.
Security & Privacy
                                                                                                                                                                                                                                                      * SEC-01: Geolocation requests require user consent and are never stored server-side.
                                                                                                                                                                                                                                                      * SEC-02: Map pins do not expose exact addresses unless user opens full property.
                                                                                                                                                                                                                                                      * SEC-03: Unauthorized listings (expired, private) are excluded from pin rendering.
                                                                                                                                                                                                                                                      * SEC-04: Map tiles and pin data are cached locally but expire appropriately to prevent stale data.

Accessibility (A11Y)
                                                                                                                                                                                                                                                         * A11Y-01: Keyboard navigation for map controls (zoom, drag, reset).
                                                                                                                                                                                                                                                         * A11Y-02: Pin tooltips use aria-describedby for screen readers.
                                                                                                                                                                                                                                                         * A11Y-03: Filters are fully tabbable and include accessible labels.
                                                                                                                                                                                                                                                         * A11Y-04: Side list view includes property summaries in text form for screen reader users.
                                                                                                                                                                                                                                                         * A11Y-05: “Search This Area” and location buttons use role="button" with labels.


8.6.11 Subscriptions (subscription.png)
  The Subscriptions feature manages premium listing plans for landlords and other paid services on ScrubHub. It allows users to upgrade from the free plan to higher visibility tiers or extended listing durations. Subscriptions are tied to the user’s account and processed securely through Stripe.
Components
                                                                                                                                                                                                                                                         * Plan Selection Interface –

                                                                                                                                                                                                                                                            * Free plan (default for new users).

                                                                                                                                                                                                                                                            * Paid plans with varying durations and benefits (e.g., Featured Listings, longer listing periods).

                                                                                                                                                                                                                                                               * Plan Details Display – Shows pricing, benefits, and comparison between tiers.

                                                                                                                                                                                                                                                               * Upgrade Button – Directs users to payment processing via Stripe.

                                                                                                                                                                                                                                                               * Payment History & Active Plan Info – Displays current subscription status, expiry date, and payment records.

                                                                                                                                                                                                                                                               * Auto-Renewal Option – Allows enabling/disabling recurring billing.
Functionality Summary
                                                                                                                                                                                                                                                                  * Enables users to view, purchase, and manage subscription plans.

                                                                                                                                                                                                                                                                  * Stripe integration for secure payment processing.

                                                                                                                                                                                                                                                                  * Tracks subscription validity and automatically updates listing privileges.

                                                                                                                                                                                                                                                                  * Provides visibility boosts and extended listing periods for paid users.

QA Expectations
Functional Tests
                                                                                                                                                                                                                                                                     * SUBS-01: Current subscription status correctly displays active plan and renewal date.
                                                                                                                                                                                                                                                                     * SUBS-02: Selecting a new plan updates pricing summary and features.
                                                                                                                                                                                                                                                                     * SUBS-03: Switching from monthly to annual adjusts price accordingly.
                                                                                                                                                                                                                                                                     * SUBS-04: Entering valid card details and subscribing succeeds with confirmation.
                                                                                                                                                                                                                                                                     * SUBS-05: Invalid card input (expired, incomplete) prompts inline error.
                                                                                                                                                                                                                                                                     * SUBS-06: Cancelling a subscription sets plan to expire on next billing date, not immediately.
Edge Cases & UX
                                                                                                                                                                                                                                                                     * SUBS-07: Switching plans mid-cycle calculates prorated charges correctly.
                                                                                                                                                                                                                                                                     * SUBS-08: Promo codes reduce price and display success confirmation.
                                                                                                                                                                                                                                                                     * SUBS-09: Changing billing address auto-updates billing info without affecting plan.
                                                                                                                                                                                                                                                                     * SUBS-10: Plans restricted by user type (e.g., landlord-only) are disabled or hidden for others.
Security & Compliance
                                                                                                                                                                                                                                                                     * SEC-01: All card data handled by secure PCI-compliant gateway (e.g., Stripe).
                                                                                                                                                                                                                                                                     * SEC-02: No card data stored on ScrubHub servers.
                                                                                                                                                                                                                                                                     * SEC-03: All subscription actions (upgrade/cancel) require user authentication.
                                                                                                                                                                                                                                                                     * SEC-04: Subscription downgrade limits are enforced (e.g., no more than 3 listings if dropping to Free).
                                                                                                                                                                                                                                                                     * SEC-05: Billing statements are available in downloadable PDF form and protected by login.
Accessibility (A11Y)
                                                                                                                                                                                                                                                                     * A11Y-01: All plan options include ARIA descriptions and accessible focus states.
                                                                                                                                                                                                                                                                     * A11Y-02: Screen reader reads pricing tiers and features as structured lists.
                                                                                                                                                                                                                                                                     * A11Y-03: Credit card input fields are keyboard-accessible and labeled with autocomplete.
                                                                                                                                                                                                                                                                     * A11Y-04: Coupon field includes error and success announcement for screen readers.
                                                                                                                                                                                                                                                                     * A11Y-05: Upgrade/Cancel buttons are clearly labeled and respond to Enter key.






















8.6.12 Get Credit Report (Get Credit Report.png)
  

The Get Credit Report feature allows tenants to request their credit rating and background check directly through the platform. This is powered by the Trustii API and uses our assigned API key for authentication. The report is reusable for multiple property applications, avoiding repeated credit checks.
Components
                                                                                                                                                                                                                                                                     * Credit Report Request Form – Captures user details required by Trustii (name, address, date of birth, etc.).

                                                                                                                                                                                                                                                                     * Payment Gateway – Collects service fee before initiating API request.

                                                                                                                                                                                                                                                                     * Trustii API Call – Uses the create-an-inquiry endpoint (docs) to submit user data and generate the report.

                                                                                                                                                                                                                                                                     * Report Storage – Securely stores returned credit report and background check results.

                                                                                                                                                                                                                                                                     * Download / Share – User can download the report as PDF or share directly with landlords via the platform.
                                                                                                                                                                                                                                                                     * 
Functionality Summary
                                                                                                                                                                                                                                                                        * Integrates with Trustii API using platform API key.

                                                                                                                                                                                                                                                                        * Payment required before report generation.

                                                                                                                                                                                                                                                                        * Reports stored securely and encrypted.

                                                                                                                                                                                                                                                                        * API response must be parsed to display structured results in the UI.

                                                                                                                                                                                                                                                                        * Users can reuse the same report for multiple applications without additional cost (validity period configurable).


QA Expectations
Functional Tests
                                                                                                                                                                                                                                                                           * CREDIT-01: All required identity fields must be filled before proceeding.
                                                                                                                                                                                                                                                                           * CREDIT-03: Toggling the consent checkbox is required to enable submission.
                                                                                                                                                                                                                                                                           * CREDIT-04: Successful generation loads summary and score immediately.
                                                                                                                                                                                                                                                                           * CREDIT-05: PDF generation and download includes all visible data and branding.
                                                                                                                                                                                                                                                                           * CREDIT-06: Clicking "Share with Landlord" creates a secure tokenized link.
Security, Privacy, & Compliance
                                                                                                                                                                                                                                                                           * SEC-01: SSN and DOB are encrypted in transit and never stored in plaintext.
                                                                                                                                                                                                                                                                           * SEC-02: Credit report data is time-limited and automatically expires (e.g., 30 days).
                                                                                                                                                                                                                                                                           * SEC-03: ScrubHub partners with a licensed credit bureau API (e.g., TransUnion, Experian).
                                                                                                                                                                                                                                                                           * SEC-04: Every report generation is logged and visible to the user.
                                                                                                                                                                                                                                                                           * SEC-05: Shared reports are revocable at any time.
Edge Cases & Validation
                                                                                                                                                                                                                                                                           * CREDIT-07: Users without sufficient data receive a message: “Unable to retrieve report.”
                                                                                                                                                                                                                                                                           * CREDIT-08: Attempting multiple generations in 24 hours is blocked.
                                                                                                                                                                                                                                                                           * CREDIT-09: Auto-filled fields for logged-in users (e.g., name, email) populate correctly.
                                                                                                                                                                                                                                                                           * CREDIT-10: Errors from the bureau API are gracefully handled with retry logic.
Accessibility (A11Y)
                                                                                                                                                                                                                                                                           * A11Y-01: All identity fields include ARIA labels and instructions.
                                                                                                                                                                                                                                                                           * A11Y-02: Error messages are read by screen readers and located adjacent to fields.
                                                                                                                                                                                                                                                                           * A11Y-03: Checkbox for consent is large, keyboard-navigable, and has descriptive label.
                                                                                                                                                                                                                                                                           * A11Y-04: Score summary has visual indicators (color and text) plus screen reader labels.
                                                                                                                                                                                                                                                                           * A11Y-05: Download and Share buttons are accessible by keyboard and clearly described.
















8.6.13 Tenant N9 Notice (Tenant N9 Notice.png)
  

The Tenant N9 Notice feature enables tenants to complete and obtain the official Ontario “N9 – Tenant’s Notice to End the Tenancy” form directly from the platform. The system pre-fills the PDF based on user-provided details, streamlining the legal termination process for leases.
Components
                                                                                                                                                                                                                                                                           * Form Input Section – Collects all required N9 form fields such as tenant name, address, termination date, and landlord details.

                                                                                                                                                                                                                                                                           * PDF Generator – Automatically fills the official N9 PDF (N9 Form) template with the submitted details.

                                                                                                                                                                                                                                                                           * Payment Gateway – Processes payment before allowing the PDF to be downloaded.

                                                                                                                                                                                                                                                                           * Download Link – Provides tenants with the completed and ready-to-submit N9 form after payment confirmation.

                                                                                                                                                                                                                                                                           * Validation Layer – Ensures all mandatory fields are completed before generating the document.

Functionality Summary
                                                                                                                                                                                                                                                                              * Allows tenants to fill in required N9 form fields directly on ScrubHub.

                                                                                                                                                                                                                                                                              * Validates all inputs to meet form requirements before submission.

                                                                                                                                                                                                                                                                              * Generates an official N9 PDF for download after payment.

                                                                                                                                                                                                                                                                              * Provides a secure and accurate process for ending tenancy agreements.


QA Expectations
Functional Tests
                                                                                                                                                                                                                                                                                 * N9-01: Page displays explanation, legal context, and instructions clearly.
                                                                                                                                                                                                                                                                                 * N9-02: Calendar picker blocks selection of dates less than 60 days ahead.
                                                                                                                                                                                                                                                                                 * N9-03: Form submission disabled until all required fields are filled and validated.
                                                                                                                                                                                                                                                                                 * N9-04: Clicking “Download PDF” correctly generates a filled-out N9 with inputs.
                                                                                                                                                                                                                                                                                 * N9-05: Submitting the form triggers backend email delivery to listed landlord address.
                                                                                                                                                                                                                                                                                 * N9-06: Saving a draft retains content in localStorage or tenant’s account.
Legal & Data Integrity
                                                                                                                                                                                                                                                                                 * N9-07: All submitted forms are time-stamped and immutable post-submission.
                                                                                                                                                                                                                                                                                 * N9-08: Landlord cannot edit the received copy; tenant retains signed original.
                                                                                                                                                                                                                                                                                 * N9-09: Email confirmation includes a copy of the notice and reference number.
                                                                                                                                                                                                                                                                                 * N9-10: If landlord email is missing, system prompts user to download PDF for manual delivery.
Security & Privacy
                                                                                                                                                                                                                                                                                 * SEC-01: Tenant identity and address data are encrypted at rest and in transit.
                                                                                                                                                                                                                                                                                 * SEC-02: Submissions are linked to user accounts and access-restricted.
                                                                                                                                                                                                                                                                                 * SEC-03: PDF downloads are tokenized, temporary, and watermarked.
                                                                                                                                                                                                                                                                                 * SEC-04: Server checks email headers to prevent N9 spam abuse.
                                                                                                                                                                                                                                                                                 * SEC-05: All form actions are protected with CSRF tokens and rate limiting.
Accessibility (A11Y)
                                                                                                                                                                                                                                                                                 * A11Y-01: All input fields follow form accessibility standards (labels, ARIA attributes).
                                                                                                                                                                                                                                                                                 * A11Y-02: Signature field is keyboard- and screen reader-compatible.
                                                                                                                                                                                                                                                                                 * A11Y-03: Error messages use ARIA live regions to be screen-reader detectable.
                                                                                                                                                                                                                                                                                 * A11Y-04: PDF download button has clear alt-text and role="button".
                                                                                                                                                                                                                                                                                 * A11Y-05: Date picker includes accessible keyboard navigation and descriptive tooltips.

8.6.14  Device Marketplace (Device Marketplace.png)
  

The Device Marketplace provides a platform for tenants, landlords, hospitals, and recruiters to list, buy, or sell medical and related equipment, the page displays 5 list items with pagination. The feature supports both free and paid listings, with pricing logic applied based on the item’s value. Hospitals and recruiters can also perform bulk listings, which are routed for admin review.
Components
                                                                                                                                                                                                                                                                                    * Listing Form – Allows users to add item details such as title, description, condition, price, and photos.

                                                                                                                                                                                                                                                                                    * Photo Upload Tool – Supports multiple image uploads with drag-and-drop reordering.

                                                                                                                                                                                                                                                                                    * Bulk Listing Option – Enables hospitals and recruiters to submit multiple listings at once, which are stored in the admin portal for moderation.

                                                                                                                                                                                                                                                                                    * Contact Information Control – Hides seller’s phone number until “Show Phone Number” is clicked.

                                                                                                                                                                                                                                                                                    * Search & Filters – Allows browsing by category, condition, price range, and location.





Pricing Logic:
Item Price Range
	Listing Fee
	$0 – $100
	Free
	$100.01 – $500
	$9.99
	$500.01 – $2,000
	$19.99
	Above $2,000
	$49.99 (max)
	                                                                                                                                                                                                                                                                                       * Fee is calculated automatically when the item price is entered.
                                                                                                                                                                                                                                                                                       * Updates dynamically if the price changes.
Functionality Summary
                                                                                                                                                                                                                                                                                       * Enables users to list devices with dynamic pricing based on product value.

                                                                                                                                                                                                                                                                                       * Supports multiple photo uploads and reordering.

                                                                                                                                                                                                                                                                                       * Allows hospitals and recruiters to submit bulk listings for admin review.

                                                                                                                                                                                                                                                                                       * Protects user privacy by hiding contact details until explicitly revealed.

                                                                                                                                                                                                                                                                                       * Provides search and filtering tools for easy discovery of items.
QA Expectations
Functional Tests
                                                                                                                                                                                                                                                                                          * MKT-01: Marketplace feed loads correctly with accurate filters and sort applied.
                                                                                                                                                                                                                                                                                          * MKT-02: Tapping an item card opens the full listing page.
                                                                                                                                                                                                                                                                                          * MKT-03: Posting a new item validates title, price, and image fields.
                                                                                                                                                                                                                                                                                          * MKT-04: Marking an item as sold removes it from active listings.
                                                                                                                                                                                                                                                                                          * MKT-05: Favorites and contact seller features work for logged-in users only.
                                                                                                                                                                                                                                                                                          * MKT-06: Images load with lazy loading or preview fallback.
Listing Management & User Flow
                                                                                                                                                                                                                                                                                          * MKT-07: Items listed by a user appear under their account or profile.
                                                                                                                                                                                                                                                                                          * MKT-08: Sellers can edit or delete their listings.
                                                                                                                                                                                                                                                                                          * MKT-09: If a seller deletes an item, it no longer appears in search or favorites.
                                                                                                                                                                                                                                                                                          * MKT-10: Buyers can save items and revisit them from the Favorites screen.
Security & Moderation
                                                                                                                                                                                                                                                                                          * SEC-01: All item descriptions are sanitized for XSS on input and display.
                                                                                                                                                                                                                                                                                          * SEC-02: Image uploads are validated (size, type) and virus scanned.
                                                                                                                                                                                                                                                                                          * SEC-03: Reported listings trigger a moderation review workflow.
                                                                                                                                                                                                                                                                                          * SEC-04: Contact details are hidden unless both parties agree to communicate.
                                                                                                                                                                                                                                                                                          * SEC-05: Repeated spam postings are blocked using rate limits and IP monitoring.
Accessibility (A11Y)
                                                                                                                                                                                                                                                                                          * A11Y-01: Marketplace feed is navigable by keyboard and screen reader.
                                                                                                                                                                                                                                                                                          * A11Y-02: Each item card includes ARIA labels for image, title, and price.
                                                                                                                                                                                                                                                                                          * A11Y-03: Posting form includes label fields and error handling via ARIA live regions.
                                                                                                                                                                                                                                                                                          * A11Y-04: Tooltips and icons include accessible alt text.
                                                                                                                                                                                                                                                                                          * A11Y-05: Visual hierarchy ensures that screen readers can distinguish cards from actions.





















8.7 Hospital Portal – ScrubHub (hospital.png)
  

Overview:
 The Hospital Dashboard serves as the landing page for authenticated hospital users. It provides a quick overview of hospital activity including open job postings, recent applicants, marketplace listings, and quick access to core modules.





Hospital – Post a Position
Overview:
 Allows hospital users to create new job or residency postings with detailed role descriptions, eligibility criteria, and application instructions.
Components:
                                                                                                                                                                                                                                                                                          * Position Details Form – Fields for title, department, description, requirements, and application deadline.

                                                                                                                                                                                                                                                                                          * Location Field – Automatically set to the hospital’s registered location.

                                                                                                                                                                                                                                                                                          * Compensation & Benefits – Optional details to display for applicants.

                                                                                                                                                                                                                                                                                          * Application Contact Info – Email, phone, or link to online application.

                                                                                                                                                                                                                                                                                          * Payment Section – Integration with Stripe for posting fees before publishing.

                                                                                                                                                                                                                                                                                          * Save as Draft – Option to create a draft for review before posting live.
Hospital – Search for Positions
Overview:
 Hospitals can browse open positions posted by other hospitals in their own location (geographically restricted search). This helps them gauge market activity, competitive offers, or find partnership opportunities.
Components:
                                                                                                                                                                                                                                                                                             * Position Listings Table – List 10 positions per page with details such as title, hospital name, department, posting date.

                                                                                                                                                                                                                                                                                             * Filters – Role type, department, posting date range.

                                                                                                                                                                                                                                                                                             * Location Restriction – Automatically limits search results to the hospital’s city or region.

                                                                                                                                                                                                                                                                                             * View Details Button – Opens full position description and application info.
Hospital – My Positions
Overview:
 Lists all positions posted by the hospital along with their status and applicant activity.
Components:
                                                                                                                                                                                                                                                                                                * Position List View – Columns for title, date posted, status (Active/Closed/Draft), and applicant count.

                                                                                                                                                                                                                                                                                                * Applicant Management – For each position, hospital users can:

                                                                                                                                                                                                                                                                                                   * View applicant list with names and application date.

                                                                                                                                                                                                                                                                                                   * Open resumes.

                                                                                                                                                                                                                                                                                                   * Email applicants directly via “mailto” link.

                                                                                                                                                                                                                                                                                                   * Update status to “Interviewing” or “Rejected.”

                                                                                                                                                                                                                                                                                                      * Edit/Delete Options – Modify job details or remove postings.
Hospital – Edit Position
Overview:
 Allows hospitals to modify details of an existing job posting without losing its posting history.
Components:
                                                                                                                                                                                                                                                                                                      * Editable Fields – Title, description, requirements, and contact details.

                                                                                                                                                                                                                                                                                                      * Status Toggle – Switch between Active/Draft/Closed.

                                                                                                                                                                                                                                                                                                      * Save Changes Button – Immediately updates live posting.

Hospital – Marketplace
Overview:
 Hospitals can list medical devices or equipment for sale.
Components:
                                                                                                                                                                                                                                                                                                         * Add Device Listing Form – Name, description, price, condition, and photos.

                                                                                                                                                                                                                                                                                                         * Pricing Logic – Follows same pricing logic on device marketplace

                                                                                                                                                                                                                                                                                                         * Photo Upload – Multiple images with drag-and-drop reordering.

                                                                                                                                                                                                                                                                                                         * Contact Info – Phone number hidden until “Show Phone Number” is clicked.

                                                                                                                                                                                                                                                                                                         * Bulk Listing Option – For uploading multiple items at once (requires admin review).

Hospital – Bulk Listing
Overview:
 Allows hospitals to upload multiple job positions or marketplace listings in a single action.
Components:
                                                                                                                                                                                                                                                                                                            * CSV Upload – Standard template provided for bulk job/equipment data.

                                                                                                                                                                                                                                                                                                            * Preview Table – Shows uploaded items before submission.

                                                                                                                                                                                                                                                                                                            * Admin Review Status – All bulk listings are routed to the admin portal for approval.

Hospital – Billing
Overview:
 Manages hospital payment methods for job postings, marketplace fees, and other platform transactions.
Components:
                                                                                                                                                                                                                                                                                                               * Saved Payment Methods – View, add, or delete cards.

                                                                                                                                                                                                                                                                                                               * Transaction History – Shows past payments with date, description, and amount.

                                                                                                                                                                                                                                                                                                               * Add Payment Method Form – Stripe integration for secure card entry.



Functionality Summary
                                                                                                                                                                                                                                                                                                                  * Dashboard displays live stats (job posts (10 per page), applicants, listings), quick links to modules, and real-time notifications.

                                                                                                                                                                                                                                                                                                                  * Post a Position creates job/residency posts with location auto-set, validation, Stripe fee processing, and optional drafts/previews.

                                                                                                                                                                                                                                                                                                                  * Search for Positions lists jobs restricted to hospital location by default with filters, sorting, and live updates.

                                                                                                                                                                                                                                                                                                                  * My Positions shows hospital’s own posts, applicant list with resume/email access, and status change controls (Interviewing / Rejected / Hired).

                                                                                                                                                                                                                                                                                                                  * Edit Position loads existing posting for updates, allows republishing or closing, and logs all edits; revalidates payments if terms change.

                                                                                                                                                                                                                                                                                                                  * Marketplace lists equipment/devices with title, price, photos, fee logic, hidden phone reveal, edit/mark sold/remove options, and admin review for flagged items.

                                                                                                                                                                                                                                                                                                                  * Bulk Listing uploads multiple job/device listings via CSV template, validates data, queues approved rows for admin moderation, and tracks status.

                                                                                                                                                                                                                                                                                                                  * Billing manages Stripe payment methods, shows transaction history, invoices, account balance, and logs all charges/refunds for audit.


QA Expectations – Hospital Portal (ScrubHub)
Post a Position
QA ID
	Test Description
	POST-01
	Required fields (title, description, location) trigger validation errors when empty.
	POST-02
	Submitting a valid job redirects to the "My Positions" module and shows confirmation.
	POST-03
	Partially completed form saves progress and reloads correctly.
	POST-04
	Invalid salary range or date format prompts field-specific errors.
	POST-05
	Job postings are correctly associated with the logged-in hospital profile.
	

Search for Positions
QA ID
	Test Description
	SEARCH-01
	Filter results match search criteria (e.g., specialization, region, job type).
	SEARCH-02
	No matching results show fallback message like “No positions found.”
	SEARCH-03
	Sorting by salary, date, or location functions as expected.
	SEARCH-04
	Pagination or infinite scroll loads data without duplication or cutoff.
	SEARCH-05
	Clicking a job listing opens the correct job detail view.
	My Positions
QA ID
	Test Description
	MYPOST-01
	Posted positions display in reverse chronological order.
	MYPOST-02
	Edits to a job post are reflected immediately after saving.
	MYPOST-03
	Archived positions are moved from the active list and do not appear in new searches.
	MYPOST-04
	Applicant lists for each job are accurate and load without error.
	MYPOST-05
	Unauthorized users cannot edit/view job posts via direct URLs.
	Billing
QA ID
	Test Description
	BILL-01
	Billing dashboard displays current balance and invoice history accurately.
	BILL-02
	Adding/editing a payment method triggers confirmation modal or toast message.
	BILL-03
	Downloading an invoice produces a valid, readable PDF with correct formatting.
	BILL-04
	Invalid credit card entries return clear inline error messages.
	BILL-05
	All billing data is securely transmitted and confirmed encrypted over HTTPS.
	

Device Marketplace
QA ID
	Test Description
	DEV-01
	Submitting a new device listing with valid data shows success and adds to the list.
	DEV-02
	Device tiles include name, thumbnail, condition, and pricing.
	DEV-03
	Filters (type, location, condition) work independently and in combination.
	DEV-04
	Contacting a buyer/seller opens the appropriate messaging workflow.
	DEV-05
	Inactive or sold listings are either hidden or marked clearly as unavailable.
	Bulk Listing & Devices
QA ID
	Test Description
	BULK-01
	Invalid or corrupted CSV file triggers clear upload error with line reference.
	BULK-02
	Valid CSV displays preview for confirmation before upload completes.
	BULK-03
	Accepted files create multiple listings accurately with correct role association.
	BULK-04
	Errors in individual rows (e.g., missing price) are caught during pre-submission check.
	BULK-05
	Bulk uploads are logged and trackable through admin or user dashboard.
	General QA for the Hospital Portal
QA ID
	Test Description
	GEN-01
	All tile modules route correctly and open their respective sections.
	GEN-02
	Responsive layout adapts to mobile, tablet, and desktop with no layout breakage.
	GEN-03
	Keyboard navigation works on all modules for accessibility compliance (WCAG 2.1).
	GEN-04
	Concurrent access is supported for multiple hospital users without session errors.
	GEN-05
	Changes made in one module (e.g., job posting) reflect system-wide without needing refresh.
	

8.8 Recruiter Portal – ScrubHub (recruiter.png)
  

The Recruiter Dashboard provides a centralized view of recruitment activities, including active job posts, applicants, and recent updates. Quick-access links allow recruiters to post new jobs, search existing opportunities, view applications, and manage marketplace listings. The layout emphasizes role-specific metrics, such as total active jobs, pending applications, and upcoming interviews.
Components
                                                                                                                                                                                                                                                                                                                     * Header Navigation: Role-specific navigation with tabs for Jobs, Applicants, Marketplace, Billing, and Profile.

                                                                                                                                                                                                                                                                                                                     * Metrics Overview: Displays counts for active positions, new applications, and devices listed in the marketplace.

                                                                                                                                                                                                                                                                                                                     * Quick Links Panel: Buttons to Post a Job, Search for Jobs, and View Marketplace.

                                                                                                                                                                                                                                                                                                                     * Notifications Panel: Shows system alerts, applicant updates, and payment reminders.

                                                                                                                                                                                                                                                                                                                     * Recent Activity Feed: Lists last job postings, application submissions, and marketplace actions.

Post a Job
Overview
 Allows recruiters to create and publish job openings. Jobs default to the recruiter’s registered location for geographic accuracy. Stripe payment is processed for applicable posting fees. Posts can be saved as drafts, previewed before publishing, and later edited.


Components
                                                                                                                                                                                                                                                                                                                        * Job Details Form: Title, description, required skills, location (auto-filled from recruiter profile).

                                                                                                                                                                                                                                                                                                                        * Compensation & Benefits: Optional salary, perks, and requirements.

                                                                                                                                                                                                                                                                                                                        * Application Settings: Deadline, contact method (email/phone), and application link.

                                                                                                                                                                                                                                                                                                                        * Fee Processing: Stripe checkout for paid job posts.

                                                                                                                                                                                                                                                                                                                        * Action Buttons: Save Draft, Preview, Publish.

Search for Jobs
Overview
 Displays jobs posted in the recruiter’s location or relevant area. 10 jobs are listed for each page, Advanced filters allow sorting by date posted, job type, specialty, or hospital/organization name. Clicking a job displays full details and application instructions.
Components
                                                                                                                                                                                                                                                                                                                           * Search Bar: Keyword and location-based search (location defaults to recruiter’s profile).

                                                                                                                                                                                                                                                                                                                           * Filters Panel: Job type, specialty, posting date, and organization.

                                                                                                                                                                                                                                                                                                                           * Job Listing Cards: Title, company/hospital, location, posting date, and brief description.

                                                                                                                                                                                                                                                                                                                           * Pagination/Load More: For browsing longer lists (10 jobs/positions per page).

My Jobs
Overview
 Shows the recruiter’s own job postings. Recruiters can edit or remove listings and track application counts. Selecting a listing opens the applicant list.
Components
                                                                                                                                                                                                                                                                                                                              * List View: Displays 10 jobs on each page with details such as Job title, posting date, number of applicants, and status (active/closed).

                                                                                                                                                                                                                                                                                                                              * Applicant Access: View list of applicants for each job post with resume and contact options.

                                                                                                                                                                                                                                                                                                                              * Actions Menu: Edit, Close, or Remove job post.

Applicants Management
Overview
 Displays all applicants for selected job posts. Recruiters can view resumes, contact applicants, and update their status.


Components
                                                                                                                                                                                                                                                                                                                                 * Applicant List: Name, resume link, application date.

                                                                                                                                                                                                                                                                                                                                 * Status Update Controls: Interviewing, Rejected, Hired.

                                                                                                                                                                                                                                                                                                                                 * Contact Options: Open email client or dialer.

Marketplace
Overview
 Allows recruiters to list devices or equipment for sale. Listings are processed with the same pricing logic as the Hospital Marketplace.
Components
                                                                                                                                                                                                                                                                                                                                    * Listing Form: Title, description, photos, price.

                                                                                                                                                                                                                                                                                                                                    * Fee Calculation: Pricing logic determines listing cost.

                                                                                                                                                                                                                                                                                                                                    * Phone Reveal: Phone numbers hidden until “Show Phone Number” clicked.

                                                                                                                                                                                                                                                                                                                                    * Actions Menu: Edit, Mark as Sold, Remove.

Bulk Listing
Overview
 Recruiters can upload multiple jobs or devices at once via a CSV template. Bulk uploads are queued for admin moderation.
Components
                                                                                                                                                                                                                                                                                                                                       * CSV Upload Field: Validates file structure and required data.

                                                                                                                                                                                                                                                                                                                                       * Bulk Status Tracker: Shows number of listings pending, approved, or rejected.

Billing
Overview
 Displays payment methods, transaction history, and invoices for job postings and marketplace listings.
Components
                                                                                                                                                                                                                                                                                                                                          * Payment Methods: Stripe integration to add, update, or remove cards.

                                                                                                                                                                                                                                                                                                                                          * Transaction Log: Lists charges, refunds, and downloadable receipts.

                                                                                                                                                                                                                                                                                                                                          * Invoices Tab: Past and current invoices with PDF download.
Functionality Summary
                                                                                                                                                                                                                                                                                                                                             * Dashboard displays recruiter-specific metrics, quick links, and live notifications.

                                                                                                                                                                                                                                                                                                                                             * Post a Job auto-fills location, supports drafts/previews, and processes Stripe payments.

                                                                                                                                                                                                                                                                                                                                             * Search for Jobs lists roles filtered to recruiter’s location with advanced search options.

                                                                                                                                                                                                                                                                                                                                             * My Jobs shows recruiter’s postings with edit, close, and applicant tracking.

                                                                                                                                                                                                                                                                                                                                             * Applicants Management enables resume viewing, contact actions, and status updates.

                                                                                                                                                                                                                                                                                                                                             * Marketplace allows device/equipment listing with pricing logic, photo uploads, and hidden phone reveal.

                                                                                                                                                                                                                                                                                                                                             * Bulk Listing supports CSV upload, validates entries, and queues for admin approval.

                                                                                                                                                                                                                                                                                                                                             * Billing manages Stripe payment methods, displays transaction history, and stores downloadable invoices.


QA Expectations – Recruiter Portal
Post a Job
QA ID
	Test Description
	POST-01
	Form submission with empty required fields shows field-level errors.
	POST-02
	Valid job post submission triggers success message and appears in “My Job Listings”.
	POST-03
	Invalid salary or date triggers field-specific validation errors.
	POST-04
	Duplicate post warning appears if title + location match existing active post.
	POST-05
	Default expiration set to 30 days if no date is manually set.
	Search for Jobs
QA ID
	Test Description
	SEARCH-01
	Filters return accurate and updated job results based on input.
	SEARCH-02
	“No results” state is styled and clearly explained.
	SEARCH-03
	Bookmarked jobs persist on reload or navigation.
	SEARCH-04
	Results can be sorted by newest, oldest, salary, or location.
	SEARCH-05
	Jobs link correctly to full job detail views.
	My Job Listings
QA ID
	Test Description
	MYJOB-01
	Only jobs posted by the current recruiter are listed.
	MYJOB-02
	Status badge updates accurately (Active, Expired, Filled).
	MYJOB-03
	Editing updates the post without duplicating or losing data.
	MYJOB-04
	Deleting a job shows confirmation modal and is non-reversible.
	MYJOB-05
	Applications are linked correctly to their respective jobs.
	Bulk Job Listings
QA ID
	Test Description
	BULK-01
	Incorrect CSV format returns clear validation error with row and column references.
	BULK-02
	Valid bulk upload shows preview screen before confirmation.
	BULK-03
	CSV rows with errors are isolated and user can retry corrected upload.
	BULK-04
	Upload log tracks status and actions (Imported, Failed, Skipped).
	BULK-05
	File size or entry limits (e.g., max 100 listings) are enforced and warned early.
	 Billing
QA ID
	Test Description
	BILL-01
	Credit card field supports common formats and shows inline errors.
	BILL-02
	Invoices are downloadable in PDF and correctly linked to posting history.
	BILL-03
	Billing history includes timestamps and job references.
	BILL-04
	Invalid payment method returns error without crashing billing page.
	BILL-05
	Payment confirmation is logged and visible instantly on page reload.
	Device Marketplace
QA ID
	Test Description
	DEV-01
	New equipment listings post successfully with image, condition, and price.
	DEV-02
	Marketplace filters (location, price, device type) are responsive and accurate.
	DEV-03
	Marketplace listings show only available/unsold items unless otherwise filtered.
	DEV-04
	Messaging seller/buyer initiates secure contact method.
	DEV-05
	Images for uploaded devices render correctly on all screen sizes.
	General QA
QA ID
	Test Description
	GEN-01
	Dashboard tiles all redirect to correct recruiter modules.
	GEN-02
	UI displays correctly on small, medium, and large viewports.
	GEN-03
	Navigation is accessible via keyboard (Tab, Enter, Esc)
	GEN-04
	Role-switching (e.g., via URL manipulation) is blocked with appropriate errors.
	GEN-05
	Data changes (e.g., post update or billing) reflect system-wide within 5 seconds.